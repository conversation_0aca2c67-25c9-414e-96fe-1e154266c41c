

import { AppConfig } from "@4-sure/ui-platform";

export async function getAppConfig() {

    return appConfig;
}

export async function getWorkflowTypeConfig(type: string) {
    const config = appConfig['workflowTypes'][type];

    return config;
}

export async function getViewConfig(type: string, viewId: string) {
  const config = appConfig['workflowTypes'][type].views[viewId];

  return config;
}

export async function getStateConfig(state: string | number) {
    const activeState = appConfig.states[state];

    return activeState;
}

export async function getScreenConfig(state: string | number, screen: string) {
    const activeScreen = appConfig.states[state].screens[screen];
    return activeScreen;
}


export const appConfig: AppConfig = {
    toolBarConfig: {
        // toolbarTabs: [
        //   {
        //     moduleName: 'Workflow',
        //     tabs: [
        //       { tabName: 'Claims & Jobs', navigationPath: '/workflow/list-view' },
        //       { tabName: 'Calendar', navigationPath: '/workflow/calendar-view' },
        //       { tabName: 'Map', navigationPath: '/workflow/map-view' },
        //       {
        //         tabName: 'Copilot Work',
        //         navigationPath: '/workflow/copilot-view',
        //       },
        //     ],
        //   },
        //   {
        //     moduleName: 'Teams',
        //     tabs: [{ tabName: 'Teams', navigationPath: '/teams' }],
        //   },
        //   {
        //     moduleName: 'Dashboard',
        //     tabs: [
        //       { tabName: 'Metrics', navigationPath: '/dashboard/metrics' },
        //       { tabName: 'Finance', navigationPath: '/dashboard/finance' },
        //       { tabName: 'Ratings', navigationPath: '/dashboard/ratings' },
        //     ],
        //   },
        //   {
        //     moduleName: 'Working Capital',
        //     tabs: [
        //       {
        //         tabName: 'Consignment',
        //         navigationPath: '/working-capital/consignment',
        //       },
        //       {
        //         tabName: 'Material Loan',
        //         navigationPath: '/working-capital/material-loan',
        //       },
        //       {
        //         tabName: 'Vehicle Loan',
        //         navigationPath: '/working-capital/vehicle-loan',
        //       },
        //     ],
        //   },
        // ],
        // toolbarTabsConfig: {
        //     moduleName: 'Workflow',
        //     tabs: [
        //       { tabName: 'Claims & Jobs', navigationPath: '/workflow/list-view' },
        //       { tabName: 'Calendar', navigationPath: '/workflow/calendar-view' },
        //       { tabName: 'Map', navigationPath: '/workflow/map-view' },
        //       {
        //         tabName: 'Copilot Work',
        //         navigationPath: '/workflow/copilot-view',
        //       },
        //     ],
        // },
        modulesList: [
          { name: 'Workflow', path: '/workflow' },
          {name: 'Teams', path: '/teams' },
          {name: 'Dashboard', path: '/dashboard' },
          {name: 'Working Capital', path: '/working-capital' },

        ],
        buttonText: 'New Job',
        username: 'Thabo Mabikho',
        email: '<EMAIL>',
        // avatarHandler: (e) => {
        //   console.log('User avatar clicked', e);
        // },
        // optionsHandler: (e) => {
        //   console.log('Options Btn clicked', e);
        // },
        menuItems: [
          {
            label: 'Switch Accounts',
            menuItems: [
                {
                    label: 'Profile Settings',
                    icon: 'bell-02',
                    // onClick: (e) => {
                    //   console.log('Click event registered:', e);
                    // },
                  }
            ],
          },
          {
            label: 'Logout',
            menuItems: [
                {
                    label: 'Access',
                  icon: 'bell-02',
                  // onClick: () => {
                  //   alert('You have been logged out');
                  // },
                }
            ],
          },
          {
            label: 'No Action',
            menuItems: [
                {
                    label: 'Disabled Action',
                    icon: 'x-xircle',
                    disabled: true,
                }
            ],
          },
        ],
      },
    fetchCalls: [
        {key: 'posts', method: 'GET', url: 'https://jsonplaceholder.typicode.com/todos'},
    ],
    onEnter: [],
    onLeave: [],
    workflow: {
      defaultWorkflowType: 'claims-and-jobs',
      workflowTypes: {
        'claims-and-jobs': {
          fetchCalls: [
              {key: 'claims', method: 'GET', url: 'https://jsonplaceholder.typicode.com/todos'},
  
          ],
          onEnter: [],
          onLeave: [],
          views: {
              details: {
                  layout: {},
                  onEnter: [],
                  onLeave: [],
                  fetchCalls: [],
                  fragments: [
                      // Filter Fragment
                      // Claim card detail list fragment
                      {component: 'TestFilteringAndPagination', props: {items: '$pageItems'}, layout: {}}
                  ],
                  filterConfig: {
                      inputCollection: 'claims',
                      outputCollection: 'filteredClaims',
                      filterConditionsSource: 'workflowFilters',
                      sortingCriteriaSource: 'workflowSorts'
                  },
                  paginationConfig: {
                      dataSource: 'filteredClaims',
                      itemsPerPage: 15,
                  }
              },
          },
          actionPanels:  [
            {
              icon: 'bell-02',
              title: 'Messages', //?actionPanel=Messages--bell-02
        
              // fetchCalls: [],
              layout: {},
              onEnter: [],
              onLeave: [],
              fragments: [
                {
                  component: 'NoteCardList',
                  layout: {
                    marginTop: '20px',
                    marginLeft: '10px',
                    marginRight: '10px',
                  },
                  props: {
                    notes: [
                      {
                        title: 'Find Invoice',
                        date: '05/10/23',
                        time: '11:42',
                        content:
                          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                      },
                      {
                        title: 'Find Invoice',
                        date: '05/10/23',
                        time: '11:42',
                        content:
                          'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                      },
                      {
                        title: 'Find Invoice',
                        date: '05/10/23',
                        time: '11:42',
                        content:
                          'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                      },
                    ],
                  },
                },
              ],
              actionLevel: 'bottomControls',
            },
            {
              icon: 'trash-01',
              title: 'Scratch Pad', //?actionPanel=Messages--bell-02
        
              // fetchCalls: [],
              layout: {},
              onEnter: [],
              onLeave: [],
              fragments: [
                {
                  component: 'NoteCardList',
                  layout: {
                    marginTop: '20px',
                    marginBottom: '20px',
                    marginLeft: '10px',
                    marginRight: '10px',
                  },
                  props: {
                    notes: [
                      {
                        title: 'Find Invoice',
                        date: '05/10/23',
                        time: '11:42',
                        content:
                          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                      },
                      {
                        title: 'Find Invoice',
                        date: '05/10/23',
                        time: '11:42',
                        content:
                          'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                      },
                      {
                        title: 'Find Invoice',
                        date: '05/10/23',
                        time: '11:42',
                        content:
                          'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                      },
                    ],
                  },
                },
              ],
              actionLevel: 'bottomControls',
            },
          ],
       },
      },
    },
    states: {
        98: {
            title: { 
                template: 'Payment preview completed {toUpperCase(todo.completed)} with userId of {foo.userId} count is {toUpperCase(todo.title)} with count {count(todo.title)}', 
                
            },
            fetchCalls: [
                    {key: 'todos', method: 'GET', url: 'https://jsonplaceholder.typicode.com/todos'},
                    {key: 'todo', method: 'GET', url: 'https://jsonplaceholder.typicode.com/todos/1'},
                    {key: 'foo', method: 'GET', url: 'https://jsonplaceholder.typicode.com/todos/2'},
            ],
            onEnter: [
                // { type: 'clientAction', action: 'log', payload:{item:  'testing something new'}},
                // { type: 'serverAction', action: 'doServerWork', payload: { foo: 'bar' } },
                // { type: 'clientAction', action: 'storeInLocalStorage', payload: { key: 'Country', value: '@form:country' } },
                // { type: 'clientAction', action: 'localStorage.setItem', payload: { key: 'test', value: 'Tendai' } },
            ],

            onLeave: [],
            defaultScreen: 'preview',
            screens: { 
                // screen
                preview: {
                    layout: {
                        backgroundColor: 'coral',
                    },
                    onEnter: [
                        // { type: 'clientAction', action: 'storeInLocalStorage', payload: { key: 'bar', value: '@form:screenInput' } },
                    ],
                    onLeave: [],
                    fetchCalls: [],
                    fragments: [
                        {
                          component: 'FormBuilder',
                          props: {
                              config: {
                                style: {
                                   display: 'grid',
                                   gridTemplateColumns: 'repeat(2, 1fr)',
                                   gridTemplateRows: 'repeat(2, 1fr)',
                                   rowGap: '15px',
                                   columnGap: '15px',
                                },
                                controls: [
                                    {type: 'underlined-text', name: 'first_name', label: 'First Name', css: {wrapper: {gridColumn: 1, gridRow: 1, }}},
                                    {type: 'underlined-text', name: 'last_name', label: 'Last Name', css: {wrapper: {gridColumn: 2, gridRow: 1, }}},
                                    {type: 'underlined-text', name: 'other_name', label: 'Other Name', css: {wrapper: {gridColumn: 1, gridRow: 2, }}},
                                    {type: 'underlined-text', name: 'middle_name', label: 'Middle Name', css: {wrapper: {gridColumn: 2, gridRow: 2,}}},
                                ]
                            }
                          },
                          layout: {marginBottom: '50px'},

                      },
                        {
                            component: 'FormBuilder',
                            props: {
                                config: {
                                  style: {
                                     display: 'grid',
                                     gridTemplateColumns: 'repeat(2, 1fr)',
                                     gridTemplateRows: 'repeat(2, 1fr)',
                                     rowGap: '15px',
                                     columnGap: '15px',
                                  },
                                  controls: [
                                      {type: 'underlined-text', name: 'first_name', label: 'First Name', css: {wrapper: {gridColumn: 1, gridRow: 1, }}},
                                      {type: 'underlined-text', name: 'last_name', label: 'Last Name', css: {wrapper: {gridColumn: 2, gridRow: 1, }}},
                                      {type: 'underlined-text', name: 'other_name', label: 'Other Name', css: {wrapper: {gridColumn: 1, gridRow: 2, }}},
                                      {type: 'underlined-text', name: 'middle_name', label: 'Middle Name', css: {wrapper: {gridColumn: 2, gridRow: 2,}}},
                                  ]
                              }
                            },
                            layout: {},

                        },

                       
                    ],
                    navs: [
                        { label: 'Go Back', position: 'left' },
                        { label: 'Next', position: 'right', colorVariant: 'proceed', toScreen: 'summary' },
                    ],
                },
                // end screen
                  // screen
                  summary: {
                    layout: {
                        backgroundColor: 'thistle'
                    },
                    onEnter: [
                        // { type: 'clientAction', action: 'storeInLocalStorage', payload: { key: 'bar', value: '@form:screenInput' } },
                    ],
                    onLeave: [],
                    fetchCalls: [],
                    fragments: [
                        {
                            component: 'TestComponentA',
                            props: {},
                            layout: {
                                "gridColumn": "1 / 2",
                                "gridRow": "1 / 2"
                            }
                        },
                        {
                            component: 'TestComponentB',   
                            props: {},
                            layout: {
                                marginBottom: '50px',
                                "gridColumn": "1 / 2",
                                "gridRow": "1 / 2"
                            }
                        },
                        {
                          component: 'FormBuilder',
                          props: {
                              config: {
                                style: {
                                   display: 'grid',
                                   gridTemplateColumns: 'repeat(2, 1fr)',
                                   gridTemplateRows: 'repeat(2, 1fr)',
                                   rowGap: '15px',
                                   columnGap: '15px',
                                   marginTop: '20px',
                                },
                                controls: [
                                    {type: 'underlined-text', name: 'last_name', label: 'Last Name', css: {wrapper: {gridColumn: 1, gridRow: 1, }}},
                                    {type: 'underlined-text', name: 'institution', label: 'Institutions', css: {wrapper: {gridColumn: 2, gridRow: 1, }}},
                                ]
                            }
                          },
                          layout: {},

                      },
                    ],
                    navs: [
                        { label: 'Go Back To..', toScreen: 'preview', position: 'left' },
                        // { label: 'Next', onClick: [], position: 'left' },
                    ],
                },
                // end screen
            },
            actionPanels:  [
                {
                  icon: 'bell-02',
                  title: 'Messages', //?actionPanel=Messages--bell-02
            
                  // fetchCalls: [],
                  layout: {},
                  onEnter: [],
                  onLeave: [],
                  fragments: [
                    {
                      component: 'NoteCardList',
                      layout: {
                        marginTop: '20px',
                        marginLeft: '10px',
                        marginRight: '10px',
                      },
                      props: {
                        notes: [
                          {
                            title: 'Find Invoice',
                            date: '05/10/23',
                            time: '11:42',
                            content:
                              'Lorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                          },
                          {
                            title: 'Find Invoice',
                            date: '05/10/23',
                            time: '11:42',
                            content:
                              'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                          },
                          {
                            title: 'Find Invoice',
                            date: '05/10/23',
                            time: '11:42',
                            content:
                              'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                          },
                        ],
                      },
                    },
                  ],
                  actionLevel: 'bottomControls',
                },
                {
                  icon: 'trash-01',
                  title: 'Scratch Pad', //?actionPanel=Messages--bell-02
            
                  // fetchCalls: [],
                  layout: {},
                  onEnter: [],
                  onLeave: [],
                  fragments: [
                    {
                      component: 'NoteCardList',
                      layout: {
                        marginTop: '20px',
                        marginBottom: '20px',
                        marginLeft: '10px',
                        marginRight: '10px',
                      },
                      props: {
                        notes: [
                          {
                            title: 'Find Invoice',
                            date: '05/10/23',
                            time: '11:42',
                            content:
                              'Lorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                          },
                          {
                            title: 'Find Invoice',
                            date: '05/10/23',
                            time: '11:42',
                            content:
                              'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                          },
                          {
                            title: 'Find Invoice',
                            date: '05/10/23',
                            time: '11:42',
                            content:
                              'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                          },
                        ],
                      },
                    },
                  ],
                  actionLevel: 'bottomControls',
                },
              ],
        }
    }
};