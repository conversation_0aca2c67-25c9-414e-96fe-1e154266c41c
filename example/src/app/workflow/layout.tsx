import { AppShell } from "@4-sure/ui-platform";
import { getAppConfig } from "../../test-config";



export default async function WorkflowFlowApplicationWrapper({ children }: any) {

    const token = '';
    // Get State level config only
    const appConfig  = await getAppConfig();

    
    //
    const fetchCalls = appConfig.fetchCalls || [];
    const fetchResultsObject = (await Promise.all(fetchCalls
        .map( async fc => {
           const response = await fetch(fc.url);
           return {[fc.key]: await response.json()};
        })))
        .reduce((acc, res) => {
          return {...acc, ...res};
      }, {});

    return (
        <AppShell appConfig={appConfig} clientDataObject={{ ...fetchResultsObject }}>
            {children}
        </AppShell>
    )
}