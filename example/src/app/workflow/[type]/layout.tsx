import { getWorkflowTypeConfig } from "../../../test-config";
import { componentMap, WorkflowTypeShell } from '@4-sure/ui-platform';


// State 
export default async function WorkflowTYpeLayout({ children, params }: any) {
    const workflowType = params.type; // claim&jobs, calendar, map, copilot etc
    const workflowTypeConfig  = await getWorkflowTypeConfig(workflowType);

    const token = '';
    // Get State level config only


    
    //
    const fetchCalls = workflowTypeConfig.fetchCalls || [];
    const fetchResultsObject = (await Promise.all(fetchCalls
        .map( async fc => {
           const response = await fetch(fc.url);
           return {[fc.key]: await response.json()};
        })))
        .reduce((acc, res) => {
          return {...acc, ...res};
      }, {});

    return (
         <WorkflowTypeShell componentMap={componentMap} workflowTypeConfig={workflowTypeConfig} clientDataObject={{ ...fetchResultsObject}}>
            {children}
         </WorkflowTypeShell>
    )
}