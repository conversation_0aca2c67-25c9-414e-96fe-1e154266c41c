import {
  ClientWorkflowFiltersProcessor,
  componentMap,
  ViewRenderer,
  ViewShell,
} from '@4-sure/ui-platform';
import { getViewConfig, getWorkflowTypeConfig } from '../../../../test-config';

export default async function ViewPage({ params }: any) {
  const type = params.type;
  const viewId = params.view;
  const viewConfig = await getViewConfig(type, viewId);

  console.log(viewConfig);

  //
  const fetchCalls = viewConfig.fetchCalls || [];
  const fetchResultsObject = (
    await Promise.all(
      fetchCalls.map(async (fc) => {
        const response = await fetch(fc.url);
        return { [fc.key]: await response.json() };
      })
    )
  ).reduce((acc, res) => {
    return { ...acc, ...res };
  }, {});

  return (
    <ViewShell
      viewConfig={viewConfig}
      clientDataObject={{ ...fetchResultsObject }}
    >
      <ClientWorkflowFiltersProcessor view={viewConfig} />
      <ViewRenderer
        viewConfig={viewConfig}
        clientDataObject={{ ...fetchResultsObject }}
        componentMap={componentMap}
        submit={{}}
        fetcher={{}}
      />
    </ViewShell>
  );
}
