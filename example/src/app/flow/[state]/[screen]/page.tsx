import { ScreenConfig, ScreenShell } from '@4-sure/ui-platform';
import type { Metadata, ResolvingMetadata } from 'next';
import { useRouter } from 'next/navigation';
import { getScreenConfig } from '../../../../test-config';
import { ScreenWrapper } from './ScreenWrapper';

type Props = {
  params: { id: string };
  searchParams: { [key: string]: string | string[] | undefined };
};

// Meta
export async function generateMetadata(
  { params, searchParams }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  // read route params
  const id = params.id;

  // fetch data
  // const product = await fetch(`https://.../${id}`).then((res) => res.json())

  // optionally access and extend (rather than replace) parent metadata
  // const previousImages = (await parent).openGraph?.images || []

  return {
    title: 'Some Screen',
    //   openGraph: {
    //     images: ['/some-specific-page-image.jpg', ...previousImages],
    //   },
  };
}

// Screen
export default async function Screen({ params }: any) {
  // const claimId = params.claimId;
  // const jobId = params.jobId;
  const token = '';

  const screenConfig = await getScreenConfig(params.state, params.screen);
  // console.log({params})

  //
  const fetchCalls = screenConfig.fetchCalls || [];
  const fetchResultsObject = (
    await Promise.all(
      fetchCalls.map(async (fc) => {
        const response = await fetch(fc.url);
        return { [fc.key]: await response.json() };
      })
    )
  ).reduce((acc, res) => {
    return { ...acc, ...res };
  }, {});

  return (
    <ScreenWrapper
      screenConfig={screenConfig}
      clientDataObject={{ ...fetchResultsObject }}
    />
  );
}
