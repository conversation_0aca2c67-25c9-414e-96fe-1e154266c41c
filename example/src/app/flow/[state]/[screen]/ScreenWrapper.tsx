'use client';

import { ScreenConfig, ScreenShell } from '@4-sure/ui-platform';
import { useRouter } from 'next/navigation';

type ScreenWrapperProps = {
  screenConfig: ScreenConfig;
  clientDataObject: { [x: string]: any };
};

export const ScreenWrapper = ({
  screenConfig,
  clientDataObject,
}: ScreenWrapperProps) => {
  const router = useRouter();
  return (
    <>
      <ScreenShell
        screenConfig={screenConfig}
        clientDataObject={{ ...clientDataObject }}
      />
    </>
  );
};
