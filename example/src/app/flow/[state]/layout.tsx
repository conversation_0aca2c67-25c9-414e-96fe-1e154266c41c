import { getStateConfig } from "../../../test-config";
import { StateShell } from '@4-sure/ui-platform';


// State 
export default async function StateLayout({ children, params }: any) {
    const claimId = params.claimId;
    const jobId = params.jobId;
    const token = '';
    // Get State level config only
    const stateConfig  = await getStateConfig(params.state);

    const claim = claimId ? await Promise.resolve({}) : null;
    const job = jobId ? await Promise.resolve({}) : null;

    
    //
    const fetchCalls = stateConfig.fetchCalls || [];
    const fetchResultsObject = (await Promise.all(fetchCalls
        .map( async fc => {
           const response = await fetch(fc.url);
           return {[fc.key]: await response.json()};
        })))
        .reduce((acc, res) => {
          return {...acc, ...res};
      }, {});

    return (
         <StateShell stateConfig={stateConfig} clientDataObject={{ ...fetchResultsObject, claim, job}}>
            {children}
         </StateShell>
    )
}