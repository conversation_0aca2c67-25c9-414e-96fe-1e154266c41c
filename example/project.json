{"name": "example", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "example", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/example"}, "configurations": {"development": {"outputPath": "example"}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "example:build", "dev": true}, "configurations": {"development": {"buildTarget": "example:build:development", "dev": true}, "production": {"buildTarget": "example:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "example:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "example/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}