import { useEffect } from "react";
import { useSpaKeycloak } from "./SpaKeycloakAuthProvider"



export const SpaPrivateRoute = ({ component: Component}: any) => {

    const {keycloak } = useSpaKeycloak();

    useEffect(() => {
        if (keycloak) {
            if (!keycloak?.authenticated) {
                keycloak?.login();
            }
        }
    }, [keycloak]);

    if (!keycloak) {
        return <div>Loading ....</div>
    }

    if (!keycloak?.authenticated) {
        return <div>Authenticating...</div>
    }

    return (
        <Component />
    );
}