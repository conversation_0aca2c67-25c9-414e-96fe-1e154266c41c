import React, {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import Keycloak, { KeycloakConfig } from 'keycloak-js';
import { useAppStore } from '../Engine/useAppStore';
import { SplashScreenLoader } from '../Components/SplashScreen';



interface Props {
  config: KeycloakConfig;
  children: any;
  fieldAccessUrl: string;
}

interface IContext {
  keycloak: Keycloak | undefined;
  authenticated: boolean;
}

const KeycloakContext = createContext<IContext>(null!);

export const SpaKeycloakAuthProvider = ({ children, config, fieldAccessUrl }: Props) => {
  const [keycloak, setKeycloak] = useState<Keycloak>();
  const [authenticated, setAuthenticated] = useState(false);
  const keycloakInstance = new Keycloak(config); // Adjust the path as necessary
  const setState = useAppStore((state: any) => state.setState);
  const keycloakInitialized = useRef(false);

  useEffect(() => {
    let refreshInterval: any;
    if (!keycloakInitialized.current) {
      keycloakInstance
        .init({
          onLoad: 'check-sso',
          pkceMethod: 'S256',
          flow: 'standard',
          checkLoginIframe: false,
        })
        .then((authenticated) => {
          if (authenticated) {
            setKeycloak(keycloakInstance);
            setAuthenticated(true);
            if (keycloakInstance.token && fieldAccessUrl) {
              getFieldAccess(keycloakInstance.token)
              .then((fieldAccess) => {
                setState({
                  fieldAccess,
                });
              });
            }
            refreshInterval = scheduleTokenRefresh(keycloakInstance);
          } else {
            keycloakInstance.login();
          }
        })
        .catch((error) => {
          console.error('Keycloak initialization error', error);
          setAuthenticated(false);
        });

      keycloakInstance.onAuthSuccess = () => {
        setAuthenticated(true);
      };

      keycloakInstance.onAuthLogout = () => {
        setAuthenticated(false);
        setState({
          auth: null,
        });
      };
      keycloakInitialized.current = true;
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval)
      }
    }
  }, []);

  useEffect(() => {
    console.log({ authenticated, keycloak });
    if (!authenticated && keycloak) {
      keycloak.login();
    } else {
      setState({
        auth: {
          // userInfo: keycloak?.userInfo,
          // profile: keycloak?.profile,
          // tokenParsed: keycloak?.tokenParsed,
          // isTokenExpired: keycloak?.isTokenExpired,
          token: keycloak?.token,
          authenticated: keycloak?.authenticated,
          ...keycloak?.tokenParsed,
        },
      });
    }
  }, [keycloak]);

  // const setupTokenRefresh = (keycloakInstance: Keycloak) => {
  //   keycloakInstance.onTokenExpired = () => {
  //     keycloakInstance.updateToken(30).catch(() => {
  //       keycloakInstance.login();
  //     });
  //   };
  // };

  const getFieldAccess = async (token: string) => {
    const response = await fetch(fieldAccessUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    const data = await response.json();
    return data.payload;
  }

  const scheduleTokenRefresh = (keycloakInstance: Keycloak) => {
    const refreshInterval = setInterval(() => {
      keycloakInstance.updateToken(70) // 70 seconds remaining before expiration
        .then(refreshed => {
          if (refreshed) {
            console.log('Token successfully refreshed');
          } else {
            // console.warn('Token not refreshed, valid for only ' +
            //   Math.round(keycloakInstance?.tokenParsed?.exp + keycloakInstance?.timeSkew - new Date().getTime() / 1000) + ' seconds');
          }
        })
        .catch(err => {
          console.error('Failed to refresh token', err);
          clearInterval(refreshInterval);
        });
    }, 60000); // Check every 60 seconds
    return refreshInterval;
  };

  return (
    <KeycloakContext.Provider value={{ keycloak, authenticated }}>
      {keycloak ? children : <SplashScreenLoader transparent={true} />}
    </KeycloakContext.Provider>
  );
};

export const useSpaKeycloak = () => {
  const context = useContext<IContext>(KeycloakContext);
  if (!context) {
    throw new Error(
      'useSpaKeycloak must be used within a KeycloakAuthProvider'
    );
  }
  return context;
};
