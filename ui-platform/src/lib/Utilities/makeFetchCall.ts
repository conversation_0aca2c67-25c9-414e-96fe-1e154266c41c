// ui-platform/src/lib/Utilities/makeFetchCall.ts
type RequestOptions = {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  token?: string;
  headers?: Record<string, string>;
  contentType?: 'application/json' | 'application/x-www-form-urlencoded'; // Added contentType parameter
};

export async function makeFetchCall<T>(
  url: string,
  requestOptions: RequestOptions,
  body?: Record<string, any>,
  tokenPrefix?: 'Bearer' | 'Token'
): Promise<T> {
  const { method, token, headers, contentType } = requestOptions;
  const authTokenPrefix = tokenPrefix || 'Bearer';
  const options: RequestInit = {
    method: method ? method : 'POST',
    headers: {
      'Content-Type': contentType || 'application/json', // Use specified contentType or default to application/json
      ...(token ? { Authorization: `${authTokenPrefix} ${token}` } : {}),
      ...headers, // Allow custom headers
    },
    ...(body
      ? {
          body:
            contentType === 'application/x-www-form-urlencoded'
              ? new URLSearchParams(body as Record<string, string>).toString()
              : JSON.stringify(body),
        }
      : {}), // Handle body based on contentType
  };

  try {
    const response = await fetch(url, options);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data; // Return the entire response data
  } catch (error) {
    console.error('Fetch call failed:', error);
    throw error; // Rethrow the error for further handling
  }
}
