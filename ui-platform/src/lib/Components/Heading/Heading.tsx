import { ComponentPropsWithoutRef, createElement } from 'react';
import styled, { css } from 'styled-components';
import { additionalFontStyling } from '../../Utilities';

type HeadingLevel = 1 | 2 | 3 | 4 | 5 | 6;
export type HeadingType =
  | 'page-heading'
  | 'sub-heading'
  | 'section-heading'
  | 'hero'
  | 'control-view-heading'
  | 'control-view-subheading'
  | 'dashboard-tile-heading'
  | 'dashboard-tile-subheading'
  | 'registration-heading'
  | 'registration-subheading';
interface HeadingProps extends ComponentPropsWithoutRef<'h1'> {
  level?: HeadingLevel;
  type?: HeadingType;
  useFontTransformer?: boolean;
}

const headingStyles = (useFontTransformer?: boolean) => {
  return {
    'page-heading': css`
      font-family: ${(props) =>
        props.theme.DesktopFlowsHeadingsPageHeading.fontFamily};
      font-size: ${(props) =>
        props.theme.DesktopFlowsHeadingsPageHeading.fontSize}px;
      line-height: 57.24px;
      letter-spacing: ${(props) =>
        props.theme.DesktopFlowsHeadingsPageHeading.letterSpacing};
      paragraph-spacing: ${(props) =>
        props.theme.DesktopFlowsHeadingsPageHeading.paragraphSpacing};
      paragraph-indent: ${(props) =>
        props.theme.DesktopFlowsHeadingsPageHeading.paragraphIndent};
      text-transform: ${(props) =>
        props.theme.DesktopFlowsHeadingsPageHeading.textCase};
      text-decoration: ${(props) =>
        props.theme.DesktopFlowsHeadingsPageHeading.textDecoration};
      ${(props) =>
        additionalFontStyling(
          props.theme.DesktopFlowsHeadingsPageHeading.fontWeight,
          useFontTransformer
        )}
    `,
    'sub-heading': css`
      font-family: ${(props) =>
        props.theme.DesktopFlowsHeadingsSubHeading.fontFamily};
      font-size: ${(props) =>
        props.theme.DesktopFlowsHeadingsSubHeading.fontSize}px;
      line-height: 25.41px;
      letter-spacing: ${(props) =>
        props.theme.DesktopFlowsHeadingsSubHeading.letterSpacing};
      paragraph-spacing: ${(props) =>
        props.theme.DesktopFlowsHeadingsSubHeading.paragraphSpacing};
      paragraph-indent: ${(props) =>
        props.theme.DesktopFlowsHeadingsSubHeading.paragraphIndent};
      text-transform: ${(props) =>
        props.theme.DesktopFlowsHeadingsSubHeading.textCase};
      text-decoration: ${(props) =>
        props.theme.DesktopFlowsHeadingsSubHeading.textDecoration};
      ${(props) =>
        additionalFontStyling(
          props.theme.DesktopFlowsHeadingsSubHeading.fontWeight,
          useFontTransformer
        )}
    `,
    'section-heading': css`
      font-family: ${(props) =>
        props.theme.DesktopFlowsHeadingsSectionHeading.fontFamily};
      font-size: ${(props) => props.theme.DesktopFlowsHeadingsSectionHeading}px;
      line-height: 25.41px;
      letter-spacing: ${(props) =>
        props.theme.DesktopFlowsHeadingsSectionHeading.letterSpacing};
      paragraph-spacing: ${(props) =>
        props.theme.DesktopFlowsHeadingsSectionHeading.paragraphSpacing};
      paragraph-indent: ${(props) =>
        props.theme.DesktopFlowsHeadingsSectionHeading.paragraphIndent};
      text-transform: ${(props) =>
        props.theme.DesktopFlowsHeadingsSectionHeading.textCase};
      text-decoration: ${(props) =>
        props.theme.DesktopFlowsHeadingsSectionHeading.textDecoration};
      ${(props) =>
        additionalFontStyling(
          props.theme.DesktopFlowsHeadingsSectionHeading.fontWeight,
          useFontTransformer
        )}
    `,
    hero: css`
      font-family: ${(props) =>
        props.theme.DesktopFlowsHeadingsHero.fontFamily};
      font-size: ${(props) => props.theme.DesktopFlowsHeadingsHero.fontSize}px;
      line-height: 84.72px;
      letter-spacing: ${(props) =>
        props.theme.DesktopFlowsHeadingsHero.letterSpacing};
      paragraph-spacing: ${(props) =>
        props.theme.DesktopFlowsHeadingsHero.paragraphSpacing};
      paragraph-indent: ${(props) =>
        props.theme.DesktopFlowsHeadingsHero.paragraphIndent};
      text-transform: ${(props) =>
        props.theme.DesktopFlowsHeadingsHero.textCase};
      text-decoration: ${(props) =>
        props.theme.DesktopFlowsHeadingsHero.textDecoration};
      ${(props) =>
        additionalFontStyling(
          props.theme.DesktopFlowsHeadingsHero.fontWeight,
          useFontTransformer
        )}
    `,
    'control-view-heading': css`
      font-family: ${(props) =>
        props.theme.DesktopControlViewsPageHeading.fontFamily};
      font-size: ${(props) =>
        props.theme.DesktopControlViewsPageHeading.fontSize}px;
      line-height: 38.12px;
      letter-spacing: ${(props) =>
        props.theme.DesktopControlViewsPageHeading.letterSpacing};
      paragraph-spacing: ${(props) =>
        props.theme.DesktopControlViewsPageHeading.paragraphSpacing};
      paragraph-indent: ${(props) =>
        props.theme.DesktopControlViewsPageHeading.paragraphIndent};
      text-transform: ${(props) =>
        props.theme.DesktopControlViewsPageHeading.textCase};
      text-decoration: ${(props) =>
        props.theme.DesktopControlViewsPageHeading.textDecoration};
      ${(props) =>
        additionalFontStyling(
          props.theme.DesktopControlViewsPageHeading.fontWeight,
          useFontTransformer
        )}
    `,
    'control-view-subheading': css`
      font-family: ${(props) =>
        props.theme.DesktopControlViewsSubHeading.fontFamily};
      font-size: ${(props) =>
        props.theme.DesktopControlViewsSubHeading.fontSize}px;
      line-height: 25.41px;
      letter-spacing: ${(props) =>
        props.theme.DesktopControlViewsSubHeading.letterSpacing};
      paragraph-spacing: ${(props) =>
        props.theme.DesktopControlViewsSubHeading.paragraphSpacing};
      paragraph-indent: ${(props) =>
        props.theme.DesktopControlViewsSubHeading.paragraphIndent};
      text-transform: ${(props) =>
        props.theme.DesktopControlViewsSubHeading.textCase};
      text-decoration: ${(props) =>
        props.theme.DesktopControlViewsSubHeading.textDecoration};
      ${(props) =>
        additionalFontStyling(
          props.theme.DesktopControlViewsSubHeading.fontWeight,
          useFontTransformer
        )}
    `,
    'dashboard-tile-heading': css`
      font-family: ${(props) =>
        props.theme.DesktopCardsDashboardTilesHeading.fontFamily};
      /* font-weight: ${(props) => props.theme.FontWeightsInter1}; */
      font-size: ${(props) =>
        props.theme.DesktopCardsDashboardTilesHeading.fontSize}px;
      line-height: 25.41px;
      letter-spacing: ${(props) =>
        props.theme.DesktopCardsDashboardTilesHeading.letterSpacing};
      paragraph-spacing: ${(props) =>
        props.theme.DesktopCardsDashboardTilesHeading.paragraphSpacing};
      paragraph-indent: ${(props) =>
        props.theme.DesktopCardsDashboardTilesHeading.paragraphIndent};
      text-transform: ${(props) =>
        props.theme.DesktopCardsDashboardTilesHeading.textCase};
      text-decoration: ${(props) =>
        props.theme.DesktopCardsDashboardTilesHeading.textDecoration};
      ${(props) =>
        additionalFontStyling(
          props.theme.DesktopCardsDashboardTilesHeading.fontWeight,
          useFontTransformer
        )}
    `,
    'dashboard-tile-subheading': css`
      font-family: ${(props) =>
        props.theme.DesktopCardsDashboardTilesSubHeading.fontFamily};
      /* font-weight: ${(props) => props.theme.FontWeightsInter5}; */
      font-size: ${(props) =>
        props.theme.DesktopCardsDashboardTilesSubHeading.fontSize}px;
      line-height: 25.41px;
      letter-spacing: ${(props) =>
        props.theme.DesktopCardsDashboardTilesSubHeading.letterSpacing};
      paragraph-spacing: ${(props) =>
        props.theme.DesktopCardsDashboardTilesSubHeading.paragraphSpacing};
      paragraph-indent: ${(props) =>
        props.theme.DesktopCardsDashboardTilesSubHeading.paragraphIndent};
      text-transform: ${(props) =>
        props.theme.DesktopCardsDashboardTilesSubHeading.textCase};
      text-decoration: ${(props) =>
        props.theme.DesktopCardsDashboardTilesSubHeading.textDecoration};
      ${(props) =>
        additionalFontStyling(
          props.theme.DesktopCardsDashboardTilesSubHeading.fontWeight,
          useFontTransformer
        )}
    `,
    'registration-heading': css`
      font-family: ${(props) =>
        props.theme.DesktopRegistrationHeadingsHeader.fontFamily};
      /* font-weight: ${(props) => props.theme.FontWeightsInter6}; */
      font-size: ${(props) =>
        props.theme.DesktopRegistrationHeadingsHeader.fontSize}px;
      line-height: 25.41px;
      letter-spacing: ${(props) =>
        props.theme.DesktopRegistrationHeadingsHeader.letterSpacing};
      paragraph-spacing: ${(props) =>
        props.theme.DesktopRegistrationHeadingsHeader.paragraphSpacing};
      paragraph-indent: ${(props) =>
        props.theme.DesktopRegistrationHeadingsHeader.paragraphIndent};
      text-transform: ${(props) =>
        props.theme.DesktopRegistrationHeadingsHeader.textCase};
      text-decoration: ${(props) =>
        props.theme.DesktopRegistrationHeadingsHeader.textDecoration};
      ${(props) =>
        additionalFontStyling(
          props.theme.DesktopRegistrationHeadingsHeader.fontWeight,
          useFontTransformer
        )}
    `,
    'registration-subheading': css`
      font-family: ${(props) =>
        props.theme.DesktopRegistrationHeadingsSubHeading.fontFamily};
      /* font-weight: ${(props) => props.theme.FontWeightsInter6}; */
      font-size: ${(props) =>
        props.theme.DesktopRegistrationHeadingsSubHeading.fontSize}px;
      line-height: 25.41px;
      letter-spacing: ${(props) =>
        props.theme.DesktopRegistrationHeadingsSubHeading.letterSpacing};
      paragraph-spacing: ${(props) =>
        props.theme.DesktopRegistrationHeadingsSubHeading.paragraphSpacing};
      paragraph-indent: ${(props) =>
        props.theme.DesktopRegistrationHeadingsSubHeading.paragraphIndent};
      text-transform: ${(props) =>
        props.theme.DesktopRegistrationHeadingsSubHeading.textCase};
      text-decoration: ${(props) =>
        props.theme.DesktopRegistrationHeadingsSubHeading.textDecoration};
      ${(props) =>
        additionalFontStyling(
          props.theme.DesktopRegistrationHeadingsSubHeading.fontWeight,
          useFontTransformer
        )}
    `,
  };
};

const Comp = styled(
  ({ level = 1, type, useFontTransformer = true, ...rest }: HeadingProps) => {
    const HeadingTag = `h${level}`;
    return createElement(HeadingTag, {
      'data-testid': 'Heading',
      type: type,
      ...rest,
    });
  }
)`
  margin: unset;
  ${({ type, useFontTransformer }) =>
    type ? headingStyles(useFontTransformer)[type] : ''}
`;

/**
 * The Heading component renders a heading element with a custom level and type
 * to support different heading styles.
 *
 * @param {number} [level=1] The level of the heading, 1-6.
 * @param {string} [type] The type of the heading, which can be one of the following:
 * - 'page-heading'
 * - 'sub-heading'
 * - 'section-heading'
 * - 'hero'
 * - 'control-view-heading'
 * - 'control-view-subheading'
 * - 'dashboard-tile-heading'
 * - 'dashboard-tile-subheading'
 * - 'registration-heading'
 * - 'registration-subheading'
 * @param {React.HTMLAttributes<HTMLHeadingElement>} [rest] The rest of the props.
 * @returns {JSX.Element} The heading element.
 */
export function Heading({
  level = 1,
  type,
  useFontTransformer = true,
  ...rest
}: HeadingProps) {
  return (
    <Comp
      level={level}
      type={type}
      useFontTransformer={useFontTransformer}
      {...rest}
    />
  );
}
