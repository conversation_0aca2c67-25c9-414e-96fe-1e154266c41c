import type { <PERSON>a, StoryObj } from '@storybook/react';
import { ComponentPropsWithoutRef } from 'react';
import { withComponentShowcase } from '../../Utilities';
import { Heading, HeadingType } from './Heading';

const meta: Meta<typeof Heading> = {
  component: Heading,
  title: 'Components/Heading',
};
export default meta;
type Story = StoryObj<typeof Heading>;

export const Overview: Story = {
  args: {
    children: 'This is a heading',
  },
};

const types: HeadingType[] = [
  'hero',
  'control-view-heading',
  'control-view-subheading',
  'dashboard-tile-heading',
  'dashboard-tile-subheading',
  'page-heading',
  'section-heading',
  'sub-heading',
  'registration-heading',
  'registration-subheading',
];

export const HeadingTypes: Story = {
  render: (args) =>
    withComponentShowcase(
      <Heading {...{ children: 'This is a heading' }} {...args} />
    )('type', types, true),
  args: {
    children: 'This is a heading',
  },
};

export const HeadingLevels: Story = {
  render: (args) =>
    withComponentShowcase(
      <Heading {...{ children: 'This is a heading' }} {...args} />
    )('level', [1, 2, 3, 4, 5, 6], true),
  args: {
    children: 'This is a heading',
  },
};
