import type { Meta, StoryObj } from '@storybook/react';
import  Badge  from './Badge';

const meta: Meta<typeof Badge> = {
  component: Badge,
  title: 'Components/Badge',
};
export default meta;
type Story = StoryObj<typeof Badge>;

export const DefaultBadgeStory: Story = {

  argTypes: {
   
    text: {
        control: 'text',
        defaultValue: '1'
    },
    color: {
        control: 'select',
        options: ['primary', 'secondary', 'error', 'success']
        
    },
    background: {
        control: 'select',
        options: ['primary', 'secondary', 'error', 'success']
        
    }
  },
  args: {
    text: '1',
    color: 'primary',
    background: 'primary'
  }
};

