import React from 'react';
import styled from 'styled-components';

/**
 * Props for BadgeContainer component.
 * 
 * @property {ReactNode} children - The child elements to render inside the container.
 */
interface BadgeContainerProps {
    children: any
}

interface BadgeColorMap {
  [key: string]: string
}

const Container = styled(({ ...rest }) => <div {...rest}></div>)`
width: 20px;`;

/**
 * BadgeContainer component that wraps children in a styled, grid-based container.
 * 
 * @param {BadgeContainerProps} props - The properties object.
 * @returns {JSX.Element} A styled container for badge components.
 */
export const BadgeContainer: React.FC<BadgeContainerProps> = ({children}) => {
  return <Container>{children}</Container>;
};

