import React from 'react';
import styled from 'styled-components';
import { BadgeContainer } from './BadgeContainer';

/**
 * BadgeProps defines the props for the Badge component.
 * 
 * @property {string} background - Defines the background color of the badge.
 * @property {string} color - Defines the text color of the badge.
 * @property {string} icon - Icon used within the badge (future enhancement).
 * @property {string} text - The text content displayed inside the badge.
 * @property {string} size - Size of the badge (future enhancement).
 */
interface BadgeProps {
  background: string;
  color: string;
  icon?: string;
  text: string;
  size?: string;
}

interface BadgeColorMap {
  [key: string]: string;
}

/**
 * Styled component for the Badge, using grid layout and consuming design tokens for colors and spacing.
 */
const BadgeDiv = styled.div<BadgeProps>`
  display: grid;
  place-items: center;
  width: 100%;
  height: 100%;
  border: 1px solid ${(props) => props.theme.ColorsBackgroundInverse};
  border-radius: 50%;
  background-color: ${(props) => {
    const backgroundMap: BadgeColorMap = {
      primary: props.theme.ColorsUtilityColorFocus,
      secondary: props.theme.ColorsUtilityColorWarning,
      success: props.theme.ColorsUtilityColorSuccess,
      error: props.theme.ColorsUtilityColorError,
    };
    return backgroundMap[props.background] || props.theme.ColorsUtilityColorFocus;
  }};
  color: ${(props) => {
    const colorMap: BadgeColorMap = {
      primary: props.theme.ColorsTypographyPrimary,
      secondary: props.theme.ColorsTypographySecondary,
      success: props.theme.ColorsStrokesDefault,
      error: props.theme.ColorsStrokesDefault,
    };
    return colorMap[props.color] || props.theme.ColorsTypographyPrimary;
  }};
`;

/**
 * Badge component that renders a badge with customizable color and background.
 * 
 * @param {BadgeProps} props - The properties object.
 * @returns {JSX.Element} A styled badge component.
 */
const Badge: React.FC<BadgeProps> = ({ color, icon, text, background }) => {
  return (
    <BadgeContainer>
      <BadgeDiv background={background} color={color} text={text}>
        {text}
      </BadgeDiv>
    </BadgeContainer>
  );
};

export default Badge;
