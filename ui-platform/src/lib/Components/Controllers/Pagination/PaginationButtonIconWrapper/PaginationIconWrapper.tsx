import { Children, cloneElement, ReactElement, ReactNode } from 'react';
import styled, { useTheme } from 'styled-components';
import { IconProps } from '../../../Icons';

export interface IPaginationIconWrapperProps {
  children: ReactElement<IconProps>;
  disabled?: boolean;
}

const BtnContent = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 12px;
  height: 12px;
  place-items: center;
  place-content: center;
  display: grid;
  color: ${(props) => props?.theme.ColorsIconColorPrimary};
  box-sizing: border-box;
`;

/**
 * Renders an SVG icon with a color based on the disabled state.
 *
 * @param {ReactElement<IconProps>} children - The icon to render
 * @param {boolean} [disabled=false] - Disable the icon
 * @return {JSX.Element} The icon with the color applied
 */
export function PaginationIconWrapper({
  children,
  disabled,
}: IPaginationIconWrapperProps) {
  const { ColorsIconColorPrimary, ColorsIconColorTertiary } = useTheme();
  const svgColor = disabled ? ColorsIconColorTertiary : ColorsIconColorPrimary;
  const SVGLabel = Children.map(children, (child) => {
    return cloneElement(child, { ...child.props, color: svgColor });
  });
  return <BtnContent>{SVGLabel}</BtnContent>;
}
