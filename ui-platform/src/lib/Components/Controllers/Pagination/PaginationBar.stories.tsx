import type { Meta, StoryObj } from '@storybook/react';
import { List, withItems } from '../../List/List';
import { PaginationModuleNavigation } from './ModuleNavigation/PaginationModuleNavigation';
import { moduleNavButtons } from './ModuleNavigation/modulePaginationDataSample';
import { PaginationBar } from './PaginationBar';
import {
  IPaginationPageCountProps,
  PaginationPageCount,
  useListPagination2,
} from './PaginationPageCount';
// import {useS}

const meta: Meta<typeof PaginationBar> = {
  component: PaginationBar,
  title: 'Components/Pagination/PaginationBar',
};
export default meta;
type Story = StoryObj<typeof PaginationBar>;

const pages: IPaginationPageCountProps['pages'] = [
  {
    label: 1,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 2,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 3,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 4,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 5,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
];

export const WithPageCount: Story = {
  args: {
    paginationItems: <PaginationPageCount pages={pages} currentPage={1} />,
  },
};

export const WithSetStartPage: Story = {
  args: {
    paginationItems: <PaginationPageCount pages={pages} currentPage={3} />,
  },
};

const mockList = [
  { id: '1', children: 'This is item 1' },
  { id: '2', children: 'This is item 2' },
  { id: '3', children: 'This is item 3' },
  { id: '4', children: 'This is item 4' },
  { id: '5', children: 'This is item 5' },
  { id: '6', children: 'This is item 6' },
  { id: '7', children: 'This is item 7' },
  { id: '8', children: 'This is item 8' },
  { id: '9', children: 'This is item 9' },
  { id: '10', children: 'This is item 10' },
  { id: '11', children: 'This is item 11' },
  { id: '12', children: 'This is item 12' },
  { id: '13', children: 'This is item 13' },
  { id: '14', children: 'This is item 14' },
  { id: '15', children: 'This is item 15' },
];

const Comp = (props: { id: string; children: string }) => (
  <div id={props.id}>{props.children}</div>
);

const PaginatedList = () => {
  const { pages, currentPage, pageItems, ...rest } = useListPagination2({
    items: mockList,
    itemsPerPage: 2,
  });
  return (
    <>
      <List style={{ color: 'white' }}>{withItems(Comp)(pageItems)}</List>
      <PaginationBar
        paginationItems={
          <PaginationPageCount
            pages={pages}
            currentPage={currentPage}
            {...rest}
          />
        }
      />
    </>
  );
};

export const WithUseListPagination: Story = {
  render: () => <PaginatedList />,
};

export const WithModuleNavigation: Story = {
  render: () => (
    <PaginationBar
      paginationItems={
        <PaginationModuleNavigation navButtons={moduleNavButtons} />
      }
    />
  ),
};
