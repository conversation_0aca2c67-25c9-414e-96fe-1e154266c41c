import * as React from 'react';
import styled from 'styled-components';

export interface IPaginationContainerProps {
  children: React.ReactNode | React.ReactNode[];
  className?: string;
}

const Container = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  height: max-content;
  padding: ${(props) => props.theme.SpacingMd} 0;
  background: linear-gradient(0deg, #262728 0%, rgba(38, 39, 40, 0) 95%);
  backdrop-filter: blur(4px);
  place-items: center;
  display: grid;
  position: relative;
`;

/**
 * Renders a pagination container component with the provided props.
 *
 * @param {string} [className] - The CSS class name for the container.
 * @param {React.ReactNode | React.ReactNode[]} children - The children to
 *   render inside the container.
 *
 * @return {JSX.Element} The rendered pagination container component.
 */

export function PaginationContainer({
  children,
  className,
}: IPaginationContainerProps) {
  return (
    <Container data-testid={'navigation-container'} {...{ className }}>
      {children}
    </Container>
  );
}
