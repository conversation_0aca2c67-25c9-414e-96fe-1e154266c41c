import type { <PERSON>a, StoryObj } from '@storybook/react';
import styled from 'styled-components';
import { PaginationContainer } from './PaginationContainer';

const meta: Meta<typeof PaginationContainer> = {
  component: PaginationContainer,
  title: 'Components/Pagination/PaginationContainer',
};
export default meta;
type Story = StoryObj<typeof PaginationContainer>;

const EmptyDiv = styled.div`
  height: 50px;
  width: 250px;
`;

export const Overview: Story = {
  args: {
    children: <EmptyDiv />,
  },
};
