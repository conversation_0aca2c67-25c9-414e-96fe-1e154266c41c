import * as React from 'react';
import styled from 'styled-components';
import { Divider } from '../../Dividers/Divider';
import { PaginationContainer } from './PaginationContainer/PaginationContainer';
import {
  IPaginationListProps,
  PaginationList,
} from './PaginationList/PaginationList';

export interface IPaginationBarProps {
  paginationItems: IPaginationListProps['children'];
  className?: string;
}

const Glowline = styled(Divider)`
  position: absolute !important;
  top: 0;
`;

/**
 * Renders a pagination bar component with the provided props.
 *
 * @param {IPaginationBarProps} props - The component props.
 * @param {React.ReactNode | React.ReactNode[]} props.paginationItems - The
 *   pagination items to display.
 * @param {string} [props.className] - The CSS class name for the container.
 *
 * @return {JSX.Element} The pagination bar component.
 */
export function PaginationBar({
  paginationItems,
  className,
}: IPaginationBarProps) {
  return (
    <PaginationContainer {...{ className }}>
      <Glowline
        background="default"
        type="tabActive"
        size="fullWidth"
        state="grey"
      />
      <PaginationList>{paginationItems}</PaginationList>
    </PaginationContainer>
  );
}
