import type { Meta, StoryObj } from '@storybook/react';
import { PaginationButton } from './PaginationButton';

const meta: Meta<typeof PaginationButton> = {
  component: PaginationButton,
  title: 'Components/Pagination/PaginationButton',
};
export default meta;
type Story = StoryObj<typeof PaginationButton>;

export const WithPageNumber: Story = {
  args: {
    btnLabel: 1,
  },
};

export const WithNextNavigation: Story = {
  args: {
    btnLabel: 'N',
  },
};

export const WithBackNavigation: Story = {
  args: {
    btnLabel: 'B',
  },
};

export const IsActive: Story = {
  args: {
    btnLabel: 1,
    active: true,
  },
};

export const IsDisabled: Story = {
  args: {
    btnLabel: 1,
    disabled: true,
  },
};
