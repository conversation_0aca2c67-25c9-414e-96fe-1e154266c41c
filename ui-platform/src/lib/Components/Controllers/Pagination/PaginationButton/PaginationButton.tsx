import React, {
  ButtonHTMLAttributes,
  CSSProperties,
  ReactElement,
  ReactNode,
  useMemo,
} from 'react';
import styled from 'styled-components';
import { IconProps } from '../../../Icons';
import { PaginationIconWrapper } from '../PaginationButtonIconWrapper/PaginationIconWrapper';
import { PaginationTextWrapper } from '../PaginationButtonTextWrapper/PaginationTextWrapper';

export interface IPaginationButtonProps
  extends ButtonHTMLAttributes<HTMLButtonElement> {
  btnLabel: ReactNode;
  active?: boolean;
}

const notIcon = (label: ReactNode) =>
  typeof label === 'string' || typeof label === 'number';

const textBaseStyling = `
  padding-bottom: 4px;
  padding-left: 8px;
  padding-right: 8px;
  padding-top: 4px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;

const iconBaseStyling = `
  padding: 6px;
  justify-content: center;
  align-items: center;
`;

const interactiveTextBaseStyling = `
  padding-bottom: 6px;
  padding-left: 12px;
  padding-right: 12px;
  padding-top: 6px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;

const interactiveIconBaseStyling = `
  padding: 6px;
  justify-content: center;
  align-items: center;
`;

const Container = styled(({ ...rest }: IPaginationButtonProps) => {
  const { active, btnLabel, ...btnProps } = rest;
  return <button {...btnProps}></button>;
})`
  background-color: unset;
  border-radius: 2px;
  overflow: hidden;
  border: 1px ${(props) => props?.theme.ColorsStrokesDefault} solid;
  gap: 10;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  cursor: pointer;
  transition: all 0.2s ease-in-out 0.1s;
  ${(props) => (notIcon(props?.btnLabel) ? textBaseStyling : iconBaseStyling)}

  &:hover {
    background: ${(props) => props?.theme.ColorsButtonColorPagenationHover};

    ${(props) =>
      notIcon(props?.btnLabel)
        ? interactiveTextBaseStyling
        : interactiveIconBaseStyling}
  }

  &:active {
    background: ${(props) => props?.theme.ColorsButtonColorPagenationActivated};
    cursor: default;

    ${(props) =>
      notIcon(props?.btnLabel)
        ? interactiveTextBaseStyling
        : interactiveIconBaseStyling}
  }

  &:disabled {
    background: ${(props) => props?.theme.ColorsButtonColorPagenationDisabled};
    color: ${(props) => props?.theme.ColorsTypographyDisabled};
    border: 1px ${(props) => props?.theme.ColorsTypographyDisabled} solid;
    cursor: default;

    ${(props) =>
      notIcon(props?.btnLabel)
        ? interactiveTextBaseStyling
        : interactiveIconBaseStyling}
  }

  && {
    ${(props) =>
      props?.active &&
      `
        background: ${props?.theme.ColorsButtonColorPagenationActivated};
        cursor: default;
        ${
          notIcon(props?.btnLabel)
            ? interactiveTextBaseStyling
            : interactiveIconBaseStyling
        }
      `}

    &:hover {
    }
  }
`;

/**
 * Renders a pagination button component.
 *
 * @param {IPaginationButtonProps} props - The props for the pagination button.
 * @param {string | number | ReactElement<IconProps>} props.btnLabel - The label for the button.
 * @param {...ButtonHTMLAttributes<HTMLButtonElement>} props.props - Additional props for the button from standard HTML Button attributes.
 * @return {ReactElement} The rendered pagination button component.
 */
export function PaginationButton({
  btnLabel,
  ...props
}: IPaginationButtonProps) {
  return (
    <Container {...{ btnLabel, ...props }}>
      {notIcon(btnLabel) ? (
        <PaginationTextWrapper>
          {btnLabel as string | number}
        </PaginationTextWrapper>
      ) : (
        <PaginationIconWrapper>
          {btnLabel as ReactElement<IconProps>}
        </PaginationIconWrapper>
      )}
    </Container>
  );
}
