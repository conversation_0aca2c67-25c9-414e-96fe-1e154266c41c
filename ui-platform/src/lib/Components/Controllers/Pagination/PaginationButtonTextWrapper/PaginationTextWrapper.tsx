import { ReactNode } from 'react';
import styled from 'styled-components';

export interface IPaginationTextWrapperProps {
  children: string | number;
}

const BtnContent = styled(({ ...rest }) => <div {...rest}></div>)`
  color: ${(props) => props?.theme.ColorsIconColorPrimary};
  font-size: ${(props) => props.theme.FontSize3}px;
  font-family: 'Inter', sans-serif;
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  text-transform: capitalize;
  word-wrap: break-word;
  box-sizing: border-box;
`;

/**
 * Renders a pagination module navigation text wrapper component.
 *
 * @param {IPaginationTextWrapperProps} props - The component props.
 * @param {ReactNode} props.children - The content to be rendered inside the component.
 * @return {JSX.Element} The pagination module navigation text wrapper component.
 */
export function PaginationTextWrapper({
  children,
}: IPaginationTextWrapperProps) {
  return <BtnContent>{children}</BtnContent>;
}
