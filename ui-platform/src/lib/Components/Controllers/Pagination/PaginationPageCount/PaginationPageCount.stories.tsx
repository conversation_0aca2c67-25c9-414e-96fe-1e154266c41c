import type { Meta, StoryObj } from '@storybook/react';
import {
  IPaginationPageCountProps,
  PaginationPageCount,
} from '.';

const meta: Meta<typeof PaginationPageCount> = {
  component: PaginationPageCount,
  title: 'Components/Pagination/PaginationPageCount',
};
export default meta;
type Story = StoryObj<typeof PaginationPageCount>;

const pages: IPaginationPageCountProps['pages'] = [
  {
    label: 1,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 2,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 3,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 4,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 5,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
];

export const WithNavigationLastPage: Story = {
  args: {
    currentPage: pages.length,
    pages: pages,
  },
};

export const WithNavigationFirstPage: Story = {
  args: {
    ...WithNavigationLastPage.args,
    currentPage: 1,
  },
};

export const WithNavigationMiddlePage: Story = {
  args: {
    ...WithNavigationLastPage.args,
    currentPage: 3,
  },
};
