import usePagination from '@mui/material/usePagination';
import { useEffect, useMemo, useState } from 'react';
import styled, { useTheme } from 'styled-components';
import { Icon } from '../../../Icons';
import { getNestedProperty } from '../../../../Engine/helpers/client-utils';
import { PaginationButton } from '../PaginationButton/PaginationButton';
import {
  IPaginationPageCountProps,
  PaginationControl,
} from './PaginationPageCountModel';

const PaginationButtonsContainer = styled.div`
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: min-content;
  justify-content: center;
  align-items: center;
  gap: ${(props) => props.theme.SpacingSm};
  width: 100%;
  margin: 4px 0;
  box-sizing: border-box;
`;

/**
 * Renders a pagination component with page count and navigation buttons.
 *
 * @param {Object} props - The props object containing the following properties:
 *   - currentPage: The current page number (default: 1).
 *   - nextControl: An object with the following properties:
 *     - label: The label for the next button (default: <Icon type="arrow-block-right" />).
 *     - onClick: A function to handle the click event of the next button (default: console.log('Next page:', currentPage && currentPage + 1)).
 *   - pages: An array of page objects.
 *   - prevControl: An object with the following properties:
 *     - label: The label for the previous button (default: <Icon type="arrow-block-left" />).
 *     - onClick: A function to handle the click event of the previous button (default: console.log('Prev page:', currentPage && currentPage - 1)).
 *
 * @return {JSX.Element} The rendered pagination component.
 */
export function PaginationPageCount({
  currentPage = 1,
  nextControl = {
    label: <Icon type="arrow-block-right" size={14} />,
    onClick: (currentPage?: number) =>
      console.log('Next page:', currentPage && currentPage + 1),
  },
  pages,
  prevControl = {
    label: <Icon type="arrow-block-left" size={14} />,
    onClick: (currentPage?: number) =>
      console.log('Prev page:', currentPage && currentPage - 1),
  },
}: IPaginationPageCountProps) {
  const theme = useTheme();
  const [activePage, setActivePage] = useState<number | undefined>(currentPage);
  const { items } = usePagination({
    count: pages.length,
    siblingCount: 0,
    page: currentPage,
  });
  const handleClick = (
    num?: number,
    onClick?: PaginationControl['onClick']
  ) => {
    setActivePage(() => {
      onClick && onClick(num);
      return num;
    });
  };
  const prevBtn = items.filter((item) => item.type === 'previous')[0];
  const nextBtn = items.filter((item) => item.type === 'next')[0];
  const selected = items.filter((item) => item.selected === true)[0];
  const handleNext = () => {
    setActivePage((prev) => {
      nextControl.onClick &&
        selected.page &&
        nextControl.onClick(selected.page);
      return prev && prev < pages.length ? prev + 1 : pages.length;
    });
  };
  const handlePrev = () => {
    setActivePage((prev) => {
      prevControl.onClick &&
        selected.page &&
        prevControl.onClick(selected.page);
      return prev && prev > 1 ? prev - 1 : 1;
    });
  };
  return (
    <>
      <PaginationButton
        style={{ marginLeft: '6px' }}
        key={`pagination-prev-nav`}
        onClick={(e) => {
          handlePrev();
          prevBtn?.onClick(e);
        }}
        btnLabel={prevControl.label}
        disabled={prevBtn?.disabled}
      />
      <PaginationButtonsContainer>
        {items.map(({ page, type, selected, onClick, ...item }, idx) => {
          if (type === 'start-ellipsis') {
            return (
              <PaginationButton
                key={`pagination-page-start-ellipsis`}
                btnLabel={'...'}
                // disabled={true}
                onClick={(e) => {
                  handlePrev();
                  prevBtn?.onClick(e);
                }}
              />
            );
          } else if (type === 'end-ellipsis') {
            return (
              <PaginationButton
                key={`pagination-page-end-ellipsis`}
                btnLabel={'...'}
                // disabled={true}
                onClick={(e) => {
                  handleNext();
                  nextBtn?.onClick(e);
                }}
              />
            );
          } else if (type === 'page' && typeof page === 'number') {
            const paginationItem = pages[page - 1];
            return (
              <PaginationButton
                key={`pagination-page-${paginationItem.label}`}
                type="button"
                onClick={(e) => {
                  handleClick(
                    idx + 1,
                    () =>
                      paginationItem?.onClick &&
                      page &&
                      paginationItem.onClick(page)
                  );
                  onClick(e);
                }}
                btnLabel={paginationItem.label}
                active={selected}
                {...item}
              />
            );
          }
        })}
      </PaginationButtonsContainer>
      <PaginationButton
        style={{ marginRight: '6px' }}
        key={`pagination-next-nav`}
        onClick={(e) => {
          handleNext();
          nextBtn?.onClick(e);
        }}
        btnLabel={nextControl.label}
        disabled={nextBtn?.disabled}
      />
    </>
  );
}
type Itemtype = { [key: string]: any };
type UsePaginationReturnType<T> = {
  pages: IPaginationPageCountProps['pages'];
  currentPage: number;
  pageItems: T[];
  setCurrentPage: (page: number) => void;  // Add this line
  nextControl?: PaginationControl;
  prevControl?: PaginationControl;
  sortItemsBy?: (key: string, order?: 'asc' | 'desc') => void;
};
type UsePaginationType<T> = {
  items: Array<T>;
  itemsPerPage: number;
};

export const useListPagination2 = <T extends Itemtype>({
  items,
  itemsPerPage,
}: UsePaginationType<T>): UsePaginationReturnType<T> => {
  const [sortedItems, setSortedItems] = useState<T[]>(items);
  
  // Update sortedItems when items change
  useEffect(() => {
    setSortedItems(items);
  }, [items]);
  
  // Calculate numberOfPages based on sortedItems length
  const numberOfPages = useMemo(() => {
    return Math.ceil(sortedItems.length / itemsPerPage);
  }, [sortedItems, itemsPerPage]);
  
  const pageItems = useMemo(
    () =>
      Array.from({ length: numberOfPages }, (_, idx) =>
        sortedItems.slice(idx * itemsPerPage, (idx + 1) * itemsPerPage)
      ),
    [sortedItems, itemsPerPage, numberOfPages]
  );

  const [currentPage, setCurrentPage] = useState<number>(1);

  const [sortConfig, setSortConfig] = useState<{ key: string; order: 'asc' | 'desc' } | null>(null);

  const sortItemsBy = (key: string, initialOrder?: 'asc' | 'desc') => {
    // Default to 'asc' if no initialOrder is provided
    let order: 'asc' | 'desc' = initialOrder || 'asc';
    
    // Toggle order if the same key is clicked again
    if (sortConfig && sortConfig.key === key) {
      order = sortConfig.order === 'asc' ? 'desc' : 'asc';
    }

    // Update sort configuration
    setSortConfig({ key, order });
    
    const newlySortedItems = [...items].sort((a, b) => {
      const aValue = getNestedProperty(a, key, null);
      const bValue = getNestedProperty(b, key, null);
      
      if (order === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });
    // setCurrentPage(1);
    setSortedItems(newlySortedItems);
  };

  const paginationPages = useMemo(
    () =>
      Array.from({ length: numberOfPages }, (_, idx) => ({
        label: idx + 1,
        onClick: () => setCurrentPage(idx + 1),
      })),
    [numberOfPages]
  );

  const nextControl = useMemo(
    () => ({
      label: <Icon size={12} type="arrow-block-right" />,
      onClick: (currentPage?: number) =>
        currentPage && setCurrentPage(currentPage + 1),
    }),
    []
  );

  const prevControl = useMemo(
    () => ({
      label: <Icon size={12} type="arrow-block-left" />,
      onClick: (currentPage?: number) =>
        currentPage && setCurrentPage(currentPage - 1),
    }),
    []
  );

  return {
    currentPage: currentPage,
    pages: paginationPages,
    pageItems: pageItems[currentPage - 1],
    setCurrentPage: setCurrentPage,  // Add this line
    nextControl: nextControl,
    prevControl: prevControl,
    sortItemsBy: sortItemsBy,
  };
};
