import { ModuleButton } from './ModuleNavigationModel';
export const moduleNavButtons: ModuleButton[] = [
  {
    btnValue: 'BACK TO WORKFLOW',
    size: 'large',
    actiontype: 'warning',
    onClick: () => alert('Button Clicked!'),
    position: 'left',
  },
  {
    btnValue: 'BACK',
    size: 'large',
    actiontype: 'alternative',
    onClick: () => alert('Button Clicked!'),
    position: 'left',
  },
  {
    btnValue: 'RESCHEDULE',
    size: 'large',
    actiontype: 'default',
    onClick: () => alert('Button Clicked!'),
    position: 'center',
  },
  {
    btnValue: 'SAVE DRAFT',
    size: 'large',
    actiontype: 'proceed',
    onClick: () => alert('Button Clicked!'),
    position: 'right',
  },
  {
    btnValue: 'SUBMIT',
    size: 'large',
    actiontype: 'preferred',
    onClick: () => alert('Button Clicked!'),
    position: 'right',
  },
];
