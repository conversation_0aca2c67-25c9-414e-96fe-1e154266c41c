import { ComponentPropsWithoutRef, useMemo } from 'react';
import styled, { useTheme } from 'styled-components';
import { TextButton } from '../../../Buttons/TextButton/TextButton';
import { ModuleButton } from './ModuleNavigationModel';

const Container = styled.div`
  display: grid;
  grid-auto-columns: 1fr;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr;
  grid-template-areas: 'Left Center Right';
  grid-auto-flow: row;
  justify-content: space-between;
  align-items: center;
  gap: 0 ${(props) => props.theme.GapXxl};
  width: 100%;
`;

const LeftSlot = styled.div`
  grid-area: Left;
  display: grid;
  grid-auto-flow: column;
  justify-content: start;
  align-items: center;
  gap: 0 ${(props) => props.theme.GapMd};
  min-width: max-content;
`;
const CenterSlot = styled.div`
  grid-area: Center;
  display: grid;
  grid-auto-flow: column;
  justify-content: center;
  align-items: center;
  gap: 0 ${(props) => props.theme.GapMd};
  min-width: max-content;
`;
const RightSlot = styled.div`
  grid-area: Right;
  display: grid;
  grid-auto-flow: column;
  justify-content: end;
  align-items: center;
  gap: 0 ${(props) => props.theme.GapMd};
  min-width: max-content;
`;

const NavButton = styled(TextButton)``;

interface Props {
  navButtons: ModuleButton[];
}

/**
 * Renders a pagination module navigation component with buttons grouped into left, center, and right slots.
 *
 * @param {Props} props - The component props.
 * @param {Array<disabled?: boolean;
 * btnValue: ReactNode;
 * actiontype?: 'preferred' | 'alternative' | 'proceed' | 'warning' | 'default' | 'attention';
 * size?: 'small' | 'large';
 * onClick?: () => void;
 * className?: string;
 * style?: CSSProperties;
 * position: 'left' | 'center' | 'right';>} props.navButtons - The navigation buttons to display.
 * @return {JSX.Element} The pagination module navigation component.
 */
export function PaginationModuleNavigation({ navButtons = [] }: Props) {
  // Get the theme
  const theme = useTheme();

  // Filter the buttons by position
  const leftNavButtons = useMemo(
    () => navButtons.filter((button) => button.position === 'left'),
    [navButtons]
  );
  const centerNavButtons = useMemo(
    () => navButtons.filter((button) => button.position === 'center'),
    [navButtons]
  );
  const rightNavButtons = useMemo(
    () => navButtons.filter((button) => button.position === 'right'),
    [navButtons]
  );

  return (
    // Render the container for the navigation buttons
    <Container>
      {/* Render the left navigation buttons */}
      <LeftSlot>
        {leftNavButtons.map(({ size, style, ...rest }: ModuleButton, index) => (
          <NavButton
            // Set the style for the button
            style={theme.DesktopButtonsModuleNavigationMedium}
            key={index}
            size={size || 'large'}
            {...rest}
          />
        ))}
      </LeftSlot>

      {/* Render the center navigation buttons */}
      <CenterSlot>
        {centerNavButtons.map(({ size, ...rest }: ModuleButton, index) => (
          <NavButton
            // Set the style for the button
            style={theme.DesktopButtonsModuleNavigationMedium}
            key={index}
            size={size || 'large'}
            {...rest}
          />
        ))}
      </CenterSlot>

      {/* Render the right navigation buttons */}
      <RightSlot>
        {rightNavButtons.map(({ size, ...rest }: ModuleButton, index) => (
          <NavButton
            // Set the style for the button
            style={theme.DesktopButtonsModuleNavigationMedium}
            key={index}
            size={size || 'large'}
            {...rest}
          />
        ))}
      </RightSlot>
    </Container>
  );
}
