import { Meta, StoryObj } from '@storybook/react';
import { PaginationModuleNavigation } from './PaginationModuleNavigation';
import { moduleNavButtons } from './modulePaginationDataSample';

const meta: Meta<typeof PaginationModuleNavigation> = {
  component: PaginationModuleNavigation,
  title: 'Components/Pagination/PaginationModuleNavigation',
};
export default meta;

type Story = StoryObj<typeof PaginationModuleNavigation>;

export const Overview: Story = {
  args: {
    navButtons: moduleNavButtons,
  },
};
