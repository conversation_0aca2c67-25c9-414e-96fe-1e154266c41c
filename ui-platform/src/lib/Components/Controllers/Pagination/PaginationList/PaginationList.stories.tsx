import type { Meta, StoryObj } from '@storybook/react';
import { IPaginationPageCountProps } from '../PaginationPageCount';
import { PaginationPageCount } from '../PaginationPageCount/PaginationPageCount';
import { PaginationList } from './PaginationList';

const meta: Meta<typeof PaginationList> = {
  component: PaginationList,
  title: 'Components/Pagination/PaginationList',
};
export default meta;
type Story = StoryObj<typeof PaginationList>;

const pages: IPaginationPageCountProps['pages'] = [
  {
    label: 1,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 2,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 3,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 4,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 5,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
];

const morePages: IPaginationPageCountProps['pages'] = [
  {
    label: 1,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 2,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 3,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 4,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 5,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 6,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 7,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 8,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 9,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
  {
    label: 10,
    onClick: (currentPage?: number) => console.log('Page:', currentPage),
  },
];

export const WithModuleNavigation: Story = {
  args: {
    children: [
      <div
        key={`nav-0`}
        style={{
          width: 124,
          paddingLeft: 16,
          paddingRight: 16,
          paddingTop: 8,
          paddingBottom: 8,
          background: '#121212',
          boxShadow: '0px 0px 3px #F8F8F8',
          borderRadius: 4,
          justifyContent: 'center',
          alignItems: 'center',
          gap: 8,
          display: 'flex',
        }}
      >
        <div
          style={{
            flex: '1 1 0',
            textAlign: 'center',
            color: '#E5E5E5',
            fontSize: 14,
            fontFamily: 'Inter',
            fontWeight: '400',
            textTransform: 'uppercase',
            wordWrap: 'break-word',
          }}
        >
          Decision
        </div>
      </div>,
      <div
        key={`nav-1`}
        style={{
          width: 124,
          paddingLeft: 16,
          paddingRight: 16,
          paddingTop: 8,
          paddingBottom: 8,
          background: '#121212',
          boxShadow: '0px 0px 3px #F8F8F8',
          borderRadius: 4,
          justifyContent: 'center',
          alignItems: 'center',
          gap: 8,
          display: 'flex',
        }}
      >
        <div
          style={{
            flex: '1 1 0',
            textAlign: 'center',
            color: '#E5E5E5',
            fontSize: 14,
            fontFamily: 'Inter',
            fontWeight: '400',
            textTransform: 'uppercase',
            wordWrap: 'break-word',
          }}
        >
          Decision
        </div>
      </div>,
      <div
        key={`nav-2`}
        style={{
          width: 124,
          paddingLeft: 16,
          paddingRight: 16,
          paddingTop: 8,
          paddingBottom: 8,
          background: '#121212',
          boxShadow: '0px 0px 3px #F8F8F8',
          borderRadius: 4,
          justifyContent: 'center',
          alignItems: 'center',
          gap: 8,
          display: 'flex',
        }}
      >
        <div
          style={{
            flex: '1 1 0',
            textAlign: 'center',
            color: '#E5E5E5',
            fontSize: 14,
            fontFamily: 'Inter',
            fontWeight: '400',
            textTransform: 'uppercase',
            wordWrap: 'break-word',
          }}
        >
          Decision
        </div>
      </div>,
      <div
        key={`nav-3`}
        style={{
          width: 124,
          paddingLeft: 16,
          paddingRight: 16,
          paddingTop: 8,
          paddingBottom: 8,
          background: '#121212',
          boxShadow: '0px 0px 3px #F8F8F8',
          borderRadius: 4,
          justifyContent: 'center',
          alignItems: 'center',
          gap: 8,
          display: 'flex',
        }}
      >
        <div
          style={{
            flex: '1 1 0',
            textAlign: 'center',
            color: '#E5E5E5',
            fontSize: 14,
            fontFamily: 'Inter',
            fontWeight: '400',
            textTransform: 'uppercase',
            wordWrap: 'break-word',
          }}
        >
          Decision
        </div>
      </div>,
    ],
  },
};

export const WithPageNavigation: Story = {
  args: { children: <PaginationPageCount pages={pages} currentPage={1} /> },
};

export const WithMorePagesNavigation: Story = {
  args: { children: <PaginationPageCount pages={morePages} currentPage={1} /> },
};
