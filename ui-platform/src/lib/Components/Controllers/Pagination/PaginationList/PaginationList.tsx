import * as React from 'react';
import styled from 'styled-components';

export interface IPaginationListProps {
  children: React.ReactElement[] | JSX.Element;
}

const Container = styled.div`
  justify-content: center;
  align-items: center;
  gap: 0 ${(props) => props.theme.GapMd};
  display: grid;
  grid-auto-flow: column;
  width: 100%;
  padding: 0 ${(props) => props.theme.SpacingLg};
`;

/**
 * Renders a pagination list component with the provided children.
 *
 * @param {IPaginationListProps} props - The component props.
 * @param {ReactNode} props.children - The children to render inside the list.
 * @return {JSX.Element} - The pagination list component.
 */
export function PaginationList({ children, ...props }: IPaginationListProps) {
  return <Container>{children}</Container>;
}
