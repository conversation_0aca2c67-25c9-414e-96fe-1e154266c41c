import React, { useState } from 'react';
import { Tooltip, ITooltipProps } from '../Tooltip';
import styled from 'styled-components';
import { TextButton } from '../../../Buttons/TextButton/TextButton';

/**
 * A HOC that adds a tooltip to a component. The tooltip is a child of the
 * component and is positioned relative to the component based on the
 * `tooltipProps.type` property. The tooltip is shown when the mouse enters the
 * component and hidden when the mouse leaves the component.
 *
 * @param WrappedComponent The component to wrap with the tooltip.
 * @param tooltipProps The props for the Tooltip component. The `type` property
 *   determines the position of the tooltip. The possible values are 'arrowLeft',
 *   'arrowRight', and 'arrowBottom'. The default is 'arrowBottom'.
 * @returns A new component that wraps the original component with a tooltip.
 */
export const withTooltip = (
  WrappedComponent: React.ComponentType<any>,
  tooltipProps: ITooltipProps
) => {
  return (props: any) => {
    const [showTooltip, setShowTooltip] = useState(false);

    /**
     * Handles the mouse enter event by setting showTooltip to true.
     * This shows the tooltip.
     */
    const handleMouseEnter = () => {
      setShowTooltip(true);
    };

    /**
     * Handles the mouse leave event by setting showTooltip to false.
     * This hides the tooltip.
     */
    const handleMouseLeave = () => {
      setShowTooltip(false);
    };

    /**
     * Returns the position of the tooltip based on the value of `tooltipProps.type`.
     * The possible values are:
     * - 'arrowLeft': positions the tooltip on the left side of the component with
     *   the arrow pointing to the right.
     * - 'arrowRight': positions the tooltip on the right side of the component with
     *   the arrow pointing to the left.
     * - 'arrowBottom': positions the tooltip below the component with the arrow
     *   pointing up.
     * The default is 'arrowBottom'.
     * @returns An object with CSS properties that define the position of the
     *   tooltip.
     */
    const getTooltipPosition = () => {
      switch (tooltipProps.type) {
        case 'arrowLeft':
          return {
            top: '50%',
            left: '100%', 
            transform: 'translate(0, -50%)', 
          };
        case 'arrowRight':
          return {
            top: '50%',
            left: '-100%', 
            transform: 'translate(-100%, -50%)', 
          };
        case 'arrowBottom':
          return {
            bottom: '100%', 
            left: '50%',
            transform: 'translate(-50%, 0)', 
          };
        default:
          return {}; 
      }
    };

    return (
      <div
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        style={{ position: 'relative', display: 'inline-block' }}
      >
        <WrappedComponent {...props} />
        {showTooltip && (
          <div style={{ position: 'absolute', ...getTooltipPosition() }}>
            <Tooltip {...tooltipProps} />
          </div>
        )}
      </div>
    );
  };
};


const Container = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); 
  gap: ${(props) => props?.theme.GapXxl};
  justify-items: start;
  padding: ${(props) => props.theme.SpacingXxl};
`;

/**
 * ExampleComponent that displays a TextButton and shows a tooltip on hover.
 * The TextButton is wrapped inside a styled Container component.
 * @returns A TextButton component wrapped with a styled container.

 */

export const ExampleComponent = () => (
  <Container>
    <TextButton
      btnValue="Hover Over Me"
      size="large"
      actiontype="preferred"
      onClick={() => alert('Button Clicked!')}
    />
  </Container>
);
