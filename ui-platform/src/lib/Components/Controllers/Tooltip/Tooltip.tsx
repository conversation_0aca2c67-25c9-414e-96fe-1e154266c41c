import * as React from 'react';
import { Children, HTMLAttributes, cloneElement } from 'react';
import styled, { useTheme } from 'styled-components';
import { Icon, IconProps } from '../../Icons';

/**
 * Tooltip component properties interface
 * @interface ITooltipProps
 * @property {'arrowBottom' | 'arrowLeft' | 'arrowRight'} type - Type of tooltip arrow positioning.
 * @property {string} message - The message to be displayed inside the tooltip.
 * @property {string} [className] - Optional class name for custom styling.
 */
export interface ITooltipProps {
  type: 'arrowBottom' | 'arrowLeft' | 'arrowRight';
  message: string;
  className?: string;
}

/**
 * Styled component for the Tooltip wrapper.
 * @param props.type - The positioning of the tooltip (arrow direction).
 */
export const ToolTipWrapper = styled.div<
  Omit<ITooltipProps & HTMLAttributes<HTMLDivElement>, 'message'>
>`
  position: relative;
  width: max-content;
  display: grid;
  grid-auto-flow: ${(props) =>
    props.type === 'arrowBottom' ? 'row' : 'column'};
  align-items: center;
  justify-content: start;
  text-align: center;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  ${(props) => props?.type === 'arrowBottom' && 'margin-bottom: -20px;'}
`;

/**
 * Styled component for the arrow tip when it is positioned at the bottom.
 */
const ArrowTipBottom = styled(({ ...rest }: Omit<IconProps, 'type'>) => (
  <Icon type="arrow-tip" {...rest} />
))`
  width: 11px;
  //position: relative;
  height: 30px;
  //margin-top: -12px;
  transform: rotate(90deg);
  grid-column: 1;
  grid-row: 2;
`;

/**
 * Styled component for the arrow tip when it is positioned to the left.
 */
const ArrowTipLeft = styled(({ ...rest }: Omit<IconProps, 'type'>) => (
  <Icon type="arrow-tip" {...rest} />
))`
  width: 11px;
  //position: relative;
  height: 30px;
  //object-fit: contain;
  transform: rotate(180deg);
  //margin-right: -2px;
  grid-column: 1;
  grid-row: 1;
`;

/**
 * Styled component for the arrow tip when it is positioned to the right.
 */
const ArrowTipRight = styled(({ ...rest }: Omit<IconProps, 'type'>) => (
  <Icon type="arrow-tip" {...rest} />
))`
  width: 11px;
  //position: relative;
  height: 30px;
  //object-fit: contain;
  //margin-left: -2px;
  grid-column: 2;
  grid-row: 1;
`;

/**
 * Styled component for the tooltip body (main area displaying the message).
 */
const ToolTipBody = styled.div`
  border-radius: ${(props) => props.theme.RadiusXs};
  background-color: ${(props) => props?.theme.ColorsControllersDefault};
  display: grid;
  grid-template-columns: 1fr;
  align-items: flex-start;
  justify-content: flex-start;
  padding: ${(props) => props.theme.SpacingLg};
  box-sizing: border-box;
  /* width: clamp(100px, 200px, 306px); */
  max-width: 306px;
`;

/**
 * Styled component for the contents inside the tooltip.
 */
const ToolTipContents = styled.div`
  flex: 1;
  position: relative;
  font-weight: ${(props) => props.theme.FontWeightsInter1};
`;

/**
 * Tooltip component displaying a message with an arrow on the left, right, or bottom.
 * @param {ITooltipProps} props - The properties of the Tooltip component.
 * @returns {JSX.Element} The Tooltip component.
 */
export function Tooltip({
  message = 'This is a tooltip',
  type,
  className,
}: ITooltipProps) {
  const { ColorsControllersDefault } = useTheme();
  const fill = ColorsControllersDefault;

  return (
    <ToolTipWrapper type={type} className={className}>
      {type === 'arrowLeft' && <ArrowTipLeft fill={fill} />}
      <ToolTipBody>
        <ToolTipContents>{message}</ToolTipContents>
      </ToolTipBody>
      {type === 'arrowBottom' && <ArrowTipBottom fill={fill} />}
      {type === 'arrowRight' && <ArrowTipRight fill={fill} />}
    </ToolTipWrapper>
  );
}
