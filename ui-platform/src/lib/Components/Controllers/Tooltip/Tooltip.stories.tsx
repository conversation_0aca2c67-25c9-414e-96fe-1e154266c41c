import { withComponentShowcase } from '../../../Utilities';
import type { Meta, StoryObj } from '@storybook/react';
import { Tooltip } from './Tooltip';
import { withTooltip, ExampleComponent } from './InteractiveTooltip/InteractiveTooltip';

const meta: Meta<typeof Tooltip> = {
  component: Tooltip,
  title: 'Components/Tooltip',
  argTypes: {
    type: {
      control: {
        type: 'select',
        options: ['arrowRight', 'arrowLeft', 'arrowBottom'],
      },
    },
    message: {
      control: 'text',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Tooltip>;

export const Overview: Story = {
  args: {
    message: 'This is a tooltip message',
  },
};

export const ArrowRight: Story = {
  args: {
    type: 'arrowRight',
    message: 'This is a right arrow tooltip message',
  },
};

export const ArrowLeft: Story = {
  args: {
    type: 'arrowLeft',
    message: 'This is a left arrow tooltip message',
  },
};

export const ArrowBottom: Story = {
  args: {
    type: 'arrowBottom',
    message: 'This is a bottom arrow tooltip message',
  },
};


const types = ['arrowRight', 'arrowLeft', 'arrowBottom'];

export const TooltipVariants: Story = {
  render: (args) =>
    withComponentShowcase(<Tooltip {...args} />)('type', types, true, {
      showPropName: false,
    }),
  args: {},
};

export const Interactive: Story = {
  render: (args) => {
    const ExampleWithTooltip = withTooltip(ExampleComponent, { type: args.type, message: args.message });
    return <ExampleWithTooltip />;
  },
  args: {
    type: 'arrowBottom',
    message: 'This tooltip appears on hover!',
  },
};

