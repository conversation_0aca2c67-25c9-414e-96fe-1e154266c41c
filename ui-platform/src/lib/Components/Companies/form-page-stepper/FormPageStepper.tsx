import {Grid, Typography, useMediaQuery, useTheme} from '@mui/material';
import FormPageStepperItem from '../form-page-stepper-item/FormPageStepperItem';
import { pageStepper } from '../utils/PageStepper';

/* eslint-disable-next-line */
export interface FormPageStepperProps {}

/**
 * Renders a row of FormPageStepperItem components. The row is 
 * scrollable on small screens and takes up the full width of the screen.
 * The grid items are evenly distributed across the row, and the row
 * is centered vertically.
 * @param props FormPageStepperProps
 * @returns A JSX element
 */
export function FormPageStepper(props: FormPageStepperProps) {
    const theme = useTheme();
    const hideOnMdDown = useMediaQuery(theme.breakpoints.up('md'));
    const gridWidthMdUp = {
        display: 'flex',
        flexDirection: 'row',
        maxWidth: 'fit-content',
        gap: '12px',
    } as const;

    const gridWidthMdDown = {
        display: 'flex',
        flexWrap: 'nowrap',
        width: '200%',
        height: '50px',
        overflow: 'scroll',
        gap: '12px',
    } as const;

    const gridWidth = hideOnMdDown ? gridWidthMdUp : gridWidthMdDown;
    return (
        <Grid container style={gridWidth}>
            { pageStepper.map((step, index) =>
                <FormPageStepperItem key={index} label={step.label} path={step.path} />
            )}

    </Grid>
    );
}

export default FormPageStepper;
