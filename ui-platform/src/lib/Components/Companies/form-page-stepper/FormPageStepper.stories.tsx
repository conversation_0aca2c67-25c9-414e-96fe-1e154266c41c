import type { Meta, StoryObj } from '@storybook/react';
import { MemoryRouter } from 'react-router-dom';
import { FormPageStepper } from './FormPageStepper';

const meta: Meta<typeof FormPageStepper> = {
  component: FormPageStepper,
  title: 'Components/Companies/FormPageStepper',
};
export default meta;
type Story = StoryObj<typeof FormPageStepper>;

export const Overview: Story = {
  render: (args) => (
    <MemoryRouter>
      <FormPageStepper {...args} />
    </MemoryRouter>
  ),
  args: {},
};
