import type { Meta, StoryObj } from '@storybook/react';
import { CompanyDocumentUploadItem } from './CompanyDocumentUploadItem';

const meta: Meta<typeof CompanyDocumentUploadItem> = {
  component: CompanyDocumentUploadItem,
  title: 'Components/Companies/CompanyDocumentUploadItem',
};
export default meta;
type Story = StoryObj<typeof CompanyDocumentUploadItem>;

export const Overview: Story = {
  args: {
    file: {
      name: 'sample-documebnt.pdf',
      lastModified: Date.now(),
      webkitRelativePath: '',
      size: 0,
      type: 'application/pdf',
      preview: '',
      arrayBuffer: () =>
        new Promise<ArrayBuffer>(() => {
          // ArrayBuffer generation logic here
        }),
      slice: (start, end, contentType) => {
        // Logic to create a Blob object based on the start, end, and contentType
        // and return it
        const blob = new Blob([JSON.stringify({ value: 'hello' }, null, 2)], {
          type: 'contentType',
        });
        return blob;
      },
      stream: () => {
        // Create a new ReadableStream<Uint8Array> instance
        const stream = new ReadableStream<Uint8Array>({
          start(controller) {
            // Simulate data by pushing Uint8Array chunks into the controller
            const data = new Uint8Array([0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x21]); // "Hello!"
            controller.enqueue(data);
            controller.close();
          },
        });
        return stream;
      },
      text: () =>
        new Promise<string>((resolve) => {
          // Logic to generate the string here
          const text = 'Hello, world!';
          resolve(text);
        }),
    },
  },
};
