import { Box, Typography } from '@mui/material';
import imgUrl from '../assets/images/upload_default.png';
import { FileWithPreview } from '../schemas/FilesWithPreview';
/* eslint-disable-next-line */
export interface CompanyDocumentUploadItemProps {
  file: FileWithPreview;
}

/**
 * Renders a box with a default image or the preview of the uploaded image,
 * and two lines of text with the name and the last modified date of the file.
 *
 * @param {CompanyDocumentUploadItemProps} props
 * @returns {JSX.Element}
 */
export function CompanyDocumentUploadItem({
  file,
}: CompanyDocumentUploadItemProps) {
  return (
    <Box
      component="div"
      sx={{
        display: 'grid',
        gridTemplateRows: '100px auto',
        width: '150px',
        border: '1px solid #ccc',
        borderRadius: '7px',
      }}
    >
      <Box
        component="div"
        sx={{
          gridRow: '1 / 2',
          width: '100%',
          height: '100px',
          background: `url(${imgUrl}) center/cover no-repeat`,
        }}
      ></Box>
      <Box
        component="div"
        sx={{
          display: 'grid',
          gridRow: '2 / 3',
          background: '#fff',
          padding: '8px',
          gap: '4px',
          borderBottomLeftRadius: '7px',
          borderBottomRightRadius: '7px',
        }}
      >
        <Typography
          component="p"
          sx={{
            color: '#333',
            fontSize: '12px',
            fontWeight: 600,
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {file.name}
        </Typography>
        <Typography
          component="p"
          sx={{
            color: '#333',
            fontSize: '12px',
            fontWeight: 600,
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {new Date(file.lastModified).toLocaleDateString()}
        </Typography>
      </Box>
    </Box>
  );
}

export default CompanyDocumentUploadItem;
