import {
  Box,
  FormGroup,
  FormLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
} from '@mui/material';
import { ReactNode } from 'react';
import {
  Controller,
  FieldError,
  FieldValues,
  Path,
  UseControllerProps,
} from 'react-hook-form';
import { useTheme } from 'styled-components';
import { IFormSelect } from '../../../models/IFormSelect';
import useShowMultichoiceAccreditationInputStore from '../../../stores/ShowMultichoiceAccreditationInoutStore';

export interface CompanyMultiSelectProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends Path<TFieldValues> = Path<TFieldValues>
> extends UseControllerProps<TFieldValues, TName> {
  label: string;
  error: FieldError | undefined | null;
  data: IFormSelect[];
  instructions?: string;
  placeHolder?: string;
}

/**
 * Renders a multi-select component for selecting multiple company types.
 *
 * @param {CompanyMultiSelectProps} props - The props for the component.
 * @param {string} props.name - The name of the field in the form.
 * @param {ControllerProps} props.control - The control props for the component.
 * @param {string} props.label - The label for the component.
 * @param {FieldError | undefined | null} props.error - The error for the component.
 * @param {IFormSelect[]} props.data - The options for the multi-select.
 * @param {string} [props.instructions] - The instructions for the component.
 * @param {string} [props.placeHolder] - The placeholder for the component.
 * @returns {ReactElement} The multi-select component.
 */
export function CompanyMultiSelect<
  TFieldValues extends FieldValues = FieldValues,
  TName extends Path<TFieldValues> = Path<TFieldValues>
>({
  name,
  control,
  label,
  error,
  data,
  instructions,
  placeHolder,
}: CompanyMultiSelectProps<TFieldValues, TName>) {
  const setShowMultichoiceAccreditationInput =
    useShowMultichoiceAccreditationInputStore(
      (state) => state.setShowMultichoiceAccreditationInput
    );

  const theme = useTheme();

  const formGroupDefaultStyles = {
    display: 'grid',
    gridTemplateRows: 'auto auto auto',
    gap: theme.GapSm,
  } as const;

  const formLabelErrorStyles = {
    justifySelf: 'end',
    color: '#B22411',
    fontSize: '14px',
  } as const;

  return (
    <FormGroup sx={formGroupDefaultStyles}>
      <FormLabel
        sx={{
          color: theme.ColorsTypographyPrimary,
          fontSize: '14px',
          fontWeight: '600',
        }}
      >
        {label}
      </FormLabel>
      <Controller
        control={control}
        name={name}
        render={({ field: { onChange, value } }) => (
          <Select
            MenuProps={{
              anchorOrigin: {
                vertical: 'bottom',
                horizontal: 'center',
              },
              style: { height: '400px' },

              anchorPosition: {
                top: 100,
                left: 100,
              },
            }}
            sx={{
              borderRadius: theme.RadiusXs,
              border: `1px solid ${theme.ColorsStrokesFocus}`,
              background: '#4A5055',
              color: 'white',
              fontSize: '14px',
              width: '100%',
              '.MuiSelect-select': {
                textAlign: 'start',
                padding: '10px',
              },
            }}
            multiple
            onChange={(event: SelectChangeEvent<string>, child: ReactNode) => {
              const {
                target: { value },
              } = event;
              onChange(value);
              console.log(value);
              const multichoiceId = '8798778f-0110-46c0-8e1b-a218eb48dcdf';
              if (value.includes(multichoiceId)) {
                setShowMultichoiceAccreditationInput(true);
              } else {
                setShowMultichoiceAccreditationInput(false);
              }
            }}
            value={value || ''}
          >
            displayEmpty={true}
            <MenuItem disabled value={''}>
              <Typography sx={{ color: 'grey' }}>{placeHolder}</Typography>
            </MenuItem>
            {data.map((option: IFormSelect) => {
              return (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              );
            })}
          </Select>
        )}
      />
      <Box component="div">
        <Typography
          sx={{
            color: theme.ColorsTypographySecondary,
            fontSize: '14px',
            fontWeight: '400',
            textAlign: 'left',
          }}
        >
          {error ? error.message : instructions}
        </Typography>
      </Box>
    </FormGroup>
  );
}

export default CompanyMultiSelect;
