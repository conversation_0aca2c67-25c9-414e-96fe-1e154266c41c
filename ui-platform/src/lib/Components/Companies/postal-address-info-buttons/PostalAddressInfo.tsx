import {
  Box,
  FormControlLabel,
  FormGroup,
  FormLabel,
  Grid,
  Radio,
  RadioGroup,
} from '@mui/material';
import React from 'react';
import {
  Controller,
  FieldValues,
  Path,
  UseControllerProps,
} from 'react-hook-form';
import { useTheme } from 'styled-components';
import useShowPostalAddressInfoStore from '../../../stores/ShowPostalAddressInfoStore';
// import companyContactInformation from '../../views/registration/company-contact-information/CompanyContactInformation';

/* eslint-disable-next-line */
export interface PostalAddressInfoProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends Path<TFieldValues> = Path<TFieldValues>
> extends UseControllerProps<TFieldValues, TName> {
  label: string;
  populateFields?: () => void;
}

/**
 * @function PostalAddressInfo
 * @description A form component that displays two radio buttons asking if the user wants to display
 * their postal address information. If the user selects 'Yes', the component will call the
 * populateFields function to populate the form fields with the user's postal address information.
 * @param {Object} props The props object.
 * @param {string} props.label The label to display above the radio buttons.
 * @param {string} props.name The name of the form field.
 * @param {Object} props.control The control object from react-hook-form.
 * @param {Function} [props.populateFields] The function to call when the user selects 'Yes'.
 * @returns {ReactElement} A React element.
 */
export function PostalAddressInfo<
  TFieldValues extends FieldValues = FieldValues,
  TName extends Path<TFieldValues> = Path<TFieldValues>
>({
  label,
  name,
  control,
  populateFields,
}: PostalAddressInfoProps<TFieldValues, TName>) {
  const textColor = useTheme().ColorsTypographyPrimary;
  const smallGap = useTheme().GapSm;
  const showPostalAddressInfo = useShowPostalAddressInfoStore(
    (state) => state.showPostalAddressInfo
  );
  const changePostalAddressDisplay = useShowPostalAddressInfoStore(
    (state) => state.setShowPostalAddressInfo
  );

  const formGroupDefaultStyles = {
    display: 'grid',
    gridTemplateRows: 'auto',
    gridTemplateColumns: '1fr',
    justifyItems: 'start',
    gap: smallGap,
  } as const;

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    changePostalAddressDisplay(event.target.value as string);
    console.log(showPostalAddressInfo);
  };

  return (
    <FormGroup sx={formGroupDefaultStyles}>
      <FormLabel
        sx={{
          color: textColor,
          fontSize: '14px',
          fontWeight: '600',
        }}
      >
        {label}
      </FormLabel>
      <Grid
        item
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(2, auto)',
          justifyContent: 'center',
          alignItems: 'center',
          gap: '12px',
        }}
      >
        <Controller
          name={name}
          control={control}
          render={({
            field: { onChange, value, name },
            fieldState: { error },
            formState,
          }) => (
            <RadioGroup
              sx={{ display: 'grid', gridTemplateColumns: 'repeat(2, auto)' }}
              name={name}
              value={showPostalAddressInfo || ''}
              onChange={handleChange}
            >
              <FormControlLabel
                label={'Yes'}
                value={'true'}
                onClick={populateFields}
                sx={{
                  display: 'grid',
                  padding: '6px 8px',
                  gridTemplateColumns: 'auto auto',
                  gap: '12px',
                  color: textColor,
                  fontSize: '14px',
                  fontWeight: '400',
                  alignItems: 'center',
                }}
                control={
                  <Radio
                    sx={{
                      color: textColor,
                      '&.Mui-checked': {
                        color: '#fff',
                      },
                    }}
                  />
                }
              />
              <FormControlLabel
                label={'No'}
                value={'false'}
                sx={{
                  display: 'grid',
                  padding: '6px 8px',
                  gridTemplateColumns: 'auto auto',
                  gap: '12px',
                  color: textColor,
                  fontSize: '14px',
                  fontWeight: '400',
                  alignItems: 'center',
                }}
                control={
                  <Radio
                    sx={{
                      color: textColor,
                      '&.Mui-checked': {
                        color: '#fff',
                      },
                    }}
                  />
                }
              />
            </RadioGroup>
          )}
        />
      </Grid>
    </FormGroup>
  );
}

export default PostalAddressInfo;
