import type { Meta, StoryObj } from '@storybook/react';
import { PostalAddressInfo } from './PostalAddressInfo';

const meta: Meta<typeof PostalAddressInfo> = {
  component: PostalAddressInfo,
  title: 'Components/Companies/PostalAddressInfo',
};
export default meta;
type Story = StoryObj<typeof PostalAddressInfo>;

export const Overview: Story = {
  args: {
    label: 'Postal Address Info',
    name: 'postalAddressInfo',
  },
};
