import * as z from 'zod';
import {id_number_regex} from "../../Accounts/utils/AccountFormsValidation";
const directorSchema = z.object({
    name: z.string({required_error: 'Please enter director name'}).min(1, 'Please enter director name'),
    id_number: z.string().min(13, {message: 'Contact number can not be less than 13 characters'}).regex(id_number_regex.regex, {message: id_number_regex.message})
});

const schema = z.object({
    directors: z
        .array(directorSchema)
})

export default schema;

export type CompanyDirectorSchemaType = z.infer<typeof schema>;
