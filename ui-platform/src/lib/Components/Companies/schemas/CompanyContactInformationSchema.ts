import * as z from 'zod';
import {contact_number_regex} from "../../Accounts/utils/AccountFormsValidation";

const schema = z.object({
    contact_primary: z.string().min(10, {message: 'Contact number can not be less than 10 characters'}).regex(contact_number_regex.regex, {message: contact_number_regex.message}),
    contact_secondary: z.string().min(10, 'Contact number can not be less than 10 characters').regex(contact_number_regex.regex, {message: contact_number_regex.message}).optional(),
    contact_person: z.string({required_error: "Primary contact person's name is required"}),
    email_receiving: z.string().email({message: "Please enter a valid email address"}),
    physical_addr: z.string({required_error: "Physical address is required"}),
    physical_city: z.string({required_error: "Physical city is required"}),
    physical_suburb: z.string({required_error: "Suburb is required"}),
    physical_code: z.string(),
    postal_address_info: z.string().optional(),
    postal_box: z.string({required_error: "Please enter a valid postal address"}).optional(),
    postal_city: z.string({required_error: "Postal city is required"}),
    postal_code: z.string({required_error: "Postal code is required"}),
    province: z.string({required_error: "Province is required"}),

});

export default schema;

export type CompanyContactInformationSchemaType = z.infer<typeof schema>;
