import * as z from 'zod';

const schema = z.object({
    co_reg: z.optional(z.string()),
    name: z.string({required_error: "Company name is required"}),
    trading_as: z.string({required_error: "Field is required"}),
    company_type: z.string({required_error: "Field is required"}),
    bbeee: z.string(),
    // vat_no: z.string({required_error: "Field is required"}).regex(vat_no.regex, {"message" :vat_no.message}).optional(),
    vat_no: z.string().optional(),
    tax_no: z.string({required_error: "Field is required"}).optional(),
});

export default schema;

export type CompanyDetailsSchemaType = z.infer<typeof schema>;
