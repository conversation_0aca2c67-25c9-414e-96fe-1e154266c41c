import * as z from 'zod';
import {bank_account_number} from "../../Accounts/utils/AccountFormsValidation";

const schema = z.object({
    bank_account_holder_name: z.string({required_error: "Field is required"}),
    bank_name: z.string({required_error: "Field is required"}),
    bank_account_number: z.string({
        required_error: "Cannot be empty",
        invalid_type_error: "Only numbers allowed"
    }).regex(bank_account_number.regex, {"message" :bank_account_number.message}),
    branch_name: z.string({required_error: "Field is required"}),
    branch_code: z.string(),
    account_type: z.string({required_error: "Field is required"}),
    vat_no: z.optional(z.string()),
    tax_no: z.optional(z.string()),
});

export default schema;

export type CompanyBankingDetailsSchemaType = z.infer<typeof schema>;
