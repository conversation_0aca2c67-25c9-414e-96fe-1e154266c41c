import * as z from 'zod';
import { multichoice_accreditation_number_regex } from '../../Accounts/utils/AccountFormsValidation';

const schema = z.object({
  your_skills: z.array(z.number()),
  after_hours_work: z.string(),
  work_suppliers: z.array(z.string()),
  multichoice_accreditation_number: z
    .string({ required_error: 'Multichoice number is required' })
    .regex(multichoice_accreditation_number_regex.regex, {
      message: multichoice_accreditation_number_regex.message,
    })
    .optional(),
});

export default schema;

export type ScopeOfWorkSchemaType = z.infer<typeof schema>;
