import { Box, Typography } from '@mui/material';
import alert from '../assets/images/summary-alert.png';
export interface CompanySummaryRequiredProps {
  message: string;
}
/**
 * Displays a warning message with an alert icon
 * @param {string} message The text to display
 * @returns {JSX.Element} A Box component containing the message and alert icon
 */
export function CompanySummaryRequired({
  message,
}: CompanySummaryRequiredProps) {
  return (
    <Box
      sx={{
        display: 'grid',
        placeItems: 'center',
        padding: '12px 24px',
      }}
    >
      <Typography
        variant={'body2'}
        sx={{
          display: 'grid',
          gridTemplateColumns: 'auto 1fr',
          justifyItems: 'center',
          alignItems: 'center',
          gap: '10px',
          color: '#B28511',
        }}
      >
        <Box
          component="img"
          style={{
            width: '22.5px',
            height: '22.5px',
          }}
          src={alert}
        />
        {message}
      </Typography>
    </Box>
  );
}
