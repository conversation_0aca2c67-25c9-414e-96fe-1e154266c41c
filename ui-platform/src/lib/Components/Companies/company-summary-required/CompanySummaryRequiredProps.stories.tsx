import type { Meta, StoryObj } from '@storybook/react';
import { CompanySummaryRequired } from './CompanySummaryRequired';

const meta: Meta<typeof CompanySummaryRequired> = {
  component: CompanySummaryRequired,
  title: 'Components/Companies/CompanySummaryRequired',
};
export default meta;
type Story = StoryObj<typeof CompanySummaryRequired>;

export const Overview: Story = {
  args: { message: 'Upload your company documents here' },
};
