import {
  Box,
  Button,
  Grid,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { joinServiceProvider } from '../../../services/ServiceProviderService';
import useFullPageLoaderStore from '../../../stores/FullPageLoaderStore';
import useJoinCompanyStore from '../../../stores/JoinCompanyStore';
import singleBuilding from '../assets/images/building-single.svg';
import buildings from '../assets/images/buildings.svg';
import phone from '../assets/images/phone.svg';
import { JoinCompanySchemaType } from '../schemas/JoinCompanySchema';

/* eslint-disable-next-line */
export interface SearchCompanyDetailsProps {}

/**
 * This component renders a company search result card with a title, subtext, and button.
 * The card is designed to be used in the SearchCompany component.
 * @param {SearchCompanyDetailsProps} props - The component's props.
 * @returns {ReactElement} - The rendered company search result card component.
 * @example
 * <SearchCompanyDetails />
 */
export function SearchCompanyDetails(props: SearchCompanyDetailsProps) {
  const theme = useTheme();
  const hideOnMdDown = useMediaQuery(theme.breakpoints.up('md'));
  const setLoaderWithMessage = useFullPageLoaderStore(
    (state) => state.setLoaderWithMessage
  );
  // const tokensTheme = useTokensTheme().theme;
  const company = useJoinCompanyStore((state) => state.company);
  const navigate = useNavigate();

  const submitRequest = async () => {
    setLoaderWithMessage(true, 'Joining your company');
    try {
      if (company === undefined) {
        return;
      }
      const companyId = {
        id: company.id,
      } as JoinCompanySchemaType;

      await joinServiceProvider(companyId);
      navigate('/companies/search/request');
      setLoaderWithMessage(false, undefined);
    } catch (error) {
      setLoaderWithMessage(false, undefined);
    }
  };

  return (
    <Box
      component="div"
      sx={{
        width: hideOnMdDown ? '100%' : '92%',
        display: 'grid',
        padding: '2px',
        gridTemplateColumns: '1fr',
        gap: '8px',
        borderRadius: '8px',
        border: '0.5px solid #217C5B',
        background: 'rgba(40, 48, 51, 0.83)',
        boxShadow: '0px 0px 3.3px 4px rgba(7, 14, 17, 0.10)',
        backdropFilter: 'blur(4.5px)',
      }}
    >
      <Button onClick={submitRequest} sx={{ width: '100%', padding: 0 }}>
        <Grid
          item
          sx={{
            display: 'grid',
            gridTemplateColumns: '1fr 2fr',
            alignItems: 'center',
            gap: '8.5px',
            padding: 1,
          }}
        >
          <Grid
            item
            sx={{
              width: '100%',
              display: 'grid',
              gap: '4px',
              gridTemplateColumns: '1fr',
            }}
          >
            <Grid
              item
              sx={{
                display: 'grid',
                gridTemplateColumns: 'auto 1fr',
                gap: '4px',
                marginBottom: '2%',
              }}
            >
              <div
                style={{
                  width: 50,
                  height: 50,
                  borderStyle: 'solid',
                  borderRadius: '100%',
                  padding: 6,
                  borderColor: 'white',
                  borderWidth: 1,
                  marginTop: 15,
                  marginRight: 10,
                  marginLeft: 6,
                  display: 'grid',
                  placeItems: 'center',
                }}
              >
                <img src={buildings} alt="buildings icon" />
              </div>
              <Grid
                item
                sx={{
                  display: 'grid',
                  gap: '4px',
                }}
              >
                <Typography
                  sx={{
                    color: '#FFF',
                    fontSize: hideOnMdDown ? '21px' : '19px',
                    fontWeight: '200',
                    fontFamily: 'Inter',
                    paddingLeft: '0%',
                  }}
                >
                  {company.name || 'Bikho '}
                </Typography>
                <Typography
                  sx={{
                    color: '#FFF',
                    fontFamily: 'Inter',
                    fontSize: hideOnMdDown ? '13px' : '12px',
                    fontWeight: '400',
                    paddingBottom: 2,
                    paddingLeft: '0%',
                  }}
                >
                  {company.email_receiving || '<EMAIL>'}
                </Typography>
              </Grid>
            </Grid>
            <Grid
              item
              sx={{
                display: 'grid',
                gridTemplateColumns: 'auto 1fr',
                gap: '4px',
                paddingBottom: '2%',
                paddingLeft: '5%',
              }}
            >
              <img src={phone} alt="phone icon" />
              <Typography
                sx={{
                  color: '#FFF',
                  fontSize: '14px',
                  fontWeight: '400',
                  marginLeft: '5%',
                }}
              >
                {' '}
                +27 555 5555 5
              </Typography>
            </Grid>
            <Grid
              item
              sx={{
                display: 'grid',
                gridTemplateColumns: 'auto 1fr',
                gap: '4px',
                paddingLeft: '5%',
              }}
            >
              <img src={singleBuilding} alt="single building icon" />
              <Typography
                sx={{
                  width: 180,
                  color: '#FFF',
                  fontSize: '14px',
                  fontWeight: '400',
                  marginLeft: '-5%',
                }}
              >
                Address details
              </Typography>
            </Grid>
          </Grid>
          <Grid
            item
            sx={{
              alignSelf: 'flex-end',
            }}
          ></Grid>
        </Grid>
      </Button>
    </Box>
  );
}

export default SearchCompanyDetails;
