import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MemoryRouter } from 'react-router-dom';
import { SearchCompanyDetails } from './SearchCompanyDetails';

const meta: Meta<typeof SearchCompanyDetails> = {
  component: SearchCompanyDetails,
  title: 'Components/Companies/SearchCompanyDetails',
};
export default meta;
type Story = StoryObj<typeof SearchCompanyDetails>;

export const Overview: Story = {
  render: (args) => (
    <MemoryRouter>
      <SearchCompanyDetails {...args} />
    </MemoryRouter>
  ),
};
