import { Box, Typography } from '@mui/material';
import { NavLink } from 'react-router-dom';
import { useTheme } from 'styled-components';

/* eslint-disable-next-line */
export interface FormPageStepperItemProps {
  label: string;
  path: string;
}

/**
 * A single item in the form page stepper.
 * This component is a navigation link that changes its styles based on whether it is active or not.
 * The styles are defined using MUI's styled component API.
 * @param {FormPageStepperItemProps} props
 * @returns {JSX.Element}
 */
export function FormPageStepperItem({ label, path }: FormPageStepperItemProps) {
  const borderImage = useTheme().GradientColorsDividerGlow;
  const borderColorFocus = useTheme().ColorsUtilityColorFocus;
  const textColorFocus = useTheme().ColorsUtilityColorFocus;
  const textColorSecondary = useTheme().ColorsTypographySecondary;

  const boxShadow = {
    borderImage: borderImage,
    borderWidth: '4px',
    borderStyle: 'solid',
  } as const;

  return (
    <NavLink to={path} style={{ textDecoration: 'none ' }}>
      {({ isActive }) => (
        <Box
          sx={{
            display: 'grid',
            placeItems: 'center',
            height: '36px',
            padding: '0px 8px 12px 8px',
            borderBottom: `1px solid ${
              isActive ? borderColorFocus : '#262728'
            }`,
          }}
        >
          <Typography
            sx={{
              color: `${isActive ? textColorFocus : textColorSecondary}`,
              fontSize: '14px',
              fontWeight: '400',
              textAlign: 'center',
            }}
          >
            {label}
          </Typography>
        </Box>
      )}
    </NavLink>
  );
}

export default FormPageStepperItem;
