import type { Meta, StoryObj } from '@storybook/react';
import { MemoryRouter } from 'react-router-dom';
import { FormPageStepperItem } from './FormPageStepperItem';

const meta: Meta<typeof FormPageStepperItem> = {
  component: FormPageStepperItem,
  title: 'Components/Companies/FormPageStepperItem',
};
export default meta;
type Story = StoryObj<typeof FormPageStepperItem>;

export const Overview: Story = {
  args: { label: 'Stepper item', path: '' },
  render: (args) => (
    <MemoryRouter>
      <FormPageStepperItem {...args} />
    </MemoryRouter>
  ),
};
