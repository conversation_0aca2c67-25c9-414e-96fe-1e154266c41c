import { Box, Grid, Typography } from '@mui/material';
import { useTheme } from 'styled-components';
import useJoinCompanyStore from '../../../stores/JoinCompanyStore';

/* eslint-disable-next-line */
export interface InternalStaffCompanyDetailsProps {}

/**
 * InternalStaffCompanyDetails
 *
 * This component displays the company name and email address associated with
 * the current user's company. It is used in the internal staff company details
 * page.
 *
 * @param {InternalStaffCompanyDetailsProps} props - The component's props.
 * @returns {JSX.Element} - The component's JSX.
 */
export function InternalStaffCompanyDetails(
  props: InternalStaffCompanyDetailsProps
) {
  const moduleNavigationBackground =
    useTheme().ColorsButtonColorModuleNavigationHover;
  const company = useJoinCompanyStore((state) => state.company);
  return (
    <Box
      component="div"
      sx={{
        display: 'grid',
        padding: '12px',
        gridTemplateColumns: '1fr',
        justifyContent: 'center',
        alignItems: 'center',
        gap: '8px',
        borderRadius: '8px',
        border: '0.5px solid #217C5B',
        background: 'rgba(40, 48, 51, 0.83)',
        boxShadow: '0px 0px 3.3px 4px rgba(7, 14, 17, 0.10)',
        backdropFilter: 'blur(4.5px)',
      }}
    >
      <Grid
        item
        sx={{
          display: 'grid',
          gridTemplateColumns: 'auto auto',
          justifyItems: 'center',
          alignItems: 'center',
        }}
      >
        <Grid
          item
          sx={{
            display: 'grid',
            gridTemplateRows: 'auto auto',
            justifyItems: 'center',
            gap: '4px',
          }}
        >
          <Typography
            sx={{
              color: '#FFF',
              fontSize: '21px',
              fontWeight: '200',
            }}
          >
            {company.name || 'Bikho Instrumentals'}
          </Typography>
          <Typography
            sx={{
              color: '#FFF',
              fontSize: '14px',
              fontWeight: '400',
            }}
          >
            {company.email_receiving || ''}
          </Typography>
        </Grid>
        <Grid
          item
          sx={{
            display: 'grid',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Box
            sx={{
              display: 'grid',
              width: '75px',
              padding: '8px 16px',
              gridTemplateColumns: '1fr',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '10px',
              borderRadius: '8px',
              border: '1px solid rgba(33, 124, 91, 0.75)',
              background: moduleNavigationBackground,
            }}
          >
            <Typography
              component="h1"
              sx={{
                color: '#FFF',
                fontSize: '47.3px',
                fontWeight: '100',
                textAlign: 'center',
              }}
            >
              V
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
}

export default InternalStaffCompanyDetails;
