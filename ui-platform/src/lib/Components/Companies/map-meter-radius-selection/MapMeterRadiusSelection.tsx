import {Box, Slider, Typography, useMediaQuery, useTheme} from '@mui/material';
import useMapMeterRadiusStore from '../../../stores/MapMeterRadiusStore';


/* eslint-disable-next-line */
export interface MapMeterRadiusSelectionProps {}

/**
 * MapMeterRadiusSelection component renders a slider input for user to select a meter radius for travel on emergencies.
 * It uses design tokens and CSS Grid layout.
 *
 * @param {object} props - The component props
 * @returns {JSX.Element} The MapMeterRadiusSelection component
 */
export function MapMeterRadiusSelection(props: MapMeterRadiusSelectionProps) {
    const radius = useMapMeterRadiusStore((state) => state.radius);
    const setRadius = useMapMeterRadiusStore((state) => state.setRadius);
    const theme = useTheme();
    const hideOnMdDown = useMediaQuery(theme.breakpoints.up('md'));
    return (
        <Box component='div' sx={{ width: hideOnMdDown ? '750px' : '100%', marginBottom: '10%' }}>
            <Box component='div' sx={{
                height: hideOnMdDown ? '80px' : '50px',
                width: hideOnMdDown ? '750px' : '100%',
                paddingTop: hideOnMdDown ? '30px' : 7.5,
                paddingBottom: hideOnMdDown ? '30px' : 7.5,
            }}>
            <Typography
                sx={{
                    fontSize: hideOnMdDown ? '18px' : '12px',
                    color: 'white'
                }}
                gutterBottom>
                How far are you prepared to travel on emergencies:
                {/*Current radius: {radius} meters*/}
            </Typography>
            </Box>
            <Slider
                sx={{
                    color: '#b3b2b2',
                    '& .MuiSlider-markLabel ': {
                        color: 'white'
                    },
                    '& .MuiSlider-mark': {
                        color: '#494949',
                        width: '16px',
                        height: '16px',
                        borderRadius: '50%',
                    }
                }}
                value={radius}
                min={100} // Minimum radius value
                max={5000} // Maximum radius value
                step={100} // Increment step
                onChange={(event, newValue) => {
                        setRadius(newValue as number);
                    }
                }
                aria-labelledby="radius-slider"
                style={{
                    width: hideOnMdDown ? '400px' : '75%',
                }}
                marks={[
                    { value: 100, label: '100m' },
                    { value: 1000, label: '1km' },
                    { value: 2000, label: '2km' },
                    { value: 3000, label: '3km' },
                    { value: 4000, label: '4km' },
                    { value: 5000, label: '5km' },
                ]}
            />

        </Box>
    );
}

export default MapMeterRadiusSelection;
