import { Box, Typography, useMediaQuery } from '@mui/material';
import { theme } from '../../../utils/CustomTheme';

/* eslint-disable-next-line */
export interface JoinOrCreateCompanyIconsProps {
  title: string;
  imgUrl: string;
  subtext: string;
}

/**
 * This component renders a company icon with a title and subtext.
 * 
 * It renders a box with a title, a box with an image, and a box with subtext.
 * 
 * The title is rendered as an h3 element with a color of white and a font size of 21px.
 * The image is rendered as an img element with a source of the provided imgUrl and a style of
 * objectFit: 'contain', width: '100px', and height: '100px' on smaller screens.
 * The subtext is rendered as an h3 element with a color of white and a font size of 14px.
 * 
 * The component is designed to be used in the JoinCompany component.
 * @param {string} title - The title of the company.
 * @param {string} imgUrl - The url of the image of the company.
 * @param {string} subtext - The subtext of the company.
 * @returns {ReactElement} - The rendered company icon component.
 */
export function JoinOrCreateCompanyIcons({
  title,
  imgUrl,
  subtext,
}: JoinOrCreateCompanyIconsProps) {
  const hideOnMdDown = useMediaQuery(theme.breakpoints.up('md'));

  const iconSizeDown = {
    objectFit: 'contain',
    width: '100px',
    height: '100px',
  };

  const iconWrapperUp = {
    display: 'grid',
    width: '285px',
    padding: '0px 62.5px',
    justifyItems: 'center',
    alignItems: 'center',
    borderRadius: '25px',
    border: `4px solid  #FFFFFF`,
  };

  const iconWrapperDown = {
    display: 'grid',
    width: '205px',
    padding: '0px 62.5px',
    justifyItems: 'center',
    alignItems: 'center',
    borderRadius: '25px',
    border: `4px solid  #FFFFFF`,
  };

  const iconWrapper = hideOnMdDown ? iconWrapperUp : iconWrapperDown;

  const iconSize = hideOnMdDown ? null : iconSizeDown;

  return (
    <Box
      component="div"
      sx={{
        display: 'grid',
        gridTemplateRows: 'auto',
        justifyItems: 'center',
        rowGap: '32px', 
      }}
    >
      <Typography
        component="h3"
        sx={{
          color: '#FFFFFF',
          fontSize: '21px',
          fontWeight: '400',
          textDecoration: 'none',
        }}
      >
        {title}
      </Typography>
      <Box component="div" sx={iconWrapper}>
        <Box sx={iconSize} component="img" alt="User Icon" src={imgUrl} />
      </Box>
      <Box
        sx={{
          width: '255px',
          height: '34px',
        }}
      >
        <Typography
          component="h3"
          sx={{
            color: '#FFFFFF',
            fontSize: '14px',
            fontWeight: '600',
            textDecoration: 'none',
          }}
        >
          {subtext}
        </Typography>
      </Box>
    </Box>
  );
}

export default JoinOrCreateCompanyIcons;
