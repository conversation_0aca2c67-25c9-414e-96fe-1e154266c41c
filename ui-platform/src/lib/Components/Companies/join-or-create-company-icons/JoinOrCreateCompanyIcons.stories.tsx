import type { Meta, StoryObj } from '@storybook/react';
import createCompanyImgUrl from '../assets/images/create_company.png';
import joinCompanyImgUrl from '../assets/images/join_company.png';
import { JoinOrCreateCompanyIcons } from './JoinOrCreateCompanyIcons';

const meta: Meta<typeof JoinOrCreateCompanyIcons> = {
  component: JoinOrCreateCompanyIcons,
  title: 'Components/Companies/JoinOrCreateCompanyIcons',
};
export default meta;
type Story = StoryObj<typeof JoinOrCreateCompanyIcons>;

export const Overview: Story = {
  args: { title: 'Title', imgUrl: '', subtext: 'Subtext' },
};
export const Join: Story = {
  args: {
    title: 'Join A Company Profile',
    imgUrl: joinCompanyImgUrl,
    subtext: 'Select if your company is already registered with 4-Sure.',
  },
};
export const Create: Story = {
  args: {
    title: 'Create Company Profile',
    imgUrl: createCompanyImgUrl,
    subtext: 'Select if your company has never been registered with 4-Sure.',
  },
};
