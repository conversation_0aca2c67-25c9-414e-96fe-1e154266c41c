import React, { useState } from 'react';
import styled from 'styled-components';
import { Divider } from '../../Dividers/Divider';
import { Heading } from '../../Heading/Heading';

const Container = styled.div`
  display: grid;
  grid-template-rows: auto;
  justify-items: center;
  gap: 16px;
`;

const Form = styled.form`
  display: grid;
  grid-template-columns: auto auto 160px 160px 160px auto;
  align-items: center;
  gap: 8px;
`;

const Label = styled.label`
  grid-column: 1 / 2;
`;

const Input = styled.input`
  grid-column: 3 / 4;
  width: 160px;
  height: 40px;
  padding: 8px;
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  border-radius: 4px;
  border: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  box-sizing: border-box;
`;

const Select = styled.select`
  grid-column: span 1;
  width: 160px;
  height: 40px;
  padding: 8px;
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  border-radius: 4px;
  border: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  box-sizing: border-box;
`;

const ButtonWrapper = styled.div`
  grid-column: 6 / 7;
  display: grid;
  grid-template-columns: repeat(2, 35px);
  align-items: center;
  gap: 5px;
`;

const AddButton = styled.button`
  width: 35px;
  height: 35px;
  background-color: ${(props) => props.theme.ColorsBackgroundShell};
  color: ${(props) => props.theme.ColorsTypographyTertiary};
  border: 1px solid ${(props) => props.theme.ColorsTypographyTertiary};
  border-radius: 50px;
  cursor: pointer;
  color: ${(props) => props.theme.ColorsInputsInverse};
  font-size: 20px;
  display: grid;
  place-items: center;
`;

const RemoveButton = styled.button`
  width: 35px;
  height: 35px;
  background-color: ${(props) => props.theme.ColorsBackgroundShell};
  color: ${(props) => props.theme.ColorsTypographyTertiary};
  border: 1px solid ${(props) => props.theme.ColorsTypographyTertiary};
  border-radius: 50px;
  cursor: pointer;
  color: ${(props) => props.theme.ColorsInputsInverse};
  font-size: 20px;
  display: grid;
  place-items: center;
`;

/**
 * JobExcess renders a form with fields for entering excess information.
 * It includes a "Mandatory Excess" label, a number input, a "Payment Method" select,
 * a "Job Skill" select, and a button to either add or remove the field.
 * The component uses the `useState` hook to store an array of excess information.
 * The component renders a divider under each form set, including the last one.
 * @returns {ReactElement} The rendered component.
 */
export const JobExcess = () => {
  const [excessFields, setExcessFields] = useState([{ id: Date.now() }]);

  /**
   * Adds a new field to the array of excess fields.
   * This function is called when the user clicks the "Add" button.
   */
  const addField = () => {
    setExcessFields([...excessFields, { id: Date.now() }]);
  };

  /**
   * Removes a field from the array of excess fields.
   * This function is called when the user clicks the "Remove" button.
   * @param {number} id - The id of the field to remove.
   */
  const removeField = (id: number) => {
    setExcessFields(excessFields.filter((field) => field.id !== id));
  };

  return (
    <Container>
      <Heading type='page-heading'>Job Excess</Heading>
      {excessFields.map((field, index) => (
        <React.Fragment key={field.id}>
          <Form>
            <Label>Mandatory Excess {index + 1}</Label>
            <span>R</span>
            <Input type="number" name="number" placeholder="0" />
            <Select name="paymentMethod">
              <option value="creditCard">Payment Method</option>
            </Select>
            <Select name="jobSkill">
              <option value="jobSkill">Job Skill</option>
            </Select>
            <ButtonWrapper>
              {index === excessFields.length - 1 ? (
                <>
                  <AddButton onClick={addField}>+</AddButton>
                  {index !== 0 && (
                    <RemoveButton onClick={() => removeField(field.id)}>
                      -
                    </RemoveButton>
                  )}
                </>
              ) : (
                <RemoveButton onClick={() => removeField(field.id)}>
                  -
                </RemoveButton>
              )}
            </ButtonWrapper>
          </Form>
          {/* Divider under each form set, including the last one */}
          <Divider
            height="default"
            state="default"
            type="tabSmll"
            background="primary"
            size="medium"
          />
        </React.Fragment>
      ))}
    </Container>
  );
};
