import styled from 'styled-components';
import { Divider } from '../../Dividers/Divider';
import { Heading } from '../../Heading/Heading';

const Container = styled.div`
  display: grid;
  grid-template-rows: auto;
  justify-items: center;
  gap: 16px;
`;

const Form = styled.form`
  display: grid;
  grid-template-columns: auto auto 160px 160px 160px;
  align-items: center;
  gap: 8px;
`;

const Label = styled.span`
  margin-right: 20px;
`;

const Input = styled.input`
  width: 160px;
  height: 40px;
  padding: 8px;
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  border-radius: 4px;
  border: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  box-sizing: border-box;
`;

const Select = styled.select`
  width: 160px;
  height: 40px;
  padding: 8px;
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  border-radius: 4px;
  border: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  box-sizing: border-box;
`;

/**
 * Renders a form with fields for entering excess information.
 * It includes a "Mandatory Excess" label, a number input, a "Payment Method" select,
 * a "Job Skill" select, and a button to either add or remove the field.
 * The component uses the `useState` hook to store an array of excess information.
 * The component renders a divider under each form set, including the last one.
 * @returns {ReactElement} The rendered component.
 */
export const PolicyExcess = () => {
  return (
    <Container>
      <Heading type='page-heading'>Policy Excess</Heading>
      <Form>
        <Label>Mandatory Excess</Label>
        <span>R</span>
        <Input type="number" name="number" placeholder="0" />
        <Select name="paymentMethod">
          <option value="creditCard">Payment Method</option>
        </Select>
        <Select name="jobSkill">
          <option value="jobSkill">Job Skill</option>
        </Select>
      </Form>
      <Divider height="default" state="default" type="tabSmll" background="primary" size="medium" />
    </Container>
  );
};
