import styled, { useTheme } from 'styled-components';
import { ProfileHeader, ProfileHeaderProps } from '../Avatars/ProfileHeader/ProfileHeader';
import { Icon } from '../Icons';

const Wrapper = styled.div`
  width: 100%;
  position: relative;
  border-radius: 8px;
  background-color: ${(props) => props.theme.ColorsBackgroundModule};
  overflow: hidden;
  display: grid;
  grid-template-columns: auto 1fr;
  align-items: center;
  padding: 12px;
  box-sizing: border-box;
  gap: 24px;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
`;

const MessageInfo = styled.div`
  display: grid;
  justify-items: end;
  align-items: flex-end;
  gap: 7px;
  grid-column: 2;
  font-size: ${(props) => props.theme.FontSize2}px;
`;

const CommunicationIcon = styled(Icon)`
  grid-column: 1;
  grid-row: 1;
  position: relative;
`;

export interface ChatProps
  extends Omit<ProfileHeaderProps, 'optionsHandler' | 'email'> {
  time: string;
  content: string;
}

/**
 * A component that displays a chat notification with the time and user's email.
 * It renders a `ProfileHeader` with the user's email and a `MessageInfo` component
 * with the time and a check-square icon.
 *
 * @param {string} time - The time of the chat notification.
 * @param {string} content - The content of the chat notification.
 * @param {ProfileHeaderProps} props - The props for the `ProfileHeader` component.
 * @returns {JSX.Element}
 */
export function ChatNotification({ time, content, ...props }: ChatProps) {
  const communicationIconColor = useTheme().ColorsUtilityColorSuccess;
  return (
    <Wrapper>
      <ProfileHeader email={content} {...props} />
      <MessageInfo>
        <div>{time}</div>
        <CommunicationIcon
          type="message-check-square"
          size={18}
          color={communicationIconColor}
        />
      </MessageInfo>
    </Wrapper>
  );
}
