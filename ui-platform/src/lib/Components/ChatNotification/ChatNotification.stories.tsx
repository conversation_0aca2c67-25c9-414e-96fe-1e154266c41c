import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { ChatNotification } from './ChatNotification';

const meta: Meta<typeof ChatNotification> = {
  component: ChatNotification,
  title: 'Components/ChatNotification',
};
export default meta;
type Story = StoryObj<typeof ChatNotification>;

export const Overview: Story = {
  args: {
    username: '<PERSON><PERSON><PERSON>',
    content: 'This is some message with text',
    time: '13:24',
  },
};
