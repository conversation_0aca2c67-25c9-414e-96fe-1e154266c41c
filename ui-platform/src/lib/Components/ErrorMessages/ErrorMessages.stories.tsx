import type { Meta, StoryObj } from '@storybook/react';
import { ErrorMessages } from './ErrorMessages';

const meta: Meta<typeof ErrorMessages> = {
  component: ErrorMessages,
  title: 'Components/ErrorMessages',
};

export default meta;
type Story = StoryObj<typeof ErrorMessages>;

export const Overview: Story = {
  args: {
    errorMessages: [
      "No date of loss has been selected (on your right Action Panel)",
      "What Matters has not been completed (on your right Action Panel)",
      "Specify skill",
      "Email address is required (in Policy Details)",
      "Province is required (in Policy Details)",
      "Excess amount is required",
      "Specify payment method",
    ],
  },
};
