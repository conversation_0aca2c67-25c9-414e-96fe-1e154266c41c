import React from 'react';
import styled from 'styled-components';
import AlertTriangle from '../public/images/alert-triangle.png';

interface ErrorMessagesProps {
  errorMessages: string[];
}

const Container = styled.div`
  display: grid;
  grid-template-rows: auto auto auto;
  justify-items: center;
  align-items: center;
  gap: 16px;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
`;

const Heading = styled.div`
  font-size: ${(props) => props.theme.FontSize8}px;
  font-family: ${(props) => props.theme.FontFamiliesInter}, sans-serif;
  font-weight: ${(props) => props.theme.FontWeightsInter2};
  text-align: center;
`;

const ErrorListWrapper = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  justify-items: start;
  width: 100%;
  gap: 16px;
`;

const ErrorItemWrapper = styled.div`
  display: grid;
  grid-template-columns: auto 1fr;
  align-items: center;
  gap: 10px;
`;

const ErrorIcon = styled.img`
  width: 20.53px;
  height: 18px;
`;

const ErrorMessage = styled.div`
  color: ${(props) => props.theme.ColorsUtilityColorWarning};
`;

const Input = styled.input`
  width: 16px;
  height: 16px;
  position: relative;
  border: 1px solid ${(props) => props.theme.ColorsUtilityColorFocus};
  border-radius: 2px;
  cursor: pointer;
`;

/**
 * ErrorItem component
 *
 * A single error message component
 *
 * @param message - The error message to display
 */

const ErrorItem: React.FC<{ message: string }> = ({ message }) => (
  <ErrorItemWrapper>
      <ErrorIcon src={AlertTriangle} alt="Alert Icon" />
    <ErrorMessage>{message}</ErrorMessage>
  </ErrorItemWrapper>
);

/**
 * ErrorMessages component
 *
 * @example
 * <ErrorMessages errorMessages={['No date of loss has been selected (on your right Action Panel)', 'What Matters has not been completed (on your right Action Panel)']} />
 *
 * @param {ErrorMessagesProps} props
 * @returns {ReactElement}
 */
export const ErrorMessages: React.FC<ErrorMessagesProps> = ({ errorMessages }) => {
  return (
    <Container>
      <Heading>Submit Claim To Proceed</Heading>
      <label htmlFor="text">
        <Input type="checkbox" name="text" id="text" />
        Upfront repudiation. Repudiate this claim now.
      </label>
      <ErrorListWrapper>
        {errorMessages.map((error, index) => (
          <ErrorItem key={index} message={error} />
        ))}
      </ErrorListWrapper>
    </Container>
  );
};
