import { <PERSON>a, StoryObj } from '@storybook/react';
import { DragAndDrop, DragAndDropProps } from './DragAndDrop';

export default {
  title: 'Components/DragAndDrop',
  component: DragAndDrop,
  argTypes: {},
} as Meta<DragAndDropProps>;

type Story = StoryObj<DragAndDropProps>;

export const Overview: Story = {
  args: {
    name: 'document',
    label: 'Document Name',
    filename: 'Drag & drop here',
  },
};
