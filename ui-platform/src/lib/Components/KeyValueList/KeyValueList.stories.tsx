import React from 'react';
import { from } from 'rxjs';
import { Meta, StoryObj } from '@storybook/react';
import { KeyValueList } from './KeyValueList';
import { Observable } from 'rxjs';

const meta: Meta<typeof KeyValueList> = {
  title: 'Components/KeyValueList',
  component: KeyValueList,
  argTypes: {
    align: {
      control: 'select',
      options: ['default', 'even', 'ends', 'left'],
    },
    colour: {
      control: 'select',
      options: ['default','preferred', 'alternative', 'proceed', 'warning', ],
    },
    size: {
      control: 'select',
      options: ['small', 'medium', 'large', 'extra-large'],
    },
    textTransform: {
      control: 'select',
      options: ['default', 'none'],
    },
    colouredHeading: {
      control: 'object'
    }
  },
};

export default meta;

type Story = StoryObj<typeof KeyValueList>;

const data$ = from([{
  key1: 'value1',
  key2: 'value2',
  key3: {
    subkey1: 'subvalue1',
    subkey2: 'subvalue2'
  },
  key4: [
    { subkey3: 'subvalue3' },
    { subkey4: 'subvalue4' }
  ]
}]);

// let heading$: Observable<string> | null | undefined

export const DefaultKeyValueList: Story = {
  args: {
    numbering: true,
    heading: 'Static Heading',
    // heading$: heading$,
    data$: data$,
    width: 'auto',
    itemMargin: '10px',
    align: 'default',
    colouredHeading: {headingString: 'Coloured Heading',headingColour: 'default'},
    colour: 'default',
    size: 'medium',
    textTransform: 'default',
  },
};

