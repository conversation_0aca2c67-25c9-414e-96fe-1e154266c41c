/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { JobAvailableCard } from '../../Cards/JobAvailableCard';
import { PaginationBar } from '../../Controllers/Pagination';
import './styles.css';
// import { ListItemProps, withItems } from '../../Widgets';
import { from } from 'rxjs';
import { TextButton } from '../../Buttons/TextButton/TextButton';
import {
  PaginationPageCount,
  useListPagination2,
} from '../../Controllers/Pagination/PaginationPageCount/PaginationPageCount';
import { Divider } from '../../Dividers/Divider';
import Filter from '../../Filter/Filter';
import { Heading } from '../../Heading/Heading';
import { Icon } from '../../Icons';
import { KeyValueList } from '../../KeyValueList/KeyValueList';
import ElementaryThemedMap from '../../Maps/ElementaryThemedMap/ElementaryThemedMap';
import { IAvailableJob } from '../../Models/IAvailableJob';
import { usePingWindow } from '../hooks/usePingWindow';
import { PingWindowContainer } from './PingWindowContainer/PingWindowContainer';
import { PingWindowOverlay } from './PingWindowOverlay/PingWindowOverlay';
import { useAvailableJobsStore } from '../../../Engine/hooks/';
import { JobAwardedOrLostCard } from '../../Cards/JobAwardedOrLostCard/JobAwardedOrLostCard';  
import { useCheckJobAvailabilityAndGetJobs } from '../../../Hooks/useCheckJobAvailabilityAndGetJobs';
import { Loader } from '../../Loader/Loader';
import { StaffMember } from '../../../Auth';



interface IPingWindowProps {
  // children: any;
  baseUrl: string;
  staffMember: StaffMember;
  width?: string;
  background?: string;
  availableJobs?: any[];
  filteredJobs?: any[];
  isOpen?: boolean;
  token?: string | null;
  showAwardedList?: boolean;
  showAvailableList?: boolean;
  skills?: Array<{
    id: number | string;
    name: string;
    active: boolean;
    mid: number | string;
  }>;
  showAwardedJobs?: boolean;
  showLostJobs?: boolean;
  shouldShowAwardedList?: boolean;
  shouldShowAvailableList?: boolean;
}

export const PingWindowAvailableJobsList = styled(({ open, ...rest }) => (
  <div {...rest}></div>
))`
  width: 100%;
  display: grid;
  grid-template-rows: auto 1fr auto; // Header, body, pagination
  height: 100%;
  // grid-gap: 2px;
  grid-auto-rows: min-content;
  border-radius: 4px;
  padding: 15px 15px 20px 15px;
  box-sizing: border-box;
  gap: 17px; // Consistent spacing between grid sections
`;

const PingWindowAvailableJobDetails = styled(({ open, ...rest }) => (
  <div {...rest}></div>
))`
  // width: 100%;
  display: grid;
  // grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* Adjust the minmax parameters to control the width of the job cards */
  grid-auto-rows: min-content;
  border-radius: 4px;
  max-width: 798px;
  min-height: 500px;
  max-height: 500px;
  // background: ${(props) => props.theme.ColorsCardColorJobCardPrimary};
  padding: 15px 15px 20px 15px;
`;
const PingWindowHeader = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: grid;
  grid-template-columns: 5fr 1fr;
  margin-bottom: 50px;
  height: auto;
  > div:nth-child(2) {
    justify-self: flex-end;
    align-self: center;
  }
`;
const PingWindowJobsAwardedList = styled(({ open, ...rest }) => (
  <div {...rest}></div>
))`
  width: 100%;
  display: grid;
  grid-template-rows: 0.2fr 1fr 1.8fr; // Header, body, pagination
  height: 100%;
  // grid-gap: 2px;
  grid-auto-rows: min-content;
  border-radius: 4px;
  padding: 15px 15px 20px 15px;
  box-sizing: border-box;
  gap: 17px; // Consistent spacing between grid sections
  > div:first-child {
    justify-self: center;
    align-self: right;
    text-align: right;
  }
  > div:nth-child(2) {
    display: grid;
    // padding-top: 40px;
    justify-self: center;
    align-self: center;
  }
  > div:nth-child(3) {
    display: grid;
    justify-self: center;
    align-self: center;
  }
`;
const PingWindowJobsAwardedListCloseButton = styled(({ ...rest }) => (
  <div {...rest}></div>
))`
  width: 100%;
  display: grid;
  grid-template-columns: 1fr;
  align-self: self-end;
  text-align: right;
  margin-bottom: 0;
  height: auto;
`;
const PingWindowJobsAwardedListIconHeader = styled(({ ...rest }) => (
  <div {...rest}></div>
))`
   width: 100%;
  display: grid;
  grid-template-columns: ${(props) =>
    props.awardedOnly ? '1fr' : props.lostOnly ? '1fr' : '1fr 1fr'};
  margin-bottom: 5px;
  height: auto;
  
  > div {
    display: grid;
    grid-template-rows: auto auto;
    justify-items: center;
    align-items: center;
    gap: 10px;
    text-align: center;
  }
  }
  
`;
const PingWindowJobsAwardedListBody = styled(({ ...rest }) => (
  <div {...rest}></div>
))`
  width: 100%;
  display: grid;
  // Dynamically set grid columns based on which lists are populated
  grid-template-columns: ${(props) =>
    props.awardedOnly ? '1fr' : props.lostOnly ? '1fr' : '1fr 1fr'};
  margin-bottom: 50px;
  height: auto;
  gap: 20px; // Add some spacing between columns
`;

const PingWindowFilter = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: grid;
  grid-template-rows: 1fr;
  box-sizing: border-box;
  // text-align: center;
  height: auto;
  // > div:nth-child(2) {
  //   justify-self: flex-end;
  // }
`;
const PingWindowDetailsHeader = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: grid;
  grid-template-rows: 1fr;
  grid-template-columns: 5fr 1fr;
  box-sizing: border-box;
  // text-align: center;
  height: auto;
  > div:nth-child(2) {
    justify-self: flex-end;
    align-self: center;
  }
`;
const PingWindowDetailsBody = styled(({ ...rest }) => <div {...rest}></div>)`
  display: grid;
  grid-template-columns: 1fr 1fr;
  > div:nth-child(1) {
    justify-self: center;
    margin-right: 35px;
  }
  > div:nth-child(2) {
    justify-self: center;
    background: teal;
    width: auto;
    height: 350px;
  }
  box-sizing: border-box;
  padding: 30px 25px 20px 0;
  width: auto;
  // margin-right: 40px;
  margin: 50px 0 0 0;
`;

const PingWindowBody = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: grid;
  grid-template-rows: repeat(
    5,
    minmax(78px, 78px)
  ); // Fixed height rows for up to 5 cards
  grid-auto-rows: 0; // Additional rows will have 0 height
  height: 100%;
  // margin-top: 17px;
  grid-gap: 4px;
  overflow-y: auto;
  min-height: 0;
  align-content: start; // Align items to the start of the grid cell
  padding-bottom: 4px;
  > * {
    transition: transform 0.3s ease-in-out;
  }

  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
    border-radius: 0 4px 4px 0;
  }
  ::-webkit-scrollbar-track {
    background: ${(props) => props.theme.ColorsCardColorJobCardPrimary};
    border-radius: 0 4px 4px 0;
  }
  ::-webkit-scrollbar-thumb:hover {
    // background: ${(props) => props.theme.ColorsCardColorDashboardPositive};
    background: blue;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }
`;
const PingWindowDetailsFooter = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: flex;
  justify-content: space-evenly;
  box-sizing: border-box;
  padding: 5px 15px 5px 15px;
  margin-top: 10px;
`;

const PaginationWrapper = styled.div`
  width: 100%;
  align-self: end; // Align to bottom of grid cell
`;

/**
 * PingWindow Component
 * Displays a list of available jobs or job details with pagination, filtering, and options to accept/decline jobs.
 *
 * @component
 * @param {IPingWindowProps} props - The properties for the PingWindow component
 * @param {React.ReactNode} props.children - Child components passed to PingWindow
 * @param {string} [props.width] - Optional width for the component
 * @param {string} [props.background] - Optional background for the component
 * @param {number} [props.availableJobs] - Number of available jobs to display
 * @param {boolean} props.isOpen - Whether the PingWindow is open or closed
 *
 * @returns {React.FC<IPingWindowProps>} The PingWindow component
 *
 * Handles switching between job list view and job details view.
 * @param {boolean} data - The view state (true for list, false for details)
 *
 * Handles closing the PingWindow component.
 */
const available_jobs: IAvailableJob[] = [
  // const available_jobs: IAvailableJob[] = [
  {
    id: 1201,
    appointments: [],
    appointment: {
      id: 1201,
      after_hours: false,
      appointment_name: 'Before',
      appointment_type_id: '5',
      range_start: '2025-02-25T16:00:00',
      range_end: '2025-02-25T16:00:00',
    },
    location: '',
    mid: '',
    address: '12 Windmill Road, Albertaville, 12345',
    skill: 'Plumber',
    customer: 'John Doe',
    cellnumber: '0801234567',
    area: 'Gauteng',
    claim_type: 'Plumbing',
    suburb: 'Roodepoort',
  },
  {
    id: 1202,
    appointments: [],
    appointment: {
      id: 1202,
      after_hours: false,
      appointment_name: 'At',
      appointment_type_id: '6',
      range_start: '2025-02-22T16:00:00',
      range_end: '2025-02-22T16:00:00',
    },
    location: '',
    mid: '',
    address: '34 Elm Street, Springfield, 12345',
    skill: 'Electrician',
    appointment_date: 'October 14, 2024',
    customer: 'Jane Smith',
    cellnumber: '0809876543',
    area: 'Gauteng',
    claim_type: 'Plumbing',
    suburb: 'Whiteridge',
  },
  {
    id: 1203,
    appointments: [],
    appointment: {
      id: 1203,
      after_hours: false,
      appointment_name: 'August 25, 2024',
      appointment_type_id: 'Carpenter',
      range_start: 'August 25, 2024',
      range_end: 'August 25, 2024',
    },
    location: '',
    mid: '',
    address: '56 Oak Avenue, Brooksville, 67890',
    skill: 'Carpenter',
    appointment_date: 'August 25, 2024',
    customer: 'David Johnson',
    cellnumber: '0804567890',
    area: 'Northwest',
    claim_type: 'Plumbing',
    suburb: 'Glenvista',
  },
  {
    id: 1204,
    appointments: [],
    appointment: {
      id: 1204,
      after_hours: false,
      appointment_name: 'September 16, 2024',
      appointment_type_id: 'Painter',
      range_start: 'September 16, 2024',
      range_end: 'September 16, 2024',
    },
    location: '',
    mid: '',
    address: '45 Sunnydale Drive, Springfield, 12345',
    skill: 'Painter',
    appointment_date: 'September 16, 2024',
    customer: 'Jane Smith',
    cellnumber: '0809876543',
    area: 'Gauteng',
    claim_type: 'Plumbing',
    suburb: 'Albertaville',
  },
  {
    id: 1205,
    appointments: [],
    appointment: {
      id: 1205,
      after_hours: false,
      appointment_name: 'August 27, 2024',
      appointment_type_id: 'Carpenter',
      range_start: 'August 27, 2024',
      range_end: 'August 27, 2024',
    },
    location: '',
    mid: '',
    address: '11 Ocean Drive, Brooksville, 67890',
    skill: 'Carpenter',
    appointment_date: 'August 27, 2024',
    customer: 'John Doe',
    cellnumber: '0801234567',
    area: 'Northwest',
    claim_type: 'Plumbing',
    suburb: 'Albertaville',
  },
  {
    id: 1206,
    appointments: [],
    appointment: {
      id: 1206,
      after_hours: false,
      appointment_name: 'September 16, 2024',
      appointment_type_id: 'Electrician',
      range_start: 'September 16, 2024',
      range_end: 'September 16, 2024',
    },
    location: '',
    mid: '',
    address: '132 Oak Avenue, Springfield, 90210',
    skill: 'Electrician',
    appointment_date: 'September 16, 2024',
    customer: 'Jane Smith',
    cellnumber: '0809876543',
    area: 'Gauteng',
    claim_type: 'Plumbing',
    suburb: 'Albertaville',
  },
  {
    id: 1207,
    appointments: [],
    appointment: {
      id: 1207,
      after_hours: false,
      appointment_name: 'September 16, 2024',
      appointment_type_id: 'Plumber',
      range_start: 'September 16, 2024',
      range_end: 'September 16, 2024',
    },
    location: '',
    mid: '',
    address: '32 Elm Street, Albertaville, 12345',
    skill: 'Plumber',
    appointment_date: 'September 16, 2024',
    customer: 'John Doe',
    cellnumber: '0801234567',
    area: 'Gauteng',
    claim_type: 'Plumbing',
    suburb: 'Albertaville',
  },
  {
    id: 1208,
    appointments: [],
    appointment: {
      id: 1208,
      after_hours: false,
      appointment_name: 'September 16, 2024',
      appointment_type_id: 'Painter',
      range_start: 'September 16, 2024',
      range_end: 'September 16, 2024',
    },
    location: '',
    mid: '',
    address: '51 Windmill Road, Brooksville, 67890',
    skill: 'Painter',
    appointment_date: 'September 16, 2024',
    customer: 'Jane Smith',
    cellnumber: '0809876543',
    area: 'Gauteng',
    claim_type: 'Plumbing',
    suburb: 'Albertaville',
  },

  // Add more objects here with randomized values
  // ...
];

export const AvailableJobsPingWindow: React.FC<IPingWindowProps> = ({
  // isOpen: isOpenProp,
  availableJobs,
  token,
  baseUrl,
  staffMember,
  ...props
}) => {
  const {
    isPingWindowVisible,
    setIsPingWindowVisible,
    // activeConfig,
    // setActiveConfig,
  } = usePingWindow();
  // const [isOpen, setIsOpen] = useState(isPingWindowVisible);
  const [listView, setListView] = useState(true);
  const [selectedJobId, setSelectedJobId] = useState<number | string | null>(
    null
  );
  // const [jobs, setJobs]= useState<IAvailableJob[]>(availableJobs || [])
  const [animatingJobId, setAnimatingJobId] = useState<number | string | null>(
    null
  );
  const [jobDetails, setJobDetails] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { filteredJobs, jobsAwarded, jobsLost, isOpen  } = useAvailableJobsStore();
  // const [isLoadingSkills, setIsLoadingSkills] = useState(true);

  // Add new state for tracking awarded list visibility
  const [isAwardedListVisible, setIsAwardedListVisible] = useState(true);
  const [isAvailableListVisible, setIsAvailableListVisible] = useState(false);
  const { registerJobInterest } = useCheckJobAvailabilityAndGetJobs({ baseUrl, staffMember });
  const shouldShowAwardedList = jobsAwarded.length > 0 || jobsLost.length > 0;
  const shouldShowAwardedListList = true;

  const [jobs, setJobs] = useState<IAvailableJob[]>(filteredJobs || []);

  const formatDateTime = (dateString: string, appointmentName: string) => {
    const date = new Date(dateString);
    const month = date.toLocaleString('default', { month: 'long' });
    const day = date.getDate();
    const time = date.toLocaleTimeString('default', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
    
    return `${month} ${day} ${appointmentName.toLowerCase()} ${time}`;
  };

  const getJobDetailsData = (jobDetails: any, skills: Array<{id: number, name: string}> = []) => {
    if (!jobDetails) return from([{}]);
  
    const getSkillNameById = (skillId: string | number | undefined) => {
      if (!skills || !skillId) return 'Unknown Skill';
      const skill = skills.find(s => s.id === Number(skillId));
      return skill?.name || 'Unknown Skill';
    };
  
    return from([{
      'Suburb': jobDetails.suburb || 'Not specified',
      'Type of job': getSkillNameById(jobDetails.skill),
      'Appointment': formatDateTime(jobDetails.appointment.range_start, jobDetails.appointment.appointment_name),
      'Claim type': jobDetails.claim_type || 'Not specified'
    }]);
  };

  const handleIgnoreJob = (jobId: number | string) => {
    setAnimatingJobId(jobId);
    setTimeout(() => {
      setJobs((prevJobs) => prevJobs.filter((job) => job.id !== jobId));
      setAnimatingJobId(null);
      setListView(true);
    }, 300);
  };

  const handleDeclineJob = (jobId: number | string) => {
    setAnimatingJobId(jobId);
    setTimeout(() => {
      setJobs((prevJobs) => prevJobs.filter((job) => job.id !== jobId));
      setAnimatingJobId(null);
      setListView(true);
    }, 300);
  };

  const handleAcceptJob = (jobId: number | string) => {
    setAnimatingJobId(jobId);
    setTimeout(() => {
      setJobs((prevJobs) => prevJobs.filter((job) => job.id !== jobId));
      setAnimatingJobId(null);
      setListView(true);
    }, 300);
  };

  // Close handler for awarded list
  const handleAwardedListClose = () => {
    setIsAwardedListVisible(false);
    setIsAvailableListVisible(true); // Show available jobs list when awarded list is closed
  };

  // Add close handler for available jobs list
  const handleAvailableListClose = () => {
    setIsAvailableListVisible(false);
    setIsPingWindowVisible(false);
  };

  const handleClose = () => {
    setIsPingWindowVisible(false);
  };

  const registerInterestAndRemoveJob = async (
    job_id: number | string,
    interest: number,
    staffId?: string | number
  ) => {
    try {
      // const numericJobId = typeof job_id === 'string' ? parseInt(job_id) : job_id;
      await registerJobInterest(job_id, interest, 170);
      setAnimatingJobId(job_id);
      // setJobs(jobs.filter((job) => job.id !== job_id));
      // Find the index of the job to remove
      // const jobindex = jobs.findIndex((job) => job.id === job_id);
      // After animation is complete, remove the job
      setTimeout(() => {
        setJobs((prevJobs) => {
          const newJobs = prevJobs.filter((job) => job.id !== job_id);
          // calculate the new total number of pages
          const newTotalPages = Math.ceil(newJobs.length / 5);
          // If current page would be empty, move to previous page
          if (currentPage > newTotalPages && setCurrentPage) {
            setCurrentPage(Math.max(1, newTotalPages));
          }
          // newJobs.splice(jobindex, 1);
          return newJobs;
        });
        setAnimatingJobId(null);
      }, 300);
    } catch (error) {
      console.error('Error registering job interest:', error);
    }

    setJobs((prevJobs) => prevJobs.filter((job) => job.id !== job_id));
  };

  const isJobVisible = (job: IAvailableJob) => {
    if (animatingJobId === job.id) {
      return true; // Keep the animating job visible during animation
    }

    // Calculate visible range for current page
    const startIndex = (currentPage - 1) * 5;
    const endIndex = startIndex + 5;
    const jobIndex = jobs.findIndex((j) => j.id === job.id);

    return jobIndex >= startIndex && jobIndex < endIndex;
  };

  const data = from([
    {
      Suburb: 'Fourways',
      'Type of job': 'DStV L2 Installation',
      Appointment: '20-08-2024 Before 09:00 AM',
      'Request from Customer':
        'Please hoot when you get to the gate. Our bell does not work',
      'Claim type': 'Pricelock SP Own Stock',
    },
  ]);
  // useEffect(() => {
  //   if (typeof isOpenProp !== 'undefined') {
  //     setIsPingWindowVisible(isOpenProp);
  //   }
  // }, [isOpenProp, setIsPingWindowVisible]);
  useEffect(() => {
    console.log('Current jobsAwarded:', jobsAwarded);
    console.log('Current jobsLost:', jobsLost);
    console.log('shouldShowAwardedListTest:', shouldShowAwardedList);
  }, [jobsAwarded, jobsLost, shouldShowAwardedList]);

  // Effect to handle listView state based on shouldShowAwardedList
  useEffect(() => {
    if (shouldShowAwardedList) {
      setListView(false);
    }
  }, [shouldShowAwardedList]);

  // When availableJobs prop changes, update the jobs state
  useEffect(() => {
    if (filteredJobs) {
      setJobs(filteredJobs);
    }
  }, [filteredJobs]);

  // Reset listView to true whenever the window visibility changes
  useEffect(() => {
    if (isPingWindowVisible) {
      setListView(true);
    }
  }, [isPingWindowVisible]);

  // Sync the ping window visibility with the store's isOpen state
  useEffect(() => {
    setIsPingWindowVisible(isOpen);
  }, [isOpen, setIsPingWindowVisible]);

  // useEffect(() => {
  //   console.log('isPingWindowVisible changed:', isPingWindowVisible);
  // }, [isPingWindowVisible]);

  async function handleDetailsClick(jobId: number | string) {
    console.log('Showing details for job:', jobId);
    setSelectedJobId(jobId); // Assuming the job data contains an id
    setListView(false);
    setIsLoading(true);

    try {
      const response = await fetch(
        `${baseUrl}/v1/job_action/get_ping_details/${jobId}/`,
        {
          method: 'POST',
          body: JSON.stringify({ job_id: jobId }),
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Token ${token}`,
          },
        }
      );
      if (!response.ok) throw new Error('Failed to fetch job details');
      const jobDetails = await response.json();
      console.log('jobDetails', jobDetails.payload);
      setJobDetails(jobDetails.payload);
      setListView(false);
    } catch (error) {
      console.error('Error fetching job details:', error);
    } finally {
      setIsLoading(false);
    }
  }
  function handleBack() {
    setListView(true);
  }
  const { pages, currentPage, pageItems, setCurrentPage, ...rest } =
    useListPagination2({
      items: jobs,
      itemsPerPage: 5,
    });
  // if (isLoadingSkills) return <Loader type="alert" text="Loading..."></Loader>;
  if (!isPingWindowVisible) return null;

  // Show awarded/lost jobs if either array has items

  if (shouldShowAwardedList && isPingWindowVisible && isAwardedListVisible) {
    const hasAwardedJobs = jobsAwarded.length > 0;
    const hasLostJobs = jobsLost.length > 0;
    const awardedOnly = hasAwardedJobs && !hasLostJobs;
    const lostOnly = !hasAwardedJobs && hasLostJobs;
    console.log('Rendering awarded/lost section:', {
      jobsAwarded,
      jobsLost,
      hasAwardedJobs,
      hasLostJobs,
      awardedOnly,
      lostOnly,
    });
    return (
      <PingWindowOverlay visible={isPingWindowVisible}>
        <PingWindowContainer
          isOverlayVisible={isPingWindowVisible}
          setIsOverlayVisisble={setIsPingWindowVisible}
        >
          <PingWindowJobsAwardedList>
            <PingWindowJobsAwardedListCloseButton>
              <div>
                <Icon
                  type="x-xircle"
                  onClick={handleAwardedListClose}
                  style={{ cursor: 'pointer' }}
                ></Icon>
              </div>
            </PingWindowJobsAwardedListCloseButton>
            <PingWindowJobsAwardedListIconHeader
              awardedOnly={awardedOnly}
              lostOnly={lostOnly}
            >
              {jobsAwarded.length > 0 && (
                <div>
                  <Icon type="check-circle" size={80} color="green" />
                  <span>You have been awarded the following jobs:</span>
                </div>
              )}
              {jobsLost.length > 0 && (
                <div>
                  <Icon type="x-xircle" size={80} color="red" />
                  <span>You have NOT been awarded the following jobs:</span>
                </div>
              )}
            </PingWindowJobsAwardedListIconHeader>
            <PingWindowJobsAwardedListBody
              awardedOnly={awardedOnly}
              lostOnly={lostOnly}
            >
              {hasAwardedJobs && (
                <div>
                  {jobsAwarded.map((job) => {
                    console.log('mapping awarded jobs', {
                      job,
                      mappedProps: {
                        job_id: job.id,
                        skill: job.skill,
                        area: job.area,
                        appointment_date: job.appointment.range_start,
                        time: job.appointment.appointment_name,
                      },
                    });
                    return (
                      <JobAwardedOrLostCard
                        key={job.id}
                        job_id={job.id}
                        skill={job.skill}
                        area={job.area}
                        appointment_date={job.appointment.range_start}
                        time={job.appointment.appointment_name}
                      />
                    );
                  })}
                </div>
              )}
              {hasLostJobs && (
                <div>
                  {jobsLost.map((job) => (
                    <JobAwardedOrLostCard
                      key={job.id}
                      job_id={job.id}
                      skill={job.skill}
                      area={job.area}
                      appointment_date={job.appointment.range_start}
                      time={job.appointment.appointment_name}
                    />
                  ))}
                </div>
              )}
            </PingWindowJobsAwardedListBody>
          </PingWindowJobsAwardedList>
        </PingWindowContainer>
      </PingWindowOverlay>
    );
  }

  return isPingWindowVisible && listView ? (
    <PingWindowOverlay visible={isPingWindowVisible}>
    <PingWindowContainer
      isOverlayVisible={isPingWindowVisible}
      setIsOverlayVisisble={setIsPingWindowVisible}
    >
      <PingWindowAvailableJobsList>
        <PingWindowHeader>
          <div>
            <h2 className="stripped">
              Indicate availability for the following jobs:
            </h2>
          </div>
          <div>
            <Icon
              type="x-xircle"
              onClick={handleAvailableListClose}
              style={{ cursor: 'pointer' }}
            ></Icon>
          </div>
        </PingWindowHeader>
        {/* <PingWindowFilter>
     <div className="filter">
          <Filter
            // just added placeholder to prevent any build errors for now
            onSelect={() => console.log('SELECT')}
            buttonText="Filter available jobs..."
            items={[
              { text: 'Skill', filterCondition: {name: '', key: '', comparator: 'contains', value: ''} },
              { text: 'Appointment Time', filterCondition: {name: '', key: '', comparator: 'contains', value: ''} },
              { text: 'Suburb',  filterCondition: {name: '', key: '', comparator: 'contains', value: ''} },
            ]}
          ></Filter>
          <div>
            <Icon type="switch-vertical"></Icon>
          </div>
        </div>
    </PingWindowFilter> */}
        {/* {withItems(JobAvailableCard)(available_jobs)} */}
        <PingWindowBody>
          {jobs.map(
            (job: IAvailableJob) =>
              isJobVisible(job) && (
                <JobAvailableCard
                  available_job={job}
                  key={job.id}
                  setDetailsView={() => handleDetailsClick(job.id)}
                  onInterestExpression={registerInterestAndRemoveJob}
                  isAnimating={animatingJobId === job.id}
                  isClicking={false}
                  declineClicking={false}
                  skills={staffMember.sp.skills || []}
                />
              )
          )}
        </PingWindowBody>

        <PaginationWrapper>
          <PaginationBar
            paginationItems={
              <PaginationPageCount
                pages={pages}
                currentPage={currentPage}
                {...rest}
              />
            }
          />
        </PaginationWrapper>
      </PingWindowAvailableJobsList>
    </PingWindowContainer>
  </PingWindowOverlay>
) : (
  <PingWindowOverlay visible={isPingWindowVisible}>
    <PingWindowContainer
      isOverlayVisible={isPingWindowVisible}
      setIsOverlayVisisble={setIsPingWindowVisible}
    >
      <PingWindowAvailableJobDetails>
        <PingWindowDetailsHeader>
          <div>
            <h2 className="stripped">Job Information:</h2>
          </div>

          <div>
            <Icon
              type="x-xircle"
              onClick={handleClose}
              style={{ cursor: 'pointer' }}
            ></Icon>
          </div>
        </PingWindowDetailsHeader>
        <PingWindowDetailsBody>
          <div style={{ textAlign: 'center', marginRight: '30px' }}>
            <Heading level={3}>Job Details</Heading>
            <KeyValueList data$={getJobDetailsData(jobDetails, staffMember.sp.skills)}></KeyValueList>
            {/* <Heading level={3}>What Matters</Heading>
            <span style={{ fontSize: '0.8rem' }}>
              
            </span> */}
          </div>
          <div>
            <ElementaryThemedMap
              joblocation={{ lat: -26.205, lng: 26.205 }}
              theme="light"
            ></ElementaryThemedMap>
          </div>
        </PingWindowDetailsBody>
        <Divider size="fullWidth" background="grey" type="tabSmll"></Divider>
        <PingWindowDetailsFooter>
          <div>
            <TextButton
              btnValue="back"
              size="large"
              onClick={handleBack}
            ></TextButton>
          </div>
          <div>
            <TextButton
              btnValue="ignore job"
              size="large"
              onClick={() => selectedJobId && handleIgnoreJob(selectedJobId)}
            ></TextButton>
          </div>
          <div>
            <TextButton
              btnValue="decline job"
              size="large"
              actiontype="alternative"
              onClick={() => selectedJobId && handleDeclineJob(selectedJobId)}
            ></TextButton>
          </div>
          <div>
            <TextButton
              btnValue="accept job"
              size="large"
              actiontype="preferred"
              onClick={() => selectedJobId && handleAcceptJob(selectedJobId)}
            ></TextButton>
          </div>
        </PingWindowDetailsFooter>
      </PingWindowAvailableJobDetails>
    </PingWindowContainer>
  </PingWindowOverlay>
);
};
