import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { AvailableJobsPingWindow } from './AvailableJobsPingWindow';
import { useAvailableJobsStore } from '../../../Engine/hooks';
import { IAvailableJob } from '../../Models';
import { useState, useEffect } from 'react';

// 🧪 Mock Skills
const mockSkills = [
  { id: 1, name: 'Plumbing', active: true, mid: 'H2902' },
  { id: 2, name: 'Electrical', active: true, mid: 'H2903' },
  { id: 3, name: 'Carpentry', active: true, mid: 'H2904' },
  { id: 4, name: 'Painting', active: true, mid: 'H2905' }
];

// 📦 Mock Jobs
const mockAvailableJobs: IAvailableJob[] = [
  {
    id: 1201,
    appointments: [],
    appointment: {
      id: 1201,
      after_hours: false,
      appointment_name: 'Before',
      appointment_type_id: '5',
      range_start: '2025-02-25T16:00:00',
      range_end: '2025-02-25T16:00:00',
    },
    location: '',
    mid: '',
    address: '12 Windmill Road, Albertaville, 12345',
    skill: 'Plumber',
    customer: '<PERSON>e',
    cellnumber: '0801234567',
    area: 'Gauteng',
    claim_type: 'Plumbing',
    suburb: 'Roodepoort',
  },
  {
    id: 1202,
    appointments: [],
    appointment: {
      id: 1202,
      after_hours: false,
      appointment_name: 'At',
      appointment_type_id: '6',
      range_start: '2025-02-22T16:00:00',
      range_end: '2025-02-22T16:00:00',
    },
    location: '',
    mid: '',
    address: '34 Elm Street, Springfield, 12345',
    skill: 'Electrician',
    appointment_date: 'October 14, 2024',
    customer: 'Jane Smith',
    cellnumber: '0809876543',
    area: 'Gauteng',
    claim_type: 'Electrical',
    suburb: 'Whiteridge',
  }
];

// 🎯 Mock Results
const mockAwardedJobs = [
  {
    id: "10981",
    skill: "Plumbing",
    area: "Downtown",
    appointment: {
      range_start: "2024-03-20",
      appointment_name: "14:00-16:00"
    }
  },
  {
    id: "10982",
    skill: "Electrical",
    area: "Midtown",
    appointment: {
      range_start: "2024-03-21",
      appointment_name: "09:00-11:00"
    }
  }
];

const mockLostJobs = [
  {
    id: "10983",
    skill: "Carpentry",
    area: "Uptown",
    appointment: {
      range_start: "2024-03-22",
      appointment_name: "13:00-15:00"
    }
  }
];

// ⏱ Hook
const useDelayedOpen = (delay: number, openInitial: boolean) => {
  const [isOpen, setIsOpen] = useState(false);
  useEffect(() => {
    if (openInitial) {
      const timeout = setTimeout(() => setIsOpen(true), delay);
      return () => clearTimeout(timeout);
    } else {
      setIsOpen(false);
    }
  }, [delay, openInitial]);
  return isOpen;
};

// 🧱 Meta
const meta: Meta<typeof AvailableJobsPingWindow> = {
  title: 'Components/AlertsAndErrors/PingWindow/AvailableJobsPingWindow',
  component: AvailableJobsPingWindow,
  parameters: {
    layout: 'centered',
  },
};
export default meta;
type Story = StoryObj<typeof AvailableJobsPingWindow>;

// 🎁 Wrapper
const PingWindowWrapper = (args: any) => {
  const isOpen = useDelayedOpen(1500, args.isOpen);

  useEffect(() => {
    useAvailableJobsStore.setState({
      // skills: mockSkills,
      availableJobs: args.availableJobs,
      jobsAwarded: args.showAwardedJobs ? mockAwardedJobs : [],
      jobsLost: args.showLostJobs ? mockLostJobs : [],
      filteredJobs: args.availableJobs,
      isOpen: isOpen,
    });
  }, [args.availableJobs, args.showAwardedJobs, args.showLostJobs, isOpen]);

  return (
    <AvailableJobsPingWindow
      {...args}
      isOpen={isOpen}
      staffMember={{
        id: 1,
        name: 'Mock User',
        sp: { id: 10, name: 'Mock SP', skills: mockSkills },
      }}
      baseUrl="https://mock-api.example.com"
      token="mock-token"
    />
  );
};

// 📚 Stories
export const Default: Story = {
  args: {
    isOpen: false,
    availableJobs: mockAvailableJobs,
    showAwardedJobs: true,
    showLostJobs: true,
    shouldShowAwardedList: true,
  },
  render: (args) => <PingWindowWrapper {...args} />,
};
export const OpenWithAvailableJobs: Story = {
  ...Default,
  args: {
    ...Default.args,
    isOpen: true,
    availableJobs: mockAvailableJobs,
    shouldShowAwardedList: false,
    showAwardedJobs: false,
    showLostJobs: false
  }
};

export const WithAvailableJobsOnly: Story = {
  ...Default,
  args: {
    ...Default.args,
    isOpen: true,
    showAwardedJobs: false,
    showLostJobs: false,
    shouldShowAwardedList: false,
  },
};

export const WithAwardedJobsOnly: Story = {
  ...Default,
  args: {
    ...Default.args,
    isOpen: true,
    showAwardedJobs: true,
    showLostJobs: false,
    shouldShowAwardedList: true,
  },
};

export const WithLostJobsOnly: Story = {
  ...Default,
  args: {
    ...Default.args,
    isOpen: true,
    showAwardedJobs: false,
    showLostJobs: true,
    shouldShowAwardedList: true,
  },
};
// one stop


// import type { Meta, StoryObj } from '@storybook/react';
// import { AvailableJobsPingWindow } from './AvailableJobsPingWindow';
// import { useAvailableJobsStore } from '../../../Engine/hooks';
// import { IAvailableJob } from '../../Models';
// import { useState, useEffect } from 'react';

// // Mock Skills Data
// const mockSkills = [
//   { id: 1, name: 'Plumbing', active: true, mid: 'H2902' },
//   { id: 2, name: 'Electrical', active: true, mid: 'H2903' },
//   { id: 3, name: 'Carpentry', active: true, mid: 'H2904' },
//   { id: 4, name: 'Painting', active: true, mid: 'H2905' }
// ];

// // Mock Available Jobs
// const mockAvailableJobs: IAvailableJob[] = [
//   {
//     id: 1201,
//     appointments: [],
//     appointment: {
//       id: 1201,
//       after_hours: false,
//       appointment_name: 'Before',
//       appointment_type_id: '5',
//       range_start: '2025-02-25T16:00:00',
//       range_end: '2025-02-25T16:00:00',
//     },
//     location: '',
//     mid: '',
//     address: '12 Windmill Road, Albertaville, 12345',
//     skill: 'Plumber',
//     customer: 'John Doe',
//     cellnumber: '0801234567',
//     area: 'Gauteng',
//     claim_type: 'Plumbing',
//     suburb: 'Roodepoort',
//   },
//   {
//     id: 1202,
//     appointments: [],
//     appointment: {
//       id: 1202,
//       after_hours: false,
//       appointment_name: 'At',
//       appointment_type_id: '6',
//       range_start: '2025-02-22T16:00:00',
//       range_end: '2025-02-22T16:00:00',
//     },
//     location: '',
//     mid: '',
//     address: '34 Elm Street, Springfield, 12345',
//     skill: 'Electrician',
//     appointment_date: 'October 14, 2024',
//     customer: 'Jane Smith',
//     cellnumber: '0809876543',
//     area: 'Gauteng',
//     claim_type: 'Electrical',
//     suburb: 'Whiteridge',
//   }
// ];

// // Mock Awarded Jobs
// const mockAwardedJobs = [
//   {
//     id: "10981",
//     skill: "Plumbing",
//     area: "Downtown",
//     appointment: {
//       range_start: "2024-03-20",
//       appointment_name: "14:00-16:00"
//     }
//   },
//   {
//     id: "10982",
//     skill: "Electrical",
//     area: "Midtown",
//     appointment: {
//       range_start: "2024-03-21",
//       appointment_name: "09:00-11:00"
//     }
//   }
// ];

// // Mock Lost Jobs
// const mockLostJobs = [
//   {
//     id: "10983",
//     skill: "Carpentry",
//     area: "Uptown",
//     appointment: {
//       range_start: "2024-03-22",
//       appointment_name: "13:00-15:00"
//     }
//   }
// ];

// // useDelayedOpen hook implementation
// const useDelayedOpen = (delay: number, initialState: boolean) => {
//   const [isOpen, setIsOpen] = useState(false);

//   useEffect(() => {
//     if (initialState) {
//       const timer = setTimeout(() => {
//         setIsOpen(true);
//       }, delay);

//       return () => clearTimeout(timer);
//     } else {
//       setIsOpen(false);
//     }
//   }, [delay, initialState]);

//   return isOpen;
// };

// const meta: Meta<typeof AvailableJobsPingWindow> = {
//   title: 'Components/AlertsAndErrors/PingWindow/AvailableJobsPingWindow',
//   component: AvailableJobsPingWindow,
//   parameters: {
//     layout: 'centered',
//   },
// };

// export default meta;
// type Story = StoryObj<typeof AvailableJobsPingWindow>;

// const PingWindowWrapper = (args: any) => {
//   const isOpen = useDelayedOpen(2500, args.isOpen);

//   // Initialize store state
//   useEffect(() => {
//     useAvailableJobsStore.setState({      
//       availableJobs: mockAvailableJobs,
//       jobsAwarded: args.showAwardedJobs ? mockAwardedJobs : [],
//       jobsLost: args.showLostJobs ? mockLostJobs : [],
//       isOpen: isOpen
//     });
//   }, [args.availableJobs, args.showAwardedJobs, args.showLostJobs, isOpen]);

//   return (
//     <div>
//       <AvailableJobsPingWindow 
//         {...args} 
//         availableJobs={args.availableJobs} 
//         isOpen={isOpen} 
//         skills={mockSkills}
//         token="mock-token"
//       />
//     </div>
//   );
// };

// export const Default: Story = {
//   args: {
//     isOpen: false,
//     availableJobs: mockAvailableJobs,
//     skills: mockSkills,
//     shouldShowAwardedList: false,
//     showAwardedJobs: true,
//     showLostJobs: true
//   },
//   render: (args) => <PingWindowWrapper {...args} />
// };

// export const OpenWithAvailableJobs: Story = {
//   ...Default,
//   args: {
//     ...Default.args,
//     isOpen: true,
//     availableJobs: mockAvailableJobs,
//     shouldShowAwardedList: false,
//     showAwardedJobs: false,
//     showLostJobs: false
//   }
// };

// export const PreOpened: Story = {
//   ...Default,
//   args: {
//     ...Default.args,
//     isOpen: true,
//   }
// };

// export const WithAwardedJobsOnly: Story = {
//   ...Default,
//   args: {
//     ...Default.args,
//     isOpen: true,
//     showAwardedJobs: true,
//     showLostJobs: false,
//     shouldShowAwardedList: true
//   }
// };

// export const WithLostJobsOnly: Story = {
//   ...Default,
//   args: {
//     ...Default.args,
//     isOpen: true,
//     showAwardedJobs: false,
//     showLostJobs: true,
//     shouldShowAwardedList: true
//   }
// };

// // DOOUBLE DOWN

// /* eslint-disable @typescript-eslint/no-explicit-any */
// import type { Meta, StoryObj } from '@storybook/react';
// import { AvailableJobsPingWindow } from './AvailableJobsPingWindow';
// import { useDelayedOpen } from '../hooks/useDelayedOpen';
// import { useAvailableJobsStore } from '../../../Engine/hooks';
// import { IAvailableJob } from '../../Models';

// const mockSkills = [
//   { id: 1, name: 'Plumbing', active: true, mid: 'H2902' },
//   { id: 2, name: 'Electrical', active: true, mid: 'H2903' },
//   { id: 3, name: 'Carpentry', active: true, mid: 'H2904' },
//   { id: 4, name: 'Painting', active: true, mid: 'H2905' }
// ];
// const mockAvailableJobs: IAvailableJob[] = [
//   // const available_jobs: IAvailableJob[] = [
//   {
//     id: 1201,
//     appointments: [],
//     appointment: {
//       id: 1201,
//       after_hours: false,
//       appointment_name: 'Before',
//       appointment_type_id: '5',
//       range_start: '2025-02-25T16:00:00',
//       range_end: '2025-02-25T16:00:00',
//     },
//     location: '',
//     mid: '',
//     address: '12 Windmill Road, Albertaville, 12345',
//     skill: 'Plumber',
//     customer: 'John Doe',
//     cellnumber: '0801234567',
//     area: 'Gauteng',
//     claim_type: 'Plumbing',
//     suburb: 'Roodepoort',
//   },
//   {
//     id: 1202,
//     appointments: [],
//     appointment: {
//       id: 1202,
//       after_hours: false,
//       appointment_name: 'At',
//       appointment_type_id: '6',
//       range_start: '2025-02-22T16:00:00',
//       range_end: '2025-02-22T16:00:00',
//     },
//     location: '',
//     mid: '',
//     address: '34 Elm Street, Springfield, 12345',
//     skill: 'Electrician',
//     appointment_date: 'October 14, 2024',
//     customer: 'Jane Smith',
//     cellnumber: '0809876543',
//     area: 'Gauteng',
//     claim_type: 'Plumbing',
//     suburb: 'Whiteridge',
//   },
//   {
//     id: 1203,
//     appointments: [],
//     appointment: {
//       id: 1203,
//       after_hours: false,
//       appointment_name: 'August 25, 2024',
//       appointment_type_id: 'Carpenter',
//       range_start: 'August 25, 2024',
//       range_end: 'August 25, 2024',
//     },
//     location: '',
//     mid: '',
//     address: '56 Oak Avenue, Brooksville, 67890',
//     skill: 'Carpenter',
//     appointment_date: 'August 25, 2024',
//     customer: 'David Johnson',
//     cellnumber: '0804567890',
//     area: 'Northwest',
//     claim_type: 'Plumbing',
//     suburb: 'Glenvista',
//   },
//   {
//     id: 1204,
//     appointments: [],
//     appointment: {
//       id: 1204,
//       after_hours: false,
//       appointment_name: 'September 16, 2024',
//       appointment_type_id: 'Painter',
//       range_start: 'September 16, 2024',
//       range_end: 'September 16, 2024',
//     },
//     location: '',
//     mid: '',
//     address: '45 Sunnydale Drive, Springfield, 12345',
//     skill: 'Painter',
//     appointment_date: 'September 16, 2024',
//     customer: 'Jane Smith',
//     cellnumber: '0809876543',
//     area: 'Gauteng',
//     claim_type: 'Plumbing',
//     suburb: 'Albertaville',
//   },
//   {
//     id: 1205,
//     appointments: [],
//     appointment: {
//       id: 1205,
//       after_hours: false,
//       appointment_name: 'August 27, 2024',
//       appointment_type_id: 'Carpenter',
//       range_start: 'August 27, 2024',
//       range_end: 'August 27, 2024',
//     },
//     location: '',
//     mid: '',
//     address: '11 Ocean Drive, Brooksville, 67890',
//     skill: 'Carpenter',
//     appointment_date: 'August 27, 2024',
//     customer: 'John Doe',
//     cellnumber: '0801234567',
//     area: 'Northwest',
//     claim_type: 'Plumbing',
//     suburb: 'Albertaville',
//   },
//   {
//     id: 1206,
//     appointments: [],
//     appointment: {
//       id: 1206,
//       after_hours: false,
//       appointment_name: 'September 16, 2024',
//       appointment_type_id: 'Electrician',
//       range_start: 'September 16, 2024',
//       range_end: 'September 16, 2024',
//     },
//     location: '',
//     mid: '',
//     address: '132 Oak Avenue, Springfield, 90210',
//     skill: 'Electrician',
//     appointment_date: 'September 16, 2024',
//     customer: 'Jane Smith',
//     cellnumber: '0809876543',
//     area: 'Gauteng',
//     claim_type: 'Plumbing',
//     suburb: 'Albertaville',
//   },
//   {
//     id: 1207,
//     appointments: [],
//     appointment: {
//       id: 1207,
//       after_hours: false,
//       appointment_name: 'September 16, 2024',
//       appointment_type_id: 'Plumber',
//       range_start: 'September 16, 2024',
//       range_end: 'September 16, 2024',
//     },
//     location: '',
//     mid: '',
//     address: '32 Elm Street, Albertaville, 12345',
//     skill: 'Plumber',
//     appointment_date: 'September 16, 2024',
//     customer: 'John Doe',
//     cellnumber: '0801234567',
//     area: 'Gauteng',
//     claim_type: 'Plumbing',
//     suburb: 'Albertaville',
//   },
//   {
//     id: 1208,
//     appointments: [],
//     appointment: {
//       id: 1208,
//       after_hours: false,
//       appointment_name: 'September 16, 2024',
//       appointment_type_id: 'Painter',
//       range_start: 'September 16, 2024',
//       range_end: 'September 16, 2024',
//     },
//     location: '',
//     mid: '',
//     address: '51 Windmill Road, Brooksville, 67890',
//     skill: 'Painter',
//     appointment_date: 'September 16, 2024',
//     customer: 'Jane Smith',
//     cellnumber: '0809876543',
//     area: 'Gauteng',
//     claim_type: 'Plumbing',
//     suburb: 'Albertaville',
//   },

//   // Add more objects here with randomized values
//   // ...
// ];
// const mockAwardedJobs = [
//   {
//     id: "10981",
//     skill: "Plumbing",
//     area: "Downtown",
//     appointment: {
//       range_start: "2024-03-20",
//       appointment_name: "14:00-16:00"
//     }
//   },
//   {
//     id: "10982",
//     skill: "Electrical",
//     area: "Midtown",
//     appointment: {
//       range_start: "2024-03-21",
//       appointment_name: "09:00-11:00"
//     }
//   }
// ];

// const mockLostJobs = [
//   {
//     id: "10983",
//     skill: "Carpentry",
//     area: "Uptown",
//     appointment: {
//       range_start: "2024-03-22",
//       appointment_name: "13:00-15:00"
//     }
//   }
// ];

// const meta: Meta<typeof AvailableJobsPingWindow> = {
//   component: AvailableJobsPingWindow,
//   title: 'Components/AlertsAndErrors/AvailableJobsPingWindow',
//   decorators: [
//     (Story) => {
//       // Initialize store with mock data
//       useAvailableJobsStore.setState({
//         availableJobs: mockAvailableJobs,
//         jobsAwarded: mockAwardedJobs,
//         jobsLost: mockLostJobs
//       });
//       return <Story />;
//     }
//   ]
// };
// export default meta;
// type Story = StoryObj<typeof AvailableJobsPingWindow>;

// const PingWindowWrapper = (args: any) => {
//   // Use the hook to handle the delayed open functionality
//   const isOpen = useDelayedOpen(2500, args.isOpen); // Delay set to 2.5 seconds
 
//   return (
//     <div>
//       <AvailableJobsPingWindow {...args} availableJobs={args.availableJobs} isOpen={isOpen} skills={mockSkills} />
//     </div>
//   );
// };

// export const DefaultPingWindowStory: Story = {
//   argTypes: {
//     // width: {
//     //   control: 'text',
//     // },
//     // background: {
//     //   control: 'color',
//     // },
//     // availableJobs: {
//     //   control: 'number',
//     // },
//     isOpen: {
//       control: 'boolean',
//     },
//     shouldShowAwardedList: {
//       control: 'boolean',
//       description: 'Show awarded jobs list'
//     },
//     showAwardedJobs: {
//       control: 'boolean',
//       description: 'Show awarded jobs list'
//     },
//     showLostJobs: {
//       control: 'boolean',
//       description: 'Show lost jobs list'
//     }
//   },
//   args: {
//     isOpen: false, // Start with isOpen as false; will change after delay
//     availableJobs: [
//       {
//         id: 1201,
//         appointments: [],
//         appointment: {
//           id: 1201,
//           after_hours: false,
//           appointment_name: 'Before',
//           appointment_type_id: '5',
//           range_start: '2025-02-25T16:00:00',
//           range_end: '2025-02-25T16:00:00',
//         },
//         location: '',
//         mid: '',
//         address: '12 Windmill Road, Albertaville, 12345',
//         skill: '1',
//         customer: 'John Doe',
//         cellnumber: '0801234567',
//         area: 'Gauteng',
//         claim_type: 'Plumbing',
//         suburb: 'Roodepoort',
//       },
//       {
//         id: 1202,
//         appointments: [],
//         appointment: {
//           id: 1202,
//           after_hours: false,
//           appointment_name: 'At',
//           appointment_type_id: '6',
//           range_start: '2025-02-22T16:00:00',
//           range_end: '2025-02-22T16:00:00',
//         },
//         location: '',
//         mid: '',
//         address: '34 Elm Street, Springfield, 12345',
//         skill: '3',
//         appointment_date: 'October 14, 2024',
//         customer: 'Jane Smith',
//         cellnumber: '0809876543',
//         area: 'Gauteng',
//         claim_type: 'Plumbing',
//         suburb: 'Whiteridge',
//       },
//       {
//         id: 1203,
//         appointments: [],
//         appointment: {
//           id: 1203,
//           after_hours: false,
//           appointment_name: 'August 25, 2024',
//           appointment_type_id: 'Carpenter',
//           range_start: 'August 25, 2024',
//           range_end: 'August 25, 2024',
//         },
//         location: '',
//         mid: '',
//         address: '56 Oak Avenue, Brooksville, 67890',
//         skill: '2',
//         appointment_date: 'August 25, 2024',
//         customer: 'David Johnson',
//         cellnumber: '0804567890',
//         area: 'Northwest',
//         claim_type: 'Plumbing',
//         suburb: 'Glenvista',
//       },
//       {
//         id: 1204,
//         appointments: [],
//         appointment: {
//           id: 1204,
//           after_hours: false,
//           appointment_name: 'September 16, 2024',
//           appointment_type_id: 'Painter',
//           range_start: 'September 16, 2024',
//           range_end: 'September 16, 2024',
//         },
//         location: '',
//         mid: '',
//         address: '45 Sunnydale Drive, Springfield, 12345',
//         skill: '4',
//         appointment_date: 'September 16, 2024',
//         customer: 'Jane Smith',
//         cellnumber: '0809876543',
//         area: 'Gauteng',
//         claim_type: 'Plumbing',
//         suburb: 'Albertaville',
//       },
//       {
//         id: 1205,
//         appointments: [],
//         appointment: {
//           id: 1205,
//           after_hours: false,
//           appointment_name: 'August 27, 2024',
//           appointment_type_id: 'Carpenter',
//           range_start: 'August 27, 2024',
//           range_end: 'August 27, 2024',
//         },
//         location: '',
//         mid: '',
//         address: '11 Ocean Drive, Brooksville, 67890',
//         skill: '2',
//         appointment_date: 'August 27, 2024',
//         customer: 'John Doe',
//         cellnumber: '0801234567',
//         area: 'Northwest',
//         claim_type: 'Plumbing',
//         suburb: 'Albertaville',
//       },
//       {
//         id: 1206,
//         appointments: [],
//         appointment: {
//           id: 1206,
//           after_hours: false,
//           appointment_name: 'September 16, 2024',
//           appointment_type_id: 'Electrician',
//           range_start: 'September 16, 2024',
//           range_end: 'September 16, 2024',
//         },
//         location: '',
//         mid: '',
//         address: '132 Oak Avenue, Springfield, 90210',
//         skill: '4',
//         appointment_date: 'September 16, 2024',
//         customer: 'Jane Smith',
//         cellnumber: '0809876543',
//         area: 'Gauteng',
//         claim_type: 'Plumbing',
//         suburb: 'Albertaville',
//       },
//       {
//         id: 1207,
//         appointments: [],
//         appointment: {
//           id: 1207,
//           after_hours: false,
//           appointment_name: 'September 16, 2024',
//           appointment_type_id: 'Plumber',
//           range_start: 'September 16, 2024',
//           range_end: 'September 16, 2024',
//         },
//         location: '',
//         mid: '',
//         address: '32 Elm Street, Albertaville, 12345',
//         skill: '4',
//         appointment_date: 'September 16, 2024',
//         customer: 'John Doe',
//         cellnumber: '0801234567',
//         area: 'Gauteng',
//         claim_type: 'Plumbing',
//         suburb: 'Albertaville',
//       },
//       {
//         id: 1208,
//         appointments: [],
//         appointment: {
//           id: 1208,
//           after_hours: false,
//           appointment_name: 'September 16, 2024',
//           appointment_type_id: 'Painter',
//           range_start: 'September 16, 2024',
//           range_end: 'September 16, 2024',
//         },
//         location: '',
//         mid: '',
//         address: '51 Windmill Road, Brooksville, 67890',
//         skill: '2',
//         appointment_date: 'September 16, 2024',
//         customer: 'Jane Smith',
//         cellnumber: '0809876543',
//         area: 'Gauteng',
//         claim_type: 'Plumbing',
//         suburb: 'Albertaville',
//       },
//     ],
//     skills: mockSkills,
//     shouldShowAwardedList: false,
//     showAwardedJobs: true,
//     showLostJobs: true
//   },
//   decorators: [
//     (Story, context) => {
//       useAvailableJobsStore.setState({
      
//         jobsAwarded: context.args.showAwardedJobs ? mockAwardedJobs : [],
//         jobsLost: context.args.showLostJobs ? mockLostJobs : []
//       });
//       return <Story />;
//     }
//   ],
  
//   render: (args) => <PingWindowWrapper {...args} />,
// };
