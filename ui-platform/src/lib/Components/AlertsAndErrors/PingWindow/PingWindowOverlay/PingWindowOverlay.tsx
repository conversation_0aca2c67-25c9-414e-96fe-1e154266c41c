import { ComponentPropsWithoutRef, memo, useRef } from 'react';
import styled, { keyframes } from 'styled-components';

const expand = keyframes` 
0% {
    transform: scale(0);
}
100% {
    transform: scale(1);
}
`;
 

const Overlay = styled.div<
    { visible: boolean } & Pick<ComponentPropsWithoutRef<'div'>, 'children'>
>`
    position: fixed;
    top: 0;
    left: 0;
    display: grid;
    width: 100%;
    height: 100%;
    display: grid;
    background: ${(props) => props?.theme.ColorsOverlayDynamicPanel};
    z-index: 999;
    animation: ${expand} 0.2s ease-out;
    transform-origin: center;
    place-items: center;
    
`;

/**
 * PingWindowOverlay component.
 * 
 * @param {Object} props - The component props.
 * @param {boolean} props.visible - Whether the overlay is visible.
 * @param {React.ReactNode} props.children - The child components to render inside the overlay.
 * 
 * @returns {React.ReactElement} The rendered overlay component.
 */
export const PingWindowOverlay = memo(
    ({ visible, children }: { visible: boolean; children: React.ReactNode }) =>  {
        const ref = useRef<HTMLDivElement>(null);
        
        return (
            <Overlay data-testid="ping-window-overlay" ref={ref} visible={visible}>
                {children}
            </Overlay>
        )
    }
);