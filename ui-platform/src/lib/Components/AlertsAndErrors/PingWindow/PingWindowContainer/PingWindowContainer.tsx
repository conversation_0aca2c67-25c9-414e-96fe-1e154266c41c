/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import styled from 'styled-components';

export interface ContainerProps {
    theme?: NonNullable<unknown>,
    clickedOutside?: boolean;
}
const Container = styled.div<ContainerProps>`
    display: grid;
    max-width: 798px;
    min-width: 798px;
    width: 100%;
    max-height: 789px;
    min-height: 610px;
    background: ${(props) => props.theme?.ColorsOverlaySurfaceOverlay};
    grid-gap: ${(props) => props.theme?.GapMd};
    // grid-auto-rows: min-content;
    border-radius: ${(props) => props.theme?.RadiusMd};
    padding: ${(props) => props.theme?.GapSm};
    box-shadow: 0px 0px 2px rgba(0, 0, 255, 0.8), 0px 0px 10px rgba(255, 255, 255, 0.8), 0px 0px 395px rgba(255, 255, 255, 0.2);
    
`


  interface PingWindowContainerProps {
    isOverlayVisible: boolean;
    setIsOverlayVisisble: React.Dispatch<React.SetStateAction<boolean>>;
    // activeConfig: PingWindowConfig;
    // setActiveConfig: React.Dispatch<React.SetStateAction<any>>;
    //configs: PingWindowConfig[];
    children: any;

  }

/**
 * A container component for the PingWindow component tree.
 *
 * @param {PingWindowContainerProps} props
 * @param {boolean} props.isOverlayVisible - Whether the overlay should be visible
 * @param {React.Dispatch<React.SetStateAction<boolean>>} props.setIsOverlayVisisble - A setter function to toggle the overlay visibility
 * @param {React.ReactNode} props.children - The child components to be rendered within the PingWindowContainer
 *
 * @returns {React.ReactElement} A Container component with the rendered children
 */
  export const PingWindowContainer = ({
      isOverlayVisible,
      setIsOverlayVisisble,
      // configs,
      children
      // activeConfig,
      // setActiveConfig
  }: PingWindowContainerProps) => {
      return (
          <Container>
            {children}
          </Container>
      )
  }
