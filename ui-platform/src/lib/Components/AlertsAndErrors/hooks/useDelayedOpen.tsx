import { is } from 'ramda';
import {useState, useEffect } from 'react';

/**
 * Hook to delay the open state of a component.
 * @param {number} delay The time to wait in milliseconds before opening the component.
 * @param {boolean} initialOpen The initial state of the component.
 * @returns {boolean} The current open state of the component.
 */
export const useDelayedOpen = (delay: number, initialOpen: boolean) => {
    const [isOpen, setIsOpen] = useState(initialOpen);
    useEffect(() => {
        if(!initialOpen) {
            const timer = setTimeout(() => {
                setIsOpen(true);
            }, delay);
            return () => clearTimeout(timer);
        }
    }, [delay, initialOpen]);
    useEffect(() => {
        setIsOpen(initialOpen);
    }, [initialOpen])
    return isOpen
}