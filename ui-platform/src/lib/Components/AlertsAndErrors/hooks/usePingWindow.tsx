import { useState } from 'react';

/**
 * usePingWindow
 *
 * Hook to manage the state of the PingWindow component
 *
 * @returns An object containing the following properties:
 * - isPingWindowVisible: boolean state indicating whether the PingWindow is visible or not
 * - setIsPingWindowVisible: function to set the isPingWindowVisible state
 * - activeConfig: any configuration object that is currently active
 * - setActiveConfig: function to set the activeConfig state
 */

export function usePingWindow() {
  const [isPingWindowVisible, setIsPingWindowVisible] = useState<boolean>(false);
  const [activeConfig, setActiveConfig] = useState<any>(null);
  return {
    isPingWindowVisible,
    setIsPingWindowVisible,
    activeConfig,
    setActiveConfig,
  };
}