import { Meta, StoryObj } from '@storybook/react';
import { ImageWithLoader } from './ImageWithLoader';

export default {
  component: ImageWithLoader,
  title: 'Components/Image/ImageWithLoader',
} as Meta<typeof ImageWithLoader>;

type Story = StoryObj<typeof ImageWithLoader>;

export const Overview: Story = {
  args: {
    src: 'https://picsum.photos/1336/1080',
    alt: 'Randomly generated image from lorem picsum.',
  },
};
