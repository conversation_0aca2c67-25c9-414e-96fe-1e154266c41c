import React, { ComponentPropsWithoutRef, useEffect, useState } from 'react';
import { ImageLoader } from '../ImageLoader/ImageLoader';

interface Props
  extends Omit<ComponentPropsWithoutRef<'img'>, 'height' | 'width'> {
  width?: number;
  height?: number;
}

/**
 * A component that renders an image and shows a loading animation while the image is
 * loading. The component takes the same props as the `img` element, with the addition of
 * `width` and `height` props that can be used to set the size of the image. The
 * component will use the `naturalWidth` and `naturalHeight` properties of the image to
 * determine its size if the `width` and `height` props are not provided.
 *
 * The component will only render the image when it has finished loading, and will render
 * a loading animation in the meantime. The loading animation is provided by the
 * `ImageLoader` component, which is rendered with the same `width` and `height` props as
 * the `ImageWithLoader` component.
 *
 * The component also takes a `loading` prop that can be set to `'lazy'` to delay the
 * loading of the image until it is needed. The default value of the `loading` prop is
 * `'lazy'`.
 *
 * @param {string} [className] - The CSS class name to apply to the component.
 * @param {'lazy'} [loading] - The loading strategy to use for the image. Set to `'lazy'`
 *   to delay the loading of the image until it is needed. The default value is `'lazy'`.
 * @param {string} [alt] - The alt text to use for the image.
 * @param {string} [src] - The URL of the image to load.
 * @param {string} [title] - The title text to use for the image.
 * @param {number} [width] - The width of the image in pixels. If not provided, the
 *   component will use the `naturalWidth` property of the image to determine its width.
 * @param {number} [height] - The height of the image in pixels. If not provided, the
 *   component will use the `naturalHeight` property of the image to determine its height.
 * @param {object} [rest] - Any additional props to pass to the `img` element.
 * @return {JSX.Element} - The rendered component.
 */
export const ImageWithLoader = ({
  className,
  loading = 'lazy',
  alt,
  src,
  title,
  ...rest
}: Props) => {
  const [loadingImg, setLoadingImg] = useState<boolean>(true);
  const [imageSrc, setImageSrc] = useState<Props['src']>();
  const [{ width: imageWidth, height: imageHeight }, setImageSize] = useState<{
    width?: number;
    height?: number;
  }>(
    rest.width && rest.height
      ? {
          width: rest.width,
          height: rest.height,
        }
      : {
          width: 200,
          height: 200,
        }
  );

  useEffect(() => {
    // loading image asynchronously
    setImageSrc(undefined);
    if (src) {
  /**
   * Event handler that is called when the image has finished loading.
   * Sets the `imageSrc` state to the `src` prop and sets `loadingImg` to true.
   */
      const handleImageLoad = () => {
        setImageSrc(src);
        setLoadingImg(true);
      };
      const loadedImage = new Image();
      // add loading event to track when image finishes loading
      loadedImage.addEventListener('load', handleImageLoad);
      loadedImage.src = src;
      /**
       * onload event handler for the image element, gets the image's natural width and height
       * and sets it to the component state, and also sets the loading state to false
       */
      loadedImage.onload = function () {
        // get image's intrinsic dimensions
        setImageSize({
          width: loadedImage.naturalWidth,
          height: loadedImage.naturalHeight,
        });
        setLoadingImg(false);
      };
      return () => {
        loadedImage.removeEventListener('load', handleImageLoad);
      };
    }
    return () => null;
  }, [src]);

  if (imageSrc === src && !loadingImg) {
    return (
      <img
        title={title}
        alt={alt}
        data-testid="image-loaded"
        src={src}
        loading={loading}
        className={className}
        {...rest}
      />
    );
  }

  return (
    <ImageLoader
      height={rest.height}
      className={className}
      imageHeight={imageHeight}
      imageWidth={imageWidth}
      width={rest.width}
    />
  );
};
