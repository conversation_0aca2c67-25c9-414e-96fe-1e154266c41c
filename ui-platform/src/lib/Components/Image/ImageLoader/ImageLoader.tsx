import { useCallback } from 'react';
import styled from 'styled-components';

interface Props {
  imageHeight?: number;
  imageWidth?: number;
  height?: number;
  width?: number;
  className?: string;
}

const ImageAnimation = styled.div`
  background-color: ${(props) => props?.theme?.ColorsBackgroundActionBar};
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
      background-color: ${(props) => props?.theme?.ColorsBackgroundActionBar};
    }
    50% {
      opacity: 0.5;
      background-color: ${(props) => props?.theme?.ColorsBackgroundInverse};
    }
  }
`;

/**
 * A component that displays an animation to indicate that an image is being
 * loaded. The animation is a pulsing box that is centered within the
 * component's bounding box.
 *
 * @param {Object} props
 * @prop {string} [className] - Additional class names to add to the component.
 * @prop {number} [imageHeight] - The height of the image.
 * @prop {number} [imageWidth] - The width of the image.
 * @prop {number} [height] - The height of the component. If only this is given,
 * the component will be a square.
 * @prop {number} [width] - The width of the component. If only this is given, the
 * component will be a square.
 * @returns {ReactElement} The ImageLoader component.
 */
export const ImageLoader = ({
  className = '',
  imageHeight,
  imageWidth,
  height,
  width,
}: Props) => {
  const imageCountainerSize = useCallback(() => {
    // if only width is given, image is made into a square
    const loaderWidth = !width && height ? height : width || imageWidth;
    // if only height is given, image is made into a square
    const loaderHeight = !height && width ? width : height || imageHeight;
    return {
      height: `${loaderHeight}px`,
      width: `${loaderWidth}px`,
    };
  }, [height, imageHeight, imageWidth, width]);

  return (
    <ImageAnimation
      data-testid="image-loading"
      className={className}
      style={imageCountainerSize()}
    ></ImageAnimation>
  );
};
