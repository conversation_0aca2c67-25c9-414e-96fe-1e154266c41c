import React from "react";

interface CompanyPlatformProps {
    cardType: string;
}

/**
 * Render a company platform card based on the given card type
 * @param {string} cardType - The type of company platform card to render. Can be either 'CreateCompanyCard' or 'JoinCompanyCard'
 * @returns {ReactElement} The rendered company platform card
 */
export const CompanyPlatform: React.FC<CompanyPlatformProps> = ({ cardType }) => {
    return (
        <div>
            {cardType === 'CreateCompanyCard' && (
                <svg width="161" height="147" viewBox="0 0 161 147" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g id="CreateCompanyCard">
                        <path id="Vector" d="M107.167 123.042C104.056 123.042 101.029 122.732 98.0867 122.112C95.1445 121.492 92.2822 120.613 89.5 119.477C96.0556 113.364 101.196 106.545 104.92 99.0202C108.644 91.495 110.504 83.6552 110.5 75.5007C110.5 67.3507 108.64 59.5108 104.92 51.9811C101.2 44.4514 96.06 37.6326 89.5 31.5246C92.2778 30.3927 95.14 29.5165 98.0867 28.8962C101.033 28.2759 104.06 27.9635 107.167 27.959C120.167 27.959 131.196 32.5728 140.253 41.8004C149.311 51.028 153.838 62.2614 153.833 75.5007C153.833 88.7444 149.307 99.9801 140.253 109.208C131.2 118.435 120.171 123.047 107.167 123.042ZM80.5 114.553C74.3889 110.251 69.5289 104.705 65.92 97.9132C62.3111 91.1215 60.5045 83.6507 60.5 75.5007C60.5 67.3507 62.3067 59.8798 65.92 53.0882C69.5333 46.2965 74.3933 40.75 80.5 36.4486C86.6111 40.75 91.4733 46.2965 95.0867 53.0882C98.7 59.8798 100.504 67.3507 100.5 75.5007C100.5 83.6507 98.6956 91.1215 95.0867 97.9132C91.4778 104.705 86.6156 110.251 80.5 114.553ZM53.8334 123.042C40.8334 123.042 29.8067 118.431 20.7534 109.208C11.7 99.9846 7.17113 88.7489 7.16669 75.5007C7.16669 62.2569 11.6956 51.0235 20.7534 41.8004C29.8111 32.5773 40.8378 27.9635 53.8334 27.959C56.9445 27.959 59.9733 28.2714 62.92 28.8962C65.8667 29.5211 68.7267 30.3972 71.5 31.5246C64.9445 37.6371 59.8067 44.4582 56.0867 51.9879C52.3667 59.5176 50.5045 67.3552 50.5 75.5007C50.5 84.443 52.3067 92.7356 55.92 100.379C59.5333 108.021 64.5045 114.501 70.8333 119.816C68.1667 120.835 65.4178 121.627 62.5867 122.193C59.7556 122.759 56.8378 123.042 53.8334 123.042Z" fill="#F0F0F0"/>
                    </g>
                </svg>
            )}
            {cardType === 'JoinCompanyCard' && (
                <svg width="161" height="147" viewBox="0 0 161 147" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g id="JoinCompanyCard">
                        <path id="Vector" d="M53.8333 113C42.7133 113 33.2667 109.113 25.4933 101.34C17.72 93.5622 13.8333 84.1156 13.8333 73C13.8333 61.88 17.72 52.4333 25.4933 44.66C33.2711 36.8867 42.7178 33 53.8333 33C57.86 33 61.7178 33.5778 65.4067 34.7333C69.0956 35.8889 72.4867 37.5622 75.58 39.7533C74.7133 40.54 73.8956 41.38 73.1267 42.2733L70.8333 44.9467C68.4422 43.2711 65.8067 41.9733 62.9267 41.0533C60.0511 40.1289 57.02 39.6667 53.8333 39.6667C44.6111 39.6667 36.7511 42.9178 30.2533 49.42C23.7556 55.9222 20.5045 63.7822 20.5 73C20.5 82.2222 23.7511 90.0844 30.2533 96.5867C36.7556 103.089 44.6156 106.338 53.8333 106.333C57.02 106.333 60.0511 105.871 62.9267 104.947C65.8067 104.027 68.4422 102.729 70.8333 101.053L73.1267 103.72C73.8956 104.618 74.7133 105.46 75.58 106.247C72.4822 108.442 69.0911 110.116 65.4067 111.267C61.7178 112.422 57.86 113 53.8333 113ZM107.167 113C103.14 113 99.2822 112.422 95.5933 111.267C91.9045 110.111 88.5133 108.438 85.42 106.247C86.2867 105.46 87.1045 104.62 87.8733 103.727L90.1667 101.053C92.5845 102.729 95.2245 104.027 98.0867 104.947C100.953 105.871 103.98 106.333 107.167 106.333C116.389 106.333 124.251 103.082 130.753 96.58C137.256 90.0778 140.504 82.2178 140.5 73C140.5 63.7778 137.249 55.9178 130.747 49.42C124.244 42.9222 116.384 39.6711 107.167 39.6667C103.98 39.6667 100.953 40.1289 98.0867 41.0533C95.2245 41.9733 92.5845 43.2711 90.1667 44.9467L87.8733 42.28C87.1045 41.3822 86.2867 40.54 85.42 39.7533C88.5178 37.5578 91.9089 35.8844 95.5933 34.7333C99.2822 33.5778 103.14 33 107.167 33C118.287 33 127.733 36.8867 135.507 44.66C143.28 52.4378 147.167 61.8844 147.167 73C147.167 84.12 143.28 93.5667 135.507 101.34C127.729 109.113 118.282 113 107.167 113ZM80.5 100.1C79.8067 100.1 79.1133 99.94 78.42 99.62C77.7311 99.3 77.1089 98.8622 76.5533 98.3067C73.5756 94.8533 71.2667 90.9467 69.6267 86.5867C67.9867 82.2311 67.1667 77.7022 67.1667 73C67.1667 68.2978 67.9889 63.7689 69.6333 59.4133C71.2689 55.0533 73.5756 51.1467 76.5533 47.6933C77.1089 47.1378 77.7311 46.7 78.42 46.38C79.1133 46.06 79.8067 45.9 80.5 45.9C81.1933 45.9 81.8867 46.06 82.58 46.38C83.2689 46.7 83.8911 47.1378 84.4467 47.6933C87.4245 51.1467 89.7333 55.0533 91.3733 59.4133C93.0133 63.7689 93.8333 68.2978 93.8333 73C93.8333 77.7022 93.0111 82.2311 91.3667 86.5867C89.7311 90.9467 87.4245 94.8533 84.4467 98.3067C83.8911 98.8622 83.2689 99.3 82.58 99.62C81.8867 99.94 81.1933 100.1 80.5 100.1Z" fill="#F0F0F0"/>
                    </g>
                </svg>
            )}
        </div>
    );
}
