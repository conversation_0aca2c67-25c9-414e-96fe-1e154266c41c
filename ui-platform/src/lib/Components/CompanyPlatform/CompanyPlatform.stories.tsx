import type { Meta, StoryObj } from '@storybook/react';
import { CompanyPlatform } from './CompanyPlatform';

const meta: Meta<typeof CompanyPlatform> = {
  component: CompanyPlatform,
  title: 'Components/CompanyPlatform',
};

export default meta;
type Story = StoryObj<typeof CompanyPlatform>;

export const CreateCompanyCard: Story = {
  args: {
    cardType: 'CreateCompanyCard'
  }
};

export const JoinCompanyCard: Story = {
  args: {
    cardType: 'JoinCompanyCard'
  }
};
