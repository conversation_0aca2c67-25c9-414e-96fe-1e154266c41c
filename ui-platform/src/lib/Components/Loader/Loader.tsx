import React from 'react';
import styled from 'styled-components';
import loaderIcon from './LoaderIcon.png';
import { useTheme } from 'styled-components';

interface LoaderProps {
  type?: 'error' | 'alert';
  text?: string;
}

const Container = styled.div<{ backgroundColor?: string }>`
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  position: fixed;
  z-index: 9999;
  background-color: ${(props) => props.backgroundColor || 'transparent'};
`;

const Wrapper = styled.div`
  width: 75px;
  height: 75px;
  display: grid;
  place-items: center;
  .loading-text {
    margin-top: 8px;
    font-size: 12px;
    color: ${(props) => props.theme.ColorsInterfaceVariant85};
  }
`;

const Loading = styled.img`
  width: 100%;
  height: auto;
  animation: spin 2s linear infinite;

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;

/**
 * A reusable Loader component that displays a loading animation with a customizable background color and opacity.
 *
 * @param {LoaderProps} props - The component props.
 * @param {('error' | 'alert')} props.type - The type of loader, which determines the background color and opacity.
 * @param {React.ReactNode} props.children - The content to be displayed on top of the loader.
 * @return {JSX.Element} The Loader component.
 */

export const Loader = ({ type, text = 'Loading' }: LoaderProps) => {
  const theme = useTheme();
  const backgroundColor =
    type === 'error'
      ? `${theme.ColorsOverlayErrorOverlay}80`
      : type === 'alert'
      ? `${theme.ColorsOverlayAlert}80`
      : 'transparent';

  return (
    <Container backgroundColor={backgroundColor}>
      <Wrapper>
        <Loading src={loaderIcon} alt="Loader Icon" />
        <span className="loading-text">{text}</span>
      </Wrapper>
    </Container>
  );
};
