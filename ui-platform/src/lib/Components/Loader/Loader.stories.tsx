import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Loader } from './Loader';
import { ProfileHero } from '../../Fragments';

const meta: Meta<typeof Loader> = {
  component: Loader,
  title: 'Components/Loader',
};
export default meta;
type Story = StoryObj<typeof Loader>;

export const Overview: Story = {
  args: {},
};

export const AlertMask: Story = {
  args: {
    type: 'alert',
  },
};

export const ErrorMask: Story = {
  render: () => (
    <div>
      <Loader type={'error'} />
      <ProfileHero></ProfileHero>
    </div>
  ),
};
