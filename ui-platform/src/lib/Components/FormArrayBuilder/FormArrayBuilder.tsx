import React, { useState } from "react";
import styled from "styled-components";
import { Heading } from "../Heading/Heading";

interface FormArrayBuilderProps {
  skillGroupOptions: string[];
  skillOptions: string[];
  providerOptions: string[];
  onChange: (selectedOptions: { skillGroup: string[], skill: string[], provider: string[] }) => void;
}

const Container = styled.div`
  display: grid;
  justify-content: center;
  gap: 0px;
  width: 100%;
`;

const DropdownContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(3, minmax(150px, auto)) auto auto;
  gap: 10px;
  align-items: center;
  margin-bottom: 10px;
  width: 100%;
`;

const Dropdown = styled.select`
  padding: 5px;
  margin: 10px;
  background-color: ${(props) => props?.theme.ColorsBackgroundShell};
  color: ${(props) => props?.theme.ColorsTypographyPrimary};

  &:invalid {
    color: ${(props) => props?.theme.ColorsTypographyPrimary};
  }
`;

const Option = styled.option`
  background-color: ${(props) => props?.theme.ColorsBackgroundShell};
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
`;

const PlaceholderOption = styled.option`
  background-color: ${(props) => props?.theme.ColorsTypographyDisabled};
  color: ${(props) => props?.theme.ColorsBackgroundShell};
`;

const ButtonWrapper = styled.div`
  display: grid;
  grid-auto-flow: column;
  gap: 5px;
  align-items: center;
`;

const AddButton = styled.button`
  width: 25px;
  height: 25px;
  border: 2px solid ${(props) => props?.theme.ColorsTypographyPrimary};
  border-radius: 50%;
  cursor: pointer;
  background-color: ${(props) => props?.theme.ColorsBackgroundShell};
  color: ${(props) => props?.theme.ColorsInputsInverse};
  font-size: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 10px;
`;

const RemoveButton = styled.button`
  width: 25px;
  height: 25px;
  border: 2px solid ${(props) => props?.theme.ColorsTypographyPrimary};
  border-radius: 50%;
  cursor: pointer;
  background-color: ${(props) => props?.theme.ColorsBackgroundShell};
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
  font-size: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 10px;
`;

const HorizontalLine = styled.hr`
  width: 100%;
  border: none;
  height: 1px;
  background: linear-gradient(to right, rgba(248, 248, 248, 0) 0%, rgba(248, 248, 248, 1) 50%, rgba(248, 248, 248, 0) 100%);
  margin: 20px 0;
`;

/**
 * FormArrayBuilder is a component that renders a form with dynamic dropdowns.
 * The form has three dropdowns per row, one for skill group, one for skill, and one for provider type.
 * The user can add more rows by clicking the '+' button.
 * The user can remove rows by clicking the '-' button.
 * The component uses the `useState` hook to keep track of the state of the form.
 * The component uses the `useCallback` hook to memoize the functions that handle the changes of the dropdowns.
 * The component uses the `useMemo` hook to memoize the options of the dropdowns.
 * The component uses the `useEffect` hook to update the state of the parent component when the state of the form changes.
 * The component takes in the following props:
 * - `skillGroupOptions`: An array of strings that contains the options for the skill group dropdown.
 * - `skillOptions`: An array of strings that contains the options for the skill dropdown.
 * - `providerOptions`: An array of strings that contains the options for the provider type dropdown.
 * - `onChange`: A function that is called when the state of the form changes.
 * The component returns a JSX element that renders the form.
 */
export const FormArrayBuilder: React.FC<FormArrayBuilderProps> = ({ skillGroupOptions, skillOptions, providerOptions, onChange }) => {
  const [selectedSkillGroups, setSelectedSkillGroups] = useState<string[]>(['']);
  const [selectedSkills, setSelectedSkills] = useState<string[]>(['']);
  const [selectedProviders, setSelectedProviders] = useState<string[]>(['']);

  /**
   * Handles the change event of the skill group dropdown for a given row index.
   * Updates the state of the form by replacing the value of the selected skill group at the given index
   * with the new value from the change event.
   * Calls the `onChange` function with the updated state of the form.
   * @param {number} index The index of the row that the change event occurred in.
   * @returns {(e: React.ChangeEvent<HTMLSelectElement>) => void} A function that takes the change event and updates the state of the form accordingly.
   */
  const handleSkillGroupChange = (index: number) => (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSelectedSkillGroups = [...selectedSkillGroups];
    newSelectedSkillGroups[index] = e.target.value;
    setSelectedSkillGroups(newSelectedSkillGroups);
    onChange({ skillGroup: newSelectedSkillGroups, skill: selectedSkills, provider: selectedProviders });
  };

  /**
   * Handles the change event of the skill dropdown for a given row index.
   * Updates the state of the form by replacing the value of the selected skill at the given index
   * with the new value from the change event.
   * Calls the `onChange` function with the updated state of the form.
   * @param {number} index The index of the row that the change event occurred in.
   * @returns {(e: React.ChangeEvent<HTMLSelectElement>) => void} A function that takes the change event and updates the state of the form accordingly.
   */
  const handleSkillChange = (index: number) => (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSelectedSkills = [...selectedSkills];
    newSelectedSkills[index] = e.target.value;
    setSelectedSkills(newSelectedSkills);
    onChange({ skillGroup: selectedSkillGroups, skill: newSelectedSkills, provider: selectedProviders });
  };

  /**
   * Handles the change event of the provider type dropdown for a given row index.
   * Updates the state of the form by replacing the value of the selected provider type at the given index
   * with the new value from the change event.
   * Calls the `onChange` function with the updated state of the form.
   * @param {number} index The index of the row that the change event occurred in.
   * @returns {(e: React.ChangeEvent<HTMLSelectElement>) => void} A function that takes the change event and updates the state of the form accordingly.
   */
  const handleProviderChange = (index: number) => (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSelectedProviderTypes = [...selectedProviders];
    newSelectedProviderTypes[index] = e.target.value;
    setSelectedProviders(newSelectedProviderTypes);
    onChange({ skillGroup: selectedSkillGroups, skill: selectedSkills, provider: newSelectedProviderTypes });
  };

  /**
   * Adds a new dropdown set to the form state.
   * Called when the user clicks the '+' button.
   * Updates the state of the form by adding an empty dropdown set to the end of the form.
   * Does not call the `onChange` function.
   */
  const addDropdownSet = () => {
    setSelectedSkillGroups([...selectedSkillGroups, '']);
    setSelectedSkills([...selectedSkills, '']);
    setSelectedProviders([...selectedProviders, '']);
  };

  /**
   * Removes a dropdown set from the form state.
   * Called when the user clicks the '-' button.
   * Updates the state of the form by removing the dropdown set at the given index.
   * Calls the `onChange` function with the updated state of the form.
   * @param {number} index The index of the row that the '-' button was clicked in.
   */
  const removeDropdownSet = (index: number) => {
    const newSelectedSkillGroups = [...selectedSkillGroups];
    const newSelectedSkills = [...selectedSkills];
    const newSelectedProviders = [...selectedProviders];
    newSelectedSkillGroups.splice(index, 1);
    newSelectedSkills.splice(index, 1);
    newSelectedProviders.splice(index, 1);
    setSelectedSkillGroups(newSelectedSkillGroups);
    setSelectedSkills(newSelectedSkills);
    setSelectedProviders(newSelectedProviders);
    onChange({ skillGroup: newSelectedSkillGroups, skill: newSelectedSkills, provider: newSelectedProviders });
  };

  return (
    <Container>
      <Heading type='page-heading'>Create a job card</Heading>
      {selectedSkillGroups.map((_, index) => (
        <div key={index}>
          <DropdownContainer>
            <div>
              <Dropdown value={selectedSkillGroups[index]} onChange={handleSkillGroupChange(index)} required>
                <PlaceholderOption value="" disabled hidden>Skill group</PlaceholderOption>
                {skillGroupOptions.map((option, optionIndex) => (
                  <Option key={optionIndex} value={option}>{option}</Option>
                ))}
              </Dropdown>

              <Dropdown value={selectedSkills[index]} onChange={handleSkillChange(index)} required>
                <PlaceholderOption value="" disabled hidden>Skill</PlaceholderOption>
                {skillOptions.map((option, optionIndex) => (
                  <Option key={optionIndex} value={option}>{option}</Option>
                ))}
              </Dropdown>

              <Dropdown value={selectedProviders[index]} onChange={handleProviderChange(index)} required>
                <PlaceholderOption value="" disabled hidden>Provider type</PlaceholderOption>
                {providerOptions.map((option, optionIndex) => (
                  <Option key={optionIndex} value={option}>{option}</Option>
                ))}
              </Dropdown>
            </div>
            <ButtonWrapper>
              {index === selectedSkillGroups.length - 1 ? (
                <>
                  <AddButton onClick={addDropdownSet}>+</AddButton>
                  {index !== 0 && <RemoveButton onClick={() => removeDropdownSet(index)}>-</RemoveButton>}
                </>
              ) : (
                <RemoveButton onClick={() => removeDropdownSet(index)}>-</RemoveButton>
              )}
            </ButtonWrapper>
          </DropdownContainer>
          {index < selectedSkillGroups.length - 1 && <HorizontalLine />}
        </div>
      ))}
    </Container>
  );
};
