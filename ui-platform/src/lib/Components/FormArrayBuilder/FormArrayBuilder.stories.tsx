import type { Meta, StoryObj } from '@storybook/react';
import { FormArrayBuilder } from './FormArrayBuilder';

const meta: Meta<typeof FormArrayBuilder> = {
  component: FormArrayBuilder,
  title: 'Components/FormArrayBuilder',
};

export default meta;
type Story = StoryObj<typeof FormArrayBuilder>;

export const Overview: Story = {
  args: {
    skillGroupOptions: ['Steelworks', 'Assessor', 'Roof contractor', 'Floor contractor', 'Electronics', 'Builder', 'Plumber'],
    skillOptions: ['Welding', 'Inspection', 'Roofing', 'Flooring', 'Circuitry', 'Construction', 'Plumbing'],
    providerOptions: ['Provider1', 'Provider2', 'Provider3', 'Provider4', 'Provider5'],
    onChange: (selectedOptions: { skillGroup: string[], skill: string[], provider: string[] }) => {
      console.log('Selected options:', selectedOptions);
    },
  },
};
