import { MouseEvent } from 'react';
import styled from 'styled-components';
import { AccordionItemProps } from '../../Models';

/**
 * Styled details component that serves as the container for an accordion item.
 * Implements custom styling for the accordion item's expanded and collapsed states,
 * including cursor styling and chevron rotation animation.
 */
const Wrapper = styled(({ children, ...rest }) => (
  <details {...rest}>{children}</details>
))`
  && {
    cursor: pointer;
    border-radius: 0.5rem;
    margin-bottom: 2rem;

    &[open] {
      summary {
        border-color: ${(props) => props.theme.ColorsButtonColorToolbarButtonsActivated};
        svg {
          transform: rotate(180deg) translateY(50%);
        }
      }
    }

  }
`;

/**
 * A component representing an individual item within an Accordion.
 * This component uses the HTML5 details/summary elements for native accessibility support
 * and implements custom styling and behavior for the accordion pattern.
 * 
 * @component
 * @param {Object} props - The component props
 * @param {boolean} [props.isOpen=false] - Controls whether the accordion item is expanded
 * @param {string} [props.key] - Unique identifier for the accordion item
 * @param {(e: MouseEvent<HTMLElement>) => void} [props.onClick] - Callback function triggered when the item is clicked
 * @param {string} [props.layout] - Layout variant for the accordion item
 * @param {string} [props.className] - Additional CSS classes to apply
 * @param {ReactNode} props.children - The content of the accordion item (should include AccordionHeading and AccordionContent)
 * 
 * @example
 * ```tsx
 * <AccordionItem isOpen={true} onClick={handleClick}>
 *   <AccordionHeading>Section Title</AccordionHeading>
 *   <AccordionContent>Section Content</AccordionContent>
 * </AccordionItem>
 * ```
 */
export const AccordionItem = ({
  isOpen = false,
  key = Date.now().toString(),
  onClick,
  layout,
  className,
  children,
  ...rest
}: AccordionItemProps) => {
  /**
   * Handles click events on the accordion item.
   * Calls the provided onClick handler if it exists.
   * 
   * @param {MouseEvent<HTMLElement>} e - The click event object
   */
  const onAccordionClick = (e: MouseEvent<HTMLElement>) => {
    onClick && onClick(e);
  };

  return (
    <Wrapper key={key} open={isOpen} onClick={onAccordionClick} style={layout} {...rest}>
      {children[0]}
      {children[1]}
    </Wrapper>
  );
};
