import { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { AccordionContent, AccordionHeading, AccordionItem } from '../';

const meta: Meta<typeof AccordionItem> = {
  title: 'Components/Accordion/AccordionItem',
  component: AccordionItem,
  parameters: {
    componentSubtitle: 'An individual expandable section within an accordion interface',
    layout: 'padded',
    docs: {
      description: {
        component: `
<div class="documentation-wrapper" style="max-width: 1200px; margin: 0 auto; padding: 20px; font-family: system-ui, -apple-system, sans-serif;">

<div style="background: linear-gradient(90deg, #2C5282, #2B6CB0); padding: 2px; border-radius: 8px; margin-bottom: 30px;">
  <div style="background: white; padding: 20px; border-radius: 6px;">
    <h1 style="color: #2C5282; margin-top: 0;">AccordionItem Component</h1>
    <p style="color: #4A5568; font-size: 1.1em; line-height: 1.5;">
      A container component that manages the state and behavior of an individual expandable section within an accordion interface, coordinating between heading and content components.
    </p>
  </div>
</div>

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
  <div style="background: #EDF2F7; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">🎯 Key Features</h3>
    <ul style="list-style-type: none; padding: 0; margin: 0;">
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Independent State Management
      </li>
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Compound Component Pattern
      </li>
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ WAI-ARIA Compliant
      </li>
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Controlled/Uncontrolled Modes
      </li>
    </ul>
  </div>
  
  <div style="background: #EDF2F7; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">⚡ Component Structure</h3>
    <ul style="list-style-type: none; padding: 0; margin: 0;">
      <li style="margin-bottom: 8px;">🔷 AccordionHeading (Required)</li>
      <li style="margin-bottom: 8px;">🔷 AccordionContent (Required)</li>
      <li style="margin-bottom: 8px;">🔷 Context Provider</li>
      <li style="margin-bottom: 8px;">🔷 State Controller</li>
    </ul>
  </div>
</div>

<div style="background: #F7FAFC; padding: 24px; border-radius: 8px; margin-bottom: 30px;">
  <h2 style="color: #2C5282; margin-top: 0;">Integration Examples</h2>
  
  <div style="background: #2C5282; padding: 2px; border-radius: 6px; margin: 20px 0;">
    <div style="background: white; padding: 20px; border-radius: 4px;">
      <h3 style="color: #2C5282; margin-top: 0;">Basic Usage</h3>
      
\`\`\`tsx
import { AccordionItem, AccordionHeading, AccordionContent } from '@4-sure/ui-platform';

function MyComponent() {
  return (
    <AccordionItem>
      <AccordionHeading>Section Title</AccordionHeading>
      <AccordionContent>
        Content for this section...
      </AccordionContent>
    </AccordionItem>
  );
}
\`\`\`
    </div>
  </div>

  <div style="background: #2C5282; padding: 2px; border-radius: 6px; margin: 20px 0;">
    <div style="background: white; padding: 20px; border-radius: 4px;">
      <h3 style="color: #2C5282; margin-top: 0;">Controlled Mode</h3>
      
\`\`\`tsx
function ControlledAccordionItem() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <AccordionItem 
      isOpen={isOpen}
      onChange={(newState) => setIsOpen(newState)}
    >
      <AccordionHeading>Controlled Section</AccordionHeading>
      <AccordionContent>
        Content with controlled state
      </AccordionContent>
    </AccordionItem>
  );
}
\`\`\`
    </div>
  </div>
</div>

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
  <div style="background: #F7FAFC; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">🛠️ Props Reference</h3>
    
    <h4 style="color: #4A5568;">Core Props</h4>
    <ul style="color: #4A5568;">
      <li><strong>isOpen</strong>: boolean - Controls expanded state</li>
      <li><strong>onChange</strong>: (isOpen: boolean) => void</li>
      <li><strong>disabled</strong>: boolean - Disables interactions</li>
      <li><strong>children</strong>: ReactNode - Component children</li>
      <li><strong>className</strong>: string - CSS class name</li>
    </ul>
  </div>
  
  <div style="background: #F7FAFC; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">🎨 Styling</h3>
    <ul style="color: #4A5568;">
      <li>Theme-aware styling</li>
      <li>Custom CSS support</li>
      <li>Inline styles</li>
      <li>Transition animations</li>
    </ul>
  </div>
</div>

<div style="background: #F7FAFC; padding: 24px; border-radius: 8px;">
  <h2 style="color: #2C5282; margin-top: 0;">Technical Details</h2>
  
  <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
    <div>
      <h3 style="color: #2C5282;">💪 Performance</h3>
      <ul style="color: #4A5568;">
        <li>React.memo optimization</li>
        <li>Lazy content loading</li>
        <li>Efficient state updates</li>
      </ul>
    </div>
    
    <div>
      <h3 style="color: #2C5282;">♿ Accessibility</h3>
      <ul style="color: #4A5568;">
        <li>WAI-ARIA accordion pattern</li>
        <li>Keyboard interactions</li>
        <li>Focus management</li>
      </ul>
    </div>
    
    <div>
      <h3 style="color: #2C5282;">🌐 Best Practices</h3>
      <ul style="color: #4A5568;">
        <li>Include required children</li>
        <li>Handle state appropriately</li>
        <li>Maintain consistent styling</li>
      </ul>
    </div>
  </div>
</div>

</div>
        `
      },
      canvas: {
        sourceState: 'shown',
        layout: 'padded'
      },
      story: {
        inline: true,
        height: '300px'
      }
    }
  }
};

type Story = StoryObj<typeof AccordionItem>;

export const Overview: Story = {
  args: {
    children: [
      <AccordionHeading>Heading</AccordionHeading>,
      <AccordionContent>Content</AccordionContent>,
    ],
  }
};

export const BasicUsage: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <AccordionItem>
        <AccordionHeading>Section 1</AccordionHeading>
        <AccordionContent>
          This is the content for section 1. The AccordionItem component manages the state
          and behavior of this expandable section.
        </AccordionContent>
      </AccordionItem>
      <AccordionItem>
        <AccordionHeading>Section 2</AccordionHeading>
        <AccordionContent>
          This is the content for section 2. Each AccordionItem operates independently
          when not wrapped in an Accordion component.
        </AccordionContent>
      </AccordionItem>
    </div>
  )
};

export const DisabledState: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <AccordionItem disabled>
        <AccordionHeading>Disabled Section</AccordionHeading>
        <AccordionContent>
          This section is disabled and cannot be interacted with.
        </AccordionContent>
      </AccordionItem>
      <AccordionItem>
        <AccordionHeading>Active Section</AccordionHeading>
        <AccordionContent>
          This section remains interactive for comparison.
        </AccordionContent>
      </AccordionItem>
    </div>
  )
};

export default meta;
