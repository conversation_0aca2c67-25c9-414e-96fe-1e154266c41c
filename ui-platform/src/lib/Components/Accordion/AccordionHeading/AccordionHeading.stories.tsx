import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import React from 'react';
import { AccordionHeading } from './AccordionHeading';

const meta: Meta<typeof AccordionHeading> = {
  title: 'Components/Accordion/AccordionHeading',
  component: AccordionHeading,
  tags: ['autodocs'],
  parameters: {
    componentSubtitle: 'Interactive header component for accordion sections with animated chevron',
    layout: 'padded',
    docs: {
      description: {
        component: `
<div class="documentation-wrapper" style="max-width: 1200px; margin: 0 auto; padding: 20px; font-family: system-ui, -apple-system, sans-serif;">

<div style="background: linear-gradient(90deg, #2C5282, #2B6CB0); padding: 2px; border-radius: 8px; margin-bottom: 30px;">
  <div style="background: white; padding: 20px; border-radius: 6px;">
    <h1 style="color: #2C5282; margin-top: 0;">AccordionHeading Component</h1>
    <p style="color: #4A5568; font-size: 1.1em; line-height: 1.5;">
      A customizable heading component that serves as the interactive trigger for accordion sections, featuring an animated chevron indicator and theme-aware styling.
    </p>
  </div>
</div>

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
  <div style="background: #EDF2F7; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">🎯 Key Features</h3>
    <ul style="list-style-type: none; padding: 0; margin: 0;">
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Animated Chevron Icon
      </li>
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Theme Integration
      </li>
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Interactive States
      </li>
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Semantic HTML Structure
      </li>
    </ul>
  </div>
  
  <div style="background: #EDF2F7; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">⚡ Visual Specs</h3>
    <ul style="list-style-type: none; padding: 0; margin: 0;">
      <li style="margin-bottom: 8px;">🎨 Themed Borders</li>
      <li style="margin-bottom: 8px;">↕️ 0.75rem Padding</li>
      <li style="margin-bottom: 8px;">📏 5px Border Radius</li>
      <li style="margin-bottom: 8px;">➡️ 24px Chevron Icon</li>
    </ul>
  </div>
</div>

<div style="background: #F7FAFC; padding: 24px; border-radius: 8px; margin-bottom: 30px;">
  <h2 style="color: #2C5282; margin-top: 0;">Implementation Guide</h2>
  
  <div style="background: #2C5282; padding: 2px; border-radius: 6px; margin: 20px 0;">
    <div style="background: white; padding: 20px; border-radius: 4px;">
      <h3 style="color: #2C5282; margin-top: 0;">Basic Usage</h3>
      
\`\`\`tsx
import { AccordionHeading } from '@4-sure/ui-platform';

function MyComponent() {
  return (
    <AccordionHeading>
      Section Title
    </AccordionHeading>
  );
}
\`\`\`
    </div>
  </div>

  <div style="background: #2C5282; padding: 2px; border-radius: 6px; margin: 20px 0;">
    <div style="background: white; padding: 20px; border-radius: 4px;">
      <h3 style="color: #2C5282; margin-top: 0;">With Custom Content</h3>
      
\`\`\`tsx
function CustomHeading() {
  return (
    <AccordionHeading>
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
        <span>🔧</span>
        <span>Configuration Settings</span>
      </div>
    </AccordionHeading>
  );
}
\`\`\`
    </div>
  </div>
</div>

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
  <div style="background: #F7FAFC; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">🛠️ Props Reference</h3>
    <ul style="color: #4A5568;">
      <li><strong>children</strong>: ReactNode
        <div style="font-size: 0.9em; color: #718096; margin-left: 1em;">Content to be displayed in the heading</div>
      </li>
      <li><strong>className</strong>: string
        <div style="font-size: 0.9em; color: #718096; margin-left: 1em;">Additional CSS classes</div>
      </li>
      <li><strong>style</strong>: CSSProperties
        <div style="font-size: 0.9em; color: #718096; margin-left: 1em;">Inline styles for custom styling</div>
      </li>
    </ul>
  </div>
  
  <div style="background: #F7FAFC; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">🎨 Theme Integration</h3>
    <ul style="color: #4A5568;">
      <li><strong>Border Color</strong>: ColorsInputsInverse</li>
      <li><strong>Background</strong>: ColorsButtonColorModuleActionsPrimary</li>
      <li><strong>Font Weight</strong>: FontWeightsInter1</li>
      <li><strong>Hover Border</strong>: ColorsButtonColorToolbarButtonsActivated</li>
    </ul>
  </div>
</div>

<div style="background: #F7FAFC; padding: 24px; border-radius: 8px;">
  <h2 style="color: #2C5282; margin-top: 0;">Technical Details</h2>
  
  <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
    <div>
      <h3 style="color: #2C5282;">💻 Structure</h3>
      <ul style="color: #4A5568;">
        <li>Uses HTML5 summary tag</li>
        <li>Styled-components based</li>
        <li>Theme-aware styling</li>
      </ul>
    </div>
    
    <div>
      <h3 style="color: #2C5282;">🔄 Interactions</h3>
      <ul style="color: #4A5568;">
        <li>Cursor pointer on hover</li>
        <li>Animated chevron rotation</li>
        <li>Border color transitions</li>
      </ul>
    </div>
    
    <div>
      <h3 style="color: #2C5282;">⚙️ Best Practices</h3>
      <ul style="color: #4A5568;">
        <li>Keep headings concise</li>
        <li>Use meaningful labels</li>
        <li>Consider mobile touch targets</li>
      </ul>
    </div>
  </div>
</div>

</div>
        `
      },
      canvas: {
        sourceState: 'shown',
        layout: 'padded'
      },
      story: {
        inline: true,
        height: '300px'
      }
    }
  }
};

export default meta;

type Story = StoryObj<typeof AccordionHeading>;

export const Overview: Story = {
  args: {
    children: 'Accordion Section Title'
  }
};

export const WithCustomContent: Story = {
  args: {
    children: (
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
        <span>🔧</span>
        <span>Configuration Settings</span>
      </div>
    )
  }
};
