import styled from 'styled-components';
import { IAccordionHeading } from '../../Models';
import { Icon } from '../../Icons/Icon';

/**
 * Styled Icon component specifically designed for the accordion heading.
 * Positions the chevron icon absolutely within the heading and handles
 * the rotation animation when the accordion item is expanded/collapsed.
 */
const StyledIcon = styled(Icon)`
  position: absolute;
  top: 50%;
  right: 1rem;
  height: 20px;
  width: 20px;
  transform: translateY(-50%);
  transition: transform 0.2s;
`;

/**
 * Styled summary component that serves as the clickable heading area of an accordion item.
 * Implements the visual styling including borders, background color, and hover states.
 * 
 * @param {IAccordionHeading} props - The component props
 * @param {ReactNode} props.children - The content to be displayed in the heading
 */
const Comp = styled(({ children, ...rest }: IAccordionHeading) => (
  <summary {...rest}>
    <StyledIcon type='chevron-down' size={24}></StyledIcon>
    {children}
  </summary>
))`
  position: relative;
  padding: 0.75rem;
  font-weight: ${(props) => props.theme.FontWeightsInter1};
  cursor: pointer;
  border-radius: 5px;
  border-top: 1px solid ${(props) => props.theme.ColorsInputsInverse};
  border-bottom: 1px solid ${(props) => props.theme.ColorsInputsInverse};
  border-left: 1px solid ${(props) => props.theme.ColorsInputsInverse};
  border-right: 1px solid ${(props) => props.theme.ColorsInputsInverse};
  background-color: ${(props) => props.theme.ColorsButtonColorModuleActionsPrimary};
  list-style: none;
  &:hover {
    border-color: ${(props) => props.theme.ColorsButtonColorToolbarButtonsActivated};
  }
`;

/**
 * A component that renders the heading section of an accordion item.
 * Includes a chevron icon that rotates to indicate the expanded/collapsed state
 * and provides interactive styling for user engagement.
 * 
 * @component
 * @param {Object} props - The component props
 * @param {ReactNode} props.children - The content to be displayed in the heading
 * 
 * @example
 * ```tsx
 * <AccordionHeading>
 *   Section Title
 * </AccordionHeading>
 * ```
 */
export const AccordionHeading = ({ children }: IAccordionHeading) => (
  <Comp>{children}</Comp>
);
