import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import React from 'react';
import { AccordionContent } from './AccordionContent';

const meta: Meta<typeof AccordionContent> = {
  title: 'Components/Accordion/AccordionContent',
  component: AccordionContent,
  tags: ['autodocs'],
  parameters: {
    componentSubtitle: 'A flexible content container for accordion sections',
    layout: 'padded',
    docs: {
      description: {
        component: `
<div class="documentation-wrapper" style="max-width: 1200px; margin: 0 auto; padding: 20px; font-family: system-ui, -apple-system, sans-serif;">

<div style="background: linear-gradient(90deg, #2C5282, #2B6CB0); padding: 2px; border-radius: 8px; margin-bottom: 30px;">
  <div style="background: white; padding: 20px; border-radius: 6px;">
    <h1 style="color: #2C5282; margin-top: 0;">AccordionContent Component</h1>
    <p style="color: #4A5568; font-size: 1.1em; line-height: 1.5;">
      A specialized container component that manages content within accordion sections, providing consistent layout and styling for expandable/collapsible content.
    </p>
  </div>
</div>

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
  <div style="background: #EDF2F7; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">🎯 Key Features</h3>
    <ul style="list-style-type: none; padding: 0; margin: 0;">
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Grid Layout System
      </li>
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Automatic Spacing
      </li>
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Background Customization
      </li>
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Styled-components Integration
      </li>
    </ul>
  </div>
  
  <div style="background: #EDF2F7; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">⚡ Technical Specs</h3>
    <ul style="list-style-type: none; padding: 0; margin: 0;">
      <li style="margin-bottom: 8px;">📐 Auto Grid Rows</li>
      <li style="margin-bottom: 8px;">↕️ 0.75rem Gap</li>
      <li style="margin-bottom: 8px;">🎨 Custom Background</li>
      <li style="margin-bottom: 8px;">🔄 Flexible Content</li>
    </ul>
  </div>
</div>

<div style="background: #F7FAFC; padding: 24px; border-radius: 8px; margin-bottom: 30px;">
  <h2 style="color: #2C5282; margin-top: 0;">Implementation Guide</h2>
  
  <div style="background: #2C5282; padding: 2px; border-radius: 6px; margin: 20px 0;">
    <div style="background: white; padding: 20px; border-radius: 4px;">
      <h3 style="color: #2C5282; margin-top: 0;">Basic Usage</h3>
      
\`\`\`tsx
import { AccordionContent } from '@4-sure/ui-platform';

function MyComponent() {
  return (
    <AccordionContent>
      <div>First section content</div>
      <div>Second section content</div>
    </AccordionContent>
  );
}
\`\`\`
    </div>
  </div>

  <div style="background: #2C5282; padding: 2px; border-radius: 6px; margin: 20px 0;">
    <div style="background: white; padding: 20px; border-radius: 4px;">
      <h3 style="color: #2C5282; margin-top: 0;">With Custom Background</h3>
      
\`\`\`tsx
function CustomizedContent() {
  return (
    <AccordionContent background="#F7FAFC">
      <div>Content with custom styling</div>
      <div>Another content section</div>
    </AccordionContent>
  );
}
\`\`\`
    </div>
  </div>
</div>

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
  <div style="background: #F7FAFC; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">🛠️ Props Reference</h3>
    <ul style="color: #4A5568;">
      <li><strong>children</strong>: ReactNode
        <div style="font-size: 0.9em; color: #718096; margin-left: 1em;">Content to be rendered within the accordion section</div>
      </li>
      <li><strong>background</strong>: string
        <div style="font-size: 0.9em; color: #718096; margin-left: 1em;">Custom background color (optional)</div>
      </li>
      <li><strong>className</strong>: string
        <div style="font-size: 0.9em; color: #718096; margin-left: 1em;">Additional CSS classes</div>
      </li>
    </ul>
  </div>
  
  <div style="background: #F7FAFC; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">🎨 Styling Guide</h3>
    <ul style="color: #4A5568;">
      <li><strong>Grid Layout</strong>: Uses CSS Grid for content organization</li>
      <li><strong>Gap Spacing</strong>: 0.75rem between elements</li>
      <li><strong>Background</strong>: Transparent by default</li>
      <li><strong>Customization</strong>: Supports styled-components</li>
    </ul>
  </div>
</div>

<div style="background: #F7FAFC; padding: 24px; border-radius: 8px;">
  <h2 style="color: #2C5282; margin-top: 0;">Technical Details</h2>
  
  <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
    <div>
      <h3 style="color: #2C5282;">💻 Architecture</h3>
      <ul style="color: #4A5568;">
        <li>Styled-components based</li>
        <li>CSS Grid layout system</li>
        <li>Auto-sizing containers</li>
      </ul>
    </div>
    
    <div>
      <h3 style="color: #2C5282;">⚡ Performance</h3>
      <ul style="color: #4A5568;">
        <li>Optimized re-renders</li>
        <li>Efficient DOM structure</li>
        <li>Minimal overhead</li>
      </ul>
    </div>
    
    <div>
      <h3 style="color: #2C5282;">📝 Best Practices</h3>
      <ul style="color: #4A5568;">
        <li>Use within AccordionItem</li>
        <li>Keep content focused</li>
        <li>Consider mobile views</li>
      </ul>
    </div>
  </div>
</div>

</div>
        `
      },
      canvas: {
        sourceState: 'shown',
        layout: 'padded'
      },
      story: {
        inline: true,
        height: '300px'
      }
    }
  }
};

export default meta;

type Story = StoryObj<typeof AccordionContent>;

export const Overview: Story = {
  args: {
    children: (
      <div style={{ padding: '1rem' }}>
        <h3>Example Content</h3>
        <p>This is an example of content within an accordion section.</p>
      </div>
    )
  }
};

export const WithCustomBackground: Story = {
  args: {
    children: (
      <div style={{ padding: '1rem' }}>
        <h3>Custom Background Example</h3>
        <p>This content has a custom background color applied.</p>
      </div>
    ),
    background: '#F7FAFC'
  }
};
