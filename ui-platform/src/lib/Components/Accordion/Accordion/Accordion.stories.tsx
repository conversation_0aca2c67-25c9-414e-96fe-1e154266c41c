import { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { ComponentPropsWithoutRef, ReactElement } from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionHeading,
  AccordionItem,
} from '../';
import { withItems } from '../../List/List';
import { JobImageListItem } from '../../JobImage/JobImageListItem/JobImageListItem';
import { CSSProperties } from 'styled-components';

const meta: Meta<typeof Accordion> = {
  title: 'Components/Accordion/Accordion',
  component: Accordion,
  parameters: {
    componentSubtitle: 'A flexible and accessible accordion component for organizing content',
    layout: 'padded',
    docs: {
      description: {
        component: `
<div class="documentation-wrapper" style="max-width: 1200px; margin: 0 auto; padding: 20px; font-family: system-ui, -apple-system, sans-serif;">

<div style="background: linear-gradient(90deg, #2C5282, #2B6CB0); padding: 2px; border-radius: 8px; margin-bottom: 30px;">
  <div style="background: white; padding: 20px; border-radius: 6px;">
    <h1 style="color: #2C5282; margin-top: 0;">Accordion Component</h1>
    <p style="color: #4A5568; font-size: 1.1em; line-height: 1.5;">
      A powerful and flexible accordion component that helps organize content into collapsible sections, perfect for FAQs, navigation menus, and content organization.
    </p>
  </div>
</div>

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
  <div style="background: #EDF2F7; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">🎯 Key Features</h3>
    <ul style="list-style-type: none; padding: 0; margin: 0;">
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Multiple or Single Expansion
      </li>
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Customizable Styling
      </li>
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Keyboard Navigation
      </li>
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Nested Content Support
      </li>
    </ul>
  </div>
  
  <div style="background: #EDF2F7; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">⚡ Component Structure</h3>
    <ul style="list-style-type: none; padding: 0; margin: 0;">
      <li style="margin-bottom: 8px;">🔷 Accordion (Container)</li>
      <li style="margin-bottom: 8px;">🔷 AccordionItem</li>
      <li style="margin-bottom: 8px;">🔷 AccordionHeading</li>
      <li style="margin-bottom: 8px;">🔷 AccordionContent</li>
    </ul>
  </div>
</div>

<div style="background: #F7FAFC; padding: 24px; border-radius: 8px; margin-bottom: 30px;">
  <h2 style="color: #2C5282; margin-top: 0;">Integration Examples</h2>
  
  <div style="background: #2C5282; padding: 2px; border-radius: 6px; margin: 20px 0;">
    <div style="background: white; padding: 20px; border-radius: 4px;">
      <h3 style="color: #2C5282; margin-top: 0;">Basic Usage</h3>
      
\`\`\`tsx
import { Accordion, AccordionItem, AccordionHeading, AccordionContent } from '@4-sure/ui-platform';

function MyComponent() {
  return (
    <Accordion allowMultipleExpanded={true}>
      <AccordionItem>
        <AccordionHeading>Section 1</AccordionHeading>
        <AccordionContent>Content for section 1</AccordionContent>
      </AccordionItem>
      <AccordionItem>
        <AccordionHeading>Section 2</AccordionHeading>
        <AccordionContent>Content for section 2</AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
\`\`\`
    </div>
  </div>

  <div style="background: #2C5282; padding: 2px; border-radius: 6px; margin: 20px 0;">
    <div style="background: white; padding: 20px; border-radius: 4px;">
      <h3 style="color: #2C5282; margin-top: 0;">With Custom Styling</h3>
      
\`\`\`tsx
function CustomStyledAccordion() {
  return (
    <Accordion>
      <AccordionItem style={{ border: '1px solid #E2E8F0' }}>
        <AccordionHeading style={{ background: '#F7FAFC', padding: '1rem' }}>
          Custom Styled Section
        </AccordionHeading>
        <AccordionContent style={{ padding: '1rem' }}>
          Content with custom styling
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
\`\`\`
    </div>
  </div>
</div>

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
  <div style="background: #F7FAFC; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">🛠️ Props & Configuration</h3>
    
    <h4 style="color: #4A5568;">Accordion Props</h4>
    <ul style="color: #4A5568;">
      <li>allowMultipleExpanded (boolean)</li>
      <li>children (AccordionItem[])</li>
      <li>className (string)</li>
      <li>style (CSSProperties)</li>
    </ul>
    
    <h4 style="color: #4A5568;">AccordionItem Props</h4>
    <ul style="color: #4A5568;">
      <li>isOpen (boolean)</li>
      <li>onClick (function)</li>
      <li>children (node)</li>
    </ul>
  </div>
  
  <div style="background: #F7FAFC; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">🎨 Styling Options</h3>
    <ul style="color: #4A5568;">
      <li>Custom CSS via className</li>
      <li>Inline styles support</li>
      <li>Styled-components integration</li>
      <li>Theme-based styling</li>
    </ul>
  </div>
</div>

<div style="background: #F7FAFC; padding: 24px; border-radius: 8px; margin-bottom: 30px;">
  <h2 style="color: #2C5282; margin-top: 0;">Technical Details</h2>
  
  <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
    <div>
      <h3 style="color: #2C5282;">💪 Performance</h3>
      <ul style="color: #4A5568;">
        <li>Optimized re-renders</li>
        <li>Lazy content loading</li>
        <li>Efficient state management</li>
      </ul>
    </div>
    
    <div>
      <h3 style="color: #2C5282;">♿ Accessibility</h3>
      <ul style="color: #4A5568;">
        <li>ARIA attributes</li>
        <li>Keyboard navigation</li>
        <li>Screen reader support</li>
      </ul>
    </div>
    
    <div>
      <h3 style="color: #2C5282;">🌐 Best Practices</h3>
      <ul style="color: #4A5568;">
        <li>Semantic HTML structure</li>
        <li>Consistent behavior</li>
        <li>Responsive design</li>
      </ul>
    </div>
  </div>
</div>

</div>
        `
      },
      canvas: {
        sourceState: 'shown',
        layout: 'padded'
      },
      story: {
        inline: true,
        height: '300px'
      }
    }
  },
};

interface IItem {
  heading: string;
  content: ReactElement | string;
  isOpen?: boolean;
  onClick?: ComponentPropsWithoutRef<typeof AccordionItem>['onClick'];
  layout?: CSSProperties;
}

const items: IItem[] = [
  {
    heading: 'Photos or Documents',
    content: (
      <div>
        <JobImageListItem
          jobImageItems={[
            {
              src: 'https://picsum.photos/1336/1080',
              name: 'Decoder Serial Number',
              mediaType: 'image',
              TeamMemberName: 'TeamMemberName',
              date: '2024/08/05',
              time: '11:69',
            },
            {
              src: 'https://picsum.photos/1336/1080',
              name: 'Decoder Serial Number',
              mediaType: 'image',
              TeamMemberName: 'TeamMemberName',
              date: '2024/08/05',
              time: '11:69',
            },
            {
              src: 'https://picsum.photos/1336/1080',
              name: 'Decoder Serial Number',
              mediaType: 'icon',
              TeamMemberName: 'TeamMemberName',
              date: '2024/08/05',
              time: '11:69',
            },
            {
              src: 'https://picsum.photos/1336/1080',
              name: 'Decoder Serial Number',
              mediaType: 'image',
              TeamMemberName: 'TeamMemberName',
              date: '2024/08/05',
              time: '11:69',
            },
            {
              src: 'https://picsum.photos/1336/1080',
              name: 'Decoder Serial Number',
              mediaType: 'image',
              TeamMemberName: 'TeamMemberName',
              date: '2024/08/05',
              time: '11:69',
            },
            {
              src: 'https://picsum.photos/1336/1080',
              name: 'Decoder Serial Number',
              mediaType: 'icon',
              TeamMemberName: 'TeamMemberName',
              date: '2024/08/05',
              time: '11:69',
            },
            {
              src: 'https://picsum.photos/1336/1080',
              name: 'Decoder Serial Number',
              mediaType: 'image',
              TeamMemberName: 'TeamMemberName',
              date: '2024/08/05',
              time: '11:69',
            },
            {
              src: 'https://picsum.photos/1336/1080',
              name: 'Decoder Serial Number',
              mediaType: 'image',
              TeamMemberName: 'TeamMemberName',
              date: '2024/08/05',
              time: '11:69',
            },
            {
              src: 'https://picsum.photos/1336/1080',
              name: 'Decoder Serial Number',
              mediaType: 'icon',
              TeamMemberName: 'TeamMemberName',
              date: '2024/08/05',
              time: '11:69',
            },
          ]}
          usecase={'DocumentView'}
          mediaType={''}
        ></JobImageListItem>
      </div>
    ),
    isOpen: true,
    onClick: (e) => console.log(e),
  },
  {
    heading: 'Text and Stuff',
    content:
      '2 Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud 3 Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud',
    isOpen: false,
    onClick: (e) => console.log(e),
  },
  // {
  //   heading: 'Heading 3',
  //   content:
  //     '3 Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud ',
  //   onClick: (e) => console.log(e),
  // },
  // {
  //   heading: 'Heading 4',
  //   content:
  //     '4 Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud ',
  //   onClick: (e) => console.log(e),
  // },
];

const Item = ({ heading, content, isOpen, onClick, layout }: IItem) => (
  <AccordionItem
    isOpen={isOpen}
    onClick={onClick}
    layout={{ padding: '3px', ...layout }}
  >
    <AccordionHeading>{heading}</AccordionHeading>
    <AccordionContent>{content}</AccordionContent>
  </AccordionItem>
);

type Story = StoryObj<typeof Accordion>;

export const Overview: Story = {
  args: {
    children: withItems(Item)(items),
    allowMultipleExpanded: true,
  },
};

export default meta;
