import React, { MouseEvent, useState } from 'react';
import styled, { useTheme } from 'styled-components';
import { Icon } from '../../Icons';
import { Avatar } from '../Avatar/Avatar';

const Wrapper = styled.div`
  width: 100%;
  position: relative;
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  gap: 8px;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  min-width: 222px;
`;

const Toolbar = styled.div`
  display: grid;
  place-items: center;
  gap: 4px;
`;

const TextWrapper = styled.div`
  display: grid;
  gap: 4px;
`;

const UserName = styled.div`
  width: 140px;
  position: relative;
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter1};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  text-align: left;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const Content = styled.div`
  width: 140px;
  position: relative;
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter0};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  text-align: left;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const Options = styled(Icon)`
  cursor: pointer;
`;

/**
 * Interface for the ProfileHeader component props.
 *
 * @typedef {Object} ProfileHeaderProps
 * @property {string} email - The email address of the user.
 * @property {string} username - The username of the user.
 * @property {function} [optionsHandler] - Optional handler for when the options icon is clicked.
 * @property {function} [avatarHandler] - Optional handler for when the avatar is clicked.
 */
export interface ProfileHeaderProps {
  email: string;
  username: string;
  image?: string;
  optionsHandler?: (e?: MouseEvent<HTMLElement>) => void;
  avatarHandler?: (e?: MouseEvent<HTMLElement>) => void;
}

const ProfileImg = styled(Avatar)`
  position: relative;
`;

/**
 * ProfileHeader component displays user information (avatar, username, email) with optional handlers for avatar and options icon.
 *
 * @param {ProfileHeaderProps} props - Props passed to the component.
 * @returns {JSX.Element} A rendered ProfileHeader component.
 */
export const ProfileHeader = ({
  email = '<EMAIL>',
  username = 'User Name',
  image,
  optionsHandler,
  avatarHandler,
}: ProfileHeaderProps) => {
  const [stateImage, setImage] = useState<string | undefined>(image);
  const color = useTheme().ColorsIconColorPrimary;

  /**
   * Handles saving an image to the component state.
   * @param {File} file The uploaded file.
   */
  const handleImageSave = (file: File) => {
    const imageUrl = URL.createObjectURL(file);
    setImage(imageUrl);
  };
  return (
    <Wrapper>
      <div onClick={avatarHandler}>
        <ProfileImg
          size="medium"
          image={stateImage ?? image}
          onFileSave={handleImageSave}
        />
      </div>
      <TextWrapper>
        <UserName>{username}</UserName>
        <Content>{email}</Content>
      </TextWrapper>
      <Toolbar onClick={optionsHandler}>
        {optionsHandler ? <Options type="dots-vertical" color={color} /> : ''}
      </Toolbar>
    </Wrapper>
  );
};
