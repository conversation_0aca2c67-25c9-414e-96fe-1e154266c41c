import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import Slider from '@mui/material/Slider';
import React, {
  <PERSON><PERSON><PERSON>,
  MouseEventHandler,
  useCallback,
  useEffect,
  useState,
} from 'react';
import { useDropzone } from 'react-dropzone';
import Cropper, { Area } from 'react-easy-crop';
import styled, { useTheme } from 'styled-components';
import { TextButton } from '../../Buttons/TextButton/TextButton';
import { Icon } from '../../Icons';
import { useProfilePic } from '../../Menu/ProfileDropdown/useProfilePic';

/**
 * Props for the Avatar component.
 */
interface IProps {
  size: 'small' | 'medium' | 'large' | 'extraLarge' | 'extraSmall';
  active?: boolean;
  onClick?: MouseEventHandler<HTMLElement>;
  color?: string;
  disabled?: boolean;
  image?: string;
  buttonActive?: boolean;
  onFileSave?: (acceptedFile: File) => void;
  className?: string;
}

const sizeStyles = {
  small: {
    width: '26px',
    height: '26px',
    borderRadius: '50px',
    border: '1px solid #f8f8f8',
    background: {
      default: '#262728',
      hover: 'rgba(38, 39, 40, 0.75)',
      active: 'rgba(17, 138, 178, 0.75)',
    },
  },
  medium: {
    width: '32px',
    height: '32px',
    borderRadius: '50px',
    border: '1px solid #f8f8f8',
    background: {
      default: '#262728',
      hover: 'rgba(38, 39, 40, 0.75)',
      active: 'rgba(17, 138, 178, 0.75)',
    },
  },
  large: {
    width: '221px',
    height: '221px',
    borderRadius: '20px',
    border: '',
    background: {
      default: '#919191',
      hover: '#919191',
      active: '#919191',
    },
  },
  extraLarge: {
    width: '335px',
    height: '322px',
    borderRadius: '0 0 60px 8px',
    border: '1px solid #f8f8f8',
    background: {
      default: '#262728',
      hover: 'rgba(38, 39, 40, 0.75)',
      active: 'rgba(17, 138, 178, 0.75)',
    },
  },
  extraSmall: {
    width: '17px',
    height: '17px',
    borderRadius: '17px',
    border: '1px solid #f8f8f8',
    background: {
      default: '#262728',
      hover: 'rgba(38, 39, 40, 0.75)',
      active: 'rgba(17, 138, 178, 0.75)',
    },
  },
};

const Container = styled.div<IProps>`
  all: unset;
  width: ${({ size }) => sizeStyles[size].width};
  height: ${({ size }) => sizeStyles[size].height};
  padding: 4px;
  border: ${({ size }) => sizeStyles[size].border};
  border-radius: ${({ size }) => sizeStyles[size].borderRadius};
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  gap: 10px;
  cursor: pointer;

  &:default {
    background: ${(props) => sizeStyles[props.size].background.default};
  }

  &:hover {
    background: ${(props) =>
      props.disabled ? 'none' : sizeStyles[props.size].background.hover};
  }

  && {
    ${(props) =>
      props?.active && {
        background: sizeStyles[props.size].background.active,
      }}
  }
`;

const UploadButton = styled.div`
  position: absolute;
  bottom: -15px;
  left: -20px;
  display: inline-flex;
  align-items: center;
  padding: 5px;
  border-radius: 100px;
  border: 1px solid #f8f8f8;
  background-color: #e5e5e5;
  color: #262728;
  cursor: pointer;

  &:hover {
    background-color: #e5e5e5;
  }
`;

const UploadedImage = styled.img`
  width: 90%;
  height: 90%;
  border-radius: 50%;
  object-fit: cover;
  position: absolute;
`;

const CropContainer = styled.div`
  position: absolute;
  width: 90%;
  height: 90%;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
`;

const CropControls = styled.div`
  position: absolute;
  bottom: 0;
  width: 80%;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
`;

const iconSize = {
  small: 24,
  medium: 30,
  large: 221,
  extraLarge: 299,
  extraSmall: 20,
};

const iconStrokeWidth = {
  small: '2',
  medium: '2',
  large: '1.5',
  extraLarge: '0.2',
  extraSmall: '1',
};

const getCroppedImg = async (
  imageSrc: string,
  cropArea: Area
): Promise<File> => {
  const createImage = (url: string): Promise<HTMLImageElement> =>
    new Promise((resolve, reject) => {
      const image = new Image();
      image.addEventListener('load', () => resolve(image));
      image.addEventListener('error', (error) => reject(error));
      image.crossOrigin = 'anonymous';
      image.src = url;
    });

  const croppedImage = await createImage(imageSrc);
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');

  if (!context) {
    throw new Error('Failed to get canvas context');
  }

  canvas.width = cropArea.width;
  canvas.height = cropArea.height;

  context.drawImage(
    croppedImage,
    cropArea.x,
    cropArea.y,
    cropArea.width,
    cropArea.height,
    0,
    0,
    cropArea.width,
    cropArea.height
  );

  return new Promise<File>((resolve, reject) => {
    canvas.toBlob((blob) => {
      if (!blob) {
        reject(new Error('Failed to create blob'));
        return;
      }
      const file = new File([blob], 'image.jpg', { type: 'image/jpeg' });
      resolve(file);
    }, 'image/jpeg');
  });
};

/**
 * Renders an avatar component.
 * @component
 * @example
 * // Example usage:
 * <Avatar size="small" onClick={handleClick} active={true} disabled={false} color="#FF0000" />
 * @param {AvatarProps} props - The props for the Avatar component.
 * @returns {JSX.Element} The rendered Avatar component.
 */

export const Avatar = ({
  size = 'extraLarge',
  onClick,
  active,
  disabled,
  color,
  buttonActive = true,
  image,
  onFileSave,
  className,
}: IProps): JSX.Element => {
  const tokensTheme = useTheme();

  const iconColor =
    size === 'large'
      ? tokensTheme.ColorsIconColorPrimary
      : tokensTheme.ColorsIconColorTertiary;

  const [uploadedImage, setUploadedImage] = useState<any>();
  const [croppedImage, setCroppedImage] = useState<any>(null);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [cropAreaPixels, setCropAreaPixels] = useState<Area | null>(null);
  const [isCropping, setIsCropping] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      console.log({ file });
      setUploadedImage(URL.createObjectURL(file));
      setIsCropping(true);
    }
  }, []);

  const { profilePic } = useProfilePic({ image });

  useEffect(() => {
    if (profilePic) {
      setUploadedImage(profilePic);
    }
  }, [profilePic]);

  // TODO: Verify from Rene and the team if I can remove this:
  useEffect(() => {
    return () => {
      if (uploadedImage) {
        URL.revokeObjectURL(uploadedImage);
      }
    };
  }, [uploadedImage]);

  const { getRootProps, getInputProps, open } = useDropzone({
    onDrop,
    accept: { 'image/jpeg': ['.jpeg', '.jpg'], 'image/png': ['.png'] },
    noClick: true,
    noKeyboard: true,
  });

  const onCropComplete = useCallback(async () => {
    if (cropAreaPixels && uploadedImage) {
      const croppedImg = await getCroppedImg(uploadedImage, cropAreaPixels);
      setCroppedImage(croppedImg);
      if (onFileSave) {
        onFileSave(croppedImg);
      }
      setIsCropping(false);
    }
  }, [cropAreaPixels, uploadedImage]);

  const isBase64Image = (str: string) => {
    console.log({ str });
    return str ? str?.startsWith('data:image') : false;
  };

  return (
    <Container
      size={size}
      onClick={onClick}
      {...{ active, disabled, className }}
    >
      {uploadedImage && (
        <UploadedImage
          src={isBase64Image(uploadedImage) ? uploadedImage : uploadedImage}
          alt="Avatar"
        />
      )}
      {croppedImage ? (
        <UploadedImage src={croppedImage} alt="Avatar" />
      ) : (
        <Icon
          size={iconSize[size]}
          strokeWidth={iconStrokeWidth[size]}
          type="user-circle"
          color={color ?? iconColor}
        />
      )}

      {size === 'large' && (
        <div {...getRootProps()}>
          <input {...getInputProps()} />
          {buttonActive === true && isCropping === false && (
            <UploadButton onClick={open}>
              <EditOutlinedIcon fontSize="small" />
              UPLOAD PHOTO
            </UploadButton>
          )}
        </div>
      )}

      {isCropping && (
        <CropContainer>
          <Cropper
            image={uploadedImage || ''}
            crop={crop}
            zoom={zoom}
            aspect={1}
            onCropChange={setCrop}
            onZoomChange={setZoom}
            onCropComplete={(croppedArea, croppedAreaPixels) => {
              setCropAreaPixels(croppedAreaPixels);
            }}
            minZoom={0.5}
          />
          <CropControls>
            <TextButton btnValue="Save" onClick={onCropComplete} />
            <Slider
              value={zoom}
              min={0.5}
              max={3}
              step={0.1}
              aria-labelledby="Zoom"
              onChange={(e, zoom) => setZoom(zoom as number)}
            />
          </CropControls>
        </CropContainer>
      )}
    </Container>
  );
};
