// external module imports
import * as React from 'react';
import styled from 'styled-components';

// internal module imports
import { Icon, IconProps } from '../Icons';

export interface InteractionBlockerProps {
  disableCondition: boolean;
  style?: React.CSSProperties;
  label?: string;
  labelSize?: string;
  icon?: IconProps;
}

const BlockerIcon = styled.span<
  { labelSize?: string } & React.ComponentProps<'span'>
>`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  gap: 8px;
`;

const Blocker = styled.div`
  position: absolute;
  display: flex;
  align-items: start;
  justify-content: end;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: ${(props) => props.theme.ColorsBackgroundModule};
  width: 100%;
  height: 100%;
  z-index: 1;
  filter: opacity(0.45);
`;

export function InteractionBlocker({
  disableCondition,
  label,
  labelSize,
  icon,
  style,
}: InteractionBlockerProps) {
  const disableComponent = React.useMemo(() => {
    return disableCondition;
  }, [disableCondition]);
  if (disableComponent)
    return (
      <Blocker style={style}>
        <BlockerIcon labelSize={labelSize}>
          {label}
          {icon && <Icon {...icon} />}
        </BlockerIcon>
      </Blocker>
    );
}
