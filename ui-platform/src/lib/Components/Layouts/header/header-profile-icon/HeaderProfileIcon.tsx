import styles from './HeaderProfileIcon.module.scss';

/* eslint-disable-next-line */
export interface HeaderProfileIconProps {}

/**
 * A component that displays a header with a profile icon.
 *
 * @param {HeaderProfileIconProps} props - The props for the component.
 * @returns {JSX.Element} The component.
 */
export function HeaderProfileIcon(props: HeaderProfileIconProps) {
    return (
        <div className={styles['container']}>
            <h1>Welcome to HeaderProfileIcon!</h1>
        </div>
    );
}

export default HeaderProfileIcon;
