import { Grid } from '@mui/material';
import UserIcon from '../../Shared/user-icon/UserIcon';
import HeaderTabItem from '../header-tab-item/HeaderTabItem';

/* eslint-disable-next-line */
export interface HeaderProps {}

/**
 * Renders a header component with a profile tab and a user icon.
 *
 * @param {HeaderProps} props - The props for the component.
 * @returns {ReactElement} The header component.
 */
export function Header(props: HeaderProps) {
  return (
    <Grid
      container
      sx={{
        height: '53px',
        display: 'grid',
        gridTemplateColumns: '1fr auto',
        alignItems: 'center',
        gap: '10px',
      }}
    >
      <Grid
        item
        sx={{
          display: 'grid',
          alignItems: 'end',
        }}
      >
        <HeaderTabItem title="Profile" />
      </Grid>
      <Grid
        item
        sx={{
          display: 'grid',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <UserIcon />
      </Grid>
    </Grid>
  );
}

export default Header;
