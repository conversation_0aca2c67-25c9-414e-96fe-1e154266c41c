import { Box, Typography } from '@mui/material';
// import { useTokensTheme } from '../../../contexts/tokens-context/TokensContext';
import { useTheme } from 'styled-components';

/* eslint-disable-next-line */
export interface HeaderTabItemProps {
  title: string;
}

/**
 * A single tab item in the header.
 *
 * @param {HeaderTabItemProps} props - The props for the header tab item.
 * @returns {JSX.Element} The header tab item.
 *
 * The header tab item renders a box with some padding and a centered text element.
 * The box has a bottom border with color from the design tokens (ColorsStrokesInverse).
 * The background color is also from the design tokens (ColorsBackgroundModule).
 * The text color is from the design tokens (ColorsUtilityColorFocus) and the font size and weight are set.
 */
export function HeaderTabItem({ title }: HeaderTabItemProps) {
  const utilityColorFocus = useTheme().ColorsUtilityColorFocus;
  const mediumSpacing = useTheme().SpacingMd;
  const strokeColorInverse = useTheme().ColorsStrokesInverse;
  const backgroundModule = useTheme().ColorsBackgroundModule;
  const fontSize = useTheme().FontSize1;
  const fontWeight = useTheme().FontWeightsInter1;

  return (
    <Box
      sx={{
        display: 'grid',
        gridTemplateColumns: '1fr',
        justifyItems: 'center',
        alignItems: 'center',
        padding: mediumSpacing,
        height: '36px',
        borderRadius: '7px 7px 0px 0px',
        borderBottom: `1px solid ${strokeColorInverse}`,
        background: backgroundModule,
      }}
    >
      <Typography
        sx={{
          color: utilityColorFocus,
          fontSize: `${fontSize}px`,
          fontWeight: fontWeight,
        }}
      >
        {title}
      </Typography>
    </Box>
  );
}

export default HeaderTabItem;
