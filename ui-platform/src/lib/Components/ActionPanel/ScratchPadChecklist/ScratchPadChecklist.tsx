import React, { ComponentPropsWithoutRef, useMemo, useState } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { useSpaKeycloak } from '../../../Auth';
import { useErrorStore } from '../../../Engine';
import { useAsyncLoaderStore } from '../../../Engine/hooks/useAsyncLoaderStore';
import { ActionConfig } from '../../../Engine/models/action.config';
import { ChecklistItem } from '../../Inputs';
import { ScratchPadWrapper } from '../ScratchPadWrapper/ScratchPadWrapper';

/**
 * Type definition for a checklist item, excluding specific props from ChecklistItem component
 * @typedef {Omit<ComponentPropsWithoutRef<typeof ChecklistItem>, 'passVerification' | 'onCheckItem'>} ChecklistItemType
 */
type ChecklistItemType = Omit<
  ComponentPropsWithoutRef<typeof ChecklistItem>,
  'passVerification' | 'onCheckItem'
>;

/**
 * Possible values for a checklist item's state
 * @typedef {'valid' | 'invalid' | 'unchecked'} CheckVal
 */
type CheckVal = 'valid' | 'invalid' | 'unchecked';

/**
 * Props interface for the ScratchPadChecklist component
 * @typedef {Object} Props
 * @property {string} title - Title displayed at the top of the checklist
 * @property {ChecklistItemType[]} items - Array of checklist items to display
 * @property {(data: Record<string, CheckVal | undefined>) => void} [onChange] - Callback when items change
 * @property {boolean} [submitOnChange] - Whether to auto-submit on item changes
 * @property {boolean} [isStory] - Whether component is being used in Storybook
 * @property {any} [store] - Store object containing company data
 * @property {ActionConfig[]} [onInvalid] - Actions to perform when items are invalid
 */
type Props = {
  title: string;
  items: ChecklistItemType[];
  onChange?: (data: Record<string, CheckVal | undefined>) => void;
  submitOnChange?: boolean;
  isStory?: boolean;
  store?: any;
  onInvalid?: ActionConfig[];
  _callClientAction?: (config: ActionConfig) => void;
};

/**
 * ScratchPadChecklist is a component that displays an interactive checklist within a ScratchPad.
 * It supports validation, automatic submission, and integration with a form context.
 *
 * Features:
 * - Interactive checklist items with validation states
 * - Form context integration via React Hook Form
 * - Automatic submission to server (optional)
 * - Error handling and display
 * - Keycloak authentication integration
 * - Async loading states
 *
 * @component
 * @param {Props} props - Component props
 *
 * @example
 * ```tsx
 * // Basic usage
 * <ScratchPadChecklist
 *   title="Onboarding Checklist"
 *   items={[
 *     { name: "documentation", label: "Read Documentation" },
 *     { name: "setup", label: "Complete Setup" }
 *   ]}
 *   onChange={(states) => console.log('Checklist states:', states)}
 * />
 * ```
 *
 * @example
 * ```tsx
 * // With auto-submit and validation
 * <ScratchPadChecklist
 *   title="Requirements Checklist"
 *   items={[
 *     {
 *       name: "requirements",
 *       label: "System Requirements",
 *       description: "Verify system meets all requirements"
 *     }
 *   ]}
 *   submitOnChange={true}
 *   store={{
 *     id: "company-1",
 *     additional: {
 *       checklist: {
 *         requirements: "valid"
 *       }
 *     }
 *   }}
 *   onInvalid={[
 *     { type: "SHOW_ERROR", payload: { message: "Please complete all items" } }
 *   ]}
 * />
 * ```
 *
 * @returns {JSX.Element} The rendered ScratchPadChecklist component
 */
export function ScratchPadChecklist({
  title,
  items,
  onChange,
  submitOnChange,
  isStory = false,
  store,
  onInvalid,
  _callClientAction,
}: Props) {
  const [asyncLoading, setAsyncLoading] = useAsyncLoaderStore((state: any) => [
    state.asyncLoading,
    state.setAsyncLoading,
  ]);
  const { getValues } = useFormContext();
  const { keycloak } = isStory ? { keycloak: null } : useSpaKeycloak();
  const sp_profile_details = useMemo(
    () => store?.sp_profile?.details,
    [store?.sp_profile?.details]
  );
  const [companyDetailsAdditionalStore, setCompanyDetailsAdditionalStore] =
    useState(() => sp_profile_details?.additional ?? {});
  const { addError } = useErrorStore();
  const [invalidCount, setInvalidCount] = useState(0);

  async function submitChange(data: { key: string; value?: CheckVal }) {
    if (!isStory) {
      try {
        const update_sp_url = `${
          (import.meta as any).env.VITE_SP_SERVER
        }/api/v1/spaas_actions/update_sp`;
        setAsyncLoading(true);
        const updatedcompanyDetailAdditional = {
          ...companyDetailsAdditionalStore,
          checklist: items.reduce((acc, cur) => {
            return {
              ...acc,
              [cur.name]:
                getValues()?.[cur.name] ??
                companyDetailsAdditionalStore?.checklist?.[cur.name] ??
                'unchecked',
            };
          }, {}),
        };
        const response = await fetch(update_sp_url, {
          method: 'POST',
          body: JSON.stringify({
            sp_id: sp_profile_details?.id,
            details: {
              additional: updatedcompanyDetailAdditional,
            },
          }),
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${keycloak?.token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to submit changes');
        }

        const res = await response.json();
        if (res?.success === false) {
          throw new Error(res?.reason?.message || 'Something went wrong.');
        }

        setCompanyDetailsAdditionalStore(res?.payload?.details?.additional);
        store?.setState && store?.setState({ sp_profile: res?.payload });
      } catch (error: any) {
        // handleServerError(error.message || 'Something went wrong.');
        // console.error('Error submitting change: ', error);
        addError({
          key: `scratchpad-checklist-submit-change-${Date.now()}`,
          message:
            error instanceof Error
              ? `${error.message}: Please try refreshing the page`
              : 'Failed to submit. Please refresh the page and try again.',
          source: 'server',
          stackTrace: error instanceof Error ? error.stack : undefined,
        });
      }
    } else {
      console.log('submit: ', { [data.key]: data.value });
    }
    setAsyncLoading(false);
  }

  const handleChange = (
    item: ChecklistItemType,
    onCheck: (value?: CheckVal) => void,
    itemVal?: CheckVal
    // value: { [key: string]: boolean }
  ) => {
    const itemName = item.name;
    const itemValue = itemVal;

    const updatedValues = {
      [itemName]: itemValue,
    };

    if (onChange) {
      onChange(updatedValues);
    }

    if (onCheck) {
      onCheck(itemValue);
    }

    submitOnChange && submitChange({ key: itemName, value: itemValue });
  };

  return (
    <ScratchPadWrapper title={title}>
      <>
        {items.map((item: ChecklistItemType, idx: number) => (
          <Controller
            key={idx}
            name={item.name}
            control={item.control}
            rules={item.rules}
            defaultValue={
              companyDetailsAdditionalStore?.checklist?.[item.name] ||
              'unchecked'
            }
            render={({ field: { onChange, value } }) => (
              <ChecklistItem
                {...item}
                key={idx}
                onCheckItem={(value) => handleChange(item, onChange, value)}
                currentValue={value}
                onInvalid={onInvalid}
                invalidCount={invalidCount}
                setInvalidCount={setInvalidCount}
                _callClientAction={_callClientAction}
              />
            )}
          />
        ))}
      </>
    </ScratchPadWrapper>
  );
}
