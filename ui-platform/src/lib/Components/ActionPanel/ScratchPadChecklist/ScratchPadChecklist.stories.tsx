import { Meta, StoryObj } from '@storybook/react';
import { ScratchPadChecklist } from './ScratchPadChecklist';

const meta: Meta<typeof ScratchPadChecklist> = {
  component: ScratchPadChecklist,
  title: 'Components/ActionPanel/ScratchPadChecklist',
};
export default meta;

type Story = StoryObj<typeof ScratchPadChecklist>;

export const Default: Story = {
  args: {
    items: [
      {
        name: 'approved_company_check_valid',
        label: 'Is the member linked to an approved company',
      },
      {
        name: 'id_document_valid',
        label: 'Confirm ID document',
      },
      {
        name: 'criminal_record_check_valid',
        label: 'Check for criminal record',
      },
    ],
    title: 'Check List',
    submitOnChange: false,
    isStory: true,
    onChange: (data) => console.log('Checked value', data),
  },
};
