import { Meta, StoryObj } from '@storybook/react';
import { FOTenantApproval } from './FOTenantApproval';

const meta: Meta<typeof FOTenantApproval> = {
  component: FOTenantApproval,
  title: 'Components/ActionPanel/FOTenantApproval',
};
export default meta;

export const Primary: StoryObj<typeof FOTenantApproval> = {
  args: {
    title: 'FOTenantApproval',
    tenants: [
      {
        name: 'Tenant 1',
        label: 'Tenant 1',
      },
      {
        name: 'Tenant 2',
        label: 'Tenant 2',
      },
      {
        name: 'Tenant 3',
        label: 'Tenant 3',
      },
    ],
    onChange: (val) => console.log('tenant statuses: ', val),
    isStory: true,
  },
};
