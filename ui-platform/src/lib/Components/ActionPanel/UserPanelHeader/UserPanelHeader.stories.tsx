import type { Meta, StoryObj } from '@storybook/react';
import { VariantsType, withComponentShowcase } from '../../../Utilities';
import { Titles, UserPanelHeader } from './UserPanelHeader';

const meta: Meta<typeof UserPanelHeader> = {
  component: UserPanelHeader,
  title: 'Components/ActionPanel/UserPanelHeader',
};
export default meta;
type Story = StoryObj<typeof UserPanelHeader>;

export const Overview: Story = {
  args: { title: 'Messages' },
  argTypes: { title: { control: 'select', options: Titles } },
};

export const DifferentTitles: Story = {
  args: { title: 'Messages' },
  argTypes: { ...Overview.argTypes },
  render: (args) =>
    withComponentShowcase(<UserPanelHeader {...args} />)(
      'title',
      Titles as unknown as VariantsType,
      true,
      {
        showPropName: false,
      }
    ),
};
