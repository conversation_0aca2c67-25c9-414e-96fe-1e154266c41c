import styled from 'styled-components';
import { ProfileHeader } from '../../Avatars/ProfileHeader/ProfileHeader';
import { Divider } from '../../Dividers/Divider';
import {additionalFontStyling} from '../../../Utilities';

/**
 * Predefined list of valid titles for the UserPanelHeader
 */
export const Titles = [
  'Messages',
  'Reminders',
  'Documents',
  'Scratch Pad',
  'Settings',
] as const;

/**
 * Props interface for the UserPanelHeader component
 * @interface UserPanelHeaderProps
 */
export interface UserPanelHeaderProps {
  /** Title to be displayed in the header */
  title: string;
  /** Optional flag to enable font transformation */
  useFontTransformer?: boolean;
}

/**
 * Styled wrapper component that provides the layout structure for the header
 */
const Wrapper = styled.div`
  width: 100%;
  position: relative;
  display: grid;
  grid-template-rows: auto auto 1fr;
  align-items: center;
  justify-items: center;
  padding: 8px;
  box-sizing: border-box;
  gap: 8px;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize6}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
`;

/**
 * Styled component for text content with optional font transformation
 */
const TextWrapper = styled.div<{useFontTransformer?: boolean}>`
  position: relative;
  ${(props) => additionalFontStyling(props.theme.FontWeightsInter1, props.useFontTransformer)}
`;

/**
 * Styled component for the header section containing the ProfileHeader
 */
const Header = styled.div`
  margin-bottom: 15px;
  margin-top: 5px;
  width: 90%;
`;

/**
 * Styled divider component extending the base Divider component
 */
const DividerLine = styled(Divider)`
  align-self: stretch;
  position: relative;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
`;

/**
 * UserPanelHeader is a component that renders a consistent header section for
 * action panels. It includes special handling for the Settings view, where it
 * displays additional user profile information.
 * 
 * Features:
 * - Consistent header styling across all panel types
 * - Special Settings view with ProfileHeader integration
 * - Configurable font transformation
 * - Responsive layout with grid system
 * - Visual separation with styled divider
 * 
 * @component
 * @param {UserPanelHeaderProps} props - Component properties
 * @param {string} props.title - Title to display in the header
 * @param {boolean} [props.useFontTransformer] - Optional flag to enable font transformation
 * 
 * @example
 * ```tsx
 * // Basic usage
 * <UserPanelHeader title="Messages" />
 * ```
 * 
 * @example
 * ```tsx
 * // Settings view with profile header
 * <UserPanelHeader 
 *   title="Settings"
 *   useFontTransformer={true}
 * />
 * ```
 * 
 * @remarks
 * When the title is "Settings", the component automatically renders a ProfileHeader
 * component with user information. For all other titles, it renders a simple header
 * with the title text.
 */
export function UserPanelHeader(props: UserPanelHeaderProps) {
  return (
    <Wrapper data-testid="user-panel-header">
      {props.title === 'Settings' && (
        <Header>
          <ProfileHeader
            email={'<EMAIL>'}
            username={'J. Sparrow'}
            optionsHandler={(e) => console.log('clicked options', e)}
          />
        </Header>
      )}
      <TextWrapper>{props.title || 'Title Missing'}</TextWrapper>
      <DividerLine size="medium" background="focus" height="thin" />
    </Wrapper>
  );
}
