import { useEffect, useState } from 'react';
import { create } from 'zustand';
import { ActionPanelConfig } from '../../../Engine';

/**
 * Type definition for an action panel configuration
 * @typedef {Object} ActionPanelConfig
 * @property {string} title - The title of the action panel
 * @property {any} [additionalProps] - Any additional properties specific to the panel
 */

/**
 * useActionPanel is a custom React hook that manages the state of an action panel,
 * including its visibility and active configuration.
 *
 * Features:
 * - Manages overlay visibility state
 * - Tracks active panel configuration
 * - Initializes state based on provided active view
 * - Provides state setters for external control
 *
 * @function
 * @param {ActionPanelConfig[]} actionPanels - Array of action panel configurations
 * @param {string} [activeView] - Optional title of the initially active panel
 *
 * @returns {Object} Hook state and controls
 * @property {boolean} isOverlayVisible - Whether the panel overlay is visible
 * @property {(visible: boolean) => void} setIsOverlayVisible - Function to control overlay visibility
 * @property {ActionPanelConfig | undefined} activeConfig - Currently active panel configuration
 * @property {(config: ActionPanelConfig) => void} setActiveConfig - Function to set active configuration
 *
 * @example
 * ```tsx
 * // Basic usage
 * const {
 *   isOverlayVisible,
 *   setIsOverlayVisible,
 *   activeConfig,
 *   setActiveConfig
 * } = useActionPanel([
 *   { title: 'Settings', content: 'Settings panel content' },
 *   { title: 'Help', content: 'Help panel content' }
 * ]);
 * ```
 *
 * @example
 * ```tsx
 * // With initial active view
 * const {
 *   isOverlayVisible,
 *   activeConfig
 * } = useActionPanel(
 *   [
 *     { title: 'Settings', content: 'Settings panel content' },
 *     { title: 'Help', content: 'Help panel content' }
 *   ],
 *   'Settings'
 * );
 * ```
 */
export function useActionPanel(actionPanels: any[], activeView?: string) {
  // const [isOverlayVisible, setIsOverlayVisible] = useState<boolean>(
  //   () => !!getActiveViewConfig()
  // );

  // const [activeConfig, setActiveConfig] = useState<any>(() =>
  //   getActiveViewConfig()
  // );
  const {
    isOverlayVisible,
    activeConfig,
    setIsOverlayVisible,
    setActiveConfig,
    reset,
  } = useActionPanelStore();

  useEffect(() => {
    const getActiveViewConfig = () =>
      actionPanels?.filter((ap) => {
        return ap?.title === activeView;
      })[0];

    if (activeView) {
      console.log(
        `ActionPanel: setting active view ${activeView}: `,
        getActiveViewConfig()
      );
      setActiveConfig(getActiveViewConfig());
      setIsOverlayVisible(true);
    } else {
      console.log('ActionPanel: No active view for action panel');
    }

    return () => reset();
  }, [actionPanels, activeView, reset, setActiveConfig, setIsOverlayVisible]);

  return {
    isOverlayVisible,
    setIsOverlayVisible,
    activeConfig,
    setActiveConfig,
    reset,
  };
}
interface ActionPanelState {
  isOverlayVisible: boolean;
  activeConfig?: ActionPanelConfig;
  setIsOverlayVisible: (visible: boolean) => void;
  setActiveConfig: (config: ActionPanelConfig) => void;
  reset: () => void;
}

export const useActionPanelStore = create<ActionPanelState>()((set) => ({
  isOverlayVisible: false,
  activeConfig: undefined,
  setIsOverlayVisible: (visible) => set({ isOverlayVisible: visible }),
  setActiveConfig: (config) => set({ activeConfig: config }),
  reset: () => set({ isOverlayVisible: false, activeConfig: undefined }),
}));
