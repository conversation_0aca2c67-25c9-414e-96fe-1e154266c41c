import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { ActionPanelContainer } from './ActionPanelContainer';

const meta: Meta<typeof ActionPanelContainer> = {
  component: ActionPanelContainer,
  title: 'Components/ActionPanel/ActionPanelContainer',
};
export default meta;
type Story = StoryObj<typeof ActionPanelContainer>;

const chatMessages = [
  {
    username: 'Thabo Mabikho',
    content: 'This is some message with text',
    time: '13:24',
  },
  {
    username: 'Thabo Mabikho',
    content: 'This is some message with text',
    time: '13:24',
  },
  {
    username: 'Thab<PERSON> Mabikho',
    content: 'This is some message with text',
    time: '13:24',
  },
  {
    username: '<PERSON>hab<PERSON>bikh<PERSON>',
    content: 'This is some message with text',
    time: '13:24',
  },
  {
    username: 'Thabo Mabikho',
    content: 'This is some message with text',
    time: '13:24',
  },
  {
    username: 'Thabo Mabikho',
    content: 'This is some message with text',
    time: '13:24',
  },
  {
    username: '<PERSON><PERSON><PERSON>kh<PERSON>',
    content: 'This is some message with text',
    time: '13:24',
  },
  {
    username: '<PERSON>hab<PERSON>kho',
    content: 'This is some message with text',
    time: '13:24',
  },
  {
    username: 'Thabo Mabikho',
    content: 'This is some message with text',
    time: '13:24',
  },
];

export const Overview: Story = {
  args: {
    configs: [
      {
        icon: 'bell-02',
        templateOptions: {
          title: 'Messages',
          username: 'Thabo Mabikho',
          client: 'Builders',
          optionsHandler: () => alert('Clicked options'),
          avatarHandler: () => alert('Clicked avatar'),
          searchPlaceholder: 'Search contacts...',
        },
      },
    ],
  },
};
