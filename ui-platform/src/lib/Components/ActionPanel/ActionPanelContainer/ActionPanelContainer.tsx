import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { ActionPanelConfig } from '../../../Engine/models/action-panel.config';
import { ActionPanelControls } from '../ActionPanelControls/ActionPanelControls';

/**
 * Props interface for the Container styled component
 * @interface ContainerProps
 */
export interface ContainerProps {
  /** Theme object containing color definitions */
  theme?: {
    /** Primary color for toolbar tabs */
    ColorsTabsToolbarPrimary?: string;
  };
  /** Indicates if a button in the container has been clicked */
  ButtonClicked?: boolean;
}

/**
 * Styled container component that provides the base layout and styling for the action panel.
 * Features a fixed width and full height design with configurable border radius and background.
 */
const Container = styled.div<ContainerProps>`
  width: 56px; // actual width of action panel
  height: 100%;
  top: 0;
  bottom: 0;
  right: 0;
  box-sizing: border-box;
  border-top-right-radius: ${(props) => props.theme?.RadiusSm};
  border-bottom-right-radius: ${(props) => props.theme?.RadiusSm};
  border-bottom-left-radius: ${(props) =>
    !props.ButtonClicked ? `${props.theme?.RadiusSm}` : '0'};
  border-top-left-radius: ${(props) =>
    !props.ButtonClicked ? `${props.theme?.RadiusSm}` : '0'};
  display: grid;
  place-items: center;
  background-color: ${(props) => props.theme?.ColorsBackgroundActionBar};
  position: absolute;
  grid-template-areas: 'content';
  grid-area: Action-Panel;
`;

/**
 * Props interface for the ActionPanelContainer component
 * @interface Props
 */
interface Props {
  /** Controls the visibility state of the overlay */
  isOverlayVisible: boolean;
  /** Function to update the overlay visibility state */
  setIsOverlayVisible:
    | React.Dispatch<React.SetStateAction<boolean>>
    | ((visible: boolean) => void);
  /** Currently active panel configuration */
  activeConfig?: ActionPanelConfig;
  /** Function to update the active configuration */
  setActiveConfig:
    | React.Dispatch<React.SetStateAction<any>>
    | ((activeConfig: any) => void);
  /** Array of available panel configurations */
  configs: ActionPanelConfig[];
}

/**
 * ActionPanelContainer serves as the primary container for the action panel's control interface.
 * It provides a fixed-width sidebar that houses the action panel controls and manages the
 * interaction between different panel states.
 *
 * Features:
 * - Fixed width sidebar layout
 * - Responsive border radius based on panel state
 * - Integration with ActionPanelControls for user interaction
 * - Theme-aware styling system
 *
 * @component
 * @param {Props} props - Component properties
 * @param {boolean} props.isOverlayVisible - Controls overlay visibility state
 * @param {Function} props.setIsOverlayVisible - Handler to update overlay visibility
 * @param {ActionPanelConfig[]} props.configs - Available panel configurations
 * @param {ActionPanelConfig} props.activeConfig - Currently active configuration
 * @param {Function} props.setActiveConfig - Handler to update active configuration
 *
 * @example
 * ```tsx
 * <ActionPanelContainer
 *   isOverlayVisible={false}
 *   setIsOverlayVisible={setIsVisible}
 *   configs={[
 *     {
 *       id: 'panel1',
 *       title: 'Settings',
 *       icon: 'settings'
 *     }
 *   ]}
 *   activeConfig={currentConfig}
 *   setActiveConfig={setConfig}
 * />
 * ```
 */
export const ActionPanelContainer = ({
  isOverlayVisible,
  setIsOverlayVisible,
  configs,
  activeConfig,
  setActiveConfig,
}: Props) => {
  const [borderClicked, setBorderClicked] = useState(false);

  useEffect(() => {
    if (isOverlayVisible) {
      setBorderClicked(true);
    } else {
      const timer = setTimeout(() => {
        setBorderClicked(false);
      }, 980);
      return () => clearTimeout(timer);
    }
  }, [isOverlayVisible]);

  return (
    <Container
      data-testid="action-panel-container"
      ButtonClicked={borderClicked}
    >
      <ActionPanelControls
        configs={configs}
        activeConfig={activeConfig}
        setActiveConfig={setActiveConfig}
        setIsOverlayVisible={setIsOverlayVisible}
        isOverlayVisible={isOverlayVisible}
      />
    </Container>
  );
};
