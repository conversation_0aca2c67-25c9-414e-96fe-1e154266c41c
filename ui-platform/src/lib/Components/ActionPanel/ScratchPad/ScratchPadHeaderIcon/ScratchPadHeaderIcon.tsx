import styled, { useTheme } from 'styled-components';
import { Icon, IconProps } from '../../../Icons';

/**
 * ScratchActionIcon is a styled icon component specifically designed for use in
 * ScratchPad headers. It provides consistent styling and interactive behavior
 * for action icons.
 * 
 * Features:
 * - Theme-aware color handling
 * - Fixed size of 24px
 * - Interactive cursor styling
 * - Relative positioning for layout flexibility
 * 
 * @styled-component
 * @component
 * 
 * @example
 * ```tsx
 * // Basic usage
 * <ScratchActionIcon type="edit" />
 * ```
 * 
 * @example
 * ```tsx
 * // With additional props
 * <ScratchActionIcon 
 *   type="delete"
 *   onClick={() => console.log('Icon clicked')}
 *   className="custom-icon"
 * />
 * ```
 */
export const ScratchActionIcon = styled(
  ({ type, size, stroke, ...props }: IconProps) => {
    const iconColor = useTheme().ColorsIconColorPrimary;
    return <Icon type={type} size={24} stroke={iconColor} {...props} />;
  }
)`
  position: relative;
  cursor: pointer;
`;
