import { ComponentPropsWithRef } from 'react';
import styled from 'styled-components';

/**
 * <PERSON>ps type for the ScratchPadBody component, extending textarea's props
 * @typedef {ComponentPropsWithRef<'textarea'>} ScratchPadBodyProps
 */
export type ScratchPadBodyProps = ComponentPropsWithRef<'textarea'>;

/**
 * ScratchPadBody is a styled textarea component designed specifically for the
 * ScratchPad's main content area. It provides a clean, borderless writing
 * surface with consistent typography and spacing.
 * 
 * Features:
 * - Full-height textarea with overflow handling
 * - Grid-based layout system
 * - Theme-aware typography and colors
 * - Borderless design
 * - Non-resizable textarea
 * - Transparent background
 * 
 * @component
 * @param {ScratchPadBodyProps} props - Standard textarea props
 * 
 * @example
 * ```tsx
 * // Basic usage
 * <ScratchPadBody
 *   placeholder="Start typing..."
 *   value={content}
 *   onChange={handleChange}
 * />
 * ```
 * 
 * @example
 * ```tsx
 * // With additional props
 * <ScratchPadBody
 *   placeholder="Enter your notes here..."
 *   value={content}
 *   onChange={handleChange}
 *   maxLength={1000}
 *   onFocus={() => console.log('Focused')}
 *   onBlur={() => console.log('Blurred')}
 * />
 * ```
 * 
 * @styled-component
 */
export const ScratchPadBody = styled((props: ScratchPadBodyProps) => {
  return <textarea {...props} />;
})`
  height: 100%;
  overflow: hidden;
  width: 100%;
  position: relative;
  display: grid;
  grid-template-rows: 1fr;
  padding: 12px 16px;
  box-sizing: border-box;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize2}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  background-color: transparent;
  border: none;
  resize: none;
`;
