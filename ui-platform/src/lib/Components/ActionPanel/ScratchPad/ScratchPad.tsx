import { useState } from 'react';
import styled from 'styled-components';
import { Divider } from '../../Dividers/Divider';
import { ScratchPadBody } from './ScratchPadBody/ScratchPadBody';
import {
  ScratchPadHeader,
  ScratchPadHeaderProps,
} from './ScratchPadHeader/ScratchPadHeader';

/**
 * Props interface for the ScratchPad component, extending ScratchPadHeaderProps
 * @interface ScratchPadProps
 */
export interface ScratchPadProps extends ScratchPadHeaderProps {
  /** Placeholder text for the main text area */
  placeHolder: string;
  /** Initial value for the text area */
  value?: string;
}

/**
 * Styled container component that provides the layout structure for the scratch pad
 */
const Container = styled.div`
  position: relative;
  border-radius: 4px;
  background-color: ${(props) =>
    props.theme.ColorsButtonColorActionPanelPrimary};
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-rows: auto auto 1fr;
  justify-items: center;
  justify-items: start;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props.theme.ColorsIconColorPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
`;

/**
 * Styled divider component with custom background color
 */
const ScratchPadDivider = styled(Divider)`
  background: ${(props) => props.theme.ColorsStrokesDefault};
`;

/**
 * ScratchPad is a versatile note-taking component that provides a title and content
 * editing area. It's designed to be a lightweight, self-contained notepad within
 * the application.
 * 
 * Features:
 * - Editable title with placeholder
 * - Large text area for content
 * - Optional clear/reset functionality
 * - State management for both title and content
 * - Responsive grid-based layout
 * - Theme-aware styling
 * 
 * @component
 * @param {Object} props - Component properties
 * @param {string} [props.title=''] - Initial title for the scratch pad
 * @param {string} props.titlePlaceholder - Placeholder text for the title
 * @param {IconTypes} [props.icon] - Optional icon to display in header
 * @param {Function} [props.iconHandler] - Callback for icon click events
 * @param {string} props.placeHolder - Placeholder text for main content area
 * @param {string} [props.value=''] - Initial value for main content area
 * 
 * @example
 * ```tsx
 * // Basic usage
 * <ScratchPad
 *   titlePlaceholder="Enter note title..."
 *   placeHolder="Start typing your note..."
 * />
 * ```
 * 
 * @example
 * ```tsx
 * // With clear functionality
 * <ScratchPad
 *   titlePlaceholder="Note title"
 *   placeHolder="Note content"
 *   icon="trash-01"
 *   iconHandler={({ heading, body }) => {
 *     console.log('Cleared note:', { heading, body });
 *     // Handle cleared content
 *   }}
 *   value="Initial content"
 *   title="Initial title"
 * />
 * ```
 * 
 * @remarks
 * The component maintains its own state for both the title and content,
 * making it self-contained. However, it also provides the iconHandler
 * callback to allow parent components to react to clear events.
 */
export function ScratchPad({
  title: initialTitle = '',
  titlePlaceholder,
  icon,
  iconHandler,
  placeHolder,
  value: initialValue = '',
}: ScratchPadProps) {
  /**
   * State for managing both heading and body content
   */
  const [state, setState] = useState({
    heading: initialTitle,
    body: initialValue,
  });

  /**
   * Handles changes to the main content area
   * @param {React.ChangeEvent<HTMLTextAreaElement>} event - Change event from textarea
   */
  const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { value } = event.target;
    setState((prevState) => ({ ...prevState, body: value }));
  };

  /**
   * Handles icon click events, clearing content and notifying parent component
   */
  const handleIconHandler = () => {
    const { heading, body } = state;
    setState({ heading: '', body: '' });
    iconHandler && iconHandler({ heading, body });
  };

  return (
    <Container>
      <ScratchPadHeader
        icon={icon}
        title={state.heading}
        titlePlaceholder={titlePlaceholder}
        iconHandler={handleIconHandler}
        data={state}
        setData={setState}
      />
      <ScratchPadDivider size="fullWidth" background="default" />
      <ScratchPadBody
        placeholder={placeHolder}
        value={state.body}
        onChange={handleChange}
      />
    </Container>
  );
}
