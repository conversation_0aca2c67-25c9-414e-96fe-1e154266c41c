import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { ScratchPad } from './ScratchPad';

const meta: Meta<typeof ScratchPad> = {
  component: ScratchPad,
  title: 'Components/ScratchPad',
};
export default meta;
type Story = StoryObj<typeof ScratchPad>;

export const Overview: Story = {
  args: {
    titlePlaceholder: 'Heading',
    icon: 'trash-01',
    iconHandler: (data) =>
      console.log(
        'got data: Heading - ' + data.heading + ' Body - ' + data.body
      ),
    placeHolder: 'Text here...',
  },
};
