/**
 * ScratchPadHeading is a styled heading component specifically designed for use in
 * ScratchPad components. It provides consistent typography and layout styling
 * while maintaining theme consistency.
 * 
 * Features:
 * - Reset default margin and font-size
 * - Theme-aware typography (font family, size, and color)
 * - Left-aligned text
 * - Relative positioning for layout flexibility
 * - Inherits Heading component functionality
 * 
 * @styled-component
 * @component
 * 
 * @example
 * ```tsx
 * // Basic usage
 * <ScratchPadHeading>Notes</ScratchPadHeading>
 * ```
 * 
 * @example
 * ```tsx
 * // With additional props
 * <ScratchPadHeading
 *   className="custom-heading"
 *   onClick={() => console.log('Heading clicked')}
 * >
 *   Important Notes
 * </ScratchPadHeading>
 * ```
 */
import styled from 'styled-components';
import { Heading } from '../../../Heading/Heading';

export const ScratchPadHeading = styled(Heading)`
  margin: unset;
  font-size: unset;
  position: relative;
  font-size: ${(props) => props.theme.FontSize3}px;
  font-family: ${(props) => props.theme.FontFamiliesInter};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  text-align: left;
`;
