import { Dispatch, SetStateAction, useState } from 'react';
import styled from 'styled-components';
import { IconTypes } from '../../../Icons';
import { ScratchActionIcon } from '../ScratchPadHeaderIcon/ScratchPadHeaderIcon';
import { ScratchPadHeading } from '../ScratchPadHeading/ScratchPadHeading';

/**
 * Props interface for the ScratchPadHeader component
 * @interface ScratchPadHeaderProps
 */
export interface ScratchPadHeaderProps {
  /** Current title text */
  title?: string;
  /** Placeholder text when title is empty */
  titlePlaceholder: string;
  /** Optional icon to display in the header */
  icon?: IconTypes;
  /** Callback function when icon is clicked */
  iconHandler?: (arg: { heading: string; body: string }) => void;
  /** Function to update the scratch pad data */
  setData: ({ heading, body }: { heading: string; body: string }) => void;
  /** Current scratch pad data */
  data: { heading: string; body: string };
}

/**
 * Styled container component that provides the layout for the header
 */
const Container = styled.div`
  width: 100%;
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  box-sizing: border-box;
  gap: 16px;
`;

/**
 * Styled input component for editing the header title
 * Removes all default styling using 'all: unset'
 */
const HeaderInput = styled.input`
  all: unset;
`;

/**
 * ScratchPadHeader is a component that provides an editable title and action icon
 * for the ScratchPad component. It manages the state between view and edit modes
 * for the title.
 * 
 * Features:
 * - Editable title with placeholder support
 * - Toggleable edit mode
 * - Optional action icon with callback
 * - Grid-based layout for consistent spacing
 * - Theme-aware styling
 * 
 * @component
 * @param {ScratchPadHeaderProps} props - Component properties
 * @param {string} [props.title] - Current title text
 * @param {string} props.titlePlaceholder - Placeholder text when title is empty
 * @param {IconTypes} [props.icon] - Optional icon to display
 * @param {Function} [props.iconHandler] - Callback for icon click events
 * @param {Function} props.setData - Function to update scratch pad data
 * @param {Object} props.data - Current scratch pad data state
 * 
 * @example
 * ```tsx
 * // Basic usage
 * <ScratchPadHeader
 *   titlePlaceholder="Enter title..."
 *   setData={updateData}
 *   data={{ heading: "", body: "" }}
 * />
 * ```
 * 
 * @example
 * ```tsx
 * // With icon and handler
 * <ScratchPadHeader
 *   title="My Notes"
 *   titlePlaceholder="Enter title..."
 *   icon="trash-01"
 *   iconHandler={({ heading, body }) => {
 *     console.log('Clear requested:', { heading, body });
 *   }}
 *   setData={updateData}
 *   data={{ heading: "My Notes", body: "Note content" }}
 * />
 * ```
 * 
 * @remarks
 * The component manages its own edit state but relies on the parent component
 * to manage the actual data through the setData prop.
 */
export function ScratchPadHeader(props: ScratchPadHeaderProps) {
  /**
   * State to track whether the title is being edited
   */
  const [editHeading, setEditHeading] = useState(false);
  return (
    <Container>
      {editHeading ? (
        <HeaderInput
          onBlur={() => setEditHeading(false)}
          onChange={(e) => {
            props.setData({ ...props.data, heading: e.target.value });
          }}
          placeholder={props.titlePlaceholder}
          value={props.title}
        />
      ) : (
        <ScratchPadHeading onClick={() => setEditHeading(true)}>
          {props.title || props.titlePlaceholder}
        </ScratchPadHeading>
      )}
      <ScratchActionIcon
        type={props.icon || 'plus'}
        size={24}
        onClick={() => props?.iconHandler && props?.iconHandler(props.data)}
      />
    </Container>
  );
}
