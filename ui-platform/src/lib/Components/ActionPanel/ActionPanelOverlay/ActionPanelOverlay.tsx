import { ComponentPropsWithoutRef, memo, useRef } from 'react';
import styled from 'styled-components';

/**
 * Styled component that creates an animated overlay panel.
 * Features smooth slide animations, backdrop blur, and shadow effects.
 *
 * @styled-component
 * @param {boolean} visible - Controls the visibility and animation state
 * @param {ReactNode} children - Content to be rendered within the overlay
 */
const Overlay = styled.div<
  { visible: boolean } & Pick<ComponentPropsWithoutRef<'div'>, 'children'>
>`
  position: absolute;
  top: 0;
  right: 56px;
  bottom: 0;
  width: ${(props) => (props.visible ? 'calc(0px + 326px)' : '0px')};
  transition: width 1s ease-in-out;
  height: 100%;
  background: ${(props) => props?.theme.ColorsOverlayDynamicPanel};

  backdrop-filter: blur(
    10px
  ); // I dont think this is part of the figma styling but it adds a lot of depth
  box-shadow: -12px 0px 13px rgba(0, 0, 0, 0.25); // it adds a shaddow
  border-top-left-radius: ${(props) => props?.theme.GapSm};
  border-bottom-left-radius: ${(props) => props?.theme.GapSm};
  overflow-y: hidden;
  overflow-x: hidden;
`;

/**
 * ActionPanelOverlay is a memoized component that provides an animated sliding panel
 * for displaying additional content or controls. It features smooth enter/exit
 * animations and a backdrop blur effect for depth.
 *
 * Features:
 * - Smooth slide-in/slide-out animations
 * - Backdrop blur for depth effect
 * - Shadow effects for elevation
 * - Configurable visibility
 * - Memoized for performance
 * - Overflow handling
 *
 * @component
 * @param {Object} props - Component properties
 * @param {boolean} props.visible - Controls the visibility state of the overlay
 * @param {React.ReactNode} props.children - Content to be rendered within the overlay
 *
 * @example
 * ```tsx
 * <ActionPanelOverlay visible={isPanelOpen}>
 *   <div>
 *     <h2>Panel Content</h2>
 *     <p>Additional information or controls can go here.</p>
 *   </div>
 * </ActionPanelOverlay>
 * ```
 *
 * @example
 * // With animation control
 * ```tsx
 * const [isVisible, setIsVisible] = useState(false);
 *
 * <ActionPanelOverlay visible={isVisible}>
 *   <div>
 *     <button onClick={() => setIsVisible(false)}>Close Panel</button>
 *     <div>Panel Content</div>
 *   </div>
 * </ActionPanelOverlay>
 * ```
 */
export const ActionPanelOverlay = memo(
  ({ visible, children }: { visible: boolean; children: React.ReactNode }) => {
    return (
      <Overlay data-testid="action-panel-overlay" visible={visible}>
        {children}
      </Overlay>
    );
  }
);
