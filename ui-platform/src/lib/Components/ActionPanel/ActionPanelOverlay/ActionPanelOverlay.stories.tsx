import type { Meta, StoryFn, StoryObj } from '@storybook/react';
import { ActionPanelOverlay } from './ActionPanelOverlay';

const meta: Meta<typeof ActionPanelOverlay> = {
  title: 'Components/ActionPanel/ActionPanelOverlay',
  component: ActionPanelOverlay,
};

export default meta;

type Story = StoryObj<typeof ActionPanelOverlay>;

export const Overview: Story = {
  args: {
    children: 'I am overlay',
    visible: true,
  },
  argTypes: {
    visible: {
      control: {
        type: 'boolean',
        defaultValue: true,
      },
    },
  },
};

export const Visible: Story = {
  args: {
    children: 'I am overlay',
    visible: true,
  },
  argTypes: {
    visible: {
      control: {
        type: 'boolean',
      },
    },
  },
};

export const Hidden: Story = {
  args: {
    children: 'I am overlay',
    visible: false,
  },
  argTypes: {
    visible: {
      control: {
        type: 'boolean',
      },
    },
  },
};

// const Demo: StoryFn<typeof ActionPanelContainer> = (args) => {
//   // const [isOverlayVisible, setIsOverlayVisible] = useState(false);
//   const { isOverlayVisible, setIsOverlayVisible } = useActionPanelContainer();
//   const toggleOverlay = () => {
//     setIsOverlayVisible(!isOverlayVisible);
//   };

//   return (
//     <>
//       <ActionPanelContainer {...args} />
//       {isOverlayVisible && <ActionPanelOverlay />}
//     </>
//   );
// };

// const Template: StoryFn = () => {
//   return <ActionPanelOverlay />;
// };

// export const OverlayInAction = Demo.bind({});
// OverlayInAction.args = {
//   controls: [
//     {
//       icon: 'bell-02',
//       templateOptions: {
//         title: 'Messages',
//         username: 'Thabo Mabikho',
//         client: 'Builders',
//         optionsHandler: () => alert('Clicked options'),
//         avatarHandler: () => alert('Clicked avatar'),
//         searchPlaceholder: 'Search contacts...',
//       },
//     },
//   ],
// };

// export const Overview = Template.bind({});
