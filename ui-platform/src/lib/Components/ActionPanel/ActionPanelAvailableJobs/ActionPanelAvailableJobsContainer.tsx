import React from 'react';
import styled from 'styled-components';

interface IActionPanelAvailableJobsContainerProps {
    children: any
}

const Container = styled.div`
  position: relative;
  border-radius: 4px;
  background-color: ${(props) =>
    props.theme.ColorsButtonColorActionPanelPrimary};
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-rows: auto;
 
`;

export const ActionPanelAvailableJobsContainer = ({
    children
}: IActionPanelAvailableJobsContainerProps) => {
    return (
        <Container>
            {children}
        </Container>
    )
}