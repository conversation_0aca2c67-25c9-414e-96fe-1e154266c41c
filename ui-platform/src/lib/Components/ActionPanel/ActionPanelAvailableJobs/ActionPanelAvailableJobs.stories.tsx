/* eslint-disable @typescript-eslint/no-explicit-any */
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { ActionPanelAvailableJobs } from './ActionPanelAvailableJobs';
import { useAppStore, useAvailableJobsStore } from '../../../Engine';
import { useEffect } from 'react';

const mockSkills = [
  { id: 1, name: 'Plumbing', active: true, mid: 'H2902' },
  { id: 2, name: 'Electrical', active: true, mid: 'H2903' },
  { id: 3, name: 'Carpentry', active: true, mid: 'H2904' },
  { id: 4, name: 'Painting', active: true, mid: 'H2905' },
];

const mockAvailableJobs = [
  {
    id: 1201,
    appointments: [],
    appointment: {
      id: 1201,
      after_hours: false,
      appointment_name: 'Before',
      appointment_type_id: '5',
      range_start: '2025-02-25T16:00:00',
      range_end: '2025-02-25T16:00:00',
    },
    location: "-26.152678600,27.921833700",
    mid: '',
    address: '12 Windmill Road, Albertaville, 12345',
    skill: '1',
    customer: '<PERSON>',
    cellnumber: '0801234567',
    area: 'Gauteng',
    claim_type: 'Plumbing',
    suburb: 'Roodepoort',
  },
  {
    id: 1202,
    appointments: [],
    appointment: {
      id: 1202,
      after_hours: false,
      appointment_name: 'At',
      appointment_type_id: '6',
      range_start: '2025-02-22T16:00:00',
      range_end: '2025-02-22T16:00:00',
    },
    location: "-26.152678600,27.921833700",
    mid: '',
    address: '34 Elm Street, Springfield, 12345',
    skill: '4',
    appointment_date: 'October 14, 2024',
    customer: 'Jane Smith',
    cellnumber: '**********',
    area: 'Gauteng',
    claim_type: 'Plumbing',
    suburb: 'Whiteridge',
  }
];

const MockAppStoreProvider = ({ children }: { children: React.ReactNode }) => {
 
    useAppStore.setState((state) => ({
      ...state,
      filtersData: [],
      auth: {
        staffMember: {
          id: 123,
          sp: {
            skills: mockSkills
          }
        }
      },
      env: {
        VITE_API_BASE_URL: 'http://mock-api.com'
      }
    }));
  
  return children;
}

// Keep existing mockSkills and mockAvailableJobs

const meta: Meta<typeof ActionPanelAvailableJobs> = {
  component: ActionPanelAvailableJobs,
  title: 'Components/ActionPanel/ActionPanelAvailableJobs',
  decorators: [
    (Story) => (
      <MockAppStoreProvider><Story /></MockAppStoreProvider>
    ),
    (Story) => {
      const setState = useAvailableJobsStore.setState;
      useEffect(() => {
        setState({
          availableJobs: mockAvailableJobs,
          filteredJobs: mockAvailableJobs,
          isOpen: true,
          jobResponses: [],
          staffMember: {id: 123, sp: {skills: mockSkills}}
        });
      }, [setState]);
      return <Story />;
      // useAvailableJobsStore.setState({
       
      //   availableJobs: mockAvailableJobs,
      //   filteredJobs: mockAvailableJobs,
      //   isOpen: true,
      //   jobResponses: []
      // });
      // return <Story />;
    }
  ]
};

export default meta;

type Story = StoryObj<typeof ActionPanelAvailableJobs>;

export const Default: Story = {
  args: {
    availableJobs: mockAvailableJobs,
    skills: mockSkills,
    showAvailableList: true
  }
};