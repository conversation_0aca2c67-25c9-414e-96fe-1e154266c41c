import type { Meta, StoryObj } from '@storybook/react';
import { ActionPanelControls } from './ActionPanelControls';

const meta: Meta<typeof ActionPanelControls> = {
  component: ActionPanelControls,
  title: 'Components/ActionPanel/ActionPanelControls',
};
export default meta;
type Story = StoryObj<typeof ActionPanelControls>;

export const Overview: Story = {
  args: {
    configs: [
      {
        icon: 'bell-02',
        templateOptions: {
          title: 'Messages',
        },
      },
    ],
  },
};
