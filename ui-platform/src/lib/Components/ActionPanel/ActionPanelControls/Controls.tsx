import styled from "styled-components";

/**
 * Controls is a styled container component for action panel control buttons.
 * It provides consistent positioning and spacing for control elements within
 * the action panel.
 * 
 * Features:
 * - Absolute positioning at the bottom
 * - Interactive cursor styling
 * - Consistent padding
 * - Automatic vertical spacing between child elements
 * - Fixed 1px margin between control buttons
 * 
 * @styled-component
 * @component
 * 
 * @example
 * ```tsx
 * // Basic usage
 * <Controls>
 *   <ActionPanelControl>Settings</ActionPanelControl>
 *   <ActionPanelControl>Help</ActionPanelControl>
 * </Controls>
 * ```
 * 
 * @example
 * ```tsx
 * // With custom styling
 * <Controls className="custom-controls">
 *   <ActionPanelControl icon="settings">Settings</ActionPanelControl>
 *   <ActionPanelControl icon="help">Help</ActionPanelControl>
 * </Controls>
 * ```
 */
export const Controls = styled.div`
  cursor: pointer;
  bottom: 0;
  position: absolute;
  padding: 2px;

  & > * {
    margin-bottom: 1px; // Adds 1px gap between each control button
  }
`;