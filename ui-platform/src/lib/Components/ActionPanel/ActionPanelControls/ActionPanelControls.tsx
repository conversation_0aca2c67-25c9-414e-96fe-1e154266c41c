import { useMemo } from 'react';
import styled from 'styled-components';
import { ActionPanelConfig } from '../../../Engine/models/action-panel.config';
import { useActionPanelStore } from '../hooks/useActionPanel';
import { ActionPanelControl } from './ActionPanelControl';

/**
 * Main container for the action panel controls.
 * Uses CSS Grid to create a two-row layout for top and bottom controls.
 */
const Container = styled.div`
  display: grid;
  grid-auto-flow: column;
  grid-template-rows: auto auto;
  grid-template-columns: auto;
  justify-content: space-between;
  height: 100%;
`;

/**
 * Container for controls that appear at the top of the action panel.
 * Provides consistent spacing and interaction styling for top-level controls.
 */
const TopControls = styled.div`
  display: grid;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  justify-content: start;
  cursor: pointer;
  padding: 2px;
  gap: ${(props) => props?.theme.GapMd};
  align-self: start;
`;

/**
 * Container for controls that appear at the bottom of the action panel.
 * Mirrors the styling of TopControls but aligns items to the bottom.
 */
const BottomControls = styled.div`
  display: grid;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  justify-content: end;
  cursor: pointer;
  padding: 2px;
  gap: ${(props) => props?.theme.GapMd};
  align-self: end;
`;

/**
 * Props interface for the ActionPanelControls component
 * @interface Props
 */
type Props = {
  /** Array of configuration objects for all available controls */
  configs: ActionPanelConfig[];
  /** Currently active control configuration */
  activeConfig?: ActionPanelConfig;
  /** Function to update the active configuration */
  setActiveConfig: any;
  /** Function to update the overlay visibility */
  setIsOverlayVisible: any;
  /** Current visibility state of the overlay */
  isOverlayVisible: boolean;
};

/**
 * ActionPanelControls component manages and renders the interactive controls
 * within the action panel. It organizes controls into two groups: top controls
 * for local/contextual actions and bottom controls for global actions.
 *
 * Features:
 * - Automatic organization of controls based on actionLevel
 * - Memoized filtering of controls for performance
 * - Responsive grid-based layout
 * - Separate styling for top and bottom control groups
 *
 * @component
 * @param {Props} props - Component properties
 * @param {ActionPanelConfig[]} props.configs - Array of control configurations
 * @param {ActionPanelConfig} props.activeConfig - Currently active control
 * @param {Function} props.setActiveConfig - Handler to update active control
 * @param {boolean} props.isOverlayVisible - Overlay visibility state
 * @param {Function} props.setIsOverlayVisible - Handler to update overlay visibility
 *
 * @example
 * ```tsx
 * <ActionPanelControls
 *   configs={[
 *     {
 *       id: 'settings',
 *       icon: 'settings',
 *       actionLevel: 'topControls',
 *       title: 'Settings'
 *     },
 *     {
 *       id: 'profile',
 *       icon: 'user',
 *       actionLevel: 'bottomControls',
 *       title: 'Profile'
 *     }
 *   ]}
 *   activeConfig={currentConfig}
 *   setActiveConfig={handleConfigChange}
 *   isOverlayVisible={isVisible}
 *   setIsOverlayVisible={setVisible}
 * />
 * ```
 */
export function ActionPanelControls({
  configs = [],
  activeConfig,
}: // setActiveConfig,
// isOverlayVisible,
// setIsOverlayVisible,
Props) {
  const { setActiveConfig, setIsOverlayVisible, isOverlayVisible } =
    useActionPanelStore();
  /**
   * Memoized array of top-level controls filtered by actionLevel
   */
  const localConfigs = useMemo(
    () =>
      configs.filter(
        (config: ActionPanelConfig) => config?.actionLevel === 'topControls'
      ),
    [configs]
  );

  /**
   * Memoized array of bottom-level controls filtered by actionLevel
   */
  const globalConfigs = useMemo(
    () =>
      configs.filter(
        (config: ActionPanelConfig) => config?.actionLevel === 'bottomControls'
      ),
    [configs]
  );

  return (
    <Container data-testid="action-panel-controls">
      <TopControls data-testid="top-controls">
        {localConfigs.map((config: ActionPanelConfig, idx: number) => (
          <ActionPanelControl
            key={config.icon as any}
            config={config}
            activeConfig={activeConfig}
            setActiveConfig={setActiveConfig}
            isOverlayVisible={isOverlayVisible}
            setIsOverlayVisible={setIsOverlayVisible}
          />
        ))}
      </TopControls>
      <BottomControls data-testid="bottom-controls">
        {globalConfigs.map((config: ActionPanelConfig, idx: number) => (
          <ActionPanelControl
            key={config.icon as any}
            config={config}
            activeConfig={activeConfig}
            setActiveConfig={setActiveConfig}
            isOverlayVisible={isOverlayVisible}
            setIsOverlayVisible={setIsOverlayVisible}
          />
        ))}
      </BottomControls>
    </Container>
  );
}
