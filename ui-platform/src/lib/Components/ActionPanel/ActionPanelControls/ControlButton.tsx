import { useCallback, useState } from 'react';
import styled, { useTheme } from 'styled-components';
import { ActionPanelConfig } from '../../../Engine/models/action-panel.config';
import { SquareButton } from '../../Buttons/SquareButton/SquareButton';
import { useActionPanelStore } from '../hooks/useActionPanel';

interface Props {
  config: ActionPanelConfig;
  setIsOverlayVisible?:
    | React.Dispatch<React.SetStateAction<boolean>>
    | ((visible: boolean) => void);
  isOverlayVisible?: boolean;
  activeConfig?: ActionPanelConfig;
  setActiveConfig?:
    | React.Dispatch<React.SetStateAction<any>>
    | ((activeConfig: any) => void);
  // active: boolean;
  disabled?: boolean;
  className?: string;
}

/**
 * A React component that renders a control button for the action panel.
 *
 * It takes in an action panel configuration, a boolean indicating whether the overlay is visible,
 * functions to set the overlay visibility and the active configuration, a boolean indicating whether
 * the button is disabled, and a class name.
 *
 * The button is rendered as a SquareButton, with the icon and color from the action panel configuration.
 * If the button is disabled, the color is set to a disabled color.
 * When the button is clicked, the active configuration is set to the given configuration, and the
 * overlay visibility is toggled if the active configuration changes.
 *
 * @param {ActionPanelConfig} config - The action panel configuration for this button.
 * @param {boolean} isOverlayVisible - A boolean indicating whether the overlay is visible.
 * @param {React.Dispatch<React.SetStateAction<boolean>>} setIsOverlayVisible - A function to set the overlay visibility.
 * @param {ActionPanelConfig} activeConfig - The currently active action panel configuration.
 * @param {React.Dispatch<React.SetStateAction<any>>} setActiveConfig - A function to set the active configuration.
 * @param {boolean} disabled - A boolean indicating whether the button is disabled.
 * @param {string} className - A class name for the button.
 * @returns {JSX.Element} The rendered button.
 */

export function ControlButton({
  config,
  // isOverlayVisible,
  // setIsOverlayVisible,
  // activeConfig,
  // setActiveConfig,
  // active,
  disabled,
  className,
}: Props) {
  const color = useTheme().ColorsIconColorPrimary;
  const disabledColor = useTheme().ColorsIconColorTertiary;
  const {
    isOverlayVisible,
    setActiveConfig,
    setIsOverlayVisible,
    activeConfig,
  } = useActionPanelStore();

  const actionHandler = useCallback(
    (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
      if (config) {
        setActiveConfig(config);
        if (activeConfig?.icon !== config.icon) {
          if (!isOverlayVisible) {
            setIsOverlayVisible(!isOverlayVisible);
          }
        } else {
          setIsOverlayVisible(!isOverlayVisible);
        }
        setActiveConfig(config);
      }
    },
    [
      config,
      activeConfig,
      setActiveConfig,
      isOverlayVisible,
      setIsOverlayVisible,
    ]
  );

  return (
    <SquareButton
      className={className}
      icon={config.icon}
      color={disabled ? disabledColor : color}
      disabled={disabled}
      active={activeConfig?.icon === config?.icon && isOverlayVisible}
      onClick={actionHandler}
    />
  );
}
