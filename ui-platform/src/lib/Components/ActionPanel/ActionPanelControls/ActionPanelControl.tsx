/**
 * ActionPanelControl is a styled button component specifically designed for use in
 * the ActionPanel's control section. It extends ControlButton with custom styling
 * for the action panel context.
 *
 * Features:
 * - Transparent background and border by default
 * - Theme-aware hover state styling
 * - Inherits ControlButton functionality
 * - Excludes active, className, and disabled props from base component
 *
 * @styled-component
 * @component
 *
 * @example
 * ```tsx
 * // Basic usage
 * <ActionPanelControl
 *   onClick={() => console.log('Control clicked')}
 * >
 *   Settings
 * </ActionPanelControl>
 * ```
 *
 * @example
 * ```tsx
 * // With icon
 * <ActionPanelControl
 *   icon="settings"
 *   onClick={() => console.log('Settings clicked')}
 * >
 *   Settings
 * </ActionPanelControl>
 * ```
 */
import styled from 'styled-components';
import { ControlButton } from './ControlButton';

export const ActionPanelControl = styled(ControlButton)<
  Omit<typeof ControlButton, 'active' | 'className' | 'disabled'>
>`
  /* add global styles here for the control button */
  background-color: transparent;
  border-color: transparent;
  &:hover {
    background-color: ${(props) =>
      props?.theme.ColorsButtonColorActionPanelHover};
  }
`;
