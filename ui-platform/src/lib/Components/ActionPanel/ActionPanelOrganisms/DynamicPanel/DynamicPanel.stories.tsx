import type { Meta, StoryObj } from '@storybook/react';
import { VariantsType, withComponentShowcase } from '../../../../Utilities';
import { Titles } from '../../UserPanelHeader/UserPanelHeader';
import { DynamicPanel } from './DynamicPanel';

const meta: Meta<typeof DynamicPanel> = {
  component: DynamicPanel,
  title: 'Components/ActionPanel/DynamicPanel',
};
export default meta;
type Story = StoryObj<typeof DynamicPanel>;

export const Overview: Story = {
  args: { title: 'Messages', children: 'Action panel content' },
  argTypes: { title: { control: 'select', options: Titles } },
};

export const DifferentTitles: Story = {
  args: { title: 'Messages', children: 'test' },
  argTypes: { ...Overview.argTypes },
  render: (args) =>
    withComponentShowcase(<DynamicPanel {...args} />)(
      'title',
      Titles as unknown as VariantsType,
      false
    ),
};
