import { ComponentPropsWithRef } from 'react';
import {
  UserPanelHeader,
  UserPanelHeaderProps,
} from '../../UserPanelHeader/UserPanelHeader';
import { ContentWrapper, DynamicPanelContainer } from './DynamicPanelContainer';

export interface DynamicPanelProps
  extends UserPanelHeaderProps,
    Omit<ComponentPropsWithRef<'div'>, 'title'> {}

/**
 * A component that renders a dynamic action panel content.
 * It can be used in workflows and views.
 *
 * @param {DynamicPanelProps} props The component props.
 * @param {string} props.title The title of the panel.
 * @param {React.ReactNode} props.children The content of the panel.
 * @param {ComponentPropsWithRef<'div'>} [props] Other props to be passed to the container.
 * @returns {JSX.Element} The rendered dynamic action panel.
 */
export function DynamicPanel({ title, children, ...props }: DynamicPanelProps) {
  return (
    <DynamicPanelContainer {...props}>
      <ContentWrapper>
        <UserPanelHeader title={title} />
        {children}
      </ContentWrapper>
    </DynamicPanelContainer>
  );
}
