import styled from 'styled-components';

/**
 * Container for the dynamic panel.
 * Applies a blurred background, scrollbar styling, and layout adjustments.
 * Wrapper for the content inside the dynamic panel.
 * Uses CSS Grid layout to manage child elements.
 * DynamicPanelContainer: The outer container that provides layout and styling for the dynamic panel.
 * ContentWrapper: The inner wrapper that manages the layout and padding for the content inside the panel
 */

export const DynamicPanelContainer = styled.div`
  width: 100%;
  position: relative;
  backdrop-filter: blur(4px);
  background-color: rgba(46, 50, 54, 0.93);
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  scrollbar-width: thin;
  display: grid;
  grid-template-rows: auto;
  justify-items: center;
  padding: 0px 8px;
  box-sizing: border-box;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
`;

export const ContentWrapper = styled.div`
  border-radius: 15px;
  overflow-x: hidden;
  overflow-y: auto;
  display: grid;
  grid-template-rows: repeat(auto-fill, minmax(100px, 1fr));
  justify-items: center;
  align-items: start;
  width: 100%;
  height: 100%;
`;
