import type { Meta, StoryObj } from '@storybook/react';
import { ActionPanel } from './ActionPanel';

const meta: Meta<typeof ActionPanel> = {
  component: ActionPanel,
  title: 'Components/ActionPanel/ActionPanel',
};
export default meta;
type Story = StoryObj<typeof ActionPanel>;

const notes = [
  {
    title: 'Find Invoice',
    date: '05/10/23',
    time: '11:42',
    content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. ',
  },
  {
    title: 'Find Invoice',
    date: '05/10/23',
    time: '11:42',
    content:
      'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
  },
  {
    title: 'Find Invoice',
    date: '05/10/23',
    time: '11:42',
    content:
      'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
  },
];

const reminders = [
  {
    background: 'primary',
    timeOfReminder: '01:00',
    reminderDate: '2024-07-01',
    description: 'This is a Description',
    label: 'This is a label',
  },
  {
    background: 'primary',
    timeOfReminder: '09:30',
    reminderDate: '2024-07-03',
    description: 'This is a Description',
    label: 'This is a label',
  },
  {
    background: 'primary',
    timeOfReminder: '00:01',
    reminderDate: '2024-07-09',
    description: 'This is a Description',
    label: 'This is a label',
  },
  {
    background: 'primary',
    timeOfReminder: '01:00',
    reminderDate: '2024-07-01',
    description: 'This is a Description',
    label: 'This is a label',
  },
  {
    background: 'primary',
    timeOfReminder: '09:30',
    reminderDate: '2024-07-03',
    description: 'This is a Description',
    label: 'This is a label',
  },
  {
    background: 'primary',
    timeOfReminder: '00:01',
    reminderDate: '2024-07-09',
    description: 'This is a Description',
    label: 'This is a label',
  },
  {
    background: 'primary',
    timeOfReminder: '01:00',
    reminderDate: '2024-07-01',
    description: 'This is a Description',
    label: 'This is a label',
  },
  {
    background: 'primary',
    timeOfReminder: '09:30',
    reminderDate: '2024-07-03',
    description: 'This is a Description',
    label: 'This is a label',
  },
  {
    background: 'primary',
    timeOfReminder: '00:01',
    reminderDate: '2024-07-09',
    description: 'This is a Description',
    label: 'This is a label',
  },
];

const messages = [
  {
    username: 'Thabo Mabikho 1',
    content: 'This is some message with text',
    time: '13:24',
  },
  {
    username: 'Thabo Mabikho 2',
    content: 'This is some message with text',
    time: '13:26',
  },
  {
    username: 'Thabo Mabikho 3',
    content: 'This is some message with text',
    time: '13:34',
  },
];

const documents = [
  {
    heading: 'Photos',
    content: [
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'image',
        TeamMemberName: 'TeamMemberName',
        date: '2024/08/05',
        time: '11:69',
      },
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'image',
        TeamMemberName: 'TeamMemberName',
        date: '2024/08/05',
        time: '11:69',
      },
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'icon',
        TeamMemberName: 'TeamMemberName',
        date: '2024/08/05',
        time: '11:69',
      },
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'image',
        TeamMemberName: 'TeamMemberName',
        date: '2024/08/05',
        time: '11:69',
      },
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'image',
        TeamMemberName: 'TeamMemberName',
        date: '2024/08/05',
        time: '11:69',
      },
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'icon',
        TeamMemberName: 'TeamMemberName',
        date: '2024/08/05',
        time: '11:69',
      },
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'image',
        TeamMemberName: 'TeamMemberName',
        date: '2024/08/05',
        time: '11:69',
      },
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'image',
        TeamMemberName: 'TeamMemberName',
        date: '2024/08/05',
        time: '11:69',
      },
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'icon',
        TeamMemberName: 'TeamMemberName',
        date: '2024/08/05',
        time: '11:69',
      },
    ],
    isOpen: 'false',
    onClick: console.log('clicked'),
    layout: '',
  },
  {
    heading: 'Documents',
    content: [
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'image',
        TeamMemberName: 'TeamMemberName',
        date: '2024/08/05',
        time: '11:69',
      },
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'image',
        TeamMemberName: 'TeamMemberName',
        date: '2024/08/05',
        time: '11:69',
      },
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'icon',
        TeamMemberName: 'TeamMemberName',
        date: '2024/08/05',
        time: '11:69',
      },
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'image',
        TeamMemberName: 'TeamMemberName',
        date: '2024/08/05',
        time: '11:69',
      },
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'image',
        TeamMemberName: 'TeamMemberName',
        date: '2024/08/05',
        time: '11:69',
      },
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'icon',
        TeamMemberName: 'TeamMemberName',
        date: '2024/08/05',
        time: '11:69',
      },
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'image',
        TeamMemberName: 'TeamMemberName',
        date: '2024/08/05',
        time: '11:69',
      },
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'image',
        TeamMemberName: 'TeamMemberName',
        date: '2024/08/05',
        time: '11:69',
      },
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'icon',
        TeamMemberName: 'TeamMemberName',
        date: '2024/08/05',
        time: '11:69',
      },
    ],
    isOpen: 'false',
    onClick: console.log('clicked'),
    layout: '',
  },
];

export const ActionPanelView: Story = {
  args: {
    configs: [
      // Reminders
      {
        icon: 'bell-02',
        title: 'Reminders', //?actionPanel=Messages--bell-02

        // fetchCalls: [],
        layout: {},
        onEnter: [],
        onLeave: [],
        fragments: [
          {
            component: 'ReminderView',
            layout: {
              marginTop: '10px',
              marginLeft: '10px',
              marginRight: '10px',
            },
            props: { reminders },
          },
        ],
        actionLevel: 'bottomControls',
      },
      // Messages
      {
        icon: 'message-check-square',
        title: 'Messages', //?actionPanel=Messages--bell-02

        // fetchCalls: [],
        layout: {},
        onEnter: [],
        onLeave: [],
        fragments: [
          {
            component: 'MessagesView',
            layout: {
              marginTop: '10px',
              marginLeft: '10px',
              marginRight: '15px',
            },
            props: { messages },
          },
        ],
        actionLevel: 'bottomControls',
      },
      // Documents
      {
        icon: 'file-07',
        title: 'Documents',
        layout: {},
        onEnter: [],
        onLeave: [],
        fragments: [
          {
            component: 'DocumentsView',
            layout: {
              marginTop: '10px',
              marginLeft: '10px',
              marginRight: '10px',
            },
            props: { documents },
          },
        ],
        actionLevel: 'bottomControls',
      },
      // Scratchpad
      {
        icon: 'plus',
        title: 'Scratch Pad', //?actionPanel=Messages--bell-02
        // fetchCalls: [],
        layout: {},
        onEnter: [],
        onLeave: [],
        fragments: [
          {
            component: 'ScratchPadView',
            layout: { marginLeft: '10px', marginRight: '10px' },
            props: {
              titlePlaceholder: 'Heading',
              icon: 'trash-01',
              iconHandler: (data: { heading: string; body: string }) =>
                console.log(
                  'got data: Heading - ' + data.heading + ' Body - ' + data.body
                ),
              placeHolder: 'Text here...',
            },
          },
        ],
        actionLevel: 'bottomControls',
      },
    ],
  },
};

export const ActionPanelTopControls: Story = {
  args: {
    configs: [
      {
        icon: 'bell-02',
        title: 'Messages',

        // fetchCalls: [],
        layout: {},
        onEnter: [],
        onLeave: [],
        fragments: [
          {
            component: 'ChatNotificationList',
            layout: {
              marginTop: '10px',
              marginLeft: '10px',
              marginRight: '15px',
            },
            props: { messages },
          },
          {
            component: 'NoteCardList',
            layout: {
              marginTop: '20px',
              marginLeft: '10px',
              marginRight: '10px',
            },
            props: { notes },
          },
        ],
        actionLevel: 'topControls',
      },
      {
        icon: 'bell-02',
        title: 'Messages',
        // fetchCalls: [],
        layout: {},
        onEnter: [],
        onLeave: [],
        fragments: [
          {
            component: 'ChatNotificationList',
            layout: {
              marginTop: '10px',
              marginLeft: '10px',
              marginRight: '15px',
            },
            props: { messages },
          },
          {
            component: 'NoteCardList',
            layout: {
              marginTop: '20px',
              marginLeft: '10px',
              marginRight: '10px',
            },
            props: { notes },
          },
        ],
        actionLevel: 'topControls',
      },
      {
        icon: 'trash-01',
        title: 'Scratch Pad',
        layout: {},
        onEnter: [],
        onLeave: [],
        fragments: [
          {
            component: 'ChatNotificationList',
            layout: {
              marginTop: '10px',
              marginLeft: '10px',
              marginRight: '15px',
            },
            props: { messages },
          },
          {
            component: 'NoteCardList',
            layout: {
              marginTop: '20px',
              marginBottom: '20px',
              marginLeft: '10px',
              marginRight: '10px',
            },
            props: { notes },
          },
        ],
        actionLevel: 'topControls',
      },
    ],
  },
};

export const ActionPanelMultipleControls: Story = {
  args: {
    configs: [
      {
        icon: 'trash-01',
        title: 'Another Scratch', //?actionPanel=Messages--bell-02

        // fetchCalls: [],
        layout: {},
        onEnter: [],
        onLeave: [],
        fragments: [
          {
            component: 'ChatNotificationList',
            layout: {
              marginTop: '10px',
              marginLeft: '10px',
              marginRight: '15px',
            },
            props: { messages },
          },
        ],
        actionLevel: 'bottomControls',
      },
      {
        icon: 'bell-02',
        title: 'Messages', //?actionPanel=Messages--bell-02

        // fetchCalls: [],
        layout: {},
        onEnter: [],
        onLeave: [],
        fragments: [
          {
            component: 'ChatNotificationList',
            layout: {
              marginTop: '10px',
              marginLeft: '10px',
              marginRight: '15px',
            },
            props: { messages },
          },
          {
            component: 'NoteCardList',
            layout: {
              marginTop: '20px',
              marginLeft: '10px',
              marginRight: '10px',
            },
            props: { notes },
          },
        ],
        actionLevel: 'bottomControls',
      },
      {
        icon: 'bell-02',
        title: 'Messages',
        // fetchCalls: [],
        layout: {},
        onEnter: [],
        onLeave: [],
        fragments: [
          {
            component: 'ChatNotificationList',
            layout: {
              marginTop: '10px',
              marginLeft: '10px',
              marginRight: '15px',
            },
            props: { messages },
          },
          {
            component: 'NoteCardList',
            layout: {
              marginTop: '20px',
              marginLeft: '10px',
              marginRight: '10px',
            },
            props: { notes },
          },
        ],
        actionLevel: 'topControls',
      },
      {
        icon: 'trash-01',
        title: 'Scratch Pad',
        layout: {},
        onEnter: [],
        onLeave: [],
        fragments: [
          {
            component: 'NoteCardList',
            layout: {
              marginTop: '20px',
              marginBottom: '20px',
              marginLeft: '10px',
              marginRight: '10px',
            },
            props: { notes },
          },
          {
            component: 'ChatNotificationList',
            layout: {
              marginTop: '10px',
              marginLeft: '10px',
              marginRight: '15px',
            },
            props: { messages },
          },
        ],
        actionLevel: 'topControls',
      },
    ],
  },
};
