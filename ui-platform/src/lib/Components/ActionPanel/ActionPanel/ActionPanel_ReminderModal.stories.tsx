import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { ButtonRow, FormBuilder, Text } from '../../../Fragments';
import { AlertModal } from '../../AlertModal/AlertModal';

const components = {
  Text,
  ButtonRow,
  FormBuilder,
};

const meta: Meta<typeof AlertModal> = {
  component: AlertModal,
  title: 'Components/ActionPanel/ReminderModal',
};

export default meta;
type Story = StoryObj<typeof AlertModal>;

export const MyProfile_UpdateProfile_ChangePasswordAlertModal: Story = {
  args: {
    display: true,
    type: 'warning',
    heading: 'Add New Reminder',
    headingType: 'page-heading',
    layout: { width: '600px' },
    callClientAction: (config: any) => console.log('Client action:', config), // Add this
    onEnter: [],
    onLeave: [],
    fragments: [
      {
        component: 'FormBuilder',
        layout: {
          display: 'grid',
          justifyItems: 'center',
        },
        props: {
          config: {
            style: {
              display: 'grid',
              gridTemplateColumns: 'repeat(2, 1fr)',
              rowGap: '1rem',
              columnGap: '1rem',
              width: '819px',
            },
            controls: [
              {
                type: 'plain-text',
                name: 'reminder_label',
                label: 'Text',
                css: {
                  wrapper: { gridColumn: '1', gridRow: '1' },
                },
              },
              {
                type: 'single-select',
                name: 'channel',
                label: 'Channel',
                labelProp: 'description',
                valueProp: 'id',
                options: {
                  source: 'literal',
                  data: [
                    {
                      id: 1,
                      description: 'General',
                      quantity: 1,
                      unitPrice: 0,
                    },
                    {
                      id: 2,
                      description: 'Phone',
                      quantity: 1,
                      compulsoryItem: false,
                    },
                    {
                      id: 3,
                      description: 'Email',
                      quantity: 1,
                      compulsoryItem: false,
                      unitPrice: 0,
                    },
                  ],
                },
                css: { wrapper: { gridColumn: '2', gridRow: '1' } },
              },
              {
                type: 'datepicker',
                name: 'reminder_date',
                label: 'Date',
                placeholder: 'Select data for reminder',
                css: { wrapper: { gridColumn: '1', gridRow: '2' } },
              },
              {
                type: 'plain-text',
                name: 'time',
                label: 'Time',
                css: { wrapper: { gridColumn: '2', gridRow: '2' } },
              },
              {
                type: 'textarea',
                name: 'what_matters',
                label: 'Reminder Message',
                placeholder: 'Reminder Message',
                css: { wrapper: { gridColumn: '1 / span 2', gridRow: '3' } },
              },
              {
                type: 'plain-text',
                name: 'link_to_claim',
                label: 'Link to claim',
                icon: 'search-sm',
                position: 'right',
                css: { wrapper: { gridColumn: '1 / span 2', gridRow: '4' } },
              },
            ],
          },
        },
      },
      {
        component: 'ButtonRow',
        layout: { marginTop: '80px' },
        props: {
          buttons: [
            {
              btnValue: 'Cancel',
              // onClick: () => {
              //   console.log('Clicked decision 1');
              // },
            },
            {
              btnValue: 'Add Reminder',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'log',
                  payload: {
                    key: "reminders",
                    operation: "push",
                    value: {
                      title: "{{formValues.reminder_label}}",
                      channel: "{{formValues.channel}}",
                      date: "{{formValues.reminder_date}}",
                      time: "{{formValues.time}}",
                      message: "{{formValues.what_matters}}",
                      link: "{{formValues.link_to_claim}}",
                    }
                  }
                },
                {
                  type: 'clientAction',
                  action: 'closeModal'
                }
              ]
            },
          ],
          callClientAction:  (config: any) => console.log('Client action:', config)
        },
      },
    ],
    componentMap: components,
    onClose: () => console.log('closing...'),
    navs: [],
    fetcher: { state: 'idle', data: null },
    submit: () => {},
  },
};
