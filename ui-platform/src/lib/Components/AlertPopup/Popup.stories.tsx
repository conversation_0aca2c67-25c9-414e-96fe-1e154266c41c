import type { Meta, StoryObj } from '@storybook/react';
import { AlertPopup } from './Popup';

const meta: Meta<typeof AlertPopup> = {
  component: AlertPopup,
  title: 'Components/AlertPopup',
};
export default meta;
type Story = StoryObj<typeof AlertPopup>;

export const ErrorPopup: Story = {
    args: { type: 'error' },
};

export const WarningPopup: Story = {
  args: { type: 'warning' },
};

export const RegistrationPopup: Story = {
    args: { type: 'registration' },
};
