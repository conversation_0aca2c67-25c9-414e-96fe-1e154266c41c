import styled, { css } from 'styled-components';
import { ReactNode } from 'react';

/**
 * Props for the AlertPopup component
 * @typedef {Object} PopupProps
 * @property {'error' | 'warning' | 'registration'} type - The type of popup to display.
 * @property {ReactNode} [children] - Content to be displayed inside the popup.
 * @property {string} [className] - Additional class names for styling.
 * 
 * Styled component for the popup wrapper.
 * Uses CSS grid layout and consumes design tokens for styling.
 * AlertPopup component that displays different types of popup (error, warning, registration).
 *
 * @component
 * @param {PopupProps} props - The props for the component.
 * @returns {JSX.Element} The rendered AlertPopup component.
 */
interface PopupProps {
  type: 'error' | 'warning' | 'registration';
  children?: ReactNode;
  className?: string;
}

const Popups = styled.div<PopupProps>`
  width: 100vw;
  height: 100vh;
  position: absolute;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0px 0px 80px 10px ${(props) => props.theme.ColorsInputsInverse};
  background-color: ${(props) => props?.theme.ColorsOverlaySurfaceOverlay};

  ${(props) =>
    props.type === 'registration' &&
    css`
      & > .registration-container {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 12px;
        overflow: hidden;
        border: 1px solid;

        & > .effect-1,
        & > .effect-2,
        & > .effect-3,
        & > .effect-4,
        & > .effect-5,
        & > .effect-6 {
          position: absolute;
          background: radial-gradient(
            100% 100% at 50% 50%,
            #2f5583 0%,
            rgba(129.05, 237.08, 251.81, 0.43) 100%
          );
          box-shadow: 100px 100px 100px;
          border-radius: 50%;
          filter: blur(100px);
        }

        & > .effect-1 {
          width: 500px;
          height: 500px;
          top: -50px;
          left: -50px;
        }

        & > .effect-2 {
          width: 600px;
          height: 600px;
          top: 200px;
          right: -100px;
        }

        & > .effect-3 {
          width: 1000px;
          height: 1000px;
          bottom: -200px;
          left: -200px;
        }

        & > .effect-4 {
          width: 1000px;
          height: 1000px;
          top: -300px;
          right: -300px;
          background: linear-gradient(
            180deg,
            #332828 0%,
            rgba(154.06, 34.66, 34.66, 0.29) 100%
          );
        }

        & > .effect-5 {
          width: 800px;
          height: 800px;
          bottom: 0;
          left: -150px;
          transform: rotate(25deg);
          transform-origin: center;
          background: radial-gradient(
            100% 100% at 50% 50%,
            #0c0f2b 0%,
            rgba(22.91, 98.32, 211.44, 0.38) 100%
          );
        }

        & > .effect-6 {
          width: 1200px;
          height: 1200px;
          top: -400px;
          right: -400px;
          transform: rotate(30deg);
          transform-origin: center;
          background: linear-gradient(
            180deg,
            #302727 0%,
            rgba(14.72, 97.87, 133.5, 0.54) 96%,
            rgba(137.06, 13.71, 13.71, 0.35) 100%
          );
        }
      }
    `}
`;

/**
 * AlertPopup component
 * @param type - type of popup, defaults to 'error'
 * @param children - content of the popup
 * @param className - additional class for the popup
 */
export const AlertPopup = ({ type = 'error', children, className }: PopupProps) => {
  return (
    <Popups type={type} className={className}>
      {type === 'registration' && (
        <div className="registration-container">
          <div className="effect-1"></div>
          <div className="effect-2"></div>
          <div className="effect-3"></div>
          <div className="effect-4"></div>
          <div className="effect-5"></div>
          <div className="effect-6"></div>
        </div>
      )}
      {children}
    </Popups>
  );
};