import { withComponentShowcase } from '../../Utilities';
import type { Meta, StoryObj } from '@storybook/react';
import styled from 'styled-components';
import { Icon } from './Icon';
import { svgs } from './svgs';

const meta: Meta<typeof Icon> = {
  component: Icon,
  title: 'Icons/Icon',
  argTypes: {
    color: {
      control: 'color',
    },
    fill: {
      control: 'color',
    },
  },
};
export default meta;
type Story = StoryObj<typeof Icon>;

export const IconWithStroke: Story = {
  args: {
    type: 'arrow-block-left',
    color: 'white',
    size: 24,
  },
};

export const IconWithFill: Story = {
  args: {
    type: 'arrow-tip',
    fill: 'white',
    size: 24,
  },
};

const icons = Object.keys(svgs);

const IconsContainer = styled.div<{ children: any }>`
  width: 50vw;
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  padding: 1rem;
  box-shadow: rgba(149, 157, 165, 0.2) 0 8px 24px;
`;

const IconContainer = styled.div<{ children: any }>`
  width: 10%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: auto;
`;

export const IconsGlossary: Story = {
  render: (args) =>
    withComponentShowcase(<Icon {...args} />)('type', icons, true, {
      showPropName: false,
    }),
  args: {},
};
