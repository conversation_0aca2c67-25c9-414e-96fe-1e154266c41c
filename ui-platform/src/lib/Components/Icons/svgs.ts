type paths = {
  d: string;
  fill: string;
  stroke: string;
  strokeWidth: string;
  clipRule?: 'evenodd' | 'nonzero' | 'none';
  fillRule?: 'evenodd' | 'nonzero' | 'none';
  strokeLinecap?: 'round' | 'inherit' | 'butt' | 'square' | 'none';
  strokeLinejoin?: 'round' | 'inherit' | 'miter' | 'bevel' | 'none';
};

export type SVG = {
  [key: string]: {
    xmlns?: string;
    viewBox?: string;
    fill?: string;
    width?: string | number;
    height?: string | number;
    paths: paths[];
  };
};

export const svgs = {
  'pencil-01': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M2.5 21.5001L8.04927 19.3657C8.40421 19.2292 8.58168 19.161 8.74772 19.0718C8.8952 18.9927 9.0358 18.9013 9.16804 18.7987C9.31692 18.6831 9.45137 18.5487 9.72028 18.2798L21 7.00006C22.1046 5.89549 22.1046 4.10463 21 3.00006C19.8955 1.89549 18.1046 1.89549 17 3.00006L5.72028 14.2798C5.45138 14.5487 5.31692 14.6831 5.20139 14.832C5.09877 14.9643 5.0074 15.1049 4.92823 15.2523C4.83911 15.4184 4.77085 15.5959 4.63433 15.9508L2.5 21.5001ZM2.5 21.5001L4.55812 16.149C4.7054 15.7661 4.77903 15.5746 4.90534 15.4869C5.01572 15.4103 5.1523 15.3813 5.2843 15.4065C5.43533 15.4354 5.58038 15.5804 5.87048 15.8705L8.12957 18.1296C8.41967 18.4197 8.56472 18.5648 8.59356 18.7158C8.61877 18.8478 8.58979 18.9844 8.51314 19.0947C8.42545 19.2211 8.23399 19.2947 7.85107 19.442L2.5 21.5001Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },
  'arrow-block-right': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M21 12L14 5V9H3.8C3.51997 9 3.37996 9 3.273 9.0545C3.17892 9.10243 3.10243 9.17892 3.0545 9.273C3 9.37996 3 9.51997 3 9.8V14.2C3 14.48 3 14.62 3.0545 14.727C3.10243 14.8211 3.17892 14.8976 3.273 14.9455C3.37996 15 3.51997 15 3.8 15H14V19L21 12Z',
        strokeWidth: '2',
        stroke: '#919191',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },

  'arrow-block-left': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M3 12L10 5V9H20.2C20.48 9 20.62 9 20.727 9.0545C20.8211 9.10243 20.8976 9.17892 20.9455 9.273C21 9.37996 21 9.51997 21 9.8V14.2C21 14.48 21 14.62 20.9455 14.727C20.8976 14.8211 20.8211 14.8976 20.727 14.9455C20.62 15 20.48 15 20.2 15H10V19L3 12Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },

  'chevron-up-double': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M17 18L12 13L7 18M17 11L12 6L7 11',
        fill: 'none',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    ],
  },

  'chevron-down-double': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M7 13L12 18L17 13M7 6L12 11L17 6',
        fill: 'none',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    ],
  },

  'star-rating': {
    width: '24',
    height: '24',
    viewBox: '0 0 12 13',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M5.64133 2.22629C5.75657 1.99282 5.81419 1.87609 5.89241 1.83879C5.96047 1.80634 6.03954 1.80634 6.1076 1.83879C6.18583 1.87609 6.24345 1.99282 6.35869 2.22629L7.45203 4.44128C7.48605 4.5102 7.50306 4.54466 7.52792 4.57142C7.54994 4.59511 7.57633 4.61431 7.60566 4.62794C7.63878 4.64335 7.6768 4.6489 7.75286 4.66002L10.1985 5.01749C10.456 5.05513 10.5848 5.07395 10.6444 5.13685C10.6962 5.19158 10.7206 5.26678 10.7108 5.34152C10.6994 5.42742 10.6062 5.51822 10.4197 5.69982L8.65072 7.42284C8.59558 7.47655 8.56801 7.50341 8.55021 7.53536C8.53446 7.56366 8.52435 7.59474 8.52046 7.62689C8.51605 7.66319 8.52256 7.70113 8.53557 7.777L8.95298 10.2107C8.997 10.4674 9.01902 10.5957 8.97765 10.6719C8.94166 10.7381 8.87768 10.7846 8.80353 10.7984C8.71831 10.8142 8.60306 10.7536 8.37256 10.6323L6.18619 9.48255C6.11807 9.44673 6.08401 9.42881 6.04812 9.42178C6.01635 9.41554 5.98367 9.41554 5.9519 9.42178C5.91601 9.42881 5.88195 9.44673 5.81383 9.48255L3.62746 10.6323C3.39696 10.7536 3.28171 10.8142 3.19649 10.7984C3.12234 10.7846 3.05836 10.7381 3.02237 10.6719C2.981 10.5957 3.00301 10.4674 3.04704 10.2107L3.46444 7.777C3.47746 7.70113 3.48396 7.66319 3.47956 7.62689C3.47566 7.59474 3.46556 7.56366 3.4498 7.53536C3.43201 7.50341 3.40444 7.47655 3.34929 7.42284L1.58028 5.69982C1.39383 5.51822 1.30061 5.42742 1.28926 5.34152C1.27939 5.26678 1.30378 5.19158 1.35563 5.13685C1.41522 5.07395 1.54399 5.05513 1.80152 5.01749L4.24716 4.66002C4.32321 4.6489 4.36124 4.64335 4.39436 4.62794C4.42368 4.61431 4.45008 4.59511 4.47209 4.57142C4.49695 4.54466 4.51396 4.5102 4.54799 4.44128L5.64133 2.22629Z',
        fill: '#B28511',
        stroke: '#B28511',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    ],
  },

  'chevron-right': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M9 18L15 12L9 6',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },

  'chevron-left': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M15 18L9 12L15 6',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },

  'users-plus': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M19 21V15M16 18H22M12 15H8C6.13623 15 5.20435 15 4.46927 15.3045C3.48915 15.7105 2.71046 16.4892 2.30448 17.4693C2 18.2044 2 19.1362 2 21M15.5 3.29076C16.9659 3.88415 18 5.32131 18 7C18 8.67869 16.9659 10.1159 15.5 10.7092M13.5 7C13.5 9.20914 11.7091 11 9.5 11C7.29086 11 5.5 9.20914 5.5 7C5.5 4.79086 7.29086 3 9.5 3C11.7091 3 13.5 4.79086 13.5 7Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },

  'user-circle': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M5.3163 19.4384C5.92462 18.0052 7.34492 17 9 17H15C16.6551 17 18.0754 18.0052 18.6837 19.4384M16 9.5C16 11.7091 14.2091 13.5 12 13.5C9.79086 13.5 8 11.7091 8 9.5C8 7.29086 9.79086 5.5 12 5.5C14.2091 5.5 16 7.29086 16 9.5ZM22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },

  'chevron-left-double': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M18 17L13 12L18 7M11 17L6 12L11 7',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },

  'chevron-right-double': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M6 17L11 12L6 7M13 17L18 12L13 7',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },

  'user-square': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M4.00002 21.8174C4.6026 22 5.41649 22 6.8 22H17.2C18.5835 22 19.3974 22 20 21.8174M4.00002 21.8174C3.87082 21.7783 3.75133 21.7308 3.63803 21.673C3.07354 21.3854 2.6146 20.9265 2.32698 20.362C2 19.7202 2 18.8802 2 17.2V6.8C2 5.11984 2 4.27976 2.32698 3.63803C2.6146 3.07354 3.07354 2.6146 3.63803 2.32698C4.27976 2 5.11984 2 6.8 2H17.2C18.8802 2 19.7202 2 20.362 2.32698C20.9265 2.6146 21.3854 3.07354 21.673 3.63803C22 4.27976 22 5.11984 22 6.8V17.2C22 18.8802 22 19.7202 21.673 20.362C21.3854 20.9265 20.9265 21.3854 20.362 21.673C20.2487 21.7308 20.1292 21.7783 20 21.8174M4.00002 21.8174C4.00035 21.0081 4.00521 20.5799 4.07686 20.2196C4.39249 18.6329 5.63288 17.3925 7.21964 17.0769C7.60603 17 8.07069 17 9 17H15C15.9293 17 16.394 17 16.7804 17.0769C18.3671 17.3925 19.6075 18.6329 19.9231 20.2196C19.9948 20.5799 19.9996 21.0081 20 21.8174M16 9.5C16 11.7091 14.2091 13.5 12 13.5C9.79086 13.5 8 11.7091 8 9.5C8 7.29086 9.79086 5.5 12 5.5C14.2091 5.5 16 7.29086 16 9.5Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },

  'qr-code-01': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M7 12H12V17M3.01 12H3M8.01 17H8M12.01 21H12M21.01 12H21M3 17H4.5M15.5 12H17.5M3 21H8M12 2V8M17.6 21H19.4C19.9601 21 20.2401 21 20.454 20.891C20.6422 20.7951 20.7951 20.6422 20.891 20.454C21 20.2401 21 19.9601 21 19.4V17.6C21 17.0399 21 16.7599 20.891 16.546C20.7951 16.3578 20.6422 16.2049 20.454 16.109C20.2401 16 19.9601 16 19.4 16H17.6C17.0399 16 16.7599 16 16.546 16.109C16.3578 16.2049 16.2049 16.3578 16.109 16.546C16 16.7599 16 17.0399 16 17.6V19.4C16 19.9601 16 20.2401 16.109 20.454C16.2049 20.6422 16.3578 20.7951 16.546 20.891C16.7599 21 17.0399 21 17.6 21ZM17.6 8H19.4C19.9601 8 20.2401 8 20.454 7.89101C20.6422 7.79513 20.7951 7.64215 20.891 7.45399C21 7.24008 21 6.96005 21 6.4V4.6C21 4.03995 21 3.75992 20.891 3.54601C20.7951 3.35785 20.6422 3.20487 20.454 3.10899C20.2401 3 19.9601 3 19.4 3H17.6C17.0399 3 16.7599 3 16.546 3.10899C16.3578 3.20487 16.2049 3.35785 16.109 3.54601C16 3.75992 16 4.03995 16 4.6V6.4C16 6.96005 16 7.24008 16.109 7.45399C16.2049 7.64215 16.3578 7.79513 16.546 7.89101C16.7599 8 17.0399 8 17.6 8ZM4.6 8H6.4C6.96005 8 7.24008 8 7.45399 7.89101C7.64215 7.79513 7.79513 7.64215 7.89101 7.45399C8 7.24008 8 6.96005 8 6.4V4.6C8 4.03995 8 3.75992 7.89101 3.54601C7.79513 3.35785 7.64215 3.20487 7.45399 3.10899C7.24008 3 6.96005 3 6.4 3H4.6C4.03995 3 3.75992 3 3.54601 3.10899C3.35785 3.20487 3.20487 3.35785 3.10899 3.54601C3 3.75992 3 4.03995 3 4.6V6.4C3 6.96005 3 7.24008 3.10899 7.45399C3.20487 7.64215 3.35785 7.79513 3.54601 7.89101C3.75992 8 4.03995 8 4.6 8Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },

  'file-07': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M14 2.26953V6.40007C14 6.96012 14 7.24015 14.109 7.45406C14.2049 7.64222 14.3578 7.7952 14.546 7.89108C14.7599 8.00007 15.0399 8.00007 15.6 8.00007H19.7305M8 15V18M16 13V18M12 10.5V18M20 9.98822V17.2C20 18.8802 20 19.7202 19.673 20.362C19.3854 20.9265 18.9265 21.3854 18.362 21.673C17.7202 22 16.8802 22 15.2 22H8.8C7.11984 22 6.27976 22 5.63803 21.673C5.07354 21.3854 4.6146 20.9265 4.32698 20.362C4 19.7202 4 18.8802 4 17.2V6.8C4 5.11984 4 4.27976 4.32698 3.63803C4.6146 3.07354 5.07354 2.6146 5.63803 2.32698C6.27976 2 7.11984 2 8.8 2H12.0118C12.7455 2 13.1124 2 13.4577 2.08289C13.7638 2.15638 14.0564 2.27759 14.3249 2.44208C14.6276 2.6276 14.887 2.88703 15.4059 3.40589L18.5941 6.59411C19.113 7.11297 19.3724 7.3724 19.5579 7.67515C19.7224 7.94356 19.8436 8.2362 19.9171 8.5423C20 8.88757 20 9.25445 20 9.98822Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },

  'chevron-up': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M18 15L12 9L6 15',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },

  'chevron-down': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M6 9L12 15L18 9',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },

  'search-sm': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M21 21L15.0001 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },

  'user-01': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M20 21C20 19.6044 20 18.9067 19.8278 18.3389C19.44 17.0605 18.4395 16.06 17.1611 15.6722C16.5933 15.5 15.8956 15.5 14.5 15.5H9.5C8.10444 15.5 7.40665 15.5 6.83886 15.6722C5.56045 16.06 4.56004 17.0605 4.17224 18.3389C4 18.9067 4 19.6044 4 21M16.5 7.5C16.5 9.98528 14.4853 12 12 12C9.51472 12 7.5 9.98528 7.5 7.5C7.5 5.01472 9.51472 3 12 3C14.4853 3 16.5 5.01472 16.5 7.5Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },

  'eye-off': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M10.7429 5.09232C11.1494 5.03223 11.5686 5 12.0004 5C17.1054 5 20.4553 9.50484 21.5807 11.2868C21.7169 11.5025 21.785 11.6103 21.8231 11.7767C21.8518 11.9016 21.8517 12.0987 21.8231 12.2236C21.7849 12.3899 21.7164 12.4985 21.5792 12.7156C21.2793 13.1901 20.8222 13.8571 20.2165 14.5805M6.72432 6.71504C4.56225 8.1817 3.09445 10.2194 2.42111 11.2853C2.28428 11.5019 2.21587 11.6102 2.17774 11.7765C2.1491 11.9014 2.14909 12.0984 2.17771 12.2234C2.21583 12.3897 2.28393 12.4975 2.42013 12.7132C3.54554 14.4952 6.89541 19 12.0004 19C14.0588 19 15.8319 18.2676 17.2888 17.2766M3.00042 3L21.0004 21M9.8791 9.87868C9.3362 10.4216 9.00042 11.1716 9.00042 12C9.00042 13.6569 10.3436 15 12.0004 15C12.8288 15 13.5788 14.6642 14.1217 14.1213',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },
  'x-xircle': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M15 9L9 15M9 9L15 15M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },
  'minus-xircle': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M 12,2 A 10,10 0 1,0 12,22 A 10,10 0 1,0 12,2 M 6,12 L 18,12',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },
  plus: {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M12 5V19M5 12H19',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },
  'edit-05': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M11 4.00001H6.8C5.11984 4.00001 4.27976 4.00001 3.63803 4.32699C3.07354 4.61461 2.6146 5.07356 2.32698 5.63804C2 6.27978 2 7.11986 2 8.80001V17.2C2 18.8802 2 19.7203 2.32698 20.362C2.6146 20.9265 3.07354 21.3854 3.63803 21.673C4.27976 22 5.11984 22 6.8 22H15.2C16.8802 22 17.7202 22 18.362 21.673C18.9265 21.3854 19.3854 20.9265 19.673 20.362C20 19.7203 20 18.8802 20 17.2V13M7.99997 16H9.67452C10.1637 16 10.4083 16 10.6385 15.9448C10.8425 15.8958 11.0376 15.815 11.2166 15.7053C11.4184 15.5816 11.5914 15.4087 11.9373 15.0628L21.5 5.50001C22.3284 4.67159 22.3284 3.32844 21.5 2.50001C20.6716 1.67159 19.3284 1.67159 18.5 2.50001L8.93723 12.0628C8.59133 12.4087 8.41838 12.5816 8.29469 12.7834C8.18504 12.9624 8.10423 13.1575 8.05523 13.3616C7.99997 13.5917 7.99997 13.8363 7.99997 14.3255V16Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },
  'check-circle': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M7.5 12L10.5 15L16.5 9M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },
  'building-07': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M7.5 11H4.6C4.03995 11 3.75992 11 3.54601 11.109C3.35785 11.2049 3.20487 11.3578 3.10899 11.546C3 11.7599 3 12.0399 3 12.6V21M16.5 11H19.4C19.9601 11 20.2401 11 20.454 11.109C20.6422 11.2049 20.7951 11.3578 20.891 11.546C21 11.7599 21 12.0399 21 12.6V21M16.5 21V6.2C16.5 5.0799 16.5 4.51984 16.282 4.09202C16.0903 3.71569 15.7843 3.40973 15.408 3.21799C14.9802 3 14.4201 3 13.3 3H10.7C9.57989 3 9.01984 3 8.59202 3.21799C8.21569 3.40973 7.90973 3.71569 7.71799 4.09202C7.5 4.51984 7.5 5.0799 7.5 6.2V21M22 21H2M11 7H13M11 11H13M11 15H13',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },
  'switch-vertical': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M 17 4 V 20 M 17 20 L 13 16 M 17 20 L 21 16 M 7 20 V 4 M 7 4 L 3 8 M 7 4 L 11 8',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },
  'map-01': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M 9 18 L 2 22 V 6 L 9 2 M 9 18 L 16 22 M 9 18 V 2 M 16 22 L 22 18 V 2 L 16 6 M 16 22 V 6 M 16 6 L 9 2',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },
  phone: {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M8.38028 8.85335C9.07627 10.303 10.0251 11.6616 11.2266 12.8632C12.4282 14.0648 13.7869 15.0136 15.2365 15.7096C15.3612 15.7694 15.4235 15.7994 15.5024 15.8224C15.7828 15.9041 16.127 15.8454 16.3644 15.6754C16.4313 15.6275 16.4884 15.5704 16.6027 15.4561C16.9523 15.1064 17.1271 14.9316 17.3029 14.8174C17.9658 14.3864 18.8204 14.3864 19.4833 14.8174C19.6591 14.9316 19.8339 15.1064 20.1835 15.4561L20.3783 15.6509C20.9098 16.1824 21.1755 16.4481 21.3198 16.7335C21.6069 17.301 21.6069 17.9713 21.3198 18.5389C21.1755 18.8242 20.9098 19.09 20.3783 19.6214L20.2207 19.779C19.6911 20.3087 19.4263 20.5735 19.0662 20.7757C18.6667 21.0001 18.0462 21.1615 17.588 21.1601C17.1751 21.1589 16.8928 21.0788 16.3284 20.9186C13.295 20.0576 10.4326 18.4332 8.04466 16.0452C5.65668 13.6572 4.03221 10.7948 3.17124 7.76144C3.01103 7.19699 2.93092 6.91477 2.9297 6.50182C2.92833 6.0436 3.08969 5.42311 3.31411 5.0236C3.51636 4.66357 3.78117 4.39876 4.3108 3.86913L4.46843 3.7115C4.99987 3.18006 5.2656 2.91433 5.55098 2.76999C6.11854 2.48292 6.7888 2.48292 7.35636 2.76999C7.64174 2.91433 7.90747 3.18006 8.43891 3.7115L8.63378 3.90637C8.98338 4.25597 9.15819 4.43078 9.27247 4.60655C9.70347 5.26945 9.70347 6.12403 9.27247 6.78692C9.15819 6.96269 8.98338 7.1375 8.63378 7.4871C8.51947 7.60142 8.46231 7.65857 8.41447 7.72538C8.24446 7.96281 8.18576 8.30707 8.26748 8.58743C8.29048 8.66632 8.32041 8.72866 8.38028 8.85335Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },
  'building-04': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M9.5 7H14.5M9.5 11H14.5M9.5 15H14.5M18 21V6.2C18 5.0799 18 4.51984 17.782 4.09202C17.5903 3.71569 17.2843 3.40973 16.908 3.21799C16.4802 3 15.9201 3 14.8 3H9.2C8.0799 3 7.51984 3 7.09202 3.21799C6.71569 3.40973 6.40973 3.71569 6.21799 4.09202C6 4.51984 6 5.0799 6 6.2V21M20 21H4',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },
  'bell-02': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M13.9998 21H9.99977M17.9998 8C17.9998 6.4087 17.3676 4.88258 16.2424 3.75736C15.1172 2.63214 13.5911 2 11.9998 2C10.4085 2 8.88235 2.63214 7.75713 3.75736C6.63192 4.88258 5.99977 6.4087 5.99977 8C5.99977 11.0902 5.22024 13.206 4.34944 14.6054C3.6149 15.7859 3.24763 16.3761 3.2611 16.5408C3.27601 16.7231 3.31463 16.7926 3.46155 16.9016C3.59423 17 4.19237 17 5.38863 17H18.6109C19.8072 17 20.4053 17 20.538 16.9016C20.6849 16.7926 20.7235 16.7231 20.7384 16.5408C20.7519 16.3761 20.3846 15.7859 19.6501 14.6054C18.7793 13.206 17.9998 11.0902 17.9998 8Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },
  'menu-01': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M3 12H21M3 6H21M3 18H21',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },

  'message-check-square': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M9 10.5L11 12.5L15.5 8M7 18V20.3355C7 20.8684 7 21.1348 7.10923 21.2716C7.20422 21.3906 7.34827 21.4599 7.50054 21.4597C7.67563 21.4595 7.88367 21.2931 8.29976 20.9602L10.6852 19.0518C11.1725 18.662 11.4162 18.4671 11.6875 18.3285C11.9282 18.2055 12.1844 18.1156 12.4492 18.0613C12.7477 18 13.0597 18 13.6837 18H16.2C17.8802 18 18.7202 18 19.362 17.673C19.9265 17.3854 20.3854 16.9265 20.673 16.362C21 15.7202 21 14.8802 21 13.2V7.8C21 6.11984 21 5.27976 20.673 4.63803C20.3854 4.07354 19.9265 3.6146 19.362 3.32698C18.7202 3 17.8802 3 16.2 3H7.8C6.11984 3 5.27976 3 4.63803 3.32698C4.07354 3.6146 3.6146 4.07354 3.32698 4.63803C3 5.27976 3 6.11984 3 7.8V14C3 14.93 3 15.395 3.10222 15.7765C3.37962 16.8117 4.18827 17.6204 5.22354 17.8978C5.60504 18 6.07003 18 7 18Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },

  'dots-vertical': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
      {
        d: 'M12 6C12.5523 6 13 5.55228 13 5C13 4.44772 12.5523 4 12 4C11.4477 4 11 4.44772 11 5C11 5.55228 11.4477 6 12 6Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
      {
        d: 'M12 20C12.5523 20 13 19.5523 13 19C13 18.4477 12.5523 18 12 18C11.4477 18 11 18.4477 11 19C11 19.5523 11.4477 20 12 20Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },
  check: {
    width: '16',
    height: '16',
    viewBox: '0 0 16 16',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M12 5L6.5 10.5L4 8',
        stroke: '#2BDF19',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
        strokeWidth: 'none',
      },
    ],
  },
  'arrow-tip': {
    width: '11',
    height: '31',
    viewBox: '0 0 11 31',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        clipRule: 'evenodd',
        fillRule: 'evenodd',
        d: 'M-1.31134e-06 0.5L0 30.5L2.00002 30.5L2.00002 28.7334C2.00002 27.4074 2.52681 26.1356 3.46449 25.1979L9.54568 19.1167C11.4983 17.1641 11.4983 13.9983 9.54568 12.0456L3.46447 5.96442C2.52678 5.02674 2 3.75497 2 2.42888L2 0.5L-1.31134e-06 0.5Z',
        fill: '#3F4142',
        stroke: 'none',
        strokeWidth: 'none',
      },
    ],
  },
  RadioDeselected: {
    width: '13',
    height: '13',
    viewBox: '0 0 13 13',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',

    paths: [
      {
        d: 'M6.5,0.5 A6,6 0 1,0 6.5,12.5 A6,6 0 1,0 6.5,0.5 Z',
        fill: '#F8F8F8',
        stroke: 'none',
        strokeWidth: 'none',
      },
    ],
  },
  DividerLine: {
    width: '1',
    height: '37',
    viewBox: '0 0 1 37',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M1 36L1 1',
        stroke: '#121212',
        strokeLinecap: 'round',
        strokeWidth: 'none',
        fill: 'none',
      },
    ],
  },
  DividerLineUnder: {
    width: '82',
    height: '2',
    viewBox: '0 0 82 2',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M1 1H81',
        stroke: '#8B8B8B',
        strokeLinecap: 'round',
        strokeWidth: 'none',
        fill: 'none',
      },
    ],
  },
  RadioSelected: {
    width: '13',
    height: '13',
    viewBox: '0 0 13 13',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',

    paths: [
      {
        d: 'M13 6.5C13 10.0899 10.0899 13 6.5 13C2.91015 13 0 10.0899 0 6.5C0 2.91015 2.91015 0 6.5 0C10.0899 0 13 2.91015 13 6.5ZM3.7447 6.5C3.7447 8.02171 4.97829 9.2553 6.5 9.2553C8.02171 9.2553 9.2553 8.02171 9.2553 6.5C9.2553 4.97829 8.02171 3.7447 6.5 3.7447C4.97829 3.7447 3.7447 4.97829 3.7447 6.5Z',
        fill: '#F8F8F8',
        stroke: 'none',
        strokeWidth: 'none',
      },
    ],
  },
  'trash-01': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',

    paths: [
      {
        d: 'M16 6V5.2C16 4.0799 16 3.51984 15.782 3.09202C15.5903 2.71569 15.2843 2.40973 14.908 2.21799C14.4802 2 13.9201 2 12.8 2H11.2C10.0799 2 9.51984 2 9.09202 2.21799C8.71569 2.40973 8.40973 2.71569 8.21799 3.09202C8 3.51984 8 4.0799 8 5.2V6M10 11.5V16.5M14 11.5V16.5M3 6H21M19 6V17.2C19 18.8802 19 19.7202 18.673 20.362C18.3854 20.9265 17.9265 21.3854 17.362 21.673C16.7202 22 15.8802 22 14.2 22H9.8C8.11984 22 7.27976 22 6.63803 21.673C6.07354 21.3854 5.6146 20.9265 5.32698 20.362C5 19.7202 5 18.8802 5 17.2V6',
        fill: 'none',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    ],
  },
  close: {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',

    paths: [
      {
        d: 'M17 7L7 17M7 7L17 17',
        fill: 'none',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    ],
  },
  'success-tick': {
    width: '204',
    height: '204',
    viewBox: '0 0 204 204',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',

    paths: [
      {
        d: 'M56.6251 102L86.8751 132.25L147.375 71.7503M202.833 102C202.833 157.689 157.689 202.834 102 202.834C46.3114 202.834 1.16675 157.689 1.16675 102C1.16675 46.3116 46.3114 1.16699 102 1.16699C157.689 1.16699 202.833 46.3116 202.833 102Z',
        fill: 'none',
        stroke: '#2BDF19',
        strokeWidth: '1.5',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    ],
  },
  'failure-tick': {
    width: '222',
    height: '222',
    viewBox: '0 0 222 222',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',

    paths: [
      {
        d: 'M111 221C171.751 221 221 171.751 221 111C221 50.2487 171.751 1 111 1C50.2487 1 1 50.2487 1 111C1 171.751 50.2487 221 111 221Z',
        fill: 'none',
        stroke: '#FF3A1F',
        strokeWidth: '1.5',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
      {
        d: 'M67 155L155 67',
        fill: 'none',
        stroke: '#FF3A1F',
        strokeWidth: '1.5',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
      {
        d: 'M155 155L67 67',
        fill: 'none',
        stroke: '#FF3A1F',
        strokeWidth: '1.5',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    ],
  },
  'download-01': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',

    paths: [
      {
        d: 'M21 15V16.2C21 17.8802 21 18.7202 20.673 19.362C20.3854 19.9265 19.9265 20.3854 19.362 20.673C18.7202 21 17.8802 21 16.2 21H7.8C6.11984 21 5.27976 21 4.63803 20.673C4.07354 20.3854 3.6146 19.9265 3.32698 19.362C3 18.7202 3 17.8802 3 16.2V15M17 10L12 15M12 15L7 10M12 15V3',
        fill: 'none',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    ],
  },
  'alarm-clock': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',

    paths: [
      {
        d: 'M5 3L2 6M22 6L19 3M6 19L4 21M18 19L20 21M12 9V13L14 15M12 21C14.1217 21 16.1566 20.1571 17.6569 18.6569C19.1571 17.1566 20 15.1217 20 13C20 10.8783 19.1571 8.84344 17.6569 7.34315C16.1566 5.84285 14.1217 5 12 5C9.87827 5 7.84344 5.84285 6.34315 7.34315C4.84285 8.84344 4 10.8783 4 13C4 15.1217 4.84285 17.1566 6.34315 18.6569C7.84344 20.1571 9.87827 21 12 21Z',
        fill: 'none',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    ],
  },
  'workflow-icon': {
    width: '16',
    height: '18',
    viewBox: '0 0 16 18',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',

    paths: [
      {
        d: 'M8 2.33317L8 11.6665C8 13.0666 8 13.7667 8.27248 14.3015C8.51217 14.7719 8.89462 15.1543 9.36502 15.394C9.8998 15.6665 10.5999 15.6665 12 15.6665H12.1667M12.1667 15.6665C12.1667 16.587 12.9129 17.3332 13.8333 17.3332C14.7538 17.3332 15.5 16.587 15.5 15.6665C15.5 14.746 14.7538 13.9998 13.8333 13.9998C12.9129 13.9998 12.1667 14.746 12.1667 15.6665ZM3.83333 2.33317L12.1667 2.33317M3.83333 2.33317C3.83333 3.25365 3.08714 3.99984 2.16667 3.99984C1.24619 3.99984 0.5 3.25365 0.5 2.33317C0.5 1.4127 1.24619 0.666504 2.16667 0.666504C3.08714 0.666504 3.83333 1.4127 3.83333 2.33317ZM12.1667 2.33317C12.1667 3.25364 12.9129 3.99984 13.8333 3.99984C14.7538 3.99984 15.5 3.25365 15.5 2.33317C15.5 1.4127 14.7538 0.666504 13.8333 0.666504C12.9129 0.666504 12.1667 1.4127 12.1667 2.33317ZM8 8.99984H12.1667M12.1667 8.99984C12.1667 9.92031 12.9129 10.6665 13.8333 10.6665C14.7538 10.6665 15.5 9.92031 15.5 8.99984C15.5 8.07936 14.7538 7.33317 13.8333 7.33317C12.9129 7.33317 12.1667 8.07936 12.1667 8.99984Z',
        fill: 'none',
        stroke: '#C4C4C4',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    ],
  },
  'team-icon': {
    width: '20',
    height: '20',
    viewBox: '0 0 20 20',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',

    paths: [
      {
        d: 'M13.3337 2.8898C14.5684 3.50343 15.417 4.77762 15.417 6.25C15.417 7.72238 14.5684 8.99657 13.3337 9.6102M15.0003 13.972C16.2599 14.5419 17.3941 15.4708 18.3337 16.6667M1.66699 16.6667C3.28907 14.6021 5.49131 13.3333 7.91699 13.3333C10.3427 13.3333 12.5449 14.6021 14.167 16.6667M11.667 6.25C11.667 8.32107 9.98806 10 7.91699 10C5.84592 10 4.16699 8.32107 4.16699 6.25C4.16699 4.17893 5.84592 2.5 7.91699 2.5C9.98806 2.5 11.667 4.17893 11.667 6.25Z',
        fill: 'none',
        stroke: '#C4C4C4',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    ],
  },
  clipboard: {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',

    paths: [
      {
        d: 'M16 4C16.93 4 17.395 4 17.7765 4.10222C18.8117 4.37962 19.6204 5.18827 19.8978 6.22354C20 6.60504 20 7.07003 20 8V17.2C20 18.8802 20 19.7202 19.673 20.362C19.3854 20.9265 18.9265 21.3854 18.362 21.673C17.7202 22 16.8802 22 15.2 22H8.8C7.11984 22 6.27976 22 5.63803 21.673C5.07354 21.3854 4.6146 20.9265 4.32698 20.362C4 19.7202 4 18.8802 4 17.2V8C4 7.07003 4 6.60504 4.10222 6.22354C4.37962 5.18827 5.18827 4.37962 6.22354 4.10222C6.60504 4 7.07003 4 8 4M9.6 6H14.4C14.9601 6 15.2401 6 15.454 5.89101C15.6422 5.79513 15.7951 5.64215 15.891 5.45399C16 5.24008 16 4.96005 16 4.4V3.6C16 3.03995 16 2.75992 15.891 2.54601C15.7951 2.35785 15.6422 2.20487 15.454 2.10899C15.2401 2 14.9601 2 14.4 2H9.6C9.03995 2 8.75992 2 8.54601 2.10899C8.35785 2.20487 8.20487 2.35785 8.10899 2.54601C8 2.75992 8 3.03995 8 3.6V4.4C8 4.96005 8 5.24008 8.10899 5.45399C8.20487 5.64215 8.35785 5.79513 8.54601 5.89101C8.75992 6 9.03995 6 9.6 6Z',
        fill: 'none',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    ],
  },
  notes: {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: `M2.25,24C1.009,24,0,22.991,0,21.75V2.25C0,1.009,1.009,0,2.25,0h19.5C22.991,0,24,1.009,24,2.25v11.379
          c0,0.317-0.066,0.625-0.196,0.916c-0.002,0.005-0.009,0.018-0.011,0.023c-0.109,0.239-0.262,0.461-0.451,0.65l-8.123,8.122
          c-0.19,0.19-0.411,0.343-0.659,0.456c-0.006,0.003-0.026,0.012-0.032,0.014C14.254,23.934,13.946,24,13.629,24H2.25z M2.25,1.5
          C1.836,1.5,1.5,1.836,1.5,2.25v19.5c0,0.414,0.336,0.75,0.75,0.75H13.5v-6.75c0-1.241,1.009-2.25,2.25-2.25h6.75V2.25
          c0-0.414-0.336-0.75-0.75-0.75H2.25z M15.75,15C15.336,15,15,15.336,15,15.75v5.689L21.439,15H15.75z`,
        stroke: '#919191',
        fill: '#919191',
        strokeWidth: '1',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
      {
        d: `M6.75,7.5C6.336,7.5,6,7.164,6,6.75S6.336,6,6.75,6h12c0.414,0,0.75,0.336,0.75,0.75S19.164,7.5,18.75,7.5H6.75z`,
        stroke: '#919191',
        fill: '#919191c',
        strokeWidth: '1',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
      {
        d: `M6.75,12C6.336,12,6,11.664,6,11.25s0.336-0.75,0.75-0.75H12c0.414,0,0.75,0.336,0.75,0.75S12.414,12,12,12H6.75z`,
        stroke: '#919191',
        fill: '#919191',
        strokeWidth: '1',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    ],
  },
  'alert-diamond': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M12 16.5C11.9258 16.5 11.8533 16.522 11.7917 16.5632C11.73 16.6044 11.6819 16.663 11.6535 16.7315C11.6252 16.8 11.6177 16.8754 11.6322 16.9482C11.6467 17.0209 11.6824 17.0877 11.7348 17.1402C11.7873 17.1926 11.8541 17.2283 11.9268 17.2428C11.9996 17.2573 12.075 17.2498 12.1435 17.2215C12.212 17.1931 12.2706 17.145 12.3118 17.0833C12.353 17.0217 12.375 16.9492 12.375 16.875C12.375 16.7755 12.3355 16.6802 12.2652 16.6098C12.1948 16.5395 12.0995 16.5 12 16.5Z',
        fill: 'none',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
      {
        d: 'M12 13.5V6',
        fill: 'none',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
      {
        d: 'M1.27995 13.2811C0.940605 12.9414 0.75 12.4808 0.75 12.0006C0.75 11.5204 0.940605 11.0599 1.27995 10.7201L10.7199 1.2811C10.8879 1.11275 11.0874 0.979181 11.307 0.888046C11.5267 0.796911 11.7621 0.75 11.9999 0.75C12.2377 0.75 12.4732 0.796911 12.6929 0.888046C12.9125 0.979181 13.112 1.11275 13.2799 1.2811L22.7199 10.7201C23.0593 11.0599 23.2499 11.5204 23.2499 12.0006C23.2499 12.4808 23.0593 12.9414 22.7199 13.2811L13.2799 22.7201C12.9404 23.0594 12.48 23.25 11.9999 23.25C11.5199 23.25 11.0595 23.0594 10.7199 22.7201L1.27995 13.2811Z',
        fill: 'none',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    ],
  },
  'file-check-02': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M20 12.5V6.8C20 5.11984 20 4.27976 19.673 3.63803C19.3854 3.07354 18.9265 2.6146 18.362 2.32698C17.7202 2 16.8802 2 15.2 2H8.8C7.11984 2 6.27976 2 5.63803 2.32698C5.07354 2.6146 4.6146 3.07354 4.32698 3.63803C4 4.27976 4 5.11984 4 6.8V17.2C4 18.8802 4 19.7202 4.32698 20.362C4.6146 20.9265 5.07354 21.3854 5.63803 21.673C6.27976 22 7.11984 22 8.8 22H12M14 11H8M10 15H8M16 7H8M14.5 19L16.5 21L21 16.5',
        fill: 'none',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    ],
  },
  x: {
    width: '12',
    height: '12',
    viewBox: '0 0 12 12',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M8.5 3.5L3.5 8.5M3.5 3.5L8.5 8.5',
        fill: 'none',
        stroke: '#919191',
        strokeWidth: 'none',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    ],
  },
  'help-circle': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M9.09 9C9.3251 8.33167 9.78915 7.76811 10.4 7.40913C11.0108 7.05016 11.7289 6.91894 12.4272 7.03871C13.1255 7.15849 13.7588 7.52152 14.2151 8.06353C14.6713 8.60553 14.9211 9.29152 14.92 10C14.92 12 11.92 13 11.92 13M12 17H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z',
        stroke: '#919191',
        fill: 'none',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    ],
  },
  'lock-04': {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M7.10102 10H7V8C7 5.23858 9.23858 3 12 3C14.7614 3 17 5.23858 17 8V10H16.899M12 14V16M19 15C19 18.866 15.866 22 12 22C8.13401 22 5 18.866 5 15C5 11.134 8.13401 8 12 8C15.866 8 19 11.134 19 15Z',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },
  badge: {
    width: '22',
    height: '22',
    viewBox: '0 0 22 22',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M11 0L20.5263 5.5V16.5L11 22L1.47372 16.5V5.5L11 0Z',
        stroke: 'none',
        fill: '#B24111',
        strokeWidth: 'none',
        strokeLinecap: 'none',
        strokeLinejoin: 'none',
      },
      {
        d: 'M10.9989 7.85728L10.9989 11.0001M10.9989 14.143H10.991M18.0703 14.189V7.81127C18.0703 7.54206 18.0703 7.40745 18.0306 7.28739C17.9956 7.18118 17.9382 7.08369 17.8624 7.00143C17.7767 6.90845 17.659 6.84308 17.4237 6.71233L11.6094 3.48218C11.3866 3.35838 11.2752 3.29648 11.1572 3.27222C11.0527 3.25074 10.945 3.25074 10.8406 3.27222C10.7226 3.29648 10.6112 3.35838 10.3884 3.48218L4.57408 6.71233C4.33874 6.84308 4.22107 6.90845 4.13538 7.00143C4.05958 7.08369 4.00221 7.18118 3.96712 7.28739C3.92745 7.40745 3.92746 7.54206 3.92746 7.81127L3.92746 14.189C3.92746 14.4582 3.92745 14.5928 3.96712 14.7129C4.00221 14.8191 4.05958 14.9166 4.13538 14.9988C4.22107 15.0918 4.33874 15.1572 4.57408 15.2879L10.3884 18.5181C10.6112 18.6419 10.7226 18.7038 10.8406 18.7281C10.945 18.7495 11.0527 18.7495 11.1572 18.7281C11.2752 18.7038 11.3866 18.6419 11.6094 18.5181L17.4237 15.2879C17.659 15.1572 17.7767 15.0918 17.8624 14.9988C17.9382 14.9166 17.9956 14.8191 18.0306 14.7129C18.0703 14.5928 18.0703 14.4582 18.0703 14.189Z',
        stroke: '#E6E6E6',
        fill: 'none',
        strokeWidth: '1.57143',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    ],
  },
  loading: {
    width: '24',
    height: '24',
    viewBox: '0 0 24 24',
    fill: 'none',
    xmlns: 'http://www.w3.org/2000/svg',
    paths: [
      {
        d: 'M12 2V6M12 18V22M6 12H2M22 12H18M19.07 4.93L16.24 7.76M19.07 19.07L16.24 16.24M4.93 19.07L7.76 16.24M4.93 4.93L7.76 7.76',
        stroke: '#919191',
        strokeWidth: '2',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        fill: 'none',
      },
    ],
  },
} as const satisfies SVG;

export type IconTypes = keyof typeof svgs;
