import type { <PERSON>a, StoryObj } from '@storybook/react';
import { withComponentShowcase } from '../../../Utilities';
import { ProgressBar } from './ProgressBar';

const meta: Meta<typeof ProgressBar> = {
  component: ProgressBar,
  title: 'Components/ProgressBar',
};
export default meta;
type Story = StoryObj<typeof ProgressBar>;

const statuses = ['neutral', 'success', 'error', 'inProgress', 'warning'];
const sizes = ['smll', 'md', 'lg'];
const types = ['node', 'bar', 'nodebar'];

export const Overview: Story = {
  argTypes: {
    size: {
      control: {
        type: 'select',
        options: sizes,
      },
    },
    status: {
      control: {
        type: 'select',
        options: statuses,
      },
    },
    progress: {
      control: {
        type: 'range',
        min: 0,
        max: 100,
        step: 1,
      },
    },
    type: {
      control: {
        type: 'select',
        options: types,
      },
    },
  },
  args: {
    size: 'smll',
    status: 'neutral',
    progress: 1,
  },
};

export const BarType: Story = {
  argTypes: { ...Overview.argTypes },
  args: {
    type: 'bar',
  },
};

export const NodeType: Story = {
  argTypes: { ...Overview.argTypes },
  args: {
    type: 'node',
  },
};

export const NodeBarType: Story = {
  argTypes: { ...Overview.argTypes },
  args: {
    type: 'nodebar',
  },
};

export const ProgressBarSizes: Story = {
  argTypes: { ...Overview.argTypes },
  render: (args) =>
    withComponentShowcase(<ProgressBar {...args} />)('size', sizes, true),
  args: {
    type: 'node',
  },
};

export const ProgressBarTypes: Story = {
  argTypes: { ...Overview.argTypes },
  render: (args) =>
    withComponentShowcase(<ProgressBar {...args} />)('type', types, true),
  args: {
    type: 'node',
  },
};

export const ProgressBarStatuses: Story = {
  argTypes: { ...Overview.argTypes },
  render: (args) =>
    withComponentShowcase(<ProgressBar {...args} />)('status', statuses, true),
  args: {
    type: 'node',
  },
};
