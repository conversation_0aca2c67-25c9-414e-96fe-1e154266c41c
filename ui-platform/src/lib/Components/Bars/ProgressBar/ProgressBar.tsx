import { ComponentPropsWithRef } from 'react';
import styled, { useTheme } from 'styled-components';

/**
 * ProgressBar component properties.
 * @typedef {Object} ProgressBarProps
 * @property {'smll' | 'md' | 'lg'} size - Size of the progress bar (small, medium, large).
 * @property {'neutral' | 'success' | 'error' | 'inProgress' | 'warning'} status - Status color.
 * @property {number} progress - Progress percentage (0-100).
 * @property {'bar' | 'node' | 'nodebar'} [type='nodebar'] - Type of the progress indicator.
 */
export interface ProgressBarProps {
  size: keyof sizeMap;
  status: keyof statusMap;
  progress: number;
  type?: 'bar' | 'node' | 'nodebar';
}

type sizeMap = {
  smll: number;
  md: number;
  lg: number;
};

export type statusMap = {
  neutral: string;
  success: string;
  error: string;
  inProgress: string;
  warning: string;
};

const sizes: sizeMap = {
  smll: 1,
  md: 3,
  lg: 5,
};

const Container = styled.div`
  width: 100%;
  height: auto;
  position: relative;
`;
const ProgressBarDiv = styled.div<
  { size: ProgressBarProps['size'] } & ComponentPropsWithRef<'div'>
>`
  width: 100%;
  height: 0;
  left: 0;
  top: 50%;
  position: absolute;
  margin: auto;
  transform: translateY(-50%);
  border: ${(props) => {
      return sizes[props.size] + 'px';
    }}
    ${(props) => props.theme.ColorsStrokesDefault} solid;
  border-radius: ${(props) => {
    return sizes[props.size] + 'px';
  }};
`;
const ProgressNode = styled.div<
  {
    size: ProgressBarProps['size'];
    color: ProgressBarProps['status'];
    type: ProgressBarProps['type'];
    width: number;
  } & ComponentPropsWithRef<'div'>
>`
  width: ${(props) =>
    props.type !== 'bar' ? `${sizes[props.size] * 5}px` : props.width + '%'};
  height: ${(props) =>
    props.type !== 'bar' ? `${sizes[props.size] * 5}px` : 0};
  left: ${(props) => (props.type !== 'bar' ? props.width : 0)}%;
  top: 50%;
  transform: translateY(-50%);
  position: absolute;
  transition: all 0.3s ease-in-out;
  border-radius: ${(props) => {
    return props.type !== 'bar' ? '50%' : sizes[props.size] + 'px';
  }};

  ${(props) => {
    const statuses: statusMap = {
      neutral: props?.theme.ColorsUtilityColorFocus,
      success: props?.theme.ColorsUtilityColorSuccess,
      error: props?.theme.ColorsUtilityColorError,
      inProgress: props?.theme.ColorsUtilityColorProgress,
      warning: props?.theme.ColorsUtilityColorWarning,
    };
    if (props.type !== 'bar') {
      return `margin: auto; background:${statuses[props.color]};`;
    } else {
      return `border: ${sizes[props.size]}px ${statuses[props.color]} solid;`;
    }
  }}
`;
const ProgressNodeBar = styled.div<
  {
    size: ProgressBarProps['size'];
    color: ProgressBarProps['status'];
    type: ProgressBarProps['type'];
    width: number;
  } & ComponentPropsWithRef<'div'>
>`
  width: ${(props) => props.width + '%'};
  height: 0;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  position: absolute;
  transition: all 0.3s ease-in-out;
  border-radius: ${(props) => sizes[props.size]}px;

  ${(props) => {
    const statuses: statusMap = {
      neutral: props?.theme.ColorsUtilityColorFocus,
      success: props?.theme.ColorsUtilityColorSuccess,
      error: props?.theme.ColorsUtilityColorError,
      inProgress: props?.theme.ColorsUtilityColorProgress,
      warning: props?.theme.ColorsUtilityColorWarning,
    };
    return `border: ${sizes[props.size]}px ${statuses[props.color]} solid;`;
  }}
`;

/**
 * ProgressBar component that displays a progress indicator in various styles.
 * 
 * @param {ProgressBarProps} props - The properties object for the ProgressBar component.
 * @returns {JSX.Element} The rendered progress bar.
 */
export function ProgressBar({
  size = 'smll',
  status = 'neutral',
  progress = 1,
  type = 'nodebar',
}: ProgressBarProps) {
  return (
    <Container>
      <ProgressBarDiv size={size}></ProgressBarDiv>
      {type === 'nodebar' && (
        <ProgressNodeBar
          size={size}
          color={status}
          width={progress}
          type={type}
        />
      )}
      <ProgressNode size={size} color={status} width={progress} type={type} />
    </Container>
  );
}
