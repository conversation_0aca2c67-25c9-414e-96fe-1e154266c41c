import type { Meta, StoryObj } from '@storybook/react';
import { IconButton } from './IconButton';

const meta: Meta<typeof IconButton> = {
  component: IconButton,
  title: 'Components/Buttons/IconButton',
};
export default meta;
type Story = StoryObj<typeof IconButton>;

export const Default: Story = {
  args: {
    icon: 'user-circle',
  },
};

// export const Hover: Story = {
//   args: {
// },
// };

export const Active: Story = {
  args: {
    active: true,
    icon: 'user-circle',
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    icon: 'user-circle',
  },
};
