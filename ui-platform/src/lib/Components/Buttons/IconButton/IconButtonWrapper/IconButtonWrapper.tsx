import { Children, cloneElement, ReactElement, useMemo } from 'react';
import styled, { useTheme } from 'styled-components';
import { IconProps } from '../../../Icons';

export interface IIconButtonWrapperProps {
  children: ReactElement<IconProps>;
  color?: string;
}

const BtnContent = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 52px;
  height: 56px;
  justify-content: center;
  align-items: center;
  display: flex;
  box-sizing: border-box;
`;

// export function SquareButtonWrapper({ children }: ISquareButtonWrapper) {
//   return <BtnContent>{children}</BtnContent>;
// }

/**
 * Wraps an SVG icon in a button container, with a default color applied
 * if none is provided.
 *
 * @param {ReactElement<IconProps>} children - The icon to render
 * @param {string} [color] - Override color for the icon
 * @returns {JSX.Element}
 */
export function IconButtonWrapper({
  children,
  color,
}: IIconButtonWrapperProps) {
  const Color = useMemo(() => color, [color]);
  const svgColor = useTheme().ColorsStrokesGrey;
  const svgName = Children.map(children, (child) => {
    return cloneElement(child, { ...child.props, color: Color ?? svgColor });
  });
  return <BtnContent>{svgName}</BtnContent>;
}
