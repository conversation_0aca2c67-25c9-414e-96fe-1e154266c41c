import { ComponentPropsWithoutRef } from 'react';
import styled from 'styled-components';
import { Icon, IconTypes } from '../../Icons';
import { SquareButtonWrapper } from './SquareButtonWrapper/SquareButtonWrapper';

export interface ISquareButtonProps extends ComponentPropsWithoutRef<'button'> {
  disabled?: boolean;
  active?: boolean;
  icon?: IconTypes;
  color?: string;
}

const Container = styled(({ icon, color, ...rest }) => {
  const { active, ...SquareButtonProps } = rest;

  return (
    <button {...SquareButtonProps}>{<Icon type={icon} color={color} />}</button>
  );
})`
  background-color: ${(props) => props?.theme.ColorsBackgroundInverse};
  width: 52px; // actual width of the button
  height: 56px; // actual height of the button
  border: 1px solid;
  color: ${(props) => props?.theme.ColorsStrokesInverse};
  display: grid;
  place-items: center;
  overflow: hidden;
  cursor: pointer;
  border-radius: ${(props) => props?.theme.RadiusSm};
  transition: all 0.2s ease-in-out 0.1s;

  &:active {
    outline: 2px solid ${(props) => props?.theme.ColorsStrokesFocus};
    cursor: default;
  }

  &:disabled {
    background: ${(props) => props?.theme.ColorsButtonColorPagenationDisabled};
    color: ${(props) => props?.theme.ColorsTypographyDisabled};
    border: 1px ${(props) => props?.theme.ColorsTypographyDisabled} solid;
    cursor: default;
  }

  && {
    ${(props) =>
      props?.active &&
      `
        outline: 2px solid ${props?.theme.ColorsUtilityColorFocus};
        cursor: 'default';
      `}
  }
`;

/**
 * A SquareButton component that wraps a Container with an icon.
 *
 * @param {IconTypes} icon - The icon to be displayed in the button.
 * @param {ISquareButtonProps} props - Additional props for the button.
 * @return {JSX.Element} A JSX element representing the SquareButton component.
 */
export function SquareButton({ icon, ...props }: ISquareButtonProps) {
  return (
    <SquareButtonWrapper>
      <Container icon={icon} {...props} />
    </SquareButtonWrapper>
  );
}
