import type { <PERSON>a, StoryObj } from '@storybook/react';
import { SquareButton } from './SquareButton';

const meta: Meta<typeof SquareButton> = {
  component: SquareButton,
  title: 'Components/Buttons/SquareButton',
};

export default meta;
type Story = StoryObj<typeof SquareButton>;

export const Default: Story = {
  args: {
    icon: 'user-square',
  },
};

// export const Hover: Story = {
//   args: {
// },
// };

export const Active: Story = {
  args: {
    icon: 'user-square',
    active: true,
  },
};

export const Disabled: Story = {
  args: {
    icon: 'user-square',
    disabled: true,
  },
};
