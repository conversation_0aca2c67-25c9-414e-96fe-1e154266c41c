import { Children, cloneElement, ReactElement, ReactNode } from 'react';
import styled, { useTheme } from 'styled-components';
import { IconProps } from '../../../Icons';

export interface ISquareButtonWrapperProps {
  children: ReactElement<IconProps>;
}

const BtnContent = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 52px;
  height: 56px;
  display: grid;
  place-items: center;
  box-sizing: border-box;
`;

/**
 * Wraps an SVG icon in a button container, with a default color applied
 * if none is provided.
 *
 * @param {ReactElement<IconProps>} children - The icon to render
 * @returns {JSX.Element}
 */
export function SquareButtonWrapper({ children }: ISquareButtonWrapperProps) {
  const svgColor = useTheme().ColorsStrokesGrey;
  const svgName = Children.map(children, (child) => {
    return cloneElement(child, { ...child.props, color: svgColor });
  });
  return <BtnContent>{svgName}</BtnContent>;
}
