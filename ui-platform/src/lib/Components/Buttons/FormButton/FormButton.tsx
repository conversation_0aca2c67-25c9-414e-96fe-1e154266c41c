import React, { FC, ButtonHTMLAttributes } from 'react';
import styled, { css } from 'styled-components';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  onClick: () => void;
  disabled?: boolean;
  width?: string,
  height?: string
}

// Define the styled button
const CircleButton = styled.button<{ width?: string; height?: string }>`
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: ${(props) => props?.width || '50px'};
  height: ${(props) => props?.height || '50px'};
  border-radius: 50%;
  cursor: pointer;
  border: 1px solid ${(props) => props?.theme.ColorsIconColorTertiary};
  background: transparent;

  ${(props) =>
    !props.disabled &&
    css`
      &:hover {
        background: ${props.theme.ColorsButtonColorActionPanelHover};
      }

      &:active {
        border: 2px solid ${props.theme.ColorsStrokesFocus};
      }
    `}

  ${(props) =>
    props.disabled &&
    css`
      cursor: not-allowed;
      border: none;
    `}
`;

/**
 * FormButton is a styled button component that can be used in forms.
 *
 * It renders a rounded button with a default width and height of 50px.
 * The button is centered both horizontally and vertically.
 *
 * The button is disabled if the disabled prop is true.
 *
 * The button's width and height can be set using the width and height props.
 *
 * The button's onClick event is handled by the onClick prop.
 *
 * The button's children are rendered inside the button.
 *
 * Example:
 * 
 * <FormButton onClick={handleClick} disabled={isDisabled} width="100px" height="40px">
 *   Submit
 * </FormButton>
 *
 * @param onClick - Function to be called when the button is clicked.
 * @param disabled - Boolean flag to disable the button.
 * @param width - Width of the button.
 * @param height - Height of the button.
 */


export const FormButton: FC<ButtonProps> = ({
  onClick,
  children,
  disabled,
  width,
  height,
  ...props
}) => {
  return (
    <CircleButton
      onClick={onClick}
      disabled={disabled}
      width={width}
      height={height}
      {...props}
    >
      {children}
    </CircleButton>
  );
};
