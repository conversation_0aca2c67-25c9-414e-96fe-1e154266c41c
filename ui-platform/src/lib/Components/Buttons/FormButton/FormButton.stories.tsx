import type { Meta, StoryObj } from '@storybook/react';
import { FormButton } from './FormButton';
import { Icon } from '../../Icons';

const meta: Meta<typeof FormButton> = {
  component: FormButton,
  title: 'Components/Buttons/FormButton',
};
export default meta;
type Story = StoryObj<typeof FormButton>;

export const Default: Story = {
  args: {
    children: <Icon type='close' size={30} />,
    disabled: false,
    onClick: () => console.log("IVE BEEN CLICKED")
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    children: <Icon type='close' size={30} />,
  },
};
