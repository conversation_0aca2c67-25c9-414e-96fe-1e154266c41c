import React, { useState } from 'react';
import styled from 'styled-components';
import { Icon } from '../../Icons';

interface FilterButtonProps {
  size?: string;
  onClick?: () => void;
}

const StyledButtonContainer = styled.div<FilterButtonProps>`
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  place-items: center;
  border-radius: 0px 0px ${(props) => props.theme.RadiusXxs},
    ${(props) => props.theme.RadiusXxs};
  border: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
  padding: ${(props) => props.theme.SpacingXs} 8px
    ${(props) => props.theme.SpacingSm} 8px;
  width: ${(props) => props.size || '150px'};
`;

const StyledText = styled.p`
  all: unset;
  font-family: ${(props) => props.theme.FontFamiliesInter};;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
`;

/**
 * A FilterButton component. This component is used to open a filter menu or
 * other search/filter functionality. It displays a toggleable chevron icon to
 * indicate the state of the filter menu. The component emits an onClick event
 * when the button is clicked.
 *
 * @param {string} [size] - The width of the button. Defaults to 150px.
 * @param {() => void} [onClick] - The function to call when the button is
 *   clicked. Called with no arguments.
 *
 * @example
 * const MyComponent = () => {
 *   const handleFilterClick = () => {
 *     // Handle the filter button being clicked
 *   };
 *
 *   return (
 *     <FilterButton size="200px" onClick={handleFilterClick} />
 *   );
 * }
 */
export const FilterButton: React.FC<FilterButtonProps> = ({ size, onClick }) => {

  const [isChevronUp, setIsChevronUp] = useState(true);

  const handleToggle = () => {
    setIsChevronUp((prevState: any) => !prevState); // Toggle the chevron direction

    if (onClick) {
      onClick(); 
    }
  };

  return (
    <StyledButtonContainer size={size} onClick={handleToggle}>
      <StyledText>Search</StyledText>
      <Icon type={isChevronUp ? 'chevron-up-double' : 'chevron-down-double'} />
      <StyledText>Filters</StyledText>
    </StyledButtonContainer>
  );
};
