import type { Meta, StoryObj } from '@storybook/react';
import { FilterButton } from './FilterButton';

const meta: Meta<typeof FilterButton> = {
  component: FilterButton,
  title: 'Components/Buttons/FilterButton',
  argTypes: {
    size: { control: 'text' }, 
    onClick: { action: 'clicked' },
  },
};
export default meta;
type Story = StoryObj<typeof FilterButton>;

export const Default: Story = {
  args: {},
};