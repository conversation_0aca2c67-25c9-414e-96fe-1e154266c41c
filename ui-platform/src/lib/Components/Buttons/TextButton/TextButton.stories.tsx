import { <PERSON>a, StoryObj } from '@storybook/react';
import { TextButton, TextButtonProps } from './TextButton';

export default {
  title: 'Components/Buttons/TextButton',
  component: TextButton,
  argTypes: {
    size: {
      control: {
        type: 'select',
        options: ['small', 'large'],
      },
    },
    actiontype: {
      control: {
        type: 'select',
        options: [
          'preferred',
          'alternative',
          'proceed',
          'warning',
          'attention',
        ],
      },
    },
    disabled: { control: 'boolean' },
    onClick: { action: 'clicked' }, // Add onClick as an action to log clicks
  },
} as Meta<TextButtonProps>;

type Story = StoryObj<TextButtonProps>;

export const ClickableButton: Story = {
  args: {
    btnValue: 'Click Me',
    size: 'large',
    actiontype: 'preferred',
    onClick: () => alert('Button Clicked!'),
  },
};
