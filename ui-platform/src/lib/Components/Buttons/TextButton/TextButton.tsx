import React, { CSSProperties, ReactNode } from 'react';
import styled, { css, useTheme } from 'styled-components';
import { ColorsButtonColorModuleNavigationDefault } from '../../../themes/tokens/outputs/desktop-light';
import { Icon, IconTypes } from '../../Icons';
import { TextButtonWrapper } from './TextButtonWrapper/TextButtonWrapper';

export interface TextButtonProps {
  disabled?: boolean;
  btnValue: ReactNode;
  btnType?: 'button' | 'submit' | 'reset';
  actiontype?:
    | 'preferred'
    | 'alternative'
    | 'proceed'
    | 'warning'
    | 'default'
    | 'attention';
  size?: 'small' | 'large';
  onClick?: (e: React.MouseEvent<HTMLButtonElement> | any) => void;
  className?: string;
  style?: CSSProperties;
  iconLeft?: IconTypes;
  iconRight?: IconTypes;
}
interface sizeStyleProps {
  [key: string]: any;
}

const notIcon = (label: ReactNode) => typeof label === 'string';

const textBaseStyling = css`
  display: grid;
  grid-auto-flow: row;
  justify-content: center;
  align-items: center;

  word-wrap: break-word;
`;

const StyledButton = styled(({ size, actiontype, ...rest }) => {
  const {
    btnValue,
    btnType = 'button',
    iconLeft,
    iconRight,
    ...TextButtonProps
  } = rest;
  return (
    <button
      className={`${actiontype}`}
      {...TextButtonProps}
      type={btnType}
      style={{ display: 'grid', gridAutoFlow: 'column', gap: '1rem' }}
    ></button>
  );
})`
  display: grid;
  grid-auto-flow: column;
  text-transform: ${(props) => props.theme.TextCaseUppercase};
  font-family: ${(props) => props.theme.DesktopButtonsModuleNavigationMedium};
  border: 2px solid ${(props) => props.theme.ColorsStrokesInverse};
  border-radius: ${(props) => props.theme.RadiusRound};
  gap: ${(props) => props.theme.GapMd};
  cursor: pointer;

  ${(props) => (notIcon(props.btnValue) ? textBaseStyling : '')}
  ${(props) => (props.size ? `${props.size}` : '')}

  ${(props) => {
    switch (props.actiontype) {
      case 'preferred':
        return `
          background-color: ${props?.theme.ColorsButtonColorModuleNavigationDefault};
          border: solid 1px ${props?.theme.ColorsUtilityColorFocus};
          color: ${props?.theme.ColorsUtilityColorFocus};
        `;
      case 'alternative':
        return `
          background-color: ${props?.theme.ColorsButtonColorModuleNavigationDefault};
           border: solid 1px ${props?.theme.ColorsUtilityColorWarning};
           color: ${props?.theme.ColorsUtilityColorWarning};
        `;
      case 'warning':
        return `
          background-color: ${props?.theme.ColorsButtonColorModuleNavigationDefault};
           border: solid 1px ${props?.theme.ColorsUtilityColorError};
           color: ${props?.theme.ColorsUtilityColorError};
        `;
      case 'proceed':
        return `
          background-color: ${props?.theme.ColorsButtonColorModuleNavigationDefault};
           border: solid 1px ${props?.theme.ColorsUtilityColorSuccess};
           color: ${props?.theme.ColorsUtilityColorSuccess};
        `;
      case 'attention':
        return `
          background-color: ${props?.theme.ColorsUtilityColorFocus};
           border: solid 1px ${props?.theme.ColorsStrokesDefault};
           color: ${props?.theme.ColorsTypographyPrimary};
        `;
      default:
        return `
        background-color: ${props?.theme.ColorsButtonColorModuleNavigationDisabled};
           border: solid 1px ${props?.theme.ColorsTypographyPrimary};
           color: ${props?.theme.ColorsTypographyPrimary};
        `;
    }
  }}

   &:hover {
    background-color: ${(props) =>
      props?.theme.ColorsButtonColorModuleNavigationHover};
    cursor: pointer;
    border: 1px solid ${(props) => props?.theme.ColorsStrokesInverse};
    color: ${(props) => props?.theme.ColorsTypographySecondary};
    ${(props) => (notIcon(props.btnValue) ? textBaseStyling : '')};
  }

  &:active {
    background-color: ${(props) =>
      props?.theme.ColorsButtonColorModuleActionsActivated};
    cursor: default;
    border: 1px solid ${(props) => props?.theme.ColorsStrokesInverse};
    color: ${(props) => props?.theme.ColorsTypographySecondary};
    ${(props) => (notIcon(props.btnValue) ? textBaseStyling : '')}
  }

  &:disabled {
    background: ${(props) =>
      props?.theme.ColorsButtonColorModuleNavigationDisabled}40;
    color: ${(props) => props?.theme.ColorsControllersDefault};
    border: 1px ${(props) => props?.theme.ColorsControllersDefault} solid;
    cursor: default;

    ${(props) => (notIcon(props.btnValue) ? textBaseStyling : '')}
  }

  && {
    ${(props) =>
      props?.active &&
      `
      // background: ${props?.theme.ColorsUtilityColorFocus};
        cursor: default;
      `}
  }
`;

/**
 * A text button component that can be used in various contexts. It can be used as a link or a button.
 * The button can have different sizes and states (hover, active, disabled). The size and state can be
 * passed as props. The button can also have an icon, which can be passed as a prop. The icon can be
 * positioned on the left or the right of the button. The button also has a default font size and
 * padding, which can be overridden by passing a custom style as a prop.
 *
 * @param {string} btnValue - The value of the button.
 * @param {'small'|'large'} size - The size of the button. Defaults to 'small'.
 * @param {() => void} onClick - The function to be called when the button is clicked.
 * @param {object} [props] - Any other props to be passed to the button.
 * @returns {ReactElement} - The text button component.
 */
export function TextButton({
  btnValue,
  size = 'small',
  onClick,
  iconLeft,
  iconRight,
  ...props
}: TextButtonProps) {
  const theme = useTheme();
  const sizeStyles: sizeStyleProps = {
    small: `
      padding: ${theme.SpacingXs} ${theme.SpacingSm};
      font-size: ${theme.FontSize2}px;
    `,
    large: `
      padding: ${theme.SpacingSm} ${theme.SpacingLg};
      font-size: ${theme.FontSize3}px;
    `,
  };
  return (
    <StyledButton
      size={sizeStyles[size]}
      onClick={onClick}
      {...{ btnValue, ...props }}
    >
      {iconLeft && <Icon type={iconLeft} size={22} />}
      {<TextButtonWrapper>{btnValue as string}</TextButtonWrapper>}
      {iconRight && <Icon type={iconRight} size={22} />}
    </StyledButton>
  );
}
