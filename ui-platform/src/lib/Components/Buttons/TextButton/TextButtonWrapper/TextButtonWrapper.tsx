import {ReactNode} from 'react';
import styled from 'styled-components';

export interface ITextButtonWrapper {
  children: string | number;
}

const BtnContent = styled(({ ...rest }) => <div {...rest}></div>)`
  font-size: ${(props) => props?.theme.FontSize5};
  font-family: 'Inter', sans-serif;
  text-transform: uppercase;
  word-wrap: break-word;
  box-sizing: border-box;
`;

/**
 * Renders a text button wrapper component.
 *
 * @param {ITextButtonWrapper} props - The component props.
 * @param {ReactNode} props.children - The content to be rendered inside the component.
 * @return {JSX.Element} The text button wrapper component.
 */
export function TextButtonWrapper ({children}: ITextButtonWrapper) {
  return (
    <BtnContent>
      {children}
    </BtnContent>
  );
}
