import React from 'react';
import styled from 'styled-components';
import { Icon } from '../../Icons';

interface ClearButtonProps {
  onClick: () => void;
  text?: string;
}

const StyledButtonContainer = styled.div`
  background-color: ${(props) => props.theme.ColorsCardColorJobCardPrimary};
  display: grid;
  height: 20px;
  padding: ${(props) => props.theme.SpacingXs},
    ${(props) => props.theme.SpacingSm};
  align-items: center;
  grid-gap: ${(props) => props.theme.GapXs};
  grid-template-columns: 80% 20%;
  align-self: stretch;
  justify-content: space-between;
  min-width: 50px;
  max-width: 235px;
  max-height: 38px;
  width: 100px;
  border-radius: ${(props) => props.theme.RadiusRound};
  border: ${(props) => props.theme.ColorsIconColorTertiary};
  cursor: pointer;
`;

const StyledButtonText = styled.div`
  overflow: hidden;
  color: var(--colors-typography-secondary, #c4c4c4);
  text-align: center;
  text-overflow: ellipsis;

  font-family: ${(props) => props.theme.FontFamiliesInter};;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  text-transform: uppercase;
`;


/**
 * Represents a Clear Button component that triggers a specified function when clicked.
 * @param onClick - Function to be executed when the button is clicked.
 * @param text - Optional text to display on the button.
 * @returns A React component that renders a styled Clear Button with text and an icon.
 */

export const ClearButton: React.FC<ClearButtonProps> = ({ onClick, text}) => {
  return (
    <StyledButtonContainer onClick={onClick}>
      <StyledButtonText>{text}</StyledButtonText>
      <Icon type="close" size={18} />
    </StyledButtonContainer>
  );
};
