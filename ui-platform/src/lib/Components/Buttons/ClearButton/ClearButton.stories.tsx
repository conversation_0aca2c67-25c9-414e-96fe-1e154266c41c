import type { <PERSON>a, StoryObj } from '@storybook/react';
import { ClearButton } from './ClearButton';

const meta: Meta<typeof ClearButton> = {
  component: ClearButton,
  title: 'Components/Buttons/ClearButton',
  argTypes: {
    onClick: { action: 'clicked' },
  },
};
export default meta;
type Story = StoryObj<typeof ClearButton>;

export const Default: Story = {
  args: {
    text: 'Clear All',
  },
};