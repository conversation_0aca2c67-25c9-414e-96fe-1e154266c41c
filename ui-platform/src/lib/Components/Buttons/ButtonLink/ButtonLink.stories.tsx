import type { Meta, StoryObj } from '@storybook/react';
import styled from 'styled-components';
import {ButtonLink} from './ButtonLink';
import { PinggoLogo } from '../../ClientLogos/PinggoLogo/PinggoLogo';
import { BetterSureLogo } from '../../ClientLogos/BetterSureLogo/BettersureLogo';

const ButtonListContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(192px, 1fr));
  gap: ${({ theme }) => theme?.GapXl};
`;

interface ButtonLinkStoryProps {
  buttons: {
    iconSrc: React.ReactNode;
    altText: string;
    href?: string;
    onClick?: () => void;
  }[];
}

const meta: Meta<typeof ButtonLink> = {
  component: ButtonLink,
  title: 'Components/Buttons/ButtonLink',
  argTypes: {},
};

export default meta;

type Story = StoryObj<ButtonLinkStoryProps>;

const Template = ({ buttons }: ButtonLinkStoryProps) => {
  return (
    <ButtonListContainer>
      {buttons.map((button, index) => (
        <ButtonLink
          key={index}
          iconSrc={button.iconSrc}
          altText={button.altText}
          href={button.href}
          onClick={button.onClick}
        />
      ))}
    </ButtonListContainer>
  );
};

export const MultipleButtonLinks: Story = {
  render: (args) => <Template {...args} />,
  args: {
    buttons: [
      {
        iconSrc: <PinggoLogo />,
        altText: 'Pinggo',
        href: 'https://example1.com',
      },
      {
        iconSrc: <BetterSureLogo />,
        altText: 'BetterSureLogo',
        href: 'https://example2.com',
      },
    ],
  },
};
