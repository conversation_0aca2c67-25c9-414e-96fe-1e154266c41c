import { ComponentPropsWithoutRef, useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';
import styled, { useTheme } from 'styled-components';
import { Icon, IconProps, IconTypes } from '../../Icons';
import { ActionConfig } from 'ui-platform/src/lib/Engine/models/action.config';
export interface ToolbarButtonProps {
  buttonText: string;
  icon?: {
    iconType?: IconTypes;
    iconSize?: IconProps['size'];
    iconFill?: IconProps['fill'];
    iconStrokeWidth?: IconProps['strokeWidth'];
  };
  onClick?: ActionConfig[];
  _formContext?: UseFormReturn;
  disabledWhen?: string;
  disabled?: boolean;
  callClientAction?: (config: ActionConfig) => void;
}
const Button = styled.button<ToolbarButtonProps>`
  all: unset;
  cursor: pointer;
  position: relative;
  box-shadow: 0px 0px 3px ${(props) => props.theme.ColorsTypographyPrimary};
  border-radius: 104px;
  background-color: ${(props) => props.theme.ColorsTabsToolbarPrimary};
  width: 100%;
  height: 34px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  box-sizing: border-box;
  gap: 8px;
  min-width: 74px;
  max-width: 200px;
  max-height: 38px;
  text-align: center;
  font-size: ${(props) => props.theme.FontSize2}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  &:hover {
    background-color: ${(props) => props?.theme.ColorsTabsToolbarHover};
  }
  &:disabled {
    border: 1px solid ${(props) => props?.theme.ColorsStrokesGrey};
    background-color: transparent;
  }
  &:active {
    background-color: ${(props) => props?.theme.ColorsTabsToolbarActivated};
    box-shadow: 0px 0px 5.2px 0px #fbfeff;
  }
`;

/**
 * Evaluates a disableWhen expression.
 *
 * @param {string} expression - The expression to evaluate.
 * @param {any} $form - The form data.
 * @param {any} $formState - The form state.
 * @returns {boolean} true if the button should be disabled, false otherwise.
 */
const evaluateDisableWhen = (
  expression: string,
  $form: any,
  $formState: any
) => {
  try {
    // Use Function constructor to evaluate the expression
    const func = new Function('$form', '$formState', `return ${expression};`);
    return func($form, $formState);
  } catch (error) {
    console.error('Error evaluating disableWhen expression:', error);
    return false;
  }
};

/**
 * A generic button component for the toolbar.
 *
 * @param {IconProps} icon - The icon to display on the button.
 * @param {string} buttonText - The text to display on the button.
 * @param {UseFormReturn} _formContext - The form context to use for disabling the button based on the form state.
 * @param {ToolbarButtonProps} props - Additional props to pass to the button element.
 * @returns {JSX.Element} The button element.
 */
export function ToolbarButton({
  icon,
  buttonText = 'New Job',
  _formContext,
  callClientAction,
  ...props
}: ToolbarButtonProps) {
  const iconColor = useTheme().ColorsIconColorPrimary;
  const $form = _formContext?.watch();
  const $formState = _formContext?.formState;

  const buttonConfig = useMemo(() => {
    const config = (btn: ToolbarButtonProps) => {
      return {
        ...btn,
        disabled:
          !!btn.disabled ||
          (btn.disabledWhen
            ? evaluateDisableWhen(btn.disabledWhen, $form, $formState)
            : false),
        onClick: async () => {
          if (btn.onClick && btn.onClick instanceof Array) {
            for (const cf of btn.onClick || []) {
              await callClientAction?.(cf);
            }
          }
        },
      };
    };
    return config({ buttonText, ...props });
  }, [$form, $formState]);
  return (
    <Button {...buttonConfig} onClick={buttonConfig.onClick as any}>
      <span>{buttonConfig?.buttonText}</span>
      <Icon
        {...icon}
        type={icon?.iconType ?? 'plus'}
        size={24}
        color={iconColor}
      />
    </Button>
  );
}
