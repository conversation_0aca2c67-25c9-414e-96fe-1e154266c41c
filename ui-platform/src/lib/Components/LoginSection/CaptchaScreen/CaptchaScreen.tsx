import styled from "styled-components";
import { Background } from "../../Background/Background";
import Bicycle1 from "../../public/images/Bicycle.png";
import Bicycle2 from "../../public/images/Bicycle_2.png";
import Bicycle3 from "../../public/images/Bicycle_3.png";
import Bicycle4 from "../../public/images/Bicycle_4.png";
import Book from "../../public/images/Book.png";
import Player from "../../public/images/Player.png";
import Building from "../../public/images/Building.png";
import Leaves from "../../public/images/Leaves.png";
import Strawberries from "../../public/images/Strawberries.png";
import LoadIcon from "../../public/images/ReloadIcon.png";
import HeadPhones from "../../public/images/MusicIcon.png";
import Details from "../../public/images/DetailsIcon.png";
import Contrator1 from "../../public/images/contractor_1.jpg";
import { additionalFontStyling } from "../../../Utilities";

interface CaptchaScreenProps {
  useFontTransformer?: boolean;
}

const FormContainer = styled.div`
  width: 100%;
  height: 100%;
  border: 1px solid ${(props) => props.theme.ColorsTypographyPrimary};
  border-radius: 50px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  gap: 24px;
  position: relative;
  overflow: hidden;
`;

const CaptchaContent = styled.div`
  width: 400px;
  height: 759px;
  display: flex;
  position: relative;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-right: 30px;
  z-index: 1;
`;

const CaptchaBox = styled.div`
  width: 400px;
  height: 582px;
  position: relative;
  background: ${(props) => props.theme.ColorsBackgroundInverse};
  box-shadow: 0px 1px 10px rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  border: 1px solid #d6d6d6;
`;

const CaptchaFooter = styled.div`
  position: absolute;
  left: 14px;
  top: 540px;
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  gap: 10px;
`;

const ImageContainer = styled.img<{ left: number }>`
  width: 24px;
  height: 24px;
  position: relative;
  left: ${({ left }) => left}px;
  top: 2px;
`;

const VerifyButton = styled.div<CaptchaScreenProps>`
  padding: 9px 23px 9px 24px;
  position: absolute;
  left: 292px;
  top: 535px;
  background: ${(props) => props.theme.ColorsUtilityColorFocus};
  border-radius: 2px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-size: ${(props) => props.theme.FontSize4}px;
  font-family: 'Roboto', sans-serif;
  ${(props) => additionalFontStyling(props.theme.FontWeightsInter3, true)};
  word-wrap: break-word;
`;

const HorizontalLine = styled.div`
  width: 400px;
  height: 1px;
  position: absolute;
  left: 0;
  top: 521px;
  background: #dfdfdf;
`;

const ImageGrid = styled.div`
  width: 386px;
  height: 386px;
  position: absolute;
  left: 7px;
  top: 129px;
`;

const ImageBox = styled.div<{ width: number, height: number, left: number, top: number }>`
  width: ${({ width }) => width}px;
  height: ${({ height }) => height}px;
  position: absolute;
  left: ${({ left }) => left}px;
  top: ${({ top }) => top}px;
  background: ${(props) => props.theme.ColorsBackgroundInverse};
  display: inline-flex;
  justify-content: center;
  align-items: center;
`;

const ImageSelectionPrompt = styled.div<CaptchaScreenProps>`
  height: 16px;
  padding: 24px 108px 69px 22px;
  position: absolute;
  left: 7px;
  top: 10px;
  background: ${(props) => props.theme.ColorsUtilityColorFocus};
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  color: ${(props) => props.theme.ColorsBackgroundInverse};
  font-size: 18px;
  font-family: 'Roboto', sans-serif;
  ${(props) => additionalFontStyling(props.theme.FontWeightsInter3, true)};
  word-wrap: break-word;
`;

const BackgroundImage = styled.div`
  width: 100%;
  height: 100%;
  background-image: url('${Contrator1}');
  background-repeat: no-repeat;
  border-radius: 50px 0 0 50px;
`;

/**
 * CaptchaScreen renders a Captcha challenge for the user to complete.
 * The challenge is to select all images with a bicycle.
 * The component renders a form with a grid of images, and a button to verify the selection.
 * The component also renders a heading and a paragraph with instructions.
 * The component uses several styled components to style the form and its elements.
 */
export const CaptchaScreen = () => {
  return (
    <div>
      <BackgroundImage>
      <FormContainer>
        <CaptchaContent>
        <Background />
                <h1>Heading</h1>
                <p>Please complete the Captcha</p>
            <CaptchaBox>
                <CaptchaFooter>
                  <ImageContainer src={LoadIcon} left={9.15} />
                  <ImageContainer src={HeadPhones} left={3} />
                  <ImageContainer src={Details} left={2} />
                </CaptchaFooter>
                <VerifyButton>VERIFY</VerifyButton>
                <HorizontalLine />
                <ImageGrid>
                    <ImageBox width={126} height={126} left={260} top={260}>
                        <img src={Bicycle3} alt="Bicycle" />
                    </ImageBox>
                    <ImageBox width={126} height={126} left={130} top={260}>
                        <img src={Bicycle2} alt="Bicycle" />
                    </ImageBox>
                    <ImageBox width={126} height={126} left={0} top={260}>
                        <img src={Book} alt="Book" />
                    </ImageBox>
                    <ImageBox width={126} height={126} left={260} top={130}>
                        <img src={Player} alt="Player" />
                    </ImageBox>
                    <ImageBox width={126} height={126} left={130} top={130}>
                        <img src={Bicycle1} alt="Bicycle" />
                    </ImageBox>
                    <ImageBox width={126} height={126} left={0} top={130}>
                        <img src={Building} alt="Building" />
                    </ImageBox>
                    <ImageBox width={126} height={126} left={260} top={0}>
                        <img src={Leaves} alt="Leaves" />
                    </ImageBox>
                    <ImageBox width={126} height={126} left={130} top={0}>
                        <img src={Bicycle4} alt="Bicycle" />
                    </ImageBox>
                    <ImageBox width={126} height={126} left={0} top={0}>
                        <img src={Strawberries} alt="Strawberries" />
                    </ImageBox>
                </ImageGrid>
                <ImageSelectionPrompt>Select all images with a bicycle.</ImageSelectionPrompt>
            </CaptchaBox>
        </CaptchaContent>
      </FormContainer>
      </BackgroundImage>
    </div>
  );
};
