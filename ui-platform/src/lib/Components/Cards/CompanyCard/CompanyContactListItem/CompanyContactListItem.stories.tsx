import type { Meta, StoryObj } from '@storybook/react';
import { CompanyContactListItem } from './CompanyContactListItem';

const meta: Meta<typeof CompanyContactListItem> = {
  component: CompanyContactListItem,
  title: 'Components/Cards/CompanyContactListItem',
};
export default meta;
type Story = StoryObj<typeof CompanyContactListItem>;

export const Overview: Story = {
  argTypes: {
    phonenumber: {
      control: {
        type: 'number',
      },
      defaultValue: 27555555555,
    },
    address: {
      control: {
        type: 'text',
      },
      defaultValue: 'Address Detail',
    },
  },
};
