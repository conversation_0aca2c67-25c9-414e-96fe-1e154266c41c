import React from 'react';
import styled, { useTheme } from 'styled-components';
import { Icon, IconTypes } from '../../../Icons';

const Wrapper = styled.div`
  width: 164px;
  height: 124px;
  gap: 8px;
  opacity: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  padding-top: 38px;
`;

const ItemWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
`;

const TextWrapper = styled.div`
  padding: 0px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
`;

const PhoneNumber = styled.div`
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  line-height: 16.94px;
  text-align: left;
  width: 114px;
  height: 17px;
  position: relative;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const Address = styled.div`
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  line-height: 16.94px;
  text-align: left;
  width: 104px;
  height: 17px;
  position: relative;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

export interface CompanyContactListItemProps {
  phonenumber: number;
  address: string;
}

/**
 * A single item in the company contact list.
 *
 * @param {Object} props Component props
 * @param {number} [props.phonenumber=2755555555] Phone number
 * @param {string} [props.address='Address Details'] Address
 */
export const CompanyContactListItem = ({
  phonenumber = 2755555555,
  address = 'Address Details',
}: CompanyContactListItemProps) => {
  const color = useTheme().ColorsIconColorPrimary;
  return (
    <Wrapper>
      <ItemWrapper>
        <Icon type="phone" size={16} color={color} />
        <TextWrapper>
          <PhoneNumber>{phonenumber}</PhoneNumber>
        </TextWrapper>
      </ItemWrapper>
      <ItemWrapper>
        <Icon type="plus" size={16} color={color} />
        <TextWrapper>
          <Address>{address}</Address>
        </TextWrapper>
      </ItemWrapper>
    </Wrapper>
  );
};
