import type { Meta, StoryObj } from '@storybook/react';
import { CompanyCard } from './CompanyCard';

const meta: Meta<typeof CompanyCard> = {
  component: CompanyCard,
  title: 'Components/Cards/CompanyCard',
};
export default meta;
type Story = StoryObj<typeof CompanyCard>;

export const DefaultCompanyCardStory: Story = {
  argTypes: {
    background: {
      control: 'select',
      options: ['primary'],
    },
  },
  args: {
    background: 'primary',
  },
};
