import React from 'react';
import styled from 'styled-components';
import { ProfileHeader } from '../../Avatars/ProfileHeader/ProfileHeader';
import { Icon, IconTypes } from '../../Icons';
import { CompanyContactListItem } from './CompanyContactListItem/CompanyContactListItem';

interface ICompanyCardProps {
  background: string;
  size: string;
  type: IconTypes;
  placeholder: string;
  email: string;
  username: string;
  phonenumber: number;
  address: string;
}

const CompanyCardContainer = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 269px;
  height: 158px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 0.5fr;
  border-radius: 8px;
  border: 0.5px;
  background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
  overflow: hidden;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize3}px;
  padding: 16px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  box-sizing: border-box;
`;

const CompanyCardDetailsSection = styled(({ ...rest }) => (
  <div {...rest}></div>
))`
  height: 126px;
  width: 237px;
  padding: 0px;
  grid-template-rows: 1fr 1fr;
  gap: 8px;
  background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
  align-self: center;
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
`;

const CompanyCardInfo = styled(({ ...rest }) => <div {...rest}></div>)`
  display: grid;
  height: 62px;
  grid-template-columns: 1fr 1fr;
  background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
  align-self: center;
  padding: 0 8px 0 8px;
`;

/**
 * @function CompanyCard
 * @description A component to display company profile information as a card.
 * @param {Object} props Component props
 * @param {string} [props.size] The size of the card
 * @param {string} [props.type] The type of the card
 * @param {string} [props.email] The email of the company
 * @param {string} [props.username] The username of the company
 * @param {string} [props.phonenumber] The phone number of the company
 * @param {string} [props.address] The address of the company
 * @returns {ReactElement} The company card component
 */
export const CompanyCard: React.FC<ICompanyCardProps> = ({
  size,
  type,
  email,
  username,
  phonenumber,
  address,
}) => {
  return (
    <CompanyCardContainer>
      <CompanyCardDetailsSection>
        <ProfileHeader email={email} username={username} />
        <CompanyCardInfo>
          <CompanyContactListItem phonenumber={phonenumber} address={address} />
        </CompanyCardInfo>
      </CompanyCardDetailsSection>
    </CompanyCardContainer>
  );
};
