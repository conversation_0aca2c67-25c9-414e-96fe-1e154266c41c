import {
  ProgressBarProps,
  statusMap,
} from '../../Bars/ProgressBar/ProgressBar';
import { ToolbarButton } from '../../Buttons/ToolbarButton/ToolbarButton';
import {
  BodySection,
  CalendarAppointmentCardExpandedHeading,
  CancellationDetailsSection,
  CancelledBody,
  InstallationDetailField,
  InstallationDetailRow,
  InstallationDetailValue,
  InstallationDetails,
} from './BodySection/BodySection';
import { Close, Container } from './Container/Container';
import { CalendarAppointmentCardDividerLine } from './Dividers/Dividers';
import {
  FooterActions,
  FooterHeading,
  FooterSection,
} from './FooterSection/FooterSection';
import {
  HeaderBodyContainer,
  HeaderContainer,
  HeaderLiveInfo,
  HeaderLiveInfoContainer,
  HeaderProgressBar,
  HeaderSection,
  HeaderSubtitle,
  HeaderTitle,
  Time,
} from './HeaderSection/HeaderSection';
export interface CalendarAppointmentCardExpandedProps
  extends Omit<ProgressBarProps, 'size'> {
  address: string;
  jobType: string;
  time: string;
  cancellationReason?: string;
  claimType: string;
  skill: string;
  contactPerson: string;
  contactNumber: string;
  notes: string;
}

/**
 * A component that displays a calendar appointment card expanded
 *
 * @param status - The status of the appointment
 * @param type - The type of the appointment
 * @param progress - The progress of the appointment
 * @param address - The address of the appointment
 * @param jobType - The job type of the appointment
 * @param time - The time of the appointment
 * @param skill - The skill of the appointment
 * @param claimType - The claim type of the appointment
 * @param contactPerson - The contact person of the appointment
 * @param contactNumber - The contact number of the appointment
 * @param notes - The notes of the appointment
 * @param cancellationReason - The cancellation reason of the appointment
 */
export function CalendarAppointmentCardExpanded({
  status,
  type,
  progress,
  address,
  jobType,
  time,
  skill,
  claimType,
  contactPerson,
  contactNumber,
  notes,
  cancellationReason,
}: CalendarAppointmentCardExpandedProps) {
  const statusMessage: statusMap = {
    neutral: 'On Track',
    success: 'Completed',
    error: 'Cancelled',
    inProgress: 'In Progress',
    warning: 'Might Be Late',
  };
  return (
    <Container>
      <HeaderSection>
        <HeaderContainer>
          <HeaderTitle>{address}</HeaderTitle>
          <HeaderSubtitle>{jobType}</HeaderSubtitle>
        </HeaderContainer>
        <HeaderBodyContainer status={status}>
          <HeaderLiveInfoContainer>
            <HeaderLiveInfo>
              {cancellationReason
                ? statusMessage['error']
                : statusMessage[status]}
            </HeaderLiveInfo>
            <Time>{time}</Time>
          </HeaderLiveInfoContainer>
          <HeaderProgressBar
            status={cancellationReason ? 'error' : status}
            type={type}
            size="lg"
            progress={progress}
          ></HeaderProgressBar>
        </HeaderBodyContainer>
      </HeaderSection>
      <CalendarAppointmentCardDividerLine />
      <BodySection>
        {cancellationReason && (
          <CancellationDetailsSection>
            <CalendarAppointmentCardExpandedHeading>
              Cancelled due to:{' '}
            </CalendarAppointmentCardExpandedHeading>
            <CancelledBody>{cancellationReason}</CancelledBody>
          </CancellationDetailsSection>
        )}
        <InstallationDetails>
          <CalendarAppointmentCardExpandedHeading>
            Installation Details
          </CalendarAppointmentCardExpandedHeading>
          <InstallationDetailRow>
            <InstallationDetailField>Claim Type</InstallationDetailField>
            <InstallationDetailValue>{claimType}</InstallationDetailValue>
            <InstallationDetailField>Skill</InstallationDetailField>
            <InstallationDetailValue>{skill}</InstallationDetailValue>
          </InstallationDetailRow>
        </InstallationDetails>
        <InstallationDetails>
          <CalendarAppointmentCardExpandedHeading>
            Contact Details
          </CalendarAppointmentCardExpandedHeading>
          <InstallationDetailRow>
            <InstallationDetailField>Contact Person</InstallationDetailField>
            <InstallationDetailValue>{contactPerson}</InstallationDetailValue>
          </InstallationDetailRow>
          <InstallationDetailRow>
            <InstallationDetailField>Contact Number</InstallationDetailField>
            <InstallationDetailValue>{contactNumber}</InstallationDetailValue>
          </InstallationDetailRow>
          <InstallationDetailRow>
            <InstallationDetailField>Notes</InstallationDetailField>
            <InstallationDetailValue>{notes}</InstallationDetailValue>
          </InstallationDetailRow>
        </InstallationDetails>
      </BodySection>
      <CalendarAppointmentCardDividerLine />
      <FooterSection>
        <FooterHeading>Action</FooterHeading>
        <FooterActions>
          <ToolbarButton buttonText="Action" />
          <ToolbarButton buttonText="Action" />
          <ToolbarButton buttonText="Action" />
        </FooterActions>
      </FooterSection>
      <Close />
    </Container>
  );
}
