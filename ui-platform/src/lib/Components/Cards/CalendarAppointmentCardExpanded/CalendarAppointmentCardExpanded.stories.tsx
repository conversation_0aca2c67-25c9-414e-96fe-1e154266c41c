import { Meta, StoryObj } from '@storybook/react';
import { withComponentShowcase } from '../../../Utilities';
import {
  CalendarAppointmentCardExpanded,
  CalendarAppointmentCardExpandedProps,
} from './CalendarAppointmentCardExpanded';

const statuses = ['neutral', 'success', 'error', 'inProgress', 'warning'];

const progressBarTypes = ['node', 'bar', 'nodebar'];

const meta: Meta<typeof CalendarAppointmentCardExpanded> = {
  component: CalendarAppointmentCardExpanded,
  title: 'Components/Cards/CalendarAppointmentCardExpanded',
  argTypes: {
    status: {
      control: {
        type: 'select',
        options: statuses,
      },
    },
    progress: {
      control: {
        type: 'range',
        min: 0,
        max: 100,
        step: 1,
      },
    },
    type: {
      control: {
        type: 'select',
        options: progressBarTypes,
      },
    },
  },
};
export default meta;
type Story = StoryObj<typeof CalendarAppointmentCardExpanded>;

const ExampleData: CalendarAppointmentCardExpandedProps = {
  address: '123 St. Douglasdale, Johannesburg ',
  jobType: 'Airconditioning',
  time: '12:30',
  progress: 67.5,
  type: 'nodebar',
  status: 'success',
  claimType: 'Pricelock Installation',
  skill: 'Airconditioning',
  contactPerson: 'Vaughn Assessor',
  contactNumber: '084 123 4567',
  notes:
    'This is a paragraph of what matters to the customer. The customer is not sure what the installation consists of. ',
};

export const Overview: Story = {
  args: ExampleData,
};
export const InProgress: Story = {
  args: {
    ...Overview.args,
    status: 'inProgress',
  },
};
export const Completed: Story = {
  args: {
    ...Overview.args,
    status: 'success',
  },
};
export const OnTrack: Story = {
  args: {
    ...Overview.args,
    status: 'neutral',
  },
};
export const Cancelled: Story = {
  args: {
    ...Overview.args,
    status: 'error',
    cancellationReason: 'Reschedule request by customer',
  },
};
export const MightBeLate: Story = {
  args: {
    ...Overview.args,
    status: 'warning',
  },
};
const ExpandedCalendarAppointmentCards = (
  args: CalendarAppointmentCardExpandedProps
) => {
  const Component = <CalendarAppointmentCardExpanded {...args} />;
  return withComponentShowcase(Component);
};
export const ProgressBarTypeVariants: Story = {
  args: {
    ...Overview.args,
  },
  render: (args) =>
    ExpandedCalendarAppointmentCards(args)('type', progressBarTypes),
};
export const StatusVariants: Story = {
  args: {
    ...Overview.args,
  },
  render: (args) => ExpandedCalendarAppointmentCards(args)('status', statuses),
};
