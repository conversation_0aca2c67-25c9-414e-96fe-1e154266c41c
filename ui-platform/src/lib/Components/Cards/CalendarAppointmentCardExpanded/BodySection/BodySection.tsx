import styled from 'styled-components';
import { Heading } from '../../../Heading/Heading';

export const BodySection = styled.div`
  display: grid;
  grid-template-rows: auto;
  grid-gap: 16px;
  align-items: start;
  justify-items: center;
  z-index: 2;
`;

export const CancellationDetailsSection = styled.div`
  position: relative;
  width: 100%;
  display: grid;
  grid-template-rows: auto;
  grid-gap: 16px;
  align-items: start;
  text-align: left;
`;

export const CalendarAppointmentCardExpandedHeading = styled(Heading)`
  margin: unset;
  font-weight: ${(props) => props.theme.FontWeightsInter0};
  font-size: ${(props) => props.theme.FontSize6}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
`;

export const CancelledBody = styled(Heading)`
  margin: 0;
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter1};
`;

export const InstallationDetails = styled.div`
  position: relative;
  width: 100%;
  display: grid;
  grid-template-rows: auto;
  padding: 0px 1px;
  box-sizing: border-box;
  grid-gap: 16px;
  text-align: center;
`;

export const InstallationDetailRow = styled.div`
  width: 100%;
  position: relative;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  display: grid;
  justify-content: center;
  gap: 20px;
  grid-template-columns: 1fr 1fr;
`;

export const InstallationDetailField = styled.div`
  font-weight: ${(props) => props.theme.FontWeightsInter1};
  text-align: right;
  max-width: 200px;
`;

export const InstallationDetailValue = styled.div`
  text-align: left;
  max-width: 200px;
`;
