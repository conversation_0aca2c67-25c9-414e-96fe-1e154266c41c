import styled, { useTheme } from 'styled-components';
import { Icon } from '../../../Icons';

export const Container = styled.div`
  width: 100%;
  position: relative;
  backdrop-filter: blur(19px);
  border-radius: 12px;
  background-color: ${(props) => props.theme.ColorsCardColorCalendarActive};
  border: 0.5px solid ${(props) => props.theme.ColorsStrokesDefault};
  box-sizing: border-box;
  display: grid;
  grid-template-rows: auto;
  padding: 24px 16px;
  grid-gap: 16px;
  justify-items: center;
  align-items: start;
  text-align: center;
  font-size: ${(props) => props.theme.FontSize6}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
`;

export const Close = styled(({ className }: { className?: string }) => {
  const color = useTheme().ColorsTypographyPrimary;
  return <Icon type="x-xircle" size={24} color={color} className={className} />;
})`
  cursor: pointer;
  width: 24px;
  position: absolute;
  margin: 0 !important;
  top: 16px;
  right: 18px;
  height: 24px;
  z-index: 6;
`;
