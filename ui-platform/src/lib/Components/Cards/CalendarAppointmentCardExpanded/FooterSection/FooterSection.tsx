import styled from 'styled-components';
import { Heading } from '../../../Heading/Heading';

export const FooterSection = styled.div`
  display: grid;
  grid-template-rows: auto auto;
  justify-items: center;
  align-items: start;
  z-index: 4;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize3}px;
  gap: 16px;
`;

export const FooterHeading = styled(Heading)`
  margin: unset;
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontFamiliesInter};
`;

export const FooterActions = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px;
`;
