import styled from 'styled-components';
import { ProgressBar, statusMap } from '../../../Bars/ProgressBar/ProgressBar';
import { Italised } from '../../../Italised/Italised';
import { Heading } from '../../../Heading/Heading';

export const HeaderSection = styled.div`
  display: grid;
  grid-template-rows: repeat(2, auto); /* Two rows: one for header and one for content */
  justify-items: center;
  align-items: start;
  padding: 16px;
  gap: 16px;
  z-index: 0;
`;

export const HeaderContainer = styled.div`
  display: grid;
  grid-template-rows: auto auto;
  justify-items: center;
  gap: 8px;
`;

export const HeaderTitle = styled.div`
  width: 100%;
  position: relative;
  font-weight: ${(props) => props.theme.FontWeightsInter5};
`;

export const HeaderSubtitle = styled(Heading)`
  width: 100%;
  position: relative;
  font-size: ${(props) => props.theme.FontSize3}px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: unset;
`;

export const HeaderBodyContainer = styled.div<{ status: keyof statusMap }>`
  width: 100%;
  position: relative;
  display: grid;
  grid-template-rows: auto auto;
  justify-items: center;
  gap: 24px;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => {
    const statuses: statusMap = {
      neutral: props?.theme.ColorsUtilityColorFocus,
      success: props?.theme.ColorsUtilityColorSuccess,
      error: props?.theme.ColorsUtilityColorError,
      inProgress: props?.theme.ColorsUtilityColorProgress,
      warning: props?.theme.ColorsUtilityColorWarning,
    };
    return statuses[props.status];
  }};
`;

export const HeaderLiveInfoContainer = styled.div`
  width: 100%;
  display: grid;
  grid-template-columns: auto auto;
  align-items: center;
  padding: 0px 1px;
  gap: 10px;
`;

export const HeaderLiveInfo = styled(Italised)`
  width: 217px;
  font-weight: ${(props) => props.theme.FontWeightsInter5};
  z-index: 0;
`;

export const Time = styled(Italised)`
  width: 66px;
  position: absolute;
  top: 0px;
  right: -0.5px;
  display: inline-block;
  font-weight: ${(props) => props.theme.FontWeightsInter5};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  text-align: right;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  z-index: 1;
`;

export const HeaderProgressBar = styled(ProgressBar)`
  align-self: stretch;
  /* display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end; */
  padding: 2px 0px;
`;
