import styled from 'styled-components';
import { Divider, DividerProps } from '../../../Dividers/Divider';

/**
 * A divider component with a predefined size and background color.
 * @component
 * @prop {Omit<DividerProps, 'size' | 'background'>} props - The properties of the component.
 * @returns {JSX.Element} The rendered Divider component.
 */

function CardDivider({
  ...props
}: Omit<DividerProps, 'size' | 'background'>) {
  return <Divider size={'fullWidth'} background={'grey'} {...props} />;
}

export const CalendarAppointmentCardDividerLine = styled(CardDivider)`
  align-self: stretch;
  position: relative;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
  z-index: 1;
`;
