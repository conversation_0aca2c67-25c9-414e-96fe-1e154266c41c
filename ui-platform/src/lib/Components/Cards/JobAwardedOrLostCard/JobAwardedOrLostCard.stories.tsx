import type { Meta, StoryObj } from '@storybook/react';
import { JobAwardedOrLostCard } from './JobAwardedOrLostCard';

const meta: Meta<typeof JobAwardedOrLostCard> = {
  title: 'Components/Cards/JobAwardedOrLostCard',
  component: JobAwardedOrLostCard,
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof JobAwardedOrLostCard>;

export const Awarded: Story = {
  args: {
    job_id: '10981',
    skill: 'Plumbing',
    area: 'Downtown',
    appointment_date: '2024-03-20',
    time: '14:00-16:00',
  },
};

export const Lost: Story = {
  args: {
    job_id: '10982',
    skill: 'Electrical',
    area: 'Suburbs',
    appointment_date: '2024-03-21',
    time: '09:00-11:00',
    
  },
};  