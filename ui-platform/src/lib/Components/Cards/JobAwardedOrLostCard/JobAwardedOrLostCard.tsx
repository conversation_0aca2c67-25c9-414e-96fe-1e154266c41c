import React from 'react';
import styled from 'styled-components';

interface IJobAwardedOrLostCardProps {
  job_id: string;
  skill: string;
  area: string;
  appointment_date: string;
  time: string;
}

const JobAwardedOrLostCardContainer = styled.div`
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 16px;
  border-radius: 4px;
  background: ${(props) => props.theme?.ColorsCardColorJobCardPrimary};
  height: 48px;
  overflow: hidden;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize3}px;
  padding: 0 16px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  box-sizing: border-box;
  margin-bottom: 14px;
`;

const CardSection = styled.div`
  display: grid;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

export const JobAwardedOrLostCard: React.FC<IJobAwardedOrLostCardProps> = (props) => {
  console.log('JobAwardedOrLostCard received props:', props);
  const {
    job_id,
    skill,
    area,
    appointment_date,
    time,
  } = props;
  
  return (
    <JobAwardedOrLostCardContainer>
      <CardSection>{job_id}</CardSection>
      <CardSection>{skill}</CardSection>
      <CardSection>{area}</CardSection>
      <CardSection>{`${appointment_date} ${time}`}</CardSection>
    </JobAwardedOrLostCardContainer>
  );
};

// export const JobAwardedOrLostCard: React.FC<IJobAwardedOrLostCardProps> = ({
//   job_id,
//   skill,
//   area,
//   appointment_date,
//   time,
// }) => {
//   return (
//     <JobAwardedOrLostCardContainer>
//       <CardSection>{job_id}</CardSection>
//       <CardSection>{skill}</CardSection>
//       <CardSection>{area}</CardSection>
//       <CardSection>{`${appointment_date} ${time}`}</CardSection>
//     </JobAwardedOrLostCardContainer>
//   );
// };