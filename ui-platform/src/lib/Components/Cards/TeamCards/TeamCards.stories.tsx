import type { Meta, StoryObj } from '@storybook/react';
import { TeamCards } from './TeamCards';

const meta: Meta<typeof TeamCards> = {
  component: TeamCards,
  title: 'Components/Cards/TeamCards',
};
export default meta;
type Story = StoryObj<typeof TeamCards>;

export const Overview: Story = {
  argTypes: {},
  args: {
    teamCardName: 'A tribe Called Quest',
    isActive: true,
    positionName: 'Team Lead',
    buildingType: 'Building',
    rating: 4.0,
    phoneNumber: '078 5555 555',
    email: '<EMAIL>',
  },
};

export const CompressedState: Story = {
  argTypes: {},
  args: {
    ...Overview.args,
  },
};

export const BasicView: Story = {
  argTypes: {},
  args: {
    teamCardName: 'A tribe Called Quest',
    isActive: true,
    positionName: 'Team Lead',
  },
};
