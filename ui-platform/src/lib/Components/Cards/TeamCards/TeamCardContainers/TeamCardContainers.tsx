import React from 'react';
import styled, { useTheme } from 'styled-components';

export const Container = styled(({ ... rest }) => <div {...rest}></div>)`
  display: flex;
  width: 756px;
  height: 56px;
  border-radius: 4px;
  font-family: ${(props) => props.theme.FontFamiliesInter};
  background-color: ${(props) => props.theme.ColorsTabsToolbarPrimary};
  font-size: ${(props) => props.theme.FontSize3}px;
  line-height: 16.94px;
  overflow: hidden;
  box-sizing: border-box;
  grid-template-columns: 1fr 0.3fr 0.3fr 0.3fr 0.3fr 0.3fr 0.3fr 0.5fr;
  display: grid;
`;

export const IconContainer = styled.div<{ width: number }>`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  overflow: hidden;
  box-sizing: border-box;
`;
