import React from 'react';
import styled, { useTheme } from 'styled-components';
import { Icon, IconProps, IconTypes } from '../../Icons';
import { Avatar } from '../../Avatars/Avatar/Avatar';
import { Container, IconContainer } from './TeamCardContainers/TeamCardContainers';
import { Divider } from '../../Dividers/Divider';
import { Circle }  from './TeamCardShapes/TeamCardShapes';
import  star  from './TeamCardShapes/assets/images/star.svg'

interface TeamCardProps {
  teamCardName?: string;
  buildingType?: string;
  positionName?: string;
  rating?: number | string;
  phoneNumber?: number | string;
  email?: string;
  icon?: {
    iconType?: IconTypes;
    iconSize?: IconProps['size'];
    iconFill?: IconProps['fill'];
    iconStrokeWidth?: IconProps['strokeWidth'];
  };
  isActive: boolean;
}

interface ShapeProps {
  size: number;
  color: string;
}

interface ActivityIndicatorProps {
  isActive: boolean;
}

/**
 * Returns a Circle component and a 'Active' text if the prop `isActive` is true.
 * Otherwise, returns null.
 * @param {ActivityIndicatorProps} props
 * @returns {JSX.Element | null}
 */
const ActivityIndicator: React.FC<ActivityIndicatorProps> = ({ isActive }) => {
  if (isActive) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
        <Circle size={16} color="" />
        <span style={{ fontSize: '14px', fontWeight: 300, lineHeight: '16.94px', textAlign: 'left', marginLeft: '8px', marginTop: '8px' }}>
          Active
        </span>
      </div>
    );
  }
  return null;
};

const TeamCardSection = styled.div<{ width: number }>`
  display: grid;
  justify-items: center;
  border: 0px, 0.5px, 0px, 0px;
  //padding: 8px, 16px, 8px, 16px;
`;



const TextLabel = styled.div`
  display: grid;
  justify-items: center;
  align-items: center;
  width: 74.5px;
  padding: 8px;
  white-space: nowrap;
  border: '0px, 0.5px, 0px, 0.5px';
  color: ${(props) => props.theme.ColorsUtilityColorFocus};
  max-width: 8ch;
  text-overflow: ellipsis;
  overflow: hidden;
  box-sizing: border-box;
`;

const ProfileImg = styled(Avatar)`
  grid-row: span 2;
  height: 32px;
  width: 32px;
`;

const DividerLine = styled(Divider)`
  grid-row: span 2;
  height: 100%;
  width: 16px;
  overflow: hidden;
  max-height: 100%;
`;

/**
 * A single team member card to be used in a list of team members.
 *
 * @example
 * <TeamCards
 *   teamCardName="John Doe"
 *   buildingType="Team Lead"
 *   positionName="Job Card"
 *   rating={4.5}
 *   phoneNumber="************"
 *   email="<EMAIL>"
 * />
 * @param {TeamCardProps} props - The props for the component
 * @returns {React.ReactElement} The component
 */
export const TeamCards: React.FC<TeamCardProps> = ({
  isActive,
  icon,
  teamCardName,
  buildingType,
  positionName,
  rating,
  phoneNumber,
  email,
}: TeamCardProps) => {

  return (
    <Container>
      <TeamCardSection style={{ gap: '16px' }} width={221}>
        <ProfileImg size="medium" onFileSave={() => {}} />
        {teamCardName}
      </TeamCardSection>
      {/* <DividerLine size="fullWidth" background="grey" height="thin" /> */}
      <TeamCardSection width={74.5}>
          <ActivityIndicator isActive={isActive} />
      </TeamCardSection>

      <TeamCardSection style={{ gap: '4px' }} width={74.5}>
        {rating}
        <img src={star} />
        </TeamCardSection>
      <TextLabel>{positionName}</TextLabel>
      <TextLabel style={{ border: '0px, 0px, 0px, 0.5px' }}>
        {buildingType}
      </TextLabel>
      <TextLabel style={{ alignItems: 'left', justifyContent: 'left' }}>{phoneNumber}</TextLabel>
      <TextLabel>{email}</TextLabel>

      <IconContainer style={{ border: '0px, 16px, 0px, 16px' }} width={88}>
        <Icon {...icon} type={icon?.iconType ?? 'edit-05'} size={24} />
        <Icon {...icon} type={icon?.iconType ?? 'chevron-down'} size={24} />
      </IconContainer>
    </Container>
  );
};
