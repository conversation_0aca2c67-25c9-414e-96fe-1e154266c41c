import React from 'react';
import styled from 'styled-components';

interface ShapeProps {
    size: number;
    color: string;
  }
  
  export const Circle = styled.div<ShapeProps>`
  width: 16px;
  height: 16px;
  background-color: ${(props) => props.theme.ColorsStrokesSuccess};
  border-radius: 50%;
  align-items: center;
  justify-content: center;
`;

// export const Star = styled.div<ShapeProps>`
//   position: relative;
//   display: inline-block;
//   color: ${({ color }) => color || 'gold'};
//   width: 0;
//   height: 0;
//   border-right: ${({ size }) => (size || 24) / 4}px solid transparent;
//   border-bottom: ${({ size }) => (size || 24) / 4}px solid ${({ color }) => color || 'gold'};
//   border-left: ${({ size }) => (size || 24) / 4}px solid transparent;
//   transform: rotate(35deg);

//   &::before,
//   &::after {
//     content: '';
//     position: absolute;
//     top: 0;
//     left: -${({ size }) => (size || 24) / 4}px;
//     width: 0;
//     height: 0;
//     border-right: ${({ size }) => (size || 24) / 4}px solid transparent;
//     border-bottom: ${({ size }) => (size || 24) / 4}px solid ${({ color }) => color || 'gold'};
//     border-left: ${({ size }) => (size || 24) / 4}px solid transparent;
//   }

//   &::before {
//     transform: rotate(-70deg);
//   }

//   &::after {
//     transform: rotate(70deg);
//   }
// `;