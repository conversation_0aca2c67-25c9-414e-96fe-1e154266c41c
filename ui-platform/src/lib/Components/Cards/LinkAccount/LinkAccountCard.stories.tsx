import type { Meta, StoryObj } from '@storybook/react';
import { LinkAccountCard } from '../LinkAccount/LinkAccountCard';

const meta: Meta<typeof LinkAccountCard> = {
  component: LinkAccountCard,
  title: 'Components/Cards/LinkAccountCard',
};
export default meta;
type Story = StoryObj<typeof LinkAccountCard>;

export const DefaultLinkAccountCardStory: Story = {
  args: {
    data: { name: "<PERSON><PERSON><PERSON>", id: "#123456", otherData: "Test"},
    nameProp: 'name',
    valueProp: 'id',
    onClick(e, data) {
      alert('The button was clicked');
    },
  },
};
