import React, { Dispatch, useMemo, useState } from 'react';
import styled from 'styled-components';
import { Text } from '../../../Fragments/Text/Text';
import { TextButton } from '../../Buttons/TextButton/TextButton';
import { Icon } from '../../Icons';

interface ILinkAccountCardProps {
  data: Record<string, any>;
  nameProp: string;
  valueProp: string;
  accountLinked?: boolean;
  onClick?: (
    e: React.MouseEvent<HTMLButtonElement> | any,
    data: Record<string, any>,
    value: any,
    componentState: {
      setState: Dispatch<React.SetStateAction<boolean>>;
      state: boolean;
    }
  ) => void;
}

const LinkAccountCardContainer = styled(({ accountLinked, ...rest }) => (
  <div {...rest}></div>
))`
  width: 100%;
  height: 49px;
  display: grid;
  grid-template-columns: 1fr 0.5fr;
  border: 1px solid
    ${(props) =>
      props.accountLinked
        ? props?.theme.ColorsUtilityColorSuccess
        : props?.theme.ColorsInputsPrimary};
  border-radius: 4px;
  background: ${(props) => props?.theme.ColorsBackgroundModule};
  box-sizing: border-box;
`;

const CompanyCardDetailsSection = styled(({ ...rest }) => (
  <div {...rest}></div>
))`
  height: 49px;
  margin-left: 7px;
  align-self: center;
  display: flex;
  align-items: center;
  color: ${(props) => props?.theme.ColorsTypographySecondary};
`;

const Button = styled(({ ...rest }) => <div {...rest}></div>)`
  height: 43px;
  align-self: center;
  margin-right: 7px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
`;

const StyledIcon = styled(Icon)`
  height: 20px;
  width: 20px;
`;

/**
 * @function CompanyCard
 * @description A component to display company profile information as a card.
 * @param {Object} props Component props
 * @param {string} [props.name] The name of the card
 * @param {() => void} onClick - The function to be called when the button is clicked.
 * @param {boolean} [props.accountLinked] Is the account linked
 * @returns {ReactElement} The account linked card component
 */

export const LinkAccountCard: React.FC<ILinkAccountCardProps> = ({
  data,
  nameProp,
  accountLinked = false,
  onClick,
  valueProp,
}) => {
  // const linked = useMemo(() => accountLinked, [accountLinked]);
  const [linked, setLinked] = useState<boolean>(accountLinked);

  const handleClick = (e: React.MouseEvent<HTMLButtonElement> | any) => {
    if (onClick) {
      onClick(e, data, valueProp, { state: linked, setState: setLinked });
    }
  };

  return (
    <LinkAccountCardContainer accountLinked={linked}>
      <CompanyCardDetailsSection>
        <Text
          textItems={[
            {
              text: data[nameProp],
              options: { format: 'dashboard_tile', type: 'heading' },
            },
          ]}
        />
      </CompanyCardDetailsSection>

      <Button>
        {linked ? (
          <StyledIcon type="check" size={24} />
        ) : (
          <TextButton btnValue="Link Account" onClick={handleClick} />
        )}
      </Button>
    </LinkAccountCardContainer>
  );
};
