import { <PERSON>Obj, Meta } from '@storybook/react/';
import { LinkCardsDropdown } from './LinkCardsDropdown';

const meta: Meta<typeof LinkCardsDropdown> = {
  component: LinkCardsDropdown,
  title: 'Components/Cards/LinkCardsDropdown',
};
export default meta;
type Story = StoryObj<typeof LinkCardsDropdown>;

export const Default: Story = {
  args: {
    placeholder: 'Type to filter', 
    options: [
      'Pipes Only',
      'Pipes + Damages',
      'Geyser Only',
      'Impact',
      'Glass and Sanitary',
      'Lightening',
      'Impact',
      'Storm',
      'Fire',
      'Impact',
      'Pipes Only',
    ], 
  },
};

// export const DropdownItemWithProfile: Story = {
//   args: {
    
//     },
// };
