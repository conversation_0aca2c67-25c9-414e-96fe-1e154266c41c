import React, { useState, MouseEvent } from 'react';
import styled from 'styled-components';
import { PlainTextInput } from '../../Inputs/PlainTextInput/PlainTextInput';
import { LinkCardItem } from './LinkCardItem/LinkCardItem';
import { Divider } from '../../Dividers/Divider';

export interface LinkCardsDropdownProps {
  options: string[];
  optionsHandler?: (e?: MouseEvent<HTMLElement>) => void;
  className?: string;
  withIcon?: boolean;
  placeholder?: string;
}

const GlowLine = styled(Divider)`
  position: absolute;
  width: 704px;
  align-items: center;
`;

const Container = styled.div`
  width: 100%;
  height: 485px;
  gap: 24px;
  opacity: 0px;
`;

const InputBox = styled(PlainTextInput)`
  height: 33px;
  line-height: 16.94px;
  width: 704px;
  gap: 8px;
  opacity: 0px;
  margin-bottom: 24px;
  border: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
  background: ${(props) => props.theme.ColorsInputsPrimary};
  opacity: 0px;
  border-radius: 4px;
`;

const LinkCardsDropdownContainer = styled.div`
  position: relative;
  max-height: 421px;
  height: 100%;
  width: 720px;
  grid-gap: 2px;
  opacity: 0px;
  overflow-y: scroll;
`;

const LinkCardsBody = styled.div`
  width: 704px;
  height: 421px;
  margin-right: 8px;
  opacity: 0px;
`;

/**
 * A dropdown component that displays a list of link cards.
 *
 * @param {{ options: string[]; className?: string; placeholder?: string }} props
 * @returns {JSX.Element}
 */
/**
 * @typedef {Object} LinkCardsDropdownProps
 * @property {string[]} options - The list of options to display in the dropdown.
 * @property {string} [className] - The CSS class name to apply to the dropdown container.
 * @property {string} [placeholder] - The placeholder text to display in the input field.
 */
export const LinkCardsDropdown: React.FC<LinkCardsDropdownProps> = ({
  options,
  className,
  placeholder = 'Type to filter',
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleOptionClick = (option: string) => {
    console.log(`Selected option: ${option}`);
  };

  const filteredOptions = options.filter((option) =>
    option.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Container>
      {/* KGOTHATSO: This is causing build to fail, uncomment it and fix it */}
      {/* <InputBox
        placeholder={placeholder}
        value={searchTerm}
        onChange={handleSearch}
        className="custom-input"
      /> */}
      <LinkCardsDropdownContainer>
      <GlowLine
          size="fullWidth"
          background="default"
          type="tabActive"
          state="grey"
        />
        <LinkCardsBody>
          {filteredOptions.map((option, index) => (
            <LinkCardItem
              key={index}
              label={option}
              onClick={() => handleOptionClick(option)}
            />
          ))}
        </LinkCardsBody>
        <GlowLine
          size="fullWidth"
          background="default"
          type="tabActive"
          state="grey"
        />
      </LinkCardsDropdownContainer>
    </Container>
  );
};
