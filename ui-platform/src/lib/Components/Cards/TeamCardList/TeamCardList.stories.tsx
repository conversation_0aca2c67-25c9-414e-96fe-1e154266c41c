import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { TeamCard } from './TeamCardList';
import styled from 'styled-components';
import { Icon } from '../../Icons';

const meta: Meta<typeof TeamCard> = {
  component: TeamCard,
  title: 'Components/Cards/TeamCardList',
};
export default meta;
type Story = StoryObj<typeof TeamCard>;

export const Overview: Story = {
  args: {
    data: [
      {
        "staff_id": "2ef34731-ed6a-4838-957b-05a438395565",
        "full_name": "<PERSON>",
        "username": "<PERSON>",
        "roles": [
          "SP Job Scheduler",
          "SP Team Leader",
          "SP Admin"
        ],
        "active": {
          "active_state": 0,
          "active_state_name": "Disabled",
          "reason": "New user incomplete"
        },
        "contact_number": "0123234568",
        "email_address": "<EMAIL>"
      }
    ],
    linkUrl: '/team/',
    reduceOpacity: false,
    isBorder: true,
    config: [
      {
        type: 'linkColumn',
        showAvatar: true, 
        showTitleText: true, // Show Name
        showRating: true,
        showStatusIndicator: true,
        titleKey: 'full_name', 
      },
      {
        type: 'textColumn',
        titleKey: 'roles', // Render roles in this section
      },
      {
        type: 'textColumn',
        titleKey: 'contact_number', // Render contact number in this section
      },
      {
        type: 'textColumn',
        titleKey: 'email_address', // Render active state in this section
      },
      {
        type: 'textColumn',
        titleKey: 'username', // Render active state in this section
      },
    ],
  },
};


