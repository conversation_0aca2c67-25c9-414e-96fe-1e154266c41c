import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { withComponentShowcase } from '../../../Utilities';
import  { MapAppointmentCard } from './MapAppointmentCard';

const meta: Meta<typeof MapAppointmentCard> = {
  component: MapAppointmentCard,
  title: 'Components/Cards/MapAppointmentCard',
};
export default meta;

type Story = StoryObj<typeof MapAppointmentCard>;

const statuses = ['neutral', 'success', 'error', 'inProgress', 'warning'];
const progressBarTypes = ['node', 'bar', 'nodebar'];

export const Overview: Story = {
  argTypes: {
    status: {
      control: {
        type: 'select',
        options: statuses,
      },
    },
    progress: {
      control: {
        type: 'range',
        min: 0,
        max: 100,
        step: 1,
      },
    },
    type: {
      control: {
        type: 'select',
        options: progressBarTypes,
      },
    },
  },
  args: {
    status: 'neutral',
    address: '123 St. Douglasdale, Johannesburg ',
    jobType: 'Airconditioning',
    time: '12:30',
    progress: 50,
    type: 'bar',
  },
};