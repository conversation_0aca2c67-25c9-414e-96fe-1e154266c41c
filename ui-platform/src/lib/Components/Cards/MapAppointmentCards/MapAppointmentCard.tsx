import {
  ProgressBar,
  ProgressBarProps,
  statusMap,
} from '../../Bars/ProgressBar/ProgressBar';
import { CalendarAppointmentCardAddress } from '../CalendarAppointmentCard/CalendarAppointmentCardAddress/CalendarAppointmentCardAddress';
import { CalendarAppointmentContainer } from '../CalendarAppointmentCard/CalendarAppointmentCardContainer/CalendarAppointmentCardContainer';
import { CalendarAppointmentCardDetails } from '../CalendarAppointmentCard/CalendarAppointmentCardDetails/CalendarAppointmentCardDetails';
import { CalendarAppointmentCardJobType } from '../CalendarAppointmentCard/CalendarAppointmentCardJobType/CalendarAppointmentCardJobType';
import {
  CalendarAppointmentCardLiveInfo,
  CalendarAppointmentCardLiveInfoProps,
} from '../CalendarAppointmentCard/CalendarAppointmentCardLiveInfo/CalendarAppointmentCardLiveInfo';

// Import the IconButton component
import { IconButton } from '../../Buttons/IconButton/IconButton';

export interface CalendarAppointmentCardProps
  extends Omit<
      CalendarAppointmentCardLiveInfoProps,
      'status' | 'statusMessage'
    >,
    Omit<ProgressBarProps, 'size'> {
  address: string;
  jobType: string;
}

/**
 * A component that renders a list of SPAllocationCards with a search functionality and a button.
 *
 * @component
 * @example
 * const allocation = [
 *   { SPName: 'Provider 1', SPNumber: '001', Allocated: 10, Score: 85 },
 *   { SPName: 'Provider 2', SPNumber: '002', Allocated: 20, Score: 90 }
 * ];
 *
 * <ManuallyAllocate allocation={allocation} />
 */
export function MapAppointmentCard({
  address,
  jobType,
  time,
  status,
  ...props
}: CalendarAppointmentCardProps) {
  const statusMessage: statusMap = {
    neutral: 'On Track',
    success: 'Complete!',
    error: 'Missed',
    inProgress: 'In Progress',
    warning: 'Running Late',
  };

  return (
    <CalendarAppointmentContainer style={{height: '118px'}}>
      <CalendarAppointmentCardDetails>
        <CalendarAppointmentCardAddress address={address} />
        <CalendarAppointmentCardJobType jobType={jobType} />
        <IconButton />
        <CalendarAppointmentCardLiveInfo
          time={time}
          status={status}
          statusMessage={statusMessage[status]}
        />
      </CalendarAppointmentCardDetails>
      <ProgressBar size="smll" status={status} {...props} />
    </CalendarAppointmentContainer>
  );
}
