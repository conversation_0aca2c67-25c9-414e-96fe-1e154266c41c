import type { Meta, StoryObj } from '@storybook/react';
import {PermissionsIndicator} from './PermissionsIndicator'

const meta: Meta<typeof PermissionsIndicator> = {
  component: PermissionsIndicator,
  title: 'Components/Cards/PermissionsIndicator',
};
export default meta;
type Story = StoryObj<typeof PermissionsIndicator>;

export const DefaultPermissionsIndicatorStory: Story = {

  argTypes: {
   color: {
    control	: 'select',
    options: ['blue', 'green', 'orange', 'purple', 'grey']
   },
   size: {
    control	: 'select',
    options: ['small', 'default']	
   },
   position: {
    control	: 'select',
    options: ['left', 'right']	
   }
  },
  args: {
   
  }
};