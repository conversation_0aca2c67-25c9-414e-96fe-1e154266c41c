import { blue, green, grey, red, yellow } from "@mui/material/colors";
import styled, { css } from "styled-components";


interface PermissionsIndicatorProps {
    color: string;
    size: string;
    position: string
    // children?: any
}

interface ColourStyleProps {
    [key: string]: any
}

interface PositionProps {
    [key: string]: any
}
interface SizeProps {
    [key: string]: any
}

const colourStyles: ColourStyleProps = {
    blue: css`
        .indicator-svg {
            fill: #2962FF;
            stroke: #0D47A1;
            filter: drop-shadow(0 0 10px #2962FF);
        }
    `,
    green: css`
        .indicator-svg {
            fill: #4CAF50;
            stroke: #2d6f30;
            filter: drop-shadow(0 0 10px #4CAF50);
        }
    `,
    orange: css`
        .indicator-svg {
            fill: #E91E63;
            stroke: #C2185B;
            filter: drop-shadow(0 0 10px #E91E63);
        }
    `,
    purple: css`
        .indicator-svg {
            fill: #be2edd;
            stroke: #7c0f94;
            filter: drop-shadow(0 0 10px #be2edd);
        }
    `,
    red: css`
        .indicator-svg {
            fill: #ffc107;
            stroke: #ffa000;
            filter: drop-shadow(0 0 10px #ffc107);
        }
    `,
    yellow: css`
        .indicator-svg {
            fill: ${(props) => props?.theme.ColorsTypographyPrimary};
            stroke: ${(props) => props?.theme.ColorsTypographyPrimary};
            filter: drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));
        }
    `,
    grey: css`
    .indicator-svg {
        fill: #1D1D1D;
      stroke: #141414;
    }
`
};

const positionStyles: PositionProps = {
    left: css``,
    right: css`
      transform: rotate(180deg);
    `,
  };
  
  const sizeStyles: SizeProps = {
    default: css`
      .indicator-svg {
        height: 36px;
        width: 6px;
      }
    `,
    small: css`
      .indicator-svg {
        height: 36px;
        width: 5px;
      }
    `,
  };

// new code above
const PermissionsIndicatorWrapper = styled(({ ...rest }) => <div {...rest}></div>)`
${({ color }) => colourStyles[color] || colourStyles.grey}
${({ position }) => positionStyles[position] || positionStyles.left}
${({ size }) => sizeStyles[size] || sizeStyles.default}
`

/**
 * A component that renders a permissions indicator, based on the given color, position and size. The indicator is a small triangle pointing in the given direction.
 * @param {PermissionsIndicatorProps} props
 * @prop {Color} color The color of the indicator. Defaults to grey.
 * @prop {Position} position The position of the indicator. Defaults to left.
 * @prop {Size} size The size of the indicator. Defaults to default.
 * @returns {ReactElement} A React element representing the indicator.
 */
export const PermissionsIndicator: React.FC<PermissionsIndicatorProps> = ({color, position, size,  ...rest}) => { 
    return (

        <PermissionsIndicatorWrapper color={color} position={position} size={size}>

<svg className="indicator-svg" viewBox="0 0 6 36">
        <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g className="indicator-svg" fill="" stroke="">
            <path
              d="M0.5,5.23418747 L0.5,30.7949873 L5.5,34.9365877 L5.5,1.06752081 L0.5,5.23418747 Z"
              id="Rectangle"
              transform="translate(3.000000, 18.000000) scale(-1, 1) translate(-3.000000, -18.000000) "
            ></path>
          </g>
        </g>
      </svg>
        </PermissionsIndicatorWrapper>
    )
  }