import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { DocumentCard } from './DocumentCard';

const meta = {
  title: 'Components/Cards/DocumentCard',
  component: DocumentCard,
  tags: ['autodocs'],
  parameters: {
    componentSubtitle:
      'A versatile component for displaying and managing document files with thumbnails',
    layout: 'padded',
    docs: {
      description: {
        component: `
<div class="documentation-wrapper" style="max-width: 1200px; margin: 0 auto; padding: 20px; font-family: system-ui, -apple-system, sans-serif;">

<div style="background: linear-gradient(90deg, #2C5282, #2B6CB0); padding: 2px; border-radius: 8px; margin-bottom: 30px;">
  <div style="background: white; padding: 20px; border-radius: 6px;">
    <h1 style="color: #2C5282; margin-top: 0;">DocumentCard Component</h1>
    <p style="color: #4A5568; font-size: 1.1em; line-height: 1.5;">
      A powerful and flexible document management component that handles file previews, uploads, and downloads with an intuitive user interface.
    </p>
  </div>
</div>

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
  <div style="background: #EDF2F7; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">🎯 Key Features</h3>
    <ul style="list-style-type: none; padding: 0; margin: 0;">
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Form Integration
      </li>
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ File Validation
      </li>
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Drag & Drop
      </li>
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Preview Support
      </li>
    </ul>
  </div>
  
  <div style="background: #EDF2F7; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">⚡ Quick Stats</h3>
    <ul style="list-style-type: none; padding: 0; margin: 0;">
      <li style="margin-bottom: 8px;">📦 Size Limit: 5MB</li>
      <li style="margin-bottom: 8px;">🖼️ Supported: PDF, Images</li>
      <li style="margin-bottom: 8px;">🎨 Themes: Light/Dark</li>
      <li style="margin-bottom: 8px;">📱 Responsive: Yes</li>
    </ul>
  </div>
</div>

<div style="background: #F7FAFC; padding: 24px; border-radius: 8px; margin-bottom: 30px;">
  <h2 style="color: #2C5282; margin-top: 0;">Integration Examples</h2>
  
  <div style="background: #2C5282; padding: 2px; border-radius: 6px; margin: 20px 0;">
    <div style="background: white; padding: 20px; border-radius: 4px;">
      <h3 style="color: #2C5282; margin-top: 0;">Basic Usage</h3>
      
\`\`\`tsx
import { DocumentCard } from '@4-sure/ui-platform';

function MyComponent() {
  return (
    <DocumentCard
      purpose="contract"
      filename="contract.pdf"
      created="2024-01-01"
      thumbnail="https://example.com/thumbnail.jpg"
    />
  );
}
\`\`\`
    </div>
  </div>

  <div style="background: #2C5282; padding: 2px; border-radius: 6px; margin: 20px 0;">
    <div style="background: white; padding: 20px; border-radius: 4px;">
      <h3 style="color: #2C5282; margin-top: 0;">Form Integration</h3>
      
\`\`\`tsx
import { useForm } from 'react-hook-form';

function MyForm() {
  const { control } = useForm();
  
  return (
    <DocumentCard
      control={control}
      name="documentField"
      rules={{ required: true }}
      purpose="contract"
      filename="contract.pdf"
      created="2024-01-01"
      thumbnail="https://example.com/thumbnail.jpg"
      onDocumentChange={(doc) => {
        console.log('Document updated:', doc);
      }}
    />
  );
}
\`\`\`
    </div>
  </div>
</div>

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
  <div style="background: #F7FAFC; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">🛡️ File Handling</h3>
    
    <h4 style="color: #4A5568;">Supported Types</h4>
    <ul style="color: #4A5568;">
      <li>PDF files (.pdf)</li>
      <li>Image files (.jpg, .jpeg, .png, .gif)</li>
    </ul>
    
    <h4 style="color: #4A5568;">Size Limits</h4>
    <ul style="color: #4A5568;">
      <li>Default max: 5MB</li>
      <li>Configurable via props</li>
    </ul>
  </div>
  
  <div style="background: #F7FAFC; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">🚨 Error Handling</h3>
    <ul style="color: #4A5568;">
      <li>File size validation</li>
      <li>Type validation</li>
      <li>Network error handling</li>
      <li>Format validation</li>
    </ul>
  </div>
</div>

<div style="background: #F7FAFC; padding: 24px; border-radius: 8px; margin-bottom: 30px;">
  <h2 style="color: #2C5282; margin-top: 0;">Technical Details</h2>
  
  <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
    <div>
      <h3 style="color: #2C5282;">💪 Performance</h3>
      <ul style="color: #4A5568;">
        <li>Lazy loading for thumbnails</li>
        <li>File streaming for large files</li>
        <li>Optimized re-renders</li>
      </ul>
    </div>
    
    <div>
      <h3 style="color: #2C5282;">♿ Accessibility</h3>
      <ul style="color: #4A5568;">
        <li>Keyboard navigation</li>
        <li>ARIA labels</li>
        <li>Screen reader support</li>
      </ul>
    </div>
    
    <div>
      <h3 style="color: #2C5282;">🌐 Browser Support</h3>
      <ul style="color: #4A5568;">
        <li>Modern browsers</li>
        <li>IE11 fallback support</li>
        <li>Mobile browsers</li>
      </ul>
    </div>
  </div>
</div>

</div>
        `,
      },
      canvas: {
        sourceState: 'shown',
        layout: 'padded',
      },
      story: {
        inline: true,
        height: '300px',
      },
    },
  },
  argTypes: {
    purpose: {
      description: 'Identifier or category of the document',
      control: 'text',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' },
        category: 'Required',
      },
    },
    filename: {
      description: 'Name of the document file',
      control: 'text',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' },
        category: 'Required',
      },
    },
    created: {
      description: 'Document creation date (format: DD/MM/YY)',
      control: 'text',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' },
        category: 'Required',
      },
    },
    thumbnail: {
      description: 'URL for the document thumbnail preview',
      control: 'text',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' },
        category: 'Required',
      },
    },
    isLandscape: {
      description: 'Use landscape layout instead of portrait',
      control: 'boolean',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: false },
        category: 'Layout',
      },
    },
    fileTypesAllowed: {
      description: 'List of allowed file types for upload',
      control: 'object',
      table: {
        type: { summary: 'string[]' },
        defaultValue: { summary: "['pdf', 'image']" },
        category: 'Validation',
      },
    },
    fileSizeLimit: {
      description: 'Maximum allowed file size in bytes',
      control: 'number',
      table: {
        type: { summary: 'number' },
        defaultValue: { summary: 5242880 },
        category: 'Validation',
      },
    },
    enableUpload: {
      description: 'Enable/disable file upload functionality',
      control: 'boolean',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: true },
        category: 'Features',
      },
    },
    control: {
      description: 'react-hook-form Control object for form integration',
      table: {
        type: { summary: 'Control' },
        category: 'Form Integration',
      },
    },
    rules: {
      description: 'react-hook-form validation rules',
      table: {
        type: { summary: 'RegisterOptions' },
        category: 'Form Integration',
      },
    },
    onDocumentChange: {
      description: 'Callback function triggered when document is changed',
      table: {
        type: { summary: 'function' },
        category: 'Events',
      },
    },
  },
  decorators: [
    (Story) => (
      <div
        style={{
          minWidth: '800px',
          padding: '20px',
          height: 'auto',
          minHeight: '300px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof DocumentCard>;

export default meta;
type Story = StoryObj<typeof DocumentCard>;

/**
 * Basic example showing a document card with thumbnail in portrait mode.
 */
export const Overview: Story = {
  parameters: {
    docs: {
      canvas: {
        width: '100%',
        height: '300px',
      },
    },
  },
  args: {
    isStory: true,
    purpose: 'Purpose for document 2',
    filename: 'document 2 name',
    created: '24/03/15',
    thumbnail: 'https://picsum.photos/1336/1080',
  },
};

/**
 * Example showing the landscape layout variation, which is useful for wider displays
 * or when more horizontal space is available.
 */
export const LandscapeView: Story = {
  parameters: {
    docs: {
      canvas: {
        width: '100%',
        height: '200px',
      },
    },
  },
  args: {
    isStory: true,
    purpose: 'Purpose for document in landscape',
    filename: 'landscape document name',
    created: '15/09/23',
    thumbnail: 'https://picsum.photos/1336/1080',
    isLandscape: true,
  },
};

/**
 * Basic example showing a document card with thumbnail in portrait mode.
 */
export const ImageNoThumbnail: Story = {
  parameters: {
    docs: {
      canvas: {
        width: '100%',
        height: '300px',
      },
    },
  },
  args: {
    isStory: true,
    purpose: 'Purpose for document 2',
    filename: 'document 2 name',
    created: '24/03/15',
    thumbnail: '',
  },
};

export const DocumentNoThumbnail: Story = {
  parameters: {
    docs: {
      canvas: {
        width: '100%',
        height: '300px',
      },
    },
  },
  args: {
    isStory: true,
    purpose: 'Purpose for document 2',
    filename: 'document 2 name',
    created: '24/03/15',
    thumbnail: '',
  },
};
