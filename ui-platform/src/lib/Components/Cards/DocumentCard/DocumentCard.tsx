// import external module
import Keycloak from 'keycloak-js';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { createPortal } from 'react-dom';
import { Control, Controller, RegisterOptions } from 'react-hook-form';
import styled from 'styled-components';

// import internal modules
import NoDocument from '../../../Assets/FallbackImages/NoDocument.png';
import NoThumbnail from '../../../Assets/FallbackImages/NoThumbnailAvailable.png';
import { useSpaKeycloak } from '../../../Auth';
import { useErrorModal, useErrorStore } from '../../../Engine/hooks';
import { Text } from '../../../Fragments/Text/Text';
import { logDebug, normalizeUrl } from '../../../Utilities';
import {
  Files,
  fileTypes,
  setFileTypes,
} from '../../../Utilities/fileUploadMimeTypes';
import { TextButton } from '../../Buttons/TextButton/TextButton';
import { ImageWithLoader } from '../../Image/ImageWithLoader/ImageWithLoader';
import { Loader } from '../../Loader/Loader';
import { DocumentViewer, DocumentViewerProps } from './DocumentViewer';

// Add error messages constant
const ERROR_MESSAGES = {
  FILE_SIZE: (limit: number) =>
    `File size must be less than ${limit / 1048576}MB`,
  FILE_TYPE: (types: string[]) =>
    `File type not supported. Allowed types: ${types.join(', ')}`,
  NETWORK: 'Network error. Please check your connection and try again.',
  DOWNLOAD: 'Unable to download the document. Please try again later.',
  DECODE: 'Invalid file format. Please try uploading the file again.',
} as const;

/**
 * A versatile component for displaying and managing document files with thumbnails.
 *
 * @component
 * @example
 * ```tsx
 * import { DocumentCard } from '@4-sure/ui-platform';
import { DocumentViewer } from './DocumentViewer';
 *
 * function MyComponent() {
 *   return (
 *     <DocumentCard
 *       purpose="contract"
 *       filename="contract.pdf"
 *       created="2024-01-01"
 *       thumbnail="https://example.com/thumbnail.jpg"
 *     />
 *   );
 * }
 * ```
 *
 * @remarks
 * The DocumentCard component is built with TypeScript and React, utilizing several key features:
 * - Integrates with `react-hook-form` for form management
 * - Uses `styled-components` for styling
 * - Implements file validation and error handling
 * - Supports both controlled and uncontrolled usage
 *
 * ### Component Architecture
 * The component is structured into several sub-components:
 * - Main container with configurable layout (landscape/portrait)
 * - Thumbnail display with loading states
 * - File upload interface with drag & drop
 * - Document information display section
 *
 * ### File Handling
 *
 * #### Supported File Types
 * By default, the component accepts:
 * - PDF files (.pdf)
 * - Image files (.jpg, .jpeg, .png, .gif)
 *
 * #### File Size Limits
 * - Default maximum size: 5MB (5,242,880 bytes)
 * - Configurable through `fileSizeLimit` prop
 *
 * ### Performance Considerations
 * - Implements lazy loading for thumbnails
 * - Uses file streaming for large files
 * - Optimizes re-renders using React.memo
 *
 * ### Accessibility
 * - Keyboard navigation support
 * - ARIA labels for interactive elements
 * - Screen reader compatible
 * - Focus management for modal dialogs
 */
export interface DocumentCardProps {
  /** Identifier or category of the document */
  purpose: string;

  /** Name of the document file */
  filename: string;

  /** Document creation date in DD/MM/YY format */
  created: string;

  /** URL for the document thumbnail preview */
  thumbnail: string;

  /** Additional file metadata */
  fileData?: any;

  /** Callback function triggered when document is changed */
  onDocumentChange?: (updatedDocument: {
    purpose: string;
    filename: string;
    created: string;
    thumbnail: string;
    file: File;
  }) => void;

  /** react-hook-form Control object for form integration */
  control?: Control;

  /** react-hook-form validation rules */
  rules?: RegisterOptions;

  /** Field name for form integration */
  name?: string;

  /** Use landscape layout instead of portrait */
  isLandscape?: boolean;

  /** Use purpose as display name */
  purposeAsName?: boolean;

  /** Storybook mode flag */
  isStory?: boolean;

  /** SharePoint related prop */
  sp?: string;

  /** Download URL */
  file_download?: string;

  /** Director identifier */
  director_id?: string;

  /** Enable/disable upload functionality */
  enableUpload?: boolean;

  /** List of allowed file types */
  fileTypesAllowed?: Array<'image' | 'pdf' | 'doc' | 'excel' | 'powerpoint'>;

  /** Maximum file size in bytes */
  fileSizeLimit?: number;

  /** Handler for no document state */
  noDocumentHandler?: (purpose: string) => void;

  /** For determining whether user is staff member or not - boolean */
  staff?: string;

  /**
   * Base url required for the functionality of obtaining a file preview
   * - must be the environment variable name from project .env file
   * */
  base_url_env_name?: string;

  /** Logger toggle for debugging */
  debug?: boolean;

  /** Return type for file request */
  returnType?: number;

  /** File Identifier */
  id?: string;

  /** Keycloak object required for authentication when downloading preview of file */
  _keycloak?: Keycloak;

  /** Controls whether ui should provide download button on preview */
  downloadable?: boolean;

  /** Authorization prefix for authentication when downloading preview of file */
  tokenPrefix?: 'Bearer' | 'Token';

  /** Passes function that can be executed on click of the document card button */
  _onButtonClick?: (c?: any) => void;

  /**
   * Blocks the upload functionality of document card without disabling it,
   * useful for creating conditional logit to temporarily stop button opening the file explorer
   * */
  _blockUpload?: boolean;
}

/**
 * Converts a date string into a formatted date string (DD/MM/YY) for display
 * purposes. If the input string is in the format DD/MM/YY, the year is
 * interpreted as being in the 2000s if it is less than 50, or in the 1900s
 * otherwise. If the input string is in a different format, it is interpreted
 * as an ISO date string.
 *
 * @param {string} dateInput - The input date string
 * @returns {string} - The formatted date string
 */
const formatDate = (dateInput: string): string => {
  let dateObj: Date;
  const regex = /^\d{2}\/\d{2}\/\d{2}$/;
  if (regex.test(dateInput)) {
    const [day, month, year] = dateInput.split('/').map(Number);
    const fullYear = year < 50 ? 2000 + year : 1900 + year;
    dateObj = new Date(fullYear, month - 1, day);
  } else {
    dateObj = new Date(dateInput);
  }
  const day = String(dateObj.getDate()).padStart(2, '0');
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const year = String(dateObj.getFullYear()).slice(-2);
  return `${day}/${month}/${year}`;
};

const Container = styled.div<{ isLandscape?: boolean }>`
  min-width: ${(props) => (props.isLandscape ? '500px' : '')};
  width: ${(props) => (props.isLandscape ? '100%' : '278px')};
  height: ${(props) => (props.isLandscape ? '152px' : '253px')};
  border-radius: 8px;
  border: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr 1fr 1fr;
  justify-items: center;
  align-items: start;

  ${(props) =>
    props.isLandscape &&
    `
    grid-template-columns: auto auto;
  `}
`;

const LandscapeContainer = styled.div`
  height: 100%;
  display: grid;
  align-items: center;

  > div {
    align-items: center;
    justify-items: center;
  }
`;

const Document = styled.div<{ isLandscape?: boolean }>`
  position: relative;
  width: 262px;
  height: 138px;
  margin-top: 8px;
  grid-column: 1;
  grid-row: 1;
  place-self: self-start;
  margin-left: ${(props) => props.theme.SpacingSm};

  ${(props) =>
    props.isLandscape &&
    `
    // margin: 8px;
  `}

  &:hover .overlay {
    display: grid;
  }
`;

const DocName = styled.div<{ isLandscape?: boolean }>`
  grid-column: ${(props) => (props.isLandscape ? '2' : '1')};
  grid-row: ${(props) => (props.isLandscape ? '1' : '2')};
  padding: 1px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  ${(props) =>
    props.isLandscape &&
    `
    display: grid;
    justify-items: center;
  `}
`;

const UploadDate = styled.div<{ isLandscape?: boolean }>`
  grid-column: ${(props) => (props.isLandscape ? '2' : '1')};
  grid-row: ${(props) => (props.isLandscape ? '2' : '3')};
  padding: 1px;
  color: ${(props) => props.theme.ColorsTypographyTertiary};

  ${(props) =>
    props.isLandscape &&
    `
    display: grid;
    justify-items: center;
  `}
`;

const Button = styled.div<{ isLandscape?: boolean }>`
  grid-column: ${(props) => (props.isLandscape ? '2' : '1')};
  grid-row: ${(props) => (props.isLandscape ? '3' : '4')};
  padding: 12px;

  ${(props) =>
    props.isLandscape &&
    `
    display: grid;
    justify-items: center;
  `}
`;

const HiddenFileInput = styled.input`
  display: none;
`;

const Overlay = styled.div`
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(145, 145, 145, 0.6); // semi-transparent background
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  z-index: 1;
  border-radius: ${({ theme }) => theme.RadiusSm};
`;

const Image = styled(ImageWithLoader)`
  width: 100%;
  height: 100%;
  border-radius: ${({ theme }) => theme.RadiusSm};
  object-fit: cover;
`;

interface Base64ProcessingResult {
  blob: Blob;
  mimeType: string;
  extension: string;
  cleanB64Data: string;
}

interface FileConfig {
  base64: string;
  filename: string;
  purpose: string;
  created: string;
  mimeType?: string;
}

interface ViewerOptions {
  debug?: boolean;
  previewFileSizeThreshold?: number;
  largeFilePreviewBrowserExclusion?: string[];
  downloadable?: boolean;
}

const DEFAULT_MIME_TYPE = 'application/octet-stream';
const DEFAULT_EXTENSION = 'file';
const SUPPORTED_MIME_TYPES = fileTypes;

const getMediaType = (mimeType: string): 'pdf' | 'image' | 'other' => {
  if (!mimeType || typeof mimeType !== 'string') {
    return 'other';
  }

  const normalizedMimeType = mimeType.toLowerCase();
  if (
    normalizedMimeType === 'application/pdf' ||
    normalizedMimeType === 'image/pdf'
  ) {
    return 'pdf';
  }
  if (
    normalizedMimeType.startsWith('image/') &&
    normalizedMimeType !== 'image/pdf'
  ) {
    return 'image';
  }
  return 'other';
};

const BASE64_REGEX = {
  VALID_CHARS: /^[A-Za-z0-9+/]+={0,2}$/,
  DATA_URI: /^data:([^;]+);base64,(.+)$/,
  MIME_TYPE: /^data:([^;]+);base64$/,
};

const BASE64_CHUNK_SIZE = 8192;
const STREAM_CHUNK_SIZE = 1024 * 1024;
const MAX_MEMORY_THRESHOLD = 5 * 1024 * 1024;

const findExtension = (mimeType: string): string | undefined => {
  return Object.entries(SUPPORTED_MIME_TYPES)
    .flatMap(([_, mimeObj]) => Object.entries(mimeObj))
    .find(([mime]) => mime === mimeType)?.[1][0]
    ?.slice(1);
};

interface ICleanB64String {
  b64: string;
  explicitMimeType?: string;
  debug: boolean;
}

interface CleanBase64Result {
  cleanBase64: string;
  deducedMimeType?: string;
}

const cleanBase64String = ({
  b64,
  explicitMimeType,
  debug = false,
}: ICleanB64String): CleanBase64Result => {
  let cleanBase64: string;
  let deducedMimeType = explicitMimeType;

  const log = logDebug('CleanBase64String', debug);
  if (debug) {
    const previewLength = Math.min(50, b64.length);
    log('Start cleaning base 64 string', {
      previewStart: b64.slice(0, previewLength),
      previewEnd: b64.slice(-previewLength),
      totalLength: b64.length,
    });
  }
  const startOfString = b64.slice(0, 100);
  const dataUriMatch = startOfString.match(BASE64_REGEX.DATA_URI);
  if (dataUriMatch) {
    deducedMimeType = deducedMimeType || dataUriMatch[1];
    const prefixLength = `data:${dataUriMatch[1]};base64,`.length;
    // cleanBase64 = dataUriMatch[2];
    cleanBase64 = b64.slice(prefixLength);
    log('Extracted from data URI', { mimeType: deducedMimeType, prefixLength });
  } else {
    cleanBase64 = b64;
  }

  cleanBase64 = cleanBase64.replace(/[\s\r\n]+|[^A-Za-z0-9+/=]/g, '');
  if (cleanBase64.length > STREAM_CHUNK_SIZE) {
    const isValidStart = BASE64_REGEX.VALID_CHARS.test(
      cleanBase64.slice(0, 1000)
    );
    const isValidEnd = BASE64_REGEX.VALID_CHARS.test(cleanBase64.slice(-1000));
    if (!isValidStart || !isValidEnd) {
      throw new Error('BASE64_INVALID_FORMAT');
    }
  } else if (!BASE64_REGEX.VALID_CHARS.test(cleanBase64)) {
    throw new Error('BASE64_INVALID_FORMAT');
  }
  return { cleanBase64, deducedMimeType };
};

const processBase64Stream = async function* (
  base64: string,
  chunkSize: number,
  debug = false
) {
  const log = logDebug('ProcessBase64Stream', debug);
  const totalLength = base64.length;
  log('Initialising base64 stream processing', {
    startOfB64: base64.slice(0, 50),
    endOfB64: base64.slice(-50),
    chunkSize,
  });
  for (let offset = 0; offset < totalLength; offset += chunkSize) {
    const chunk = base64.slice(offset, offset + chunkSize);
    log('Processed chunk', {
      chunkStart: offset,
      chunkLength: chunk.length,
    });
    yield chunk;
  }
};

const processBase64 = async (
  base64: string,
  explicitMimeType?: string,
  debug = false
): Promise<Base64ProcessingResult> => {
  const startTime = debug ? performance.now() : 0;

  const log = logDebug('ProcessBase64', debug);
  const errorLog = logDebug('ProcessBase64', debug, 'error');
  try {
    if (!base64?.trim()) {
      throw new Error('BASE64_EMPTY_INPUT');
    }

    const useStreaming = base64.length > MAX_MEMORY_THRESHOLD;
    log('Starting base64 processing', {
      inputLength: base64.length,
      mode: useStreaming ? 'streaming' : 'memory',
    });
    const cleanResult = cleanBase64String({
      b64: base64,
      explicitMimeType,
      debug,
    });
    let cleanedBase64 = cleanResult.cleanBase64;
    const deducedMimeType = cleanResult.deducedMimeType;

    const paddingNeeded = (4 - (cleanedBase64.length % 4)) % 4;
    if (paddingNeeded > 0) {
      cleanedBase64 = cleanedBase64 + '='.repeat(paddingNeeded);
    }

    log('Base64 string prepared', {
      originalLength: base64.length,
      cleanedLength: cleanedBase64.length,
      padding: paddingNeeded,
    });

    // const byteCharacters = atob(cleanedBase64);
    const byteArrays: Uint8Array[] = [];

    if (useStreaming) {
      let offset = 0;
      for await (const chunk of processBase64Stream(
        cleanedBase64,
        BASE64_CHUNK_SIZE
      )) {
        try {
          const byteCharacters = atob(chunk);
          const byteNumbers = new Uint8Array(byteCharacters.length);

          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
            // log('Decoded units', {
            //   decodedLength: byteNumbers.length,
            // });
          }
          byteArrays.push(byteNumbers);
          offset += BASE64_CHUNK_SIZE;
        } catch (error) {
          errorLog('Chunk decoding failed', {
            offset,
            chunkLength: chunk.length,
            error,
          });
          throw new Error('BASE64_CHUNK_DECODE_FAIL');
        }
      }
    } else {
      try {
        const byteCharacters = atob(cleanedBase64);
        const byteNumbers = new Uint8Array(byteCharacters.length);

        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
          // log('Decoded units', {
          //   decodedLength: byteNumbers.length,
          // });
        }
        byteArrays.push(byteNumbers);
      } catch (error) {
        errorLog('B64 string decoding failed', {
          error,
        });
        throw new Error('BASE64_DECODE_FAIL');
      }
    }

    const finalMimeType =
      deducedMimeType?.toLowerCase() === 'image/pdf'
        ? 'application/pdf'
        : deducedMimeType || DEFAULT_MIME_TYPE;
    const extension = findExtension(finalMimeType) || DEFAULT_EXTENSION;
    const blob = new Blob(byteArrays, { type: finalMimeType });
    const processedResult: Base64ProcessingResult = {
      blob,
      mimeType: finalMimeType,
      extension,
      cleanB64Data: cleanedBase64,
    };

    if (debug) {
      const endTime = performance.now();
      log('Processing completed', {
        processingTime: `${(endTime - startTime).toFixed(2)}ms`,
        outputSize: processedResult.blob.size,
        mimeType: processedResult.mimeType,
        extension: processedResult.extension,
      });
    }

    return processedResult;
  } catch (error) {
    const errorDetails = {
      code: error instanceof Error ? error.message : 'UNKNOWN_ERROR',
      inputLength: base64.length,
      inputPreview: base64.substring(0, 50) + '...',
      timestamp: new Date().toISOString(),
      stack: error instanceof Error ? error.stack : undefined,
    };
    errorLog('Processing failed', errorDetails);

    const errorMessages: Record<string, string> = {
      BASE64_EMPTY_INPUT: 'The provided base64 string is empty',
      BASE64_INVALID_FORMAT:
        'The provided string is not in valid base64 format',
      UNKNOWN_ERROR: 'An unexpected error occurred while processing the file',
      BASE64_CHUNK_DECODE_FAIL: 'Failed to decode a chunk of the base64 string',
      BASE64_DECODE_FAIL: 'Failed to decode the entire base64 string',
    };

    throw new Error(
      errorMessages[errorDetails.code] || `Unknown error: ${errorDetails.code}`
    );
  }
};

const previewFile = async ({
  base64,
  filename,
  mimeType,
  purpose,
  created,
  debug = false,
  previewFileSizeThreshold = 2,
  largeFilePreviewBrowserExclusion = ['KHTML', 'Chrome', 'OPR'],
}: FileConfig & ViewerOptions) => {
  if (!base64) return;

  const log = logDebug('PreviewFile', debug);
  const errorLog = logDebug('PreviewFile', debug, 'error');
  const b64FileSizeMB = base64.length / (1024 * 1024);
  const approximateDecodedSizeMB = b64FileSizeMB * 0.75;
  log('Base64 file size:', {
    b64FileSizeMB: `${b64FileSizeMB}MB`,
    approximateDecodedSizeMB: `${approximateDecodedSizeMB}MB`,
  });
  let browserWillNotPreviewB64File = false;
  if (approximateDecodedSizeMB > previewFileSizeThreshold) {
    browserWillNotPreviewB64File = largeFilePreviewBrowserExclusion.some(
      (brwsr: string) => {
        const browserProperties = navigator.userAgent;
        const browserHasProperty = browserProperties.includes(brwsr);
        log(`Checking if browser has - ${brwsr} in its user agent string:`, {
          browserProperties,
          browserHasProperty,
        });
        return browserHasProperty;
      }
    );
  }
  log('Browser will preview b64 file:', !browserWillNotPreviewB64File);
  try {
    const {
      blob,
      mimeType: determinedMimeType,
      extension,
      cleanB64Data,
    } = await processBase64(base64, mimeType, debug);

    log('Base64 processing outcome', {
      file: blob,
      fileMimeType: determinedMimeType,
      fileExtension: extension,
      cleanedB64DataSample: cleanB64Data.slice(0, 100),
    });

    const fileURL = URL.createObjectURL(blob);
    const finalFilename = filename || `document.${determinedMimeType}`;
    const mediaType = !browserWillNotPreviewB64File
      ? getMediaType(determinedMimeType)
      : 'other';

    const src = mediaType === 'pdf' ? cleanB64Data : fileURL;
    const viewerProps: DocumentViewerProps = {
      src: src,
      purpose,
      fileName: finalFilename,
      mediaType,
      onClose: () => URL.revokeObjectURL(fileURL), // Cleanup on close
      created,
      onDownload: () =>
        downloadFile({
          base64,
          filename: finalFilename,
          mimeType: determinedMimeType,
          created,
          purpose,
          debug,
        }),
      onOpenInTab: () => {
        const newWindow = window.open(fileURL, '_blank');
        if (!newWindow) {
          throw new Error(
            'Failed to open preview - popup blocker might be enabled'
          );
        }
        setTimeout(() => URL.revokeObjectURL(fileURL), 30000);
      },
    };
    return viewerProps;
  } catch (error) {
    const errorDetails = {
      code: error instanceof Error ? error.message : 'UNKNOWN_ERROR',
      inputLength: base64.length,
      inputPreview: base64.substring(0, 50) + '...',
      timestamp: new Date().toISOString(),
      stack: error instanceof Error ? error.stack : undefined,
    };
    const errorMessage =
      error instanceof Error
        ? `${error.message}: Please try refreshing the page`
        : 'Unknown error: Try refreshing the website';
    errorLog('Failed to preview file', error);
    const errorMessages: Record<string, string> = {
      BASE64_EMPTY_INPUT: 'The provided base64 string is empty',
      BASE64_INVALID_FORMAT:
        'The provided string is not in valid base64 format',
      UNKNOWN_ERROR: 'An unexpected error occurred while processing the file',
      BASE64_CHUNK_DECODE_FAIL: 'Failed to decode a chunk of the base64 string',
    };
    throw new Error(`Failed to preview file: ${errorMessage}`);
  }
};

// Refactored downloadFile (unchanged)
const downloadFile = async ({
  base64,
  filename,
  mimeType,
  created,
  purpose,
  debug = false,
  downloadable = true,
}: FileConfig & ViewerOptions): Promise<void> => {
  if (!downloadable) return;
  if (!base64) return;
  const log = logDebug('DownloadFile', debug);
  const errorLog = logDebug('DownloadFile', debug, 'error');

  try {
    const { blob, extension } = await processBase64(base64, mimeType, debug);
    const finalFilename = filename || `document.${extension}`;

    const downloadLink = document.createElement('a');
    downloadLink.href = URL.createObjectURL(blob);
    downloadLink.download = finalFilename;

    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);

    setTimeout(() => URL.revokeObjectURL(downloadLink.href), 60000);
  } catch (error) {
    const errorMessage =
      error instanceof Error
        ? `${error.message}: Please try refreshing the page`
        : 'Unknown error: Try refreshing the website';
    errorLog('Download failed:', errorMessage);
    throw new Error(`Failed to download file: ${errorMessage}`);
  }
};

/**
 * A component that displays a document card with a thumbnail, filename, and upload date.
 * It also allows the user to replace the document by clicking on the "Replace" button.
 * If the document is a PDF, it can be downloaded by clicking on the "VIEW DOCUMENT" button.
 * The component is designed to be used in a form with a Controller from react-hook-form.
 *
 * @param {{ purpose: string; filename: string; created: string; thumbnail: string; onDocumentChange?: (updatedDocument: { purpose: string; filename: string; created: string; thumbnail: string; file: File; }) => void; control?: Control; rules?: RegisterOptions; name?: string; isLandscape?: boolean; purposeAsName?: boolean; isStory?: boolean; sp: string; file_download?: string; director_id?: string; }} props
 * @returns {JSX.Element}
 */
export function DocumentCard({
  id,
  purpose,
  filename,
  created,
  thumbnail,
  onDocumentChange,
  control,
  rules,
  name,
  fileData,
  isLandscape = false,
  purposeAsName = true,
  isStory = false,
  sp,
  file_download,
  director_id,
  enableUpload = true,
  fileTypesAllowed = ['pdf', 'image'],
  fileSizeLimit = 5242880,
  noDocumentHandler,
  staff,
  base_url_env_name: base_url,
  debug = false,
  returnType,
  _keycloak,
  downloadable = true,
  tokenPrefix = 'Bearer',
  _blockUpload = false,
  _onButtonClick,
  ...props
}: DocumentCardProps) {
  const [currentThumbnail, setCurrentThumbnail] = useState<string | undefined>(
    thumbnail
  );
  const [currentFilename, setCurrentFilename] = useState<string>(filename);
  const [uploadDate, setUploadDate] = useState<string>(() =>
    created ? formatDate(created) : ''
  );
  const [openPreview, setOpenPreview] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const keycloak = isStory ? null : _keycloak;
  const [isLoading, setIsLoading] = useState(false);
  const [b64File, setB64File] = useState('');
  const [documentPreview, setDocument] = useState<
    DocumentViewerProps | undefined
  >(undefined);

  const { handleServerError } = useErrorModal();
  const execButtonClicked = useCallback(
    () => _onButtonClick && _onButtonClick(),
    [_onButtonClick]
  );
  const blockUpload = useMemo(() => _blockUpload, [_blockUpload]);

  const handleReplaceClick = () => {
    execButtonClicked();
    if (blockUpload) return;
    fileInputRef.current?.click();
  };
  const log = logDebug('DocumentCard', debug);
  const errorLog = logDebug('DocumentCard', debug, 'error');

  log('Rendering document card', {
    purpose,
    filename,
    created,
    thumbnail,
    onDocumentChange,
    control,
    rules,
    name,
    fileData,
    isLandscape,
    purposeAsName,
    isStory,
    sp,
    file_download,
    director_id,
    enableUpload,
    fileTypesAllowed,
    fileSizeLimit,
    noDocumentHandler,
    staff,
    base_url,
    debug,
    id,
    _keycloak,
    downloadable,
  });

  useEffect(() => {
    const getThumbnail = () => {
      if (!Array.isArray(fileData)) {
        if (thumbnail || created || (fileData && fileData.thumbnail))
          return thumbnail;
        if (created || (fileData && !fileData.thumbnail)) return NoThumbnail;
      }
      noDocumentHandler && noDocumentHandler(purpose);
      return NoDocument;
    };

    setTimeout(() => {
      setCurrentThumbnail(getThumbnail());
    }, 3000);
  }, [
    thumbnail,
    created,
    fileData,
    fileData?.thumbnail,
    noDocumentHandler,
    purpose,
  ]);

  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    onChange: (value: any) => void
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      // File size validation
      if (file.size > fileSizeLimit) {
        handleServerError(ERROR_MESSAGES.FILE_SIZE(fileSizeLimit));
        event.target.value = '';
        return;
      }

      const allowedFileTypes = setFileTypes(fileTypesAllowed, {
        format: 'fileExtString',
      }) as string;

      // File type validation
      const fileExtension = file.name.split('.').pop()?.toLowerCase() || '';
      console.log('File extension:', { allowedFileTypes, fileExtension });
      if (
        !allowedFileTypes
          .split(',')
          .map((type) => type.trim())
          .includes(`.${fileExtension}`)
      ) {
        handleServerError(
          ERROR_MESSAGES.FILE_TYPE(allowedFileTypes.split(','))
        );
        event.target.value = '';
        return;
      }

      const fileUrl = URL.createObjectURL(file);
      const fileName = file.name;
      // const thumbnail = file.type.includes('image') ? fileUrl : NoThumbnail;
      const thumbnail = file.type.includes('image') ? fileUrl : NoThumbnail;
      // const today = new window.Date().toLocaleDateString('en-GB');
      const today = new window.Date().toISOString();

      setCurrentThumbnail(fileUrl);
      setCurrentFilename(fileName);
      setUploadDate(today);

      const updatedDocument = {
        purpose,
        filename: fileName,
        created: today,
        thumbnail: thumbnail,
        file: file,
      };

      if (onDocumentChange) {
        onDocumentChange(updatedDocument);
      }
      onChange(updatedDocument);
    } catch (error) {
      handleServerError('Error processing file. Please try again.');
      errorLog('File processing error:', error);
    }
  };

  const getClickedDocument = async ({
    downloadLink,
    fileDownloadData,
    debug = false,
  }: {
    downloadLink: string;
    fileDownloadData: {
      file_id: string;
      sp_id?: string;
      staff_id?: string;
      director_id?: string;
      return_type?: number;
    };
    debug?: boolean;
  }) => {
    try {
      setIsLoading(true);
      const baseUrl = base_url
        ? import.meta.env[base_url]
        : sp
        ? import.meta.env.VITE_SP_SERVER
        : import.meta.env.VITE_STAFF_SERVER;

      const response = await fetch(`${normalizeUrl(downloadLink, baseUrl)}`, {
        method: 'POST',
        body: JSON.stringify(fileDownloadData),
        headers: {
          'Content-Type': 'application/json',
          Authorization: `${tokenPrefix} ${keycloak?.token}`,
        },
      });

      if (!response.ok) {
        throw new Error(ERROR_MESSAGES.NETWORK);
      }

      const res = await response.json();
      if (!res.success) {
        throw new Error(res.reason || ERROR_MESSAGES.DOWNLOAD);
      }

      const base64File = res.payload.file;

      log('Base64 File:', base64File);

      const filePreview = await previewFile({
        base64: base64File,
        filename: fileData?.filename || filename,
        purpose: fileData?.purpose || purpose,
        created: fileData?.created || created,
        debug: debug,
      });
      setB64File(base64File);
      setDocument(filePreview);
      setOpenPreview(true);
    } catch (error) {
      log('Error: ', error);
      errorLog(
        'Document preview error:',
        error ?? 'Unable to load the document, please try again later.'
      );
      handleServerError(
        'Document preview error:',
        error instanceof Error
          ? error.message ?? String(error)
          : 'Unable to load the document, please try again later.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleDocumentClick = async () => {
    if (!(fileData?.id || id) && !isStory) return;
    const log = logDebug('HandleDocumentClick', debug);
    const errorLog = logDebug('HandleDocumentClick', debug, 'error');
    log('File details:', fileData);

    const downloadLink = file_download || `/api/v1/file_actions/get_file`;
    const fileDownloadData = {
      file_id: fileData?.id || id,
      sp_id: sp,
      staff_id: staff,
      ...(director_id && { director_id }),
      ...(returnType && { return_type: returnType }),
    };

    if (!fileDownloadData.file_id) {
      errorLog('File download data missing required fields', fileDownloadData);
      return;
    }
    if (isLoading) return;
    await getClickedDocument({ downloadLink, fileDownloadData, debug });
  };

  return (
    <>
      {isLoading && <Loader />}
      {openPreview &&
        documentPreview &&
        createPortal(
          <DocumentViewer
            src={documentPreview.src}
            purpose={documentPreview.purpose}
            fileName={documentPreview.fileName}
            mediaType={documentPreview.mediaType}
            onClose={() => setOpenPreview(false)}
            created={documentPreview.created}
            onDownload={() =>
              downloadFile({
                base64: b64File,
                filename: fileData?.filename || filename,
                purpose: fileData?.purpose || purpose,
                created: fileData?.created || created,
                downloadable,
              })
            }
            onOpenInTab={() => documentPreview?.onOpenInTab?.()}
            downloadable={downloadable}
          />,
          document.body
        )}
      <Controller
        name={name || filename}
        control={control}
        rules={rules}
        render={({ field: { onChange, value }, fieldState: { error } }) => {
          return (
            <CardContainer
              isLandscape={isLandscape}
              value={value}
              handleDocumentClick={handleDocumentClick}
              purposeAsName={purposeAsName}
              purpose={purpose}
              currentFilename={currentFilename}
              fileData={fileData}
              uploadDate={uploadDate}
              currentThumbnail={currentThumbnail}
              enableUpload={enableUpload}
              handleReplaceClick={handleReplaceClick}
              handleFileChange={handleFileChange}
              onChange={onChange}
              fileInputRef={fileInputRef}
              fileTypesAllowed={fileTypesAllowed}
              fileSizeLimit={fileSizeLimit}
            />
          );
        }}
      />
    </>
  );
}

const CardContainer = ({
  isLandscape,
  value,
  handleDocumentClick,
  purposeAsName,
  purpose,
  currentFilename,
  fileData,
  uploadDate,
  currentThumbnail,
  enableUpload,
  handleReplaceClick,
  handleFileChange,
  onChange,
  fileInputRef,
  fileTypesAllowed,
  fileSizeLimit,
}: {
  isLandscape: boolean;
  enableUpload: boolean;
  purposeAsName: boolean;
  value: any;
  handleDocumentClick: () => void;
  purpose: string;
  currentFilename: string;
  fileData: any;
  uploadDate: string;
  currentThumbnail?: string;
  handleReplaceClick: () => void;
  handleFileChange: (
    event: React.ChangeEvent<HTMLInputElement>,
    onChange: (value: any) => void
  ) => void;
  onChange: (value: any) => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
  fileTypesAllowed: Files[];
  fileSizeLimit: number;
}) => {
  const memoizedThumbnail = useMemo(() => {
    if (!(value?.thumbnail || fileData?.thumbnail || currentThumbnail)) {
      if (value?.fileName?.split('.').pop()?.toLowerCase().includes('pdf'))
        return NoThumbnail;
      if (
        value?.fileName?.split('.').pop()?.toLowerCase().includes('jpeg') ||
        value?.fileName?.split('.').pop()?.toLowerCase().includes('jpg') ||
        value?.fileName?.split('.').pop()?.toLowerCase().includes('png')
      )
        return NoThumbnail;
      return NoThumbnail;
    }
    return value?.thumbnail || fileData?.thumbnail || currentThumbnail;
  }, [value, fileData, currentThumbnail]);
  const memoizedTimestamp = useMemo(() => {
    return value?.created || fileData?.created || uploadDate;
  }, [value, fileData, uploadDate]);
  const memoizedFilename = useMemo(() => {
    return purposeAsName ? purpose : value?.filename || currentFilename;
  }, [purposeAsName, purpose, value?.filename, currentFilename]);
  return (
    <Container isLandscape={isLandscape}>
      <Document>
        <Image
          src={memoizedThumbnail}
          alt={memoizedFilename}
          width={262}
          height={138}
        />
        {(!value || fileData) && (
          <Overlay className="overlay">
            <TextButton
              btnValue="VIEW DOCUMENT"
              onClick={handleDocumentClick}
            />
          </Overlay>
        )}
      </Document>
      {isLandscape ? (
        <LandscapeContainer>
          <div>
            <DocName isLandscape={isLandscape}>
              <Text
                textItems={[
                  {
                    text: memoizedFilename,
                    options: { format: 'form', type: 'heading' },
                  },
                ]}
              />
            </DocName>
            {value || (fileData && fileData.created) || uploadDate ? (
              <UploadDate isLandscape={isLandscape}>
                <Text
                  textItems={[
                    {
                      text: `Uploaded: ${formatDate(memoizedTimestamp)}`,
                      options: { format: 'form', type: 'sub_text' },
                    },
                  ]}
                />
              </UploadDate>
            ) : (
              <UploadDate isLandscape={isLandscape}>
                <Text
                  textItems={[
                    {
                      text: `No file uploaded`,
                      options: { format: 'form', type: 'sub_text' },
                    },
                  ]}
                />
              </UploadDate>
            )}
            <Button isLandscape={isLandscape}>
              {enableUpload && (
                <TextButton
                  btnValue={fileData?.filename ? 'REPLACE' : 'BROWSE'}
                  onClick={handleReplaceClick}
                />
              )}
              <HiddenFileInput
                ref={fileInputRef}
                type="file"
                accept={
                  setFileTypes(fileTypesAllowed, {
                    format: 'fileExtString',
                  }) as string
                }
                onChange={(e) => handleFileChange(e, onChange)}
              />
            </Button>
          </div>
        </LandscapeContainer>
      ) : (
        <>
          <DocName isLandscape={isLandscape}>
            <Text
              textItems={[
                {
                  text: memoizedFilename,
                  options: { format: 'form', type: 'heading' },
                },
              ]}
            />
          </DocName>
          {value || (fileData && fileData.created) || uploadDate ? (
            <UploadDate isLandscape={isLandscape}>
              <Text
                textItems={[
                  {
                    text: `Uploaded: ${formatDate(memoizedTimestamp)}`,
                    options: { format: 'form', type: 'sub_text' },
                  },
                ]}
              />
            </UploadDate>
          ) : (
            <UploadDate isLandscape={isLandscape}>
              <Text
                textItems={[
                  {
                    text: `No file uploaded`,
                    options: { format: 'form', type: 'sub_text' },
                  },
                ]}
              />
            </UploadDate>
          )}
          <Button isLandscape={isLandscape}>
            {enableUpload && (
              <TextButton
                btnValue={
                  value?.filename || fileData?.filename ? 'REPLACE' : 'BROWSE'
                }
                onClick={handleReplaceClick}
              />
            )}
            <HiddenFileInput
              ref={fileInputRef}
              type="file"
              accept={
                setFileTypes(fileTypesAllowed, {
                  format: 'fileExtString',
                }) as string
              }
              onChange={(e) => handleFileChange(e, onChange)}
            />
          </Button>
        </>
      )}
    </Container>
  );
};
