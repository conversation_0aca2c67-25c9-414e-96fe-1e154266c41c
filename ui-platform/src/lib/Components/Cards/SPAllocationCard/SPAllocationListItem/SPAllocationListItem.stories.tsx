import type { Meta, StoryObj } from '@storybook/react';
import { SPAllocationListItem, SPAllocationListItemProps } from './SPAllocationListItem';

const meta: Meta<typeof SPAllocationListItem> = {
  component: SPAllocationListItem,
  title: 'Components/Cards/SPAllocationCard/SPAllocationListItem',
  argTypes: {
    spBoldLabel: { control: 'text', description: 'The bold label text or number' },
    spSubText: { control: 'text', description: 'The subtext label text or number' },
  },
};
export default meta;

type Story = StoryObj<SPAllocationListItemProps>;

export const DefaultSPAllocationListItem: Story = {
  args: {
    spBoldLabel: 'This is the bold label',
    spSubText: 'This is the secondary label',
  },
};


