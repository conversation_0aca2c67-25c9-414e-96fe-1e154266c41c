import React from 'react';
import styled from 'styled-components';

export interface SPAllocationListItemProps {
  spBoldLabel: string | number;
  spSubText: string | number;
}

const TextContainer = styled.div`
  width: 100%;
  height: 77px;
  background: ${(props) => props?.theme.ColorsBackgroundModule};
  opacity: 1;
  position: relative;
  display: grid;
  grid-template-rows: repeat(2, auto);
  align-items: start;
  justify-items: start;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
`;

const ContentWrapper = styled.div<{ gap?: string }>`
  display: grid;
  padding: 16px 0px 16px 16px;
  grid-template-rows: auto auto;
  align-items: start;
  justify-items: start;
  gap: ${(props) => props.gap || '8px'};
`;

const ItemWrapper = styled.div`
  display: grid;
  grid-template-columns: auto;
  align-items: start;
  justify-items: start;
  height: 17px;
`;

const BoldLabel = styled.div`
  font-weight: ${(props) => props.theme.FontWeightsInter6};
  text-align: left;
  height: 17px;
  position: relative;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const SubTextLabel = styled.div`
  font-weight: ${(props) => props.theme.FontWeightsInter5};
  text-align: left;
  height: 17px;
  position: relative;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

/**
 * A component that renders a list item for a SP allocation card.
 *
 * @example
 * <SPAllocationListItem
 *   spBoldLabel='This is the bold label'
 *   spSubText='This is the secondary label'
 * />
 */
export const SPAllocationListItem = ({
  spBoldLabel = 'This is the bold label',
  spSubText = 'This is the secondary label',
}: SPAllocationListItemProps) => {
  return (
    <TextContainer>
      <ContentWrapper>
        <ItemWrapper>
          <BoldLabel>{spBoldLabel}</BoldLabel>
        </ItemWrapper>
        <ItemWrapper>
          <SubTextLabel>{spSubText}</SubTextLabel>
        </ItemWrapper>
      </ContentWrapper>
    </TextContainer>
  );
};
