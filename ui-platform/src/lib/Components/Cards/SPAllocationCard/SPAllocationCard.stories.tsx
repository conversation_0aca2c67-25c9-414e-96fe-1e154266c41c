import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { SPAllocationCard, SPAllocationCardProps } from './SPAllocationCard';

const meta: Meta<typeof SPAllocationCard> = {
  component: SPAllocationCard,
  title: 'Components/Cards/SPAllocationCard',
  argTypes: {
    SPName: { control: 'text' },
    SPNumber: { control: 'text' },
    Allocated: { control: 'number' },
    Score: { control: 'number' },
  },
};
export default meta;

type Story = StoryObj<SPAllocationCardProps>;

export const DefaultSPAllocationCard: Story = {
  args: {
    SPName:'SP NAME',
    SPNumber: 'SP Number',
    Allocated: 0,
    Score: 0,
  },
};


