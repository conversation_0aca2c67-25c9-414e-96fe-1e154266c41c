import React from 'react';
import styled from 'styled-components';
import { SPAllocationListItem } from './SPAllocationListItem/SPAllocationListItem';
import { PermissionsIndicator } from '../PermissionsIndicator/PermissionsIndicator';

export interface SPAllocationCardProps {
  SPName: string;
  SPNumber: string | number;
  Allocated: number;
  Score: number;
  onClick?: () => void;
}

const SPAllocationCardContainer = styled.div<{ gap?: string }>`
  width: 100%;
  height: 78px;
  border-radius: ${({ theme }) => theme?.RadiusXs};
  border: 0.5px;
  background: ${(props) => props?.theme.ColorsButtonColorActionPanelPrimary};
  overflow: hidden;
  box-sizing: border-box;
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: ${(props) => props.gap || '4px'};
  align-items: center;
`;

const StyledPermissionIndicator = styled.div`
  display: grid;
  align-items: center;
  justify-items: end;
  height: 100%;
  width: 100%;
  padding-left: 4px;
`;

const SPAllocationRightContainer = styled.div<{ gap?: string }>`
  display: grid;
  grid-template-columns: 1fr auto;
  gap: ${(props) => props.gap || '4px'};
  align-items: center;
`;

/**
 * A component that renders a list of SPAllocationCards with a search functionality and a button.
 *
 * @component
 * @example
 * const allocation = [
 *   { SPName: 'Provider 1', SPNumber: '001', Allocated: 10, Score: 85 },
 *   { SPName: 'Provider 2', SPNumber: '002', Allocated: 20, Score: 90 }
 * ];
 *
 * <ManuallyAllocate allocation={allocation} />
 */
export const SPAllocationCard: React.FC<SPAllocationCardProps> = ({
  SPName,
  SPNumber,
  Allocated,
  Score,
  onClick,
}) => {
  return (
    <SPAllocationCardContainer onClick={onClick}>
      <SPAllocationListItem spBoldLabel={SPName} spSubText={SPNumber} />
      <SPAllocationRightContainer>
        <SPAllocationListItem
          spBoldLabel={`Allocated today - ${Allocated}`}
          spSubText={`Score: ${Score}`}
        />
        <StyledPermissionIndicator>
          <PermissionsIndicator color="green" size="default" position="right" />
        </StyledPermissionIndicator>
      </SPAllocationRightContainer>
    </SPAllocationCardContainer>
  );
};
