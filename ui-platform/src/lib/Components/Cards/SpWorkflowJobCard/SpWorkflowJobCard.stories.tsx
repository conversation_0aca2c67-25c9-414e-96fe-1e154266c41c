/* eslint-disable @typescript-eslint/no-explicit-any */
import type { Meta, StoryObj } from '@storybook/react';
import { SpWorkflowJobCard } from './SpWorkflowJobCard';
import { sampleJobs } from '../../../Screens/WorkflowModule/JobsOnly/JobsDetailedView/sample-jobs';

const meta: Meta<typeof SpWorkflowJobCard> = {
  component: SpWorkflowJobCard,
  title: 'Components/Cards/SILSpWorkflowJobCard',
};
export default meta;

type Story = StoryObj<typeof SpWorkflowJobCard>;

const job = sampleJobs[0]


export const DefaultSpWorkflowJobCard: Story = {
  args: {
    job: job,
    LinkRouter: ({ children }: {children: any}) => children,
  },
};
