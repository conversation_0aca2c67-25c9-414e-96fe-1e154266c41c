import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { TeamCard } from './TeamCard';

const meta: Meta<typeof TeamCard> = {
  component: TeamCard,
  title: 'Components/Cards/TeamCard',
};
export default meta;
type Story = StoryObj<typeof TeamCard>;

export const Overview: Story = {
    args: {
      name: "<PERSON><PERSON>",
      rating: 4.0,
      // teamLead: "SP Admin, Scheduler",
      // jobCard: "Technical, Building , Painting",
      contactNumber: "085 5555 555", 
      emailAddress: "<EMAIL>",
        reduceOpacity: false
      },
};

export const WithSelectedOption: Story = {
  args: {
    name: "<PERSON><PERSON>",
    rating: 4.0,
    // teamLead: "SP Admin, Scheduler",
    // jobCard: "Technical, Building , Painting",
    contactNumber: "085 5555 555", 
    emailAddress: "<EMAIL>",
      reduceOpacity: false
    },
};
