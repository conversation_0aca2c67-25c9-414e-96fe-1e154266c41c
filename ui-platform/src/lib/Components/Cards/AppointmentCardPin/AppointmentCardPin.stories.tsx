import type { Meta, StoryObj } from '@storybook/react';
import { AppointmentCardPin } from './AppointmentCardPin';

const meta: Meta<typeof AppointmentCardPin> = {
  component: AppointmentCardPin,
  title: 'Components/Cards/AppointmentCardPin',
  argTypes: {
    color: {
        control: 'select',
        options: ['#118AB2', '#11B29F', '#FF3A1F', '#B28511'],
      },
      size: {
        control: 'select',
        options: ['small', 'medium', 'large'],
      },
      onClick: { action: 'clicked' }, 
  }
};
export default meta;
type Story = StoryObj<typeof AppointmentCardPin>;

export const Overview: Story = {
  args: {
    color: 'red', 
    size: 'medium', 
  },
};

export const Glossary: Story = {
    render: () => (
      <div style={{ display: 'flex', gap: '10px' }}>
        {['#118AB2', '#11B29F', '#FF3A1F', '#B28511'].map((color) => (
          <AppointmentCardPin
            key={color}
            color={color}
            size="small"
            onClick={() => console.log('Clicked!')}
          />
        ))}
      </div>
    ),
  };