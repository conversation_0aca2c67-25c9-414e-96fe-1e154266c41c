import React from 'react'
import styled from 'styled-components';
import { Avatar } from '../../Avatars/Avatar/Avatar'

interface IAppointmentCardPinProps {
    color: string,
    onClick: () => void,
    size: "small" | "medium" | "large" | "extraLarge"
}

const StyledDiv = styled.div<{ color: string; size: 'small' | 'medium' | 'large' | 'extraLarge'}>`
  background-color: ${({ color }) => color};
  border-radius: 99%;
  padding: ${(props) => props.theme.SpacingXs};
  display: grid;
  place-items: center;
  width: ${({ size }) => sizeMapping[size]};
  height: ${({ size }) => sizeMapping[size]};
`;

const sizeMapping = {
    small: '40px',
    medium: '60px',
    large: '80px',
    extraLarge: '100px',
  };

/**
 * Renders an Appointment Card Pin component.
 * 
 * @param color - The color of the pin.
 * @param onClick - Function to handle click events.
 * @param size - The size of the pin.
 * @returns JSX element representing the Appointment Card Pin.
 */
export const AppointmentCardPin = ({color, onClick, size}: IAppointmentCardPinProps) => {
  return (
    <StyledDiv color={color} size={size}>
        <Avatar size={size} color={'#ffffff'} onClick={onClick} onFileSave={() => {}} />
    </StyledDiv>
  )
}
