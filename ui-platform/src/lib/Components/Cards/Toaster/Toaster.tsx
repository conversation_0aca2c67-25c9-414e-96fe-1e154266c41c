import { ReactNode } from 'react';
import styled from 'styled-components';

interface Props {
  content: ReactNode;
  className?: string;
}

const Container = styled.div`
  padding: ${(props) => props.theme.SpacingXxl};
  background: ${(props) => props.theme.ColorsOverlaySurfaceOverlay};
  border-radius: ${(props) => props.theme.RadiusXs};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
`;

/**
 * A Toaster component that displays a message in a box with a subtle drop shadow.
 *
 * @param {object} props Component props
 * @param {string} [props.className] CSS class name
 * @param {ReactNode} props.content Toaster content
 * @returns {ReactElement} Toaster element
 */
export function Toaster({ className, content }: Props) {
  return <Container className={className}>{content}</Container>;
}
