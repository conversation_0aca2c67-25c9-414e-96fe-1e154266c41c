import React from 'react';

type CustomToastProps = {
  message: string;
  type: 'success' | 'error' | 'info' | 'warning' | 'default';
  onClose: () => void;
};

export const CustomToastStyle = ({ message, type, onClose }: CustomToastProps) => {
  const getToastStyles = (type: CustomToastProps['type']) => {
    const baseStyles = {
      padding: '12px 24px',
      borderRadius: '4px',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      color: 'white',
      minWidth: '300px',
    };

    const typeStyles = {
      success: {
        backgroundColor: '#4caf50',
        borderLeft: '4px solid #45a049',
        height: '50px'
      },
      error: {
        backgroundColor: '#f44336',
        borderLeft: '4px solid #da190b',
        height: '50px'
      },
      info: {
        backgroundColor: '#2196F3',
        borderLeft: '4px solid #0b7dda',
        height: '50px'
      },
      warning: {
        backgroundColor: '#ff9800',
        borderLeft: '4px solid #e68a00',
        height: '50px'
      },
      default: {
        backgroundColor: '#ffffff',
        color: '#333333',
        borderLeft: '4px solid #cccccc',
        height: '50px'
      }
    };

    return { ...baseStyles, ...typeStyles[type] };
  };

  return (
    <div style={getToastStyles(type)}>
      {message}
    </div>
  );
};