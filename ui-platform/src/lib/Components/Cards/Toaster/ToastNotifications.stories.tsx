import { Meta, StoryObj } from '@storybook/react';
import { Toaster } from './Toaster';
import { useToast, ToastService } from '../../../Engine';
import { Message } from '../../LoginSection/Message/Message';
import { CustomToastStyle } from './CustomToastStyle';

type CustomToastprops = {message:string, type:'success' | 'error' | 'info' | 'warning' | 'default', onClose: () => void};
const CustomToast = (t: CustomToastprops) => {
  return <Toaster content={t.message}/>
};

const ToastDemo: React.FC = () => {
  const toast = useToast();

  return (
    <div>
      <button onClick={() => toast.success('Operation successful!')}>
        Show Success
      </button>
      <button onClick={() => toast.error('Something went wrong!')}>
        Show Error
      </button>
      <button onClick={() => toast.info('Heads up!')}>
        Show Info
      </button>
      <button onClick={() => toast.warning('Be careful!')}>
        Show Warning
      </button>
      <ToastService CustomToast={CustomToastStyle}/>
    </div>
  );
};

const meta: Meta<typeof ToastDemo> = {
  component: ToastDemo,
  title: 'Components/Cards/ToastNotifications',
};

export default meta;

type Story = StoryObj<typeof ToastDemo>;

export const Overview: Story = {
  args: {
  },
};




