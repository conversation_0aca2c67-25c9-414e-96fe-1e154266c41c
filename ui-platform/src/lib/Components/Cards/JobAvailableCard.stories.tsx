import type { <PERSON>a, StoryObj } from '@storybook/react';
import {JobAvailableCardStory} from './JobAvailableCard'

const meta: Meta<typeof JobAvailableCardStory> = {
  component: JobAvailableCardStory,
  title: 'Components/Cards/JobAvailableCard',
};
export default meta;
type Story = StoryObj<typeof JobAvailableCardStory>;

export const DefaultJobAvailableCardStory: Story = {

  argTypes: {
    // background: {
    //     control: 'select',
    //     options: ['primary']
    // }
  },
  args: {
    skills: [
      {
        id: 1,
        name: 'Skill 1'
      }
    ]
  }
};