import React from 'react';
import styled from 'styled-components';

const Wrapper = styled.div`
  width: 170px;
  position: relative;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr 1fr;
  flex-direction: column;
  justify-content: flex-start;
  font-size: 14px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
`;

const Label = styled.div`
  grid-column: 1;
  grid-row: 1;
  font-weight: 700;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const Description = styled.div`
  grid-column: 1;
  grid-row: 2;
  font-weight: ${(props) => props.theme.FontWeightsInter5};

  text-overflow: ellipsis;
  white-space: nowrap;
`;

export interface ReminderListItemProps {
  label: string;
  description: string;
}

/**
 * A component that displays a single reminder list item.
 *
 * @param {string} [label='This is a label'] - The label of the reminder.
 * @param {string} [description='This is a description'] - The description of the reminder.
 * @returns {JSX.Element} A JSX element representing the reminder list item.
 */
export const ReminderListItem = ({
  label = 'This is a label',
  description = 'This is a description',
}: ReminderListItemProps) => {
  return (
    <Wrapper>
      <Label>{label}</Label>

      <Description>{description}</Description>
    </Wrapper>
  );
};
