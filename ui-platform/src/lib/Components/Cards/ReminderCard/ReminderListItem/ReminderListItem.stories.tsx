import type { Meta, StoryObj } from '@storybook/react';
import { ReminderListItem } from './ReminderListItem';

const meta: Meta<typeof ReminderListItem> = {
  component: ReminderListItem,
  title: 'Components/Cards/ReminderListItem',
};
export default meta;
type Story = StoryObj<typeof ReminderListItem>;

export const DefaultReminderCardDate: Story = {
  argTypes: {
    label: {
      control: 'text',
      options: ['This is a label'],
    },
    description: {
      control: 'text',
      options: ['This is a description'],
    },
  },
  args: {},
};
