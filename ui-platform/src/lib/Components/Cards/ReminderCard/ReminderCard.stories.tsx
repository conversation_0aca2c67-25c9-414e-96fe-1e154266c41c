import type { Meta, StoryObj } from '@storybook/react';
import { ReminderCard } from './ReminderCard';

const meta: Meta<typeof ReminderCard> = {
  component: ReminderCard,
  title: 'Components/Cards/ReminderCard',
};
export default meta;
type Story = StoryObj<typeof ReminderCard>;

export const DefaultReminderCardStory: Story = {
  argTypes: {
    background: {
      control: 'select',
      options: ['primary'],
    },
    timeOfReminder: {
      control: 'text',
      options: ['00:00'],
    },
    reminderDate: {
      control: 'text',
      options: ['2024-07-01'],
    },
    description: {
      control: 'text',
      options: ['This is a description'],
    },
    label: {
      control: 'text',
      options: ['This is a label'],
    },
  },
  args: {
    background: 'primary',
  },
};

export const DefaultMultipleReminders: Story = {
  argTypes: {
    background: {
      control: 'select',
      options: ['primary'],
    },
    timeOfReminder: {
      control: 'text',
      options: ['00:00'],
    },
    reminderDate: {
      control: 'text',
      options: ['2024-07-01'],
    },
    description: {
      control: 'text',
      options: ['This is a description'],
    },
    label: {
      control: 'text',
      options: ['This is a label'],
    },
  },
  args: {
    background: 'primary',
  },
};
