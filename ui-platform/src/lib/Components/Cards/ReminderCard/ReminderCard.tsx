import React from 'react';
import styled from 'styled-components';
import { Icon } from '../../Icons';
import { ReminderDateItem } from './ReminderCardDate/ReminderCardDate';
import { ReminderListItem } from './ReminderListItem/ReminderListItem';

export interface ReminderCardProps {
  background: string;
  description: string;
  label: string;
  timeOfReminder: string;
  reminderDate: string;
  onDelete?: () => void;
}

const ReminderCardContainer = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  height: 76px;
  border-radius: ${(props) => props?.theme.RadiusXxs};
  background: ${(props) => props?.theme.ColorsButtonColorActionPanelPrimary};
  font-size: ${(props) => props.theme.FontSize3}px;
  box-sizing: border-box;
  display: grid;
  grid-template-columns: 1fr 4fr 1fr;
  grid-template-rows: 1fr;
`;

const LabelContent = styled(({ ...rest }) => <div {...rest}></div>)`
  grid-column: 2;
  grid-row: 1;
  align-self: center;
  background: ${(props) => props?.theme.ColorsButtonColorActionPanelPrimary};
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
`;

const DateOfReminder = styled(({ ...rest }) => <div {...rest}></div>)`
  grid-column: 1;
  grid-row: 1;
  align-self: center;
  background: ${(props) => props?.theme.ColorsButtonColorActionPanelPrimary};
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
`;

const TrashCan = styled(({ ...rest }) => <div {...rest}></div>)`
  background: ${(props) => props?.theme?.ColorsButtonColorActionPanelPrimary};
  color: ${(props) => props?.theme?.ColorsTypographyPrimary};
  grid-column: 3;
  grid-row: 1;
  justify-self: end;
  padding: 3px;
`;

/**
 * Renders a reminder card with a date of reminder, a label, a description and a trash can.
 * 
 * @param timeOfReminder - Time of reminder in 24-hour format (e.g. "00:00"). Defaults to "00:00".
 * @param reminderDate - Date of reminder in "YYYY-MM-DD" format. Defaults to "2024-07-01".
 * @param label - The label of the reminder. Defaults to "".
 * @param description - The description of the reminder. Defaults to "".
 * @returns A JSX element representing the reminder card.
 */
export const ReminderCard: React.FC<ReminderCardProps> = ({
  timeOfReminder,
  reminderDate,
  label,
  description,
  onDelete,
}) => {
  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    }
  }
  return (
    <ReminderCardContainer>
      <DateOfReminder>
        <ReminderDateItem
          reminderDate={reminderDate}
          timeOfReminder={timeOfReminder}
        />
      </DateOfReminder>
      <LabelContent>
        <ReminderListItem label={label} description={description} />
      </LabelContent>
      <TrashCan onClick={handleDelete}>
        <Icon type="trash-01" size={16} style={{ cursor: 'pointer' }}/>
      </TrashCan>
    </ReminderCardContainer>
  );
};
