import type { Meta, StoryObj } from '@storybook/react';
import { ReminderDateItem } from './ReminderCardDate';

const meta: Meta<typeof ReminderDateItem> = {
  component: ReminderDateItem,
  title: 'Components/Cards/ReminderDateItem',
};
export default meta;
type Story = StoryObj<typeof ReminderDateItem>;

export const DefaultReminderCardDate: Story = {
  argTypes: {
    timeOfReminder: {
      control: 'text',
      options: ['00:00'],
    },
    reminderDate: {
      control: 'text',
      options: ['2024-07-01'],
    },
  },
  args: {},
};
