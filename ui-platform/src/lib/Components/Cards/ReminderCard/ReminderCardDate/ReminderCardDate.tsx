import React from 'react';
import styled from 'styled-components';
import { Icon } from '../../../Icons';
import dayjs from 'dayjs';

const Wrapper = styled.div`
  position: relative;
  width: 75px;
  height: 56px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  display: grid;
  padding-left: 10px;
  padding-right: 10px;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr 1fr 1fr;
  gap: 1px;
`;

const AvatarWrapper = styled.div`
  grid-column: 1;
  grid-row: 1;
  padding: 0px;
  justify-self: center;
  font-size: 1px;
`;

const Date = styled.div`
  grid-column: 1;
  grid-row: 2;
  font-size: 12px;
  font-weight: 400;
  justify-self: center;
`;

const Time = styled.div`
  grid-column: 1;
  grid-row: 3;
  padding: 0px;
  font-size: 14px;
  font-weight: 700;
  justify-self: center;
`;

export interface ReminderDateItemProps {
  timeOfReminder: string;
  reminderDate: string;
}

/**
 * Renders a reminder date item that shows the time of reminder and the reminder
 * date in a relative format (e.g. "Today", "Tomorrow", "1 day ago", "2 days ago", etc.).
 *
 * @param timeOfReminder - Time of reminder in 24-hour format (e.g. "00:00"). Defaults to "00:00".
 * @param reminderDate - Date of reminder in "YYYY-MM-DD" format. Defaults to "2024-07-01".
 * @returns A JSX element representing the reminder date item.
 */
export const ReminderDateItem = ({
  timeOfReminder = '00:00',
  reminderDate = '2024-07-01',
}: ReminderDateItemProps) => {
  function getReminderDuration(reminderDate: string): string {
    const now = dayjs().startOf('day');
    const targetDate = dayjs(reminderDate).startOf('day');
    const diffDays = targetDate.diff(now, 'day');
    let message;
    if (diffDays === -1) {
      message = `1 day ago`;
    } else if (diffDays < 0) {
      message = `${Math.abs(diffDays)} days ago`;
    } else if (diffDays === 0) {
      message = 'Today';
    } else if (diffDays === 1) {
      message = 'Tomorrow';
    } else {
      message = `${diffDays} days`;
    }
    return message;
  }

  return (
    <Wrapper>
      <AvatarWrapper>
        <Icon type="user-circle" size={22} color="white" />
      </AvatarWrapper>
      <Date>{getReminderDuration(reminderDate)}</Date>
      <Time>{timeOfReminder}</Time>
    </Wrapper>
  );
};
