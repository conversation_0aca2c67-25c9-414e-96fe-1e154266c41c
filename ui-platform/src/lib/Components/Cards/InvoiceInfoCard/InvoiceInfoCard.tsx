import React from 'react'
import styled from 'styled-components'

export const StyledContainer = styled.div`
    background-color: transparent;
    border-radius: 8px;
    border: 1px solid #2862ff;
    padding: 20px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    width: 90%;
`

const List = styled.ul`
  margin: 0;
  padding-left: 20px;
  list-style-type: disc;
  color: #2862ff;
`;

const ListItem = styled.li`
  font-size: 12px;
`;

interface InvoiceInfoCardProps {
  data: string[];
}

export const InvoiceInfoCard: React.FC<InvoiceInfoCardProps> = ({data}) => {
  return (
    <StyledContainer>
      <List>
        {data.map((item, index) => (
          <ListItem key={index}>{item}</ListItem>
        ))}
      </List>
    </StyledContainer>
  )
}
