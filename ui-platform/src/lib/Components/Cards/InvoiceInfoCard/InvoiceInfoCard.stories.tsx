import { StoryObj, Meta } from '@storybook/react/';
import { InvoiceInfoCard } from './InvoiceInfoCard';

const meta: Meta<typeof InvoiceInfoCard> = {
  component: InvoiceInfoCard,
  title: 'Components/Cards/InvoiceInfoCard',
};
export default meta;
type Story = StoryObj<typeof InvoiceInfoCard>;

export const Default: Story = {
  args: {
    data: ['Invoice Number: 123456', 'Invoice Date: 12/12/2021', 'Invoice Amount: $1000'],
  },
};