import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { withComponentShowcase } from '../../../Utilities';
import { CalendarAppointmentCard } from './CalendarAppointmentCard';

const meta: Meta<typeof CalendarAppointmentCard> = {
  component: CalendarAppointmentCard,
  title: 'Components/Cards/CalendarAppointmentCard',
};
export default meta;
type Story = StoryObj<typeof CalendarAppointmentCard>;

const statuses = ['neutral', 'success', 'error', 'inProgress', 'warning'];

const progressBarTypes = ['node', 'bar', 'nodebar'];

export const Overview: Story = {
  argTypes: {
    status: {
      control: {
        type: 'select',
        options: statuses,
      },
    },
    progress: {
      control: {
        type: 'range',
        min: 0,
        max: 100,
        step: 1,
      },
    },
    type: {
      control: {
        type: 'select',
        options: progressBarTypes,
      },
    },
  },
  args: {
    status: 'neutral',
    address: '123 St. Douglasdale, Johannesburg ',
    jobType: 'Airconditioning',
    time: '12:30',
    progress: 1,
    type: 'bar',
  },
};

export const DifferentStates: Story = {
  argTypes: { ...Overview.argTypes },
  render: (args) =>
    withComponentShowcase(<CalendarAppointmentCard {...args} />)(
      'status',
      statuses,
      true
    ),
  args: {
    address: '123 St. Douglasdale, Johannesburg ',
    jobType: 'Airconditioning',
    time: '12:30',
    type: 'bar',
  },
};

export const ProgressVariations: Story = {
  argTypes: { ...Overview.argTypes },
  render: (args) =>
    withComponentShowcase(<CalendarAppointmentCard {...args} />)(
      'type',
      progressBarTypes,
      true
    ),
  args: {
    address: '123 St. Douglasdale, Johannesburg ',
    jobType: 'Airconditioning',
    time: '12:30',
    status: 'neutral',
  },
};
