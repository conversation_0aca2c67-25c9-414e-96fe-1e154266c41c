import {
  ProgressBar,
  ProgressBarProps,
  statusMap,
} from '../../Bars/ProgressBar/ProgressBar';
import { CalendarAppointmentCardAddress } from './CalendarAppointmentCardAddress/CalendarAppointmentCardAddress';
import { CalendarAppointmentContainer } from './CalendarAppointmentCardContainer/CalendarAppointmentCardContainer';
import { CalendarAppointmentCardDetails } from './CalendarAppointmentCardDetails/CalendarAppointmentCardDetails';
import { CalendarAppointmentCardJobType } from './CalendarAppointmentCardJobType/CalendarAppointmentCardJobType';
import {
  CalendarAppointmentCardLiveInfo,
  CalendarAppointmentCardLiveInfoProps,
} from './CalendarAppointmentCardLiveInfo/CalendarAppointmentCardLiveInfo';
export interface CalendarAppointmentCardProps
  extends Omit<
      CalendarAppointmentCardLiveInfoProps,
      'status' | 'statusMessage'
    >,
    Omit<ProgressBarProps, 'size'> {
  address: string;
  jobType: string;
}

/**
 * A component that displays a calendar appointment card
 *
 * @param address - The address of the appointment
 * @param jobType - The job type of the appointment
 * @param time - The time of the appointment
 * @param status - The status of the appointment
 * @param props - Additional props to pass to the ProgressBar component
 */
export function CalendarAppointmentCard({
  address,
  jobType,
  time,
  status,
  ...props
}: CalendarAppointmentCardProps) {
  const statusMessage: statusMap = {
    neutral: 'On Track',
    success: 'Complete!',
    error: 'Missed',
    inProgress: 'In Progress',
    warning: 'Running Late',
  };
  return (
    <CalendarAppointmentContainer>
      <CalendarAppointmentCardDetails>
        <CalendarAppointmentCardAddress address={address} />
        <CalendarAppointmentCardJobType jobType={jobType} />
        <CalendarAppointmentCardLiveInfo
          time={time}
          status={status}
          statusMessage={statusMessage[status]}
        />
      </CalendarAppointmentCardDetails>
      <ProgressBar size="smll" status={status} {...props} />
    </CalendarAppointmentContainer>
  );
}
