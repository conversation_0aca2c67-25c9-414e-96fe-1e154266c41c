import { ComponentPropsWithRef } from 'react';
import styled from 'styled-components';

/**
 * Props for the `CalendarAppointmentCardContainer` component.
 * This is a type that extends the default props for a `div` element.
 * 
 * @typedef {ComponentPropsWithRef<'div'>} CalendarAppointmentCardContainerProps
 */
export type CalendarAppointmentCardContainerProps =
  ComponentPropsWithRef<'div'>;

export const CalendarAppointmentContainer = styled.div`
  width: 100%;
  position: relative;
  backdrop-filter: blur(3px);
  border-radius: 4px;
  background-color: ${(props) => props.theme.ColorsCardColorCalendarActive};
  border: 0.5px solid ${(props) => props.theme.ColorsStrokesDefault};
  box-sizing: border-box;
  overflow: hidden;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto;
  padding: 8px;
  gap: 8px;
  min-width: 51px;
  min-height: 100px;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
`;
