import { ComponentPropsWithRef } from 'react';
import styled from 'styled-components';

const Container = styled.div`
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: start;
  justify-content: end;
  width: 100%;
`;

const JobType = styled.div`
  grid-column: 1 / 2;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

interface CalendarAppointmentCardJobTypeProps
  extends ComponentPropsWithRef<'div'> {
  jobType: string;
}

/**
 * Displays the job type for a calendar appointment card.
 *
 * @param {string} jobType the job type
 * @param {ComponentPropsWithRef<'div'>} props div props
 * @returns {JSX.Element} the job type text
 */
export function CalendarAppointmentCardJobType({
  jobType,
  ...props
}: CalendarAppointmentCardJobTypeProps) {
  return (
    <Container>
      <JobType {...props}>{jobType}</JobType>
    </Container>
  );
}
