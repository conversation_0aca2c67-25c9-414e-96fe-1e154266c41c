import { ComponentProps } from 'react';
import styled from 'styled-components';
import { Heading } from '../../../Heading/Heading';

const CalendarAppointmentCardHeading = styled(Heading)`
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: unset;
  font-size: unset;
`;

const Container = styled.div`
  align-self: stretch;
  display: grid;
  grid-template-columns: 1fr;
  align-items: start;
`;

export interface CalendarAppointmentCardAddressProps
  extends Omit<
    ComponentProps<typeof CalendarAppointmentCardHeading>,
    'children' | 'ref'
  > {
  address: string;
  ref?: React.Ref<HTMLHeadingElement> | undefined;
}

/**
 * A component that displays an address as a heading, with a maximum
 * width. The component is designed to be used within a
 * CalendarAppointmentCard component, but can be used elsewhere.
 *
 * @param {string} address - The address to display.
 * @param {Object} props - Additional props to pass through to the
 *   CalendarAppointmentCardHeading component.
 * @returns {ReactElement} A React component.
 */
export function CalendarAppointmentCardAddress({
  address,
  ...props
}: CalendarAppointmentCardAddressProps) {
  return (
    <Container>
      <CalendarAppointmentCardHeading {...props}>
        {address}
      </CalendarAppointmentCardHeading>
    </Container>
  );
}
