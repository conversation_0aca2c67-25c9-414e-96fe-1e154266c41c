import styled from 'styled-components';
import { statusMap } from '../../../Bars/ProgressBar/ProgressBar';
import { Italised } from '../../../Italised/Italised';

const Container = styled.div<{ status: keyof statusMap }>`
  display: grid;
  grid-template-columns: 1fr auto;
  grid-gap: ${(props) => props.theme.SpacingXs};
  color: ${(props) => {
    const statuses: statusMap = {
      neutral: props?.theme.ColorsUtilityColorFocus,
      success: props?.theme.ColorsUtilityColorSuccess,
      error: props?.theme.ColorsUtilityColorError,
      inProgress: props?.theme.ColorsUtilityColorProgress,
      warning: props?.theme.ColorsUtilityColorWarning,
    };
    return statuses[props.status];
  }};
  padding: ${(props) => props.theme.SpacingSm};
  align-items: start;
  width: 100%;
`;

const LiveInfo = styled.div`
  grid-column: 1 / 2;
  position: relative;
  display: inline-block;
  font-weight: 300;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 90px;
`;

const Time = styled(Italised)`
  grid-column: 2 / 3;
  font-weight: ${(props) => props.theme.FontWeightsInter5};
  color: ${(props) => props.theme.ColorsTypographySecondary};
  text-align: right;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

export interface CalendarAppointmentCardLiveInfoProps {
  status: keyof statusMap;
  statusMessage: string;
  time: string;
}

/**
 * A component that displays the status, status message and time of a calendar
 * appointment card in a live info section.
 *
 * @param {keyof statusMap} status - The status of the appointment.
 * @param {string} statusMessage - The message to display for the given status.
 * @param {string} time - The time of the appointment.
 * @returns {JSX.Element} A container with the status, status message and time.
 */
export function CalendarAppointmentCardLiveInfo({
  status,
  statusMessage,
  time,
}: CalendarAppointmentCardLiveInfoProps) {
  return (
    <Container status={status}>
      <LiveInfo>{statusMessage}</LiveInfo>
      <Time>{time}</Time>
    </Container>
  );
}
