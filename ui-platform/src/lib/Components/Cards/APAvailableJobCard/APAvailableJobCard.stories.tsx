import type { <PERSON>a, StoryObj } from '@storybook/react';
import { APAvailableJobCard } from './APAvailableJobCard';

const meta: Meta<typeof APAvailableJobCard> = {
  component: APAvailableJobCard,
  title: 'Components/Cards/APAvailableJobCard',
};

export default meta;
type Story = StoryObj<typeof APAvailableJobCard>;

const mockJob = {
  id: 1201,
  appointments: [],
  appointment: {
    id: 1201,
    after_hours: false,
    appointment_name: 'Before',
    appointment_type_id: '5',
    range_start: '2025-02-25T16:00:00',
    range_end: '2025-02-25T16:00:00',
  },
  location: '',
  mid: '',
  address: '12 Windmill Road, Albertaville, 12345',
  skill: 'Plumbing',
  customer: '<PERSON>',
  cellnumber: '0801234567',
  area: 'Gauteng',
  claim_type: 'Plumbing',
  suburb: 'Roodepoort',
};

export const Default: Story = {
  args: {
    job: mockJob,
    onClick: (job) => console.log('Card clicked:', job),
  },
};