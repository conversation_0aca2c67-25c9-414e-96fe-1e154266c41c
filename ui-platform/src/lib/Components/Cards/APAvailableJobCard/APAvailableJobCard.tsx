import React from "react";
import styled from "styled-components";
import { IAvailableJob } from '../../Models/IAvailableJob';
import { StaffMember } from "../../../Auth";

interface IAPAvailableJobCardProps {
    job: IAvailableJob;
    onClick?: (job: IAvailableJob) => void;
    skills: Array<{id: number, name: string}>;
  
}
const APAvailableJobCardContainer = styled.div`
    display: grid;
    grid-template-columns: 3fr 1fr;
    border-radius: 0.3rem;
    font-size: ${(props) => props.theme.FontSize3}px;
    color: ${(props) => props.theme.ColorsTypographyPrimary};
    font-family: ${(props) => props.theme.FontFamiliesInter};
    width: 100%;
    background-color: ${(props) => props.theme?.ColorsCardColorJobCardPrimary};
    padding: 0.6rem 1rem;
    margin: 0 0 7px 0;
    &:hover {
    cursor: pointer;
    background: ${(props) => props.theme?.ColorsButtonColorModuleNavigationHover};
    }
    
`

const APAvailableJobCardListItem = styled.div`
    display: grid;
    grid-template-rows: 1fr 1fr 1fr;
    overflow: hidden;
    grid-gap: 6px;
    
`

const formatDateTime = (dateString: string, appointmentName: string) => {
    try {
        const date = new Date(dateString);

        // check if date is valid
        if(isNaN(date.getTime())) {
            console.error('Invalid date string', dateString);
            return 'Invalid date';

            }

        const month = date.toLocaleString('en-ZA', { month: 'long' });
        const day = date.getDate();
        const time = date.toLocaleTimeString('en-ZA', { 
            hour: '2-digit', 
            minute: '2-digit',
            hour12: false 
        })
        return `${month} ${day} ${appointmentName.toLowerCase()} ${time}`;
    } catch (error) {
        console.error('Error formatting date', error);
        return 'Date formatting error';
    }
    // const date = new Date(dateString);
    // const month = date.toLocaleString('default', { month: 'long' });
    // const day = date.getDate();
    // const time = date.toLocaleTimeString('default', { 
    //   hour: '2-digit', 
    //   minute: '2-digit',
    //   hour12: false 
    // });
    // return `${month} ${day} ${appointmentName.toLowerCase()} ${time}`;
}

enum Interested {
    available = 1,
    decline = -1,
    ignore = 0,
  }
  

export const APAvailableJobCard:  React.FC<IAPAvailableJobCardProps>= ({job, onClick, skills}) => {
    
    
    const handleClick = () => { 
        if (onClick) {
            console.log('Action Panlel job', job);
            onClick(job);
        }
    }
    const getSkillNameById = (skillId: string | number | undefined, skills: Array<{id: number, name: string}> = []) => {
        if (!skills || !skillId) return 'Unknown Skill';
        const skill = skills.find(s => s.id === Number(skillId));
        return skill?.name || 'Unknown Skill';
    };

    return (
        <APAvailableJobCardContainer onClick={handleClick}>
            <APAvailableJobCardListItem>
            <div style={{fontWeight: '700'}}>{getSkillNameById(job.skill, skills)}</div>
            <div>{job.suburb}</div>
            <div>
                {formatDateTime(
                    job.appointment.range_start,
                    job.appointment.appointment_name
                )}
            </div>
            </APAvailableJobCardListItem>
            
        </APAvailableJobCardContainer>
    )
}

