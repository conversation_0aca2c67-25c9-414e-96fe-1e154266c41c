// GridLayout.tsx
import React, { CSSProperties, ReactNode } from 'react';
import styled from 'styled-components';

// Types for props
interface GridItemProps {
  colSpan?: number;
  rowSpan?: number;
  children?: ReactNode;
  style?: CSSProperties;
  className?: string;
}

interface GridContainerProps {
  columns?: number | string;
  rows?: number | string;
  gap?: string;
  padding?: string;
  width?: string;
  maxWidth?: string;
  margin?: string;
  mobileColumns?: number;
  style?: CSSProperties;
  enforceGrid?: boolean;
}

interface GridLayoutProps extends GridContainerProps {
  children?: ReactNode;
  className?: string;
}

// Helper type for styled-components props
type StyledGridContainerProps = {
  columns: number | string;
  rows?: number | string;
  gap?: string;
  padding?: string;
  width?: string;
  maxWidth?: string;
  margin?: string;
  mobileColumns?: number;
};

// GridItem wrapper to handle individual item sizing
const GridItem = styled.div<GridItemProps>`
  grid-column: ${(props) =>
    props.colSpan ? `span ${props.colSpan}` : 'span 1'};
  grid-row: ${(props) => (props.rowSpan ? `span ${props.rowSpan}` : 'span 1')};
  min-width: 0; // Prevents content from overflowing
`;

// Main grid container
const GridContainer = styled.div<StyledGridContainerProps>`
  display: grid;
  grid-template-columns: ${(props) => {
    if (props.columns) {
      if (typeof props.columns === 'number') {
        return `repeat(${props.columns}, 1fr)`;
      }
      return props.columns;
    }
    return 'repeat(12, 1fr)'; // Default 12-column grid
  }};
  grid-template-rows: ${(props) => {
    if (props.rows) {
      if (typeof props.rows === 'number') {
        return `repeat(${props.rows}, 1fr)`;
      }
      return props.rows;
    }
    return 'repeat(6, 1fr)'; // Default 12-column grid
  }};
  gap: ${(props) => props.gap || '1rem'};
  padding: ${(props) => props.padding || '0'};
  width: ${(props) => props.width || '100%'};
  max-width: ${(props) => props.maxWidth || 'none'};
  margin: ${(props) => props.margin || '0'};
  height: 100%;

  @media (max-width: 768px) {
    grid-template-columns: ${(props) => {
      if (props.mobileColumns) {
        return `repeat(${props.mobileColumns}, 1fr)`;
      }
      return 'repeat(4, 1fr)'; // Default 4 columns on mobile
    }};
  }
`;

// Type guard to check if child has gridItem props
const hasGridItemProps = (child: React.ReactElement): boolean => {
  return Boolean(child.props?.colSpan || child.props?.rowSpan);
};

// Main component
const GridLayout = React.forwardRef<HTMLDivElement, GridLayoutProps>(
  (
    {
      children,
      columns = 12,
      gap,
      padding,
      width,
      maxWidth,
      margin,
      mobileColumns,
      className,
      enforceGrid = false,
      ...props
    },
    ref
  ) => {
    return (
      <GridContainer
        ref={ref}
        data-testid="grid-layout-container"
        columns={columns}
        gap={gap}
        padding={padding}
        width={width}
        maxWidth={maxWidth}
        margin={margin}
        mobileColumns={mobileColumns}
        className={className}
        {...props}
      >
        {React.Children.map(children, (child) => {
          if (React.isValidElement(child)) {
            if (hasGridItemProps(child) || !enforceGrid) return child;
            if (enforceGrid)
              return (
                <GridItem
                  data-testid="Imposed-grid-item"
                  className={child.props.className}
                  style={child.props.style}
                  colSpan={child.props.colSpan}
                  rowSpan={child.props.rowSpan}
                >
                  {child}
                </GridItem>
              );
          }
          return child;
        })}
      </GridContainer>
    );
  }
);

// Higher-order component to add grid item props to any component
interface WithGridItemProps {
  colSpan?: number;
  rowSpan?: number;
}

function withGridItem<P extends object>(
  WrappedComponent: React.ComponentType<P>
): React.FC<P & WithGridItemProps> {
  return ({ colSpan, rowSpan, ...props }: WithGridItemProps & P) => (
    <GridItem data-testid="HOC-Grid_item" colSpan={colSpan} rowSpan={rowSpan}>
      <WrappedComponent {...(props as P)} />
    </GridItem>
  );
}

export { GridItem, GridLayout, withGridItem };
export type { GridItemProps, GridLayoutProps, WithGridItemProps };
