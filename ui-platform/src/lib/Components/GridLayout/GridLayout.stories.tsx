import { Meta, StoryObj } from '@storybook/react';
import styled from 'styled-components';
import Contractor from '../../public/images/contractor_3.jpg';
import { Avatar } from '../Avatars/Avatar/Avatar';
import { DragAndDrop } from '../DragAndDrop/DragAndDrop';
import { Heading } from '../Heading/Heading';
import { DropdownSelect, PlainTextInput } from '../Inputs';
import { ScrollableContent } from '../Scrollbar/Scrollbar';
import { GridItem, GridLayout, withGridItem } from './GridLayout';

const meta: Meta<typeof GridLayout> = {
  component: GridLayout,
  title: 'Components/GridLayout/GridLayout',
};

export default meta;

type Story = StoryObj<typeof GridLayout>;

const FieldBlock = styled.div`
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 1rem;
  background: gainsboro;
  color: red;
  border: 1px solid red;
`;

export const Overview: Story = {
  args: {
    columns: 2,
    rows: 4,
    gap: '1rem',
    children: [
      <FieldBlock style={{ gridColumn: '1 / span 2', gridRow: '1 / span 2' }}>
        Item 1
      </FieldBlock>,
      <FieldBlock style={{ gridColumn: '1 / span 2', gridRow: '3' }}>
        Item 2
      </FieldBlock>,
      <FieldBlock style={{ gridColumn: '1', gridRow: '4' }}>Item 3</FieldBlock>,
      <FieldBlock style={{ gridColumn: '2', gridRow: '4' }}>Item 3</FieldBlock>,
    ],
  },
};

const CenteredGridItem = styled(GridItem)`
  display: grid;
  justify-content: center;
  width: 100%;
`;

const StyledDragDrop = styled(DragAndDrop)`
  width: 100%;
  grid-column: span 2;
`;

const StyledAvatar = styled(Avatar)`
  margin-bottom: 2rem;
`;

const GridTextField = withGridItem(PlainTextInput);

export const ProfileDetail: Story = {
  args: {
    columns: 2,
    gap: '1rem',
    width: '50vw',
    style: {
      margin: 'auto',
    },
    mobileColumns: 1,
    children: [
      <CenteredGridItem data-testid="Grid-item" colSpan={2}>
        <Heading type="page-heading" children="Complete your Profile" />
      </CenteredGridItem>,
      <CenteredGridItem colSpan={2} rowSpan={3}>
        <StyledAvatar size="large" active={true} />
      </CenteredGridItem>,
      <GridTextField name="" />,

      <GridTextField name="" />,
      <StyledDragDrop onDrop={() => console.log('Dropped')} name="" />,

      <GridTextField name="" />,
    ],
  },
};

export const WithScrollableContent: Story = {
  render: (args) => {
    return (
      <ScrollableContent style={{ height: '400px', margin: '6rem 2rem' }}>
        <GridLayout {...args} />
      </ScrollableContent>
    );
  },
  args: {
    columns: 2,
    gap: '1rem',
    width: '50vw',
    style: {
      margin: 'auto',
    },
    mobileColumns: 1,
    children: [
      <CenteredGridItem data-testid="Grid-item" colSpan={2}>
        <Heading type="page-heading" children="Complete your Profile" />
      </CenteredGridItem>,
      <CenteredGridItem colSpan={2} rowSpan={3}>
        <StyledAvatar size="large" active={true} />
      </CenteredGridItem>,
      <GridTextField name="" />,

      <GridTextField name="" />,
      <StyledDragDrop onDrop={() => console.log('Dropped')} name="" />,

      <GridTextField name="" />,
    ],
  },
};
