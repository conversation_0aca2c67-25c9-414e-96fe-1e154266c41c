import styled from "styled-components";

/**
 * Container that holds the background elements.
 * The container is set to cover the entire viewport.
 * Styled components representing gradient shapes for the background.
 */
const Container = styled.div`
    width: 100vw;
    height: 100vh;
    position: absolute;
    z-index: -1;
`;

const GradientCircle1 = styled.div`
    width: 408px;
    height: 408px;
    left: 185px;
    top: 232px;
    position: absolute;
    background: radial-gradient(100% 100% at 50% 50%, #2F5583 0%, rgba(129, 185, 252, 0) 100%);
    box-shadow: 100px 100px 100px;
    border-radius: 9999px;
    filter: blur(100px);
`;

const GradientCircle2 = styled.div`
    width: 408px;
    height: 408px;
    left: -77px;
    top: 512px;
    position: absolute;
    background: radial-gradient(100% 100% at 50% 50%, #2F8379 0%, rgba(129, 230, 252, 0) 100%);
    box-shadow: 100px 100px 100px;
    border-radius: 9999px;
    filter: blur(100px);
`;

const GradientCircle3 = styled.div`
    width: 862px;
    height: 862px;
    left: -304px;
    top: -257px;
    position: absolute;
    background: radial-gradient(100% 100% at 50% 50%, #772F83 0%, rgba(252, 129, 203, 0) 100%);
    box-shadow: 100px 100px 100px;
    border-radius: 9999px;
    filter: blur(100px);
`;

const GradientRect1 = styled.div`
    width: 852px;
    height: 990px;
    left: -241px;
    top: 425px;
    position: absolute;
    background: linear-gradient(180deg, #2B2833 0%, rgba(35, 39, 154, 0) 100%);
    box-shadow: 100px 100px 100px;
    filter: blur(100px);
`;

const GradientRect2 = styled.div`
    width: 696.75px;
    height: 562.44px;
    left: -23.07px;
    top: 93.83px;
    position: absolute;
    transform: rotate(25.7deg);
    transform-origin: 0 0;
    background: radial-gradient(100% 100% at 50% 50%, #0C232B 0%, rgba(23, 164, 211, 0.22) 100%);
    box-shadow: 100px 100px 100px;
    border: 1px solid;
    filter: blur(100px);
`;

const GradientRect3 = styled.div`
    width: 828.85px;
    height: 1106.44px;
    left: 121.93px;
    top: -527.47px;
    position: absolute;
    transform: rotate(31.61deg);
    transform-origin: 0 0;
    background: linear-gradient(180deg, #273030 0%, rgba(14, 137, 137, 0) 100%);
    box-shadow: 100px 100px 100px;
    border: 1px solid;
    filter: blur(100px);
`;

/**
 * Background component that renders various gradient shapes as part of the background.
 * 
 * @returns {JSX.Element} The JSX element for the Background.
 */
export const Background = () => {
    return (
        <Container>
            <GradientCircle1 />
            <GradientCircle2 />
            <GradientCircle3 />
            <GradientRect1 />
            <GradientRect2 />
            <GradientRect3 />
        </Container>
    );
};
