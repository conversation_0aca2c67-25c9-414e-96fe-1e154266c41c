import { Button } from '@mui/material';
import { useTheme } from 'styled-components';

/**
 * Props interface for the AccountFormButton component.
 */
export interface AccountFormButtonProps {
  /** The text to be displayed on the button */
  title: string;
}

/**
 * A styled submit button component specifically designed for account-related forms.
 * Uses Material-UI Button component with custom styling to maintain consistency
 * with the application's design system.
 *
 * The button is styled with:
 * - Full width layout
 * - Circular border radius
 * - Custom border and background colors
 * - Specific typography settings
 * - Hover state styling
 *
 * @component
 * @param {Object} props - Component props
 * @param {string} props.title - The text to display on the button
 *
 * @example
 * ```tsx
 * <AccountFormButton title="Create Account" />
 * ```
 *
 * @example
 * ```tsx
 * <form onSubmit={handleSubmit}>
 *   <AccountFormButton title="Save Changes" />
 * </form>
 * ```
 */
export function AccountFormButton({ title }: AccountFormButtonProps) {
  const strokeColorInverse = useTheme().ColorsStrokesInverse;
  const inputsColorInverse = useTheme().ColorsInputsInverse;
  const typographyColorInverse = useTheme().ColorsTypographyInverse;
  const semiBold = useTheme().FontWeightsInter6;
  const inter = useTheme().FontFamiliesInter;
  return (
    <Button
      fullWidth
      type="submit"
      variant="contained"
      sx={{
        height: '57px',
        padding: '16px 77px',
        borderRadius: '104px',
        border: `2px solid ${strokeColorInverse}`,
        background: `${inputsColorInverse}`,
        color: `${typographyColorInverse}`,
        textAlign: 'center',
        fontFamily: `${inter}`,
        fontSize: '21px',
        fontWeight: `${semiBold}`,
        '&:hover': { backgroundColor: `${inputsColorInverse}` },
      }}
    >
      {title}
    </Button>
  );
}

export default AccountFormButton;
