import {
  IMaxLengthValidationMessages,
  IMinLengthValidationMessages,
  IRegexValidationMessages,
  IValidationMessages,
} from '../../../models/IValidationMessages';

export const username: IValidationMessages = {
  message: 'Please enter a username',
};

export const bank_account_number: IRegexValidationMessages = {
  regex: /^\d{9,16}$/,
  message: 'Account number is invalid. Please confirm banking details',
};

export const vat_no: IRegexValidationMessages = {
  regex: /^(4\d{9}|N\/A)$/,
  message: 'Invalid VAT number',
};

export const invalid_requirement_message: IValidationMessages = {
  message: 'Required field invalid',
};

export const invalid_password_message: IValidationMessages = {
  message: 'Password is invalid',
};

export const email_address: IValidationMessages = {
  message: 'Invalid email',
};

export const cellphone_number_min: IMinLengthValidationMessages = {
  minLength: 10,
  message: 'Invalid cell number',
};

export const cellphone_number_max: IMaxLengthValidationMessages = {
  maxLength: 10,
  message: ' Invalid cell number',
};

export const password_min: IMinLengthValidationMessages = {
  minLength: 8,
  message: 'Password must be at least 8 characters long',
};

export const password_regex: IRegexValidationMessages = {
  regex: /^(?=.*[0-9])(?=.*[A-Z])(?=.*[!@#$%^&*()_+={}\[\]:;'"\\|,.<>?]).*$/,
  message:
    'Password must contain at least one number, one uppercase letter, and one special character',
};

export const new_password_regex: IRegexValidationMessages = {
  regex: /^(?=.*[0-9])(?=.*[A-Z])(?=.*[!@#$%^&*()_+={}\[\]:;'"\\|,.<>?]).*$/,
  message: 'Does not meet requirement',
};

export const contact_number_regex: IRegexValidationMessages = {
  regex: /^\+?\(?(\d{1,3})\)?[- ]?(\d{3})[- ]?(\d{4})$/,
  message:
    'Contact number must be more than 10 characters and may not contain invalid characters',
};

export const id_number_regex: IRegexValidationMessages = {
  regex: /^\d{13}$/,
  message:
    'ID number must be 13 characters long and may not contain invalid characters',
};

export const multichoice_accreditation_number_regex: IRegexValidationMessages =
  {
    regex: /^000\w{5}MC$/,
    message: 'Invalid mulitchoice entry',
  };
