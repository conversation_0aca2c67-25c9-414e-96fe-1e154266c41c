import { render } from '@testing-library/react';

import FormInputText from './FormInputText';

describe('FormInputText', () => {
  it('should render successfully', () => {
    const { baseElement } = render(
      <FormInputText
        label={'Label'}
        error={{ type: 'error', message: 'error' }}
        type="text"
        name={'name'}
      />
    );
    expect(baseElement).toBeTruthy();
  });
});
