import {Controller, FieldError, FieldVal<PERSON>, UseControllerProps, Path} from 'react-hook-form';
import {FormGroup, FormLabel, InputAdornment, TextField, Typography} from '@mui/material';
import React, {HTMLInputTypeAttribute} from 'react';
import { useTheme } from 'styled-components';

/**
 * Interface for FormInputText component props that extends React Hook Form's UseControllerProps.
 * Provides type safety for form field values and names.
 * 
 * @template TFieldValues - Type for the form's field values
 * @template TName - Type for the field name, must be a valid path in TFieldValues
 */
export interface FormInputTextProps<TFieldValues extends FieldValues = FieldValues,
    TName extends Path<TFieldValues> = Path<TFieldValues>> extends UseControllerProps<TFieldValues, TName> {
    /** The label text to display above the input field */
    label: string;
    /** Error object containing validation error details */
    error: FieldError | undefined | null;
    /** HTML input type attribute (e.g., "text", "password", "email") */
    type: HTMLInputTypeAttribute | undefined;
    /** Optional icon element to display at the end of the input field */
    icon?: React.JSX.Element;
}

/**
 * A controlled form input component that integrates with React Hook Form.
 * Provides a styled text input field with label, error handling, and optional icon.
 * 
 * @component
 * @template TFieldValues - Type for the form's field values
 * @template TName - Type for the field name, must be a valid path in TFieldValues
 * 
 * @param {Object} props - Component props
 * @param {string} props.name - The name of the form field
 * @param {Object} props.rules - React Hook Form validation rules
 * @param {Object} props.control - React Hook Form control object
 * @param {string} props.label - Label text for the input field
 * @param {FieldError} props.error - Error object from React Hook Form
 * @param {HTMLInputTypeAttribute} props.type - HTML input type
 * @param {React.JSX.Element} [props.icon] - Optional icon element
 * 
 * @example
 * ```tsx
 * <FormInputText
 *   name="email"
 *   control={control}
 *   rules={{ required: 'Email is required' }}
 *   label="Email Address"
 *   type="email"
 *   error={errors.email}
 *   icon={<EmailIcon />}
 * />
 * ```
 */
export function FormInputText<
    TFieldValues extends FieldValues = FieldValues,
    TName extends Path<TFieldValues> = Path<TFieldValues>
>({name, rules, control, label, error, type, icon}: FormInputTextProps<TFieldValues, TName>) {

    const inputColorError = useTheme().ColorsInputsError;
    const inputsColorInverse = useTheme().ColorsInputsInverse;
    const utilityColorFocus = useTheme().ColorsUtilityColorFocus;

    const formGroupErrorStyles = {
        borderRadius: '4px',
        border: '1px solid #B22411',
        background: `${inputColorError}`,
        padding: '8px'
    } as const;

    const formGroupDefaultStyles = {
        borderRadius: '4px',
        border: `1px solid ${utilityColorFocus}`,
        background: `${inputsColorInverse}`,
        padding: '8px'
    } as const;
    const formLabelErrorStyles = {
        float: 'right',
        color: '#B22411',
        fontSize: '14px',
    } as const;

    const formGroupStyles = error ? formGroupErrorStyles : formGroupDefaultStyles;
    return (
        <FormGroup sx={formGroupStyles}>
            <FormLabel sx={{
                fontSize: '14px'
            }}>{label} <Typography sx={formLabelErrorStyles}
                                   component='span'>{error ? error.message : null}</Typography></FormLabel>
            <Controller
                name={name}
                control={control}
                rules={rules}
                render={({
                             field: {onChange, value, name: nm},
                             fieldState: {error},
                             formState,
                         }) => (
                    <TextField
                        InputProps={{
                            endAdornment: (
                                <InputAdornment sx={{cursor: 'pointer'
                                }} position={"end"}>
                                    {icon}
                                </InputAdornment>
                            ),

                        }}
                        id={name}
                        name={nm}
                        error={!!error}
                        onChange={onChange}
                        value={value || ''}
                        fullWidth={true}
                        variant='standard'
                        type={type}
                        sx={{
                            'input': {
                                paddingTop: 0,
                                paddingBottom: '4px',
                                fontSize: '14px',
                            }
                        }}
                    />
                )}
            />
        </FormGroup>
    );
}

export default FormInputText;
