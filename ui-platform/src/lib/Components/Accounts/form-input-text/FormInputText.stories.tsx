import type { Meta, StoryObj } from '@storybook/react';
import { FormInputText } from './FormInputText';

const meta: Meta<typeof FormInputText> = {
  component: FormInputText,
  title: 'Components/Accounts/FormInputText',
};
export default meta;
type Story = StoryObj<typeof FormInputText>;

export const Overview: Story = {
  args: {
    label: 'Label',
    name: 'name',
    type: 'text',
    error: null,
  },
};
