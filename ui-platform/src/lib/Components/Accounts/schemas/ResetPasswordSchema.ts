import * as z from 'zod';
import { new_password_regex, password_min } from '../utils/AccountFormsValidation';

const schema = z.object({
    password_challenge: z.optional(z.string()),
    new_password: z
        .string()
        .min(password_min.minLength, {message: password_min.message})
        .regex(new_password_regex.regex, {
            message: new_password_regex.message,
        }),
    confirm_password: z
        .string()
        .min(password_min.minLength, {message: password_min.message})
        .regex(new_password_regex.regex, {
            message: new_password_regex.message,
        })
}).refine((data) => data.new_password   === data.confirm_password, {
    message: "Passwords do not match",
    path: ["confirm_password"]
})

export default schema;

export type ResetPasswordSchemaType = z.infer<typeof schema>;
