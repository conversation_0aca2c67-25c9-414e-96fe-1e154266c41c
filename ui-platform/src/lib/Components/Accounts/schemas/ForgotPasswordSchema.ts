import * as z from 'zod';
import { email_address, cellphone_number_min, cellphone_number_max } from '../utils/AccountFormsValidation';

const schema = z.object({
    email: z.optional(
            z.string()
            .email(email_address.message)),
    sms: z.optional(
        z.string()
        .min(cellphone_number_min.minLength, {
            message: cellphone_number_min.message,
        })
        .max(cellphone_number_max.maxLength, {
            message: cellphone_number_max.message,
        })),
   
})

export default schema;

export type ForgotPasswordSchemaType = z.infer<typeof schema>;
