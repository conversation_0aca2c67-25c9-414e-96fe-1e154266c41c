import * as z from 'zod';

const schema = z.object({
    email: z.optional(z.string().email()),
    sms: z.optional(z.string().min(10, { message: " Please enter a minimum length of 10"}).max(10, { message: " Please enter a maximum length of 10"})),
    otp: z.string()
    .min(1, { message: " Please enter a minimum length of 1"})
    .max(6, { message: " Please enter a maximum length of 6"}),
})

export default schema;

export type RecoveryOtpSchemaType = z.infer<typeof schema>;
