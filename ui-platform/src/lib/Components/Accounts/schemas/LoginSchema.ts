import * as z from 'zod';
import {
    invalid_password_message,
    invalid_requirement_message,
    password_min,
    password_regex,
    username
} from '../utils/AccountFormsValidation';

const schema = z.object({
    username: z.string({
        required_error: username.message,
    }).min(1, 'Please enter your username'),
    password: z
        .string(
            {
                required_error: invalid_requirement_message.message
            }
        )
        .min(password_min.minLength, {message: invalid_password_message.message})
        .regex(password_regex.regex, {
            message: invalid_password_message.message,
        }),
})

export default schema;

export type LoginSchemaType = z.infer<typeof schema>;
