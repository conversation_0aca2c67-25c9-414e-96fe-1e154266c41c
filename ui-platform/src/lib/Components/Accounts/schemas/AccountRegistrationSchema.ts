import * as z from 'zod';
import { cellphone_number_max, cellphone_number_min, email_address, password_min, password_regex, username } from '../utils/AccountFormsValidation';

const schema = z
    .object({
        username: z.string({
            required_error: username.message,
        }).min(1, 'Please enter a username').default(''),
        email_address: z
            .string()
            .email(email_address.message)
            .default(''),
        cellphone_number: z
            .string()
            .min(cellphone_number_min.minLength, {
                message: cellphone_number_min.message,
            })
            .max(cellphone_number_max.maxLength, {
                message: cellphone_number_max.message,
            })
            .default(''),
        password: z
            .string()
            .min(password_min.minLength, { message: password_min.message })
            .regex(password_regex.regex, {
                message: password_regex.message,
            })
            .default(''),
        confirm_password: z
            .string()
            .min(password_min.minLength, { message: password_min.message })
            .regex(password_regex.regex, {
                message: password_regex.message,
            })
            .default(''),
        recaptcha: z
            .object({
                token: z.string().optional(),
                action: z.string().optional(),
            })
            .optional(),
    })
    .refine((data) => data.password === data.confirm_password, {
        message: "Passwords do not match",
        path: ["confirm_password"]
    });

export default schema;

export type AccountRegistrationSchemaType = z.infer<typeof schema>;
