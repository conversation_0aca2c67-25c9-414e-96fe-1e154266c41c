import type { Meta, StoryObj } from '@storybook/react';
import { NetworkStatus } from './NetworkStatus';

const meta: Meta<typeof NetworkStatus> = {
  component: NetworkStatus,
  title: 'Components/NetworkStatus',
  parameters: {
    layout: 'fullscreen',
  },
};

export default meta;
type Story = StoryObj<typeof NetworkStatus>;

export const Default: Story = {
  render: () => <NetworkStatus />,
  parameters: {
    docs: {
      description: {
        story: 'Shows full banner on disconnect, slides to icon-only, then shows restore banner when reconnected.',
      },
    },
  },
};

export const Offline: Story = {
  render: () => {
    Object.defineProperty(navigator, 'onLine', {
      value: false,
      writable: true,
    });
    return <NetworkStatus />;
  },
  parameters: {
    docs: {
      description: {
        story: 'Simulates offline state to demonstrate banner animation.',
      },
    },
  },
};
