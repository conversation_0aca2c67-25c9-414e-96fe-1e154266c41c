import React, { useEffect, useState } from 'react';
import styled, { keyframes, css } from 'styled-components';
import { useToast } from '../../Engine/hooks/useToastStore';
import { SignalLostIcon, OnlineIcon, OfflineIcon } from './icons';

// Configure these values to control the banner's slide behavior
const SLIDE_START = '100%';  // Where the banner starts (off-screen)
const SLIDE_END = '0';       // Where the banner stops (fully visible)
const SLIDE_PARTIAL = '75%'; // Where the banner stops when partially visible

const slideIn = (startDistance: string, endDistance: string) => keyframes`
  from { transform: translateX(${startDistance}); }
  to { transform: translateX(${endDistance}); }
`;

const slideOut = (startDistance: string, endDistance: string) => keyframes`
  from { transform: translateX(${startDistance}); }
  to { transform: translateX(${endDistance}); }
`;

const Banner = styled.div<{ 
  $isIconOnly?: boolean; 
  $status: 'online' | 'offline' | 'restored'; 
  $isExiting?: boolean;
  $isFinalExit?: boolean;
}>`
  position: fixed;
  top: 0;
  right: 0;
  color: white;
  padding: 10px 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  width: ${({ $isIconOnly }) => ($isIconOnly ? '60px' : '300px')};
  height: 50px;
  overflow: hidden;
  z-index: 9999;
  transform: ${({ $isExiting, $isFinalExit }) => {
    if ($isFinalExit) return `translateX(${SLIDE_PARTIAL})`;
    if ($isExiting) return `translateX(${SLIDE_PARTIAL})`;
    return `translateX(${SLIDE_END})`;
  }};
  animation: ${({ $isExiting, $isFinalExit }) => {
    if ($isFinalExit) return slideOut(SLIDE_PARTIAL, SLIDE_START);
    if ($isExiting) return slideOut(SLIDE_END, SLIDE_PARTIAL);
    return slideIn(SLIDE_START, SLIDE_END);
  }} 0.4s ease-out;
  animation-fill-mode: forwards;
`;

const NotificationBox = styled.div<{ $show: boolean }>`
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #111;
  color: #fff;
  padding: 2rem;
  width: 300px;
  height: 200px;
  text-align: center;
  box-shadow: 0 0 50px rgba(255, 0, 0, 0.4);
  z-index: 9999;
  display: ${({ $show }) => ($show ? 'block' : 'none')};
`;

const NotificationTitle = styled.div`
  font-size: 0.7rem;
  margin-bottom: 1rem;
  color: #fff;
  margin-top: 1rem;
`;

const NotificationMessage = styled.div`
  font-size: 0.5rem;
  color: #ccc;
  margin-bottom: 1rem;
`;

const ContinueButton = styled.button`
  background-color: #333;
  color: #fff;
  border: none;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-radius: 5px;

  &:hover {
    background-color: #444;
  }
`;


export const NetworkStatus: React.FC = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showOffline, setShowOffline] = useState(!navigator.onLine);
  const [showIconOnly, setShowIconOnly] = useState(false);
  const [showRestored, setShowRestored] = useState(false);
  const [isExiting, setIsExiting] = useState(false);
  const [isFinalExit, setIsFinalExit] = useState(false);
  const [showNotification, setShowNotification] = useState(false);

  const toast = useToast();

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setShowOffline(false);
      setShowIconOnly(false);
      setShowRestored(true);
      setShowNotification(false);
      setIsExiting(false);
      setIsFinalExit(false);
      toast.success('Network Restored');

      // First wait 3 seconds before sliding to 75%
      setTimeout(() => {
        setIsExiting(true);
      }, 3000);

      // Then wait 7 seconds total (3s full + 4s at 75%) before final slide out
      setTimeout(() => {
        setIsFinalExit(true);
      }, 7000);

      // Then wait 0.4s for the slide animation before hiding
      setTimeout(() => {
        setShowRestored(false);
        setIsExiting(false);
        setIsFinalExit(false);
      }, 7400);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowOffline(true);
      setShowIconOnly(false);
      setShowRestored(false);
      setIsExiting(false);
      setShowNotification(true);

      // Wait 3 seconds before sliding to 75%
      setTimeout(() => {
        setIsExiting(true);
      }, 3000);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [toast]);

  const handleContinue = () => {
    setShowNotification(false);
  };

  return (
    <>
      {showOffline && (
        <Banner 
          $status="offline" 
          $isIconOnly={showIconOnly} 
          $isExiting={isExiting}
        >
          <SignalLostIcon />
          {!showIconOnly}
        </Banner>
      )}

      {showRestored && (
        <Banner 
          $status="restored" 
          $isIconOnly={showIconOnly}
          $isExiting={isExiting}
          $isFinalExit={isFinalExit}
        >
          <OnlineIcon />
          {!showIconOnly}
        </Banner>
      )}

      <NotificationBox $show={showNotification}>
        <OfflineIcon />
        <NotificationTitle>Network Connection Lost</NotificationTitle>
        <NotificationMessage>
          Please check your network.
        </NotificationMessage>
        <ContinueButton onClick={handleContinue}>CONTINUE</ContinueButton>
      </NotificationBox>
    </>
  );
};
