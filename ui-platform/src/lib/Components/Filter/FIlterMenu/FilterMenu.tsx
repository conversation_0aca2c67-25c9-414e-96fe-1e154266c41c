import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { svgs } from '../../Icons/svgs';

type Item =
  | string
  | {
      text: string;
      icon?: 'chevron-right' | 'chevron-left';
      iconPosition?: 'left' | 'right';
      dropdownOptions?: string[];
    };

export interface FilterMenuProps {
  items: Item[];
}

const Property1DefaultContainer = styled.div`
  width: 201px;
  position: relative;
  backdrop-filter: blur(4px);
  border-radius: 2px;
  background-color: rgba(46, 50, 54, 0.93);
  border: 1px solid ${(props) => props?.theme.ColorsControllersTertiary};
  box-sizing: border-box;
  display: grid;
  grid-template-rows: repeat(auto-fill, minmax(32px, auto));
  gap: 4px;
  padding: 4px 8px;
  text-align: center;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props?.theme.ColorsTypographySecondary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  cursor: pointer;
`;

const Http = styled.div`
  position: relative;
  text-transform: capitalize;
  font-weight: ${(props) => props.theme.FontWeightsInter5};
`;

const GeneralIcon = styled.svg`
  width: 24px;
  height: 24px;
  justify-self: end;
`;

const FilterButton = styled.div<{ isSelected?: boolean }>`
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  align-self: stretch;
  height: 32px;
  border-radius: 2px;
  padding: 8px;
  box-sizing: border-box;
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
  cursor: pointer;

  ${(props) =>
    props.isSelected &&
    `
    background-color: #3f4142;
    border: 0.5px solid #118ab2;
    `}
`;

const FilterButton1 = styled.div<{ isSelected?: boolean }>`
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  width: 100%;
  height: 32px;
  overflow: hidden;
  flex-shrink: 0;
  border-radius: 2px;
  padding: 8px;
  box-sizing: border-box;
  color: ${(props) => props?.theme.ColorsIconColorPrimary};
  cursor: pointer;

  ${(props) =>
    props.isSelected &&
    `
    background-color: #3f4142;
    border: 0.5px solid #118ab2;
    `}
`;

const Dropdown = styled.div<{ isVisible: boolean; top: number }>`
  display: ${(props) => (props.isVisible ? 'block' : 'none')};
  position: absolute;
  top: ${(props) => props.top}px;
  left: 100%;
  margin-left: 8px;
  width: 150px;
  background-color: rgba(46, 50, 54, 0.93);
  border: 1px solid ${(props) => props?.theme.ColorsControllersTertiary};
  border-radius: 2px;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 10;
  padding: 8px;
  gap: 4px;
  cursor: pointer;
`;

/**
 * The FilterMenu component renders a list of items with the ability to select
 * one of them. If an item has a dropdown option, it will show the dropdown when
 * the item is selected. The selected item will be highlighted, and the dropdown
 * will be shown below the selected item. The component also handles
 * deselection of the selected item by clicking on it again.
 *
 * The component takes in an array of items, where each item is either a string
 * or an object with a 'text' property and an optional 'dropdownOptions' property.
 * The 'dropdownOptions' property is an array of strings that will be used to
 * populate the dropdown menu.
 *
 * @example
 * <FilterMenu
 *   items={[
 *     { text: 'Main item 1', dropdownOptions: ['Option 1', 'Option 2'] },
 *     'Main item 2',
 *     { text: 'Main item 3', dropdownOptions: ['Option 3', 'Option 4'] },
 *   ]}
 * />
 */
export const FilterMenu: React.FC<FilterMenuProps> = ({ items }) => {
  const [selected, setSelected] = useState<null | number>(null);
  const [selectedDropdown, setSelectedDropdown] = useState<string | null>(null);
  const [dropdownTop, setDropdownTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  /**
   * Handles the click on the icon. If the selected item is already selected, it
   * deselects it. Otherwise, it selects the item and shows the dropdown if it
   * exists.
   * @param index The index of the item that was clicked.
   * @param event The event of the click.
   */
  const handleIconClick = (index: number, event: React.MouseEvent) => {
    if (selected === index) {
      setSelected(null);
      setSelectedDropdown(null);
    } else {
      setSelected(index);
      setSelectedDropdown(null);
      setDropdownTop(
        event.currentTarget.getBoundingClientRect().top -
          containerRef.current!.getBoundingClientRect().top
      );
    }
    if (typeof items[index] === 'string') {
      console.log(`Selected main item: ${items[index]}`);
    } else {
      console.log(
        `Selected main item with dropdown: ${(items[index] as any).text}`
      );
    }
  };

  /**
   * Handles selection of a dropdown option. Logs the selection and sets the
   * selectedDropdown state to the selected text. Also resets the selected
   * state to null.
   * @param {string} text - The text of the selected dropdown option
   * @param {number} index - The index of the selected item from the items array
   */
  const handleDropdownSelect = (text: string, index: number) => {
    setSelectedDropdown(text);
    setSelected(null);
    console.log(
      `Selected dropdown option: ${text} from main item: ${
        (items[index] as any).text
      }`
    );
  };

  useEffect(() => {
    if (selected === null) {
      setDropdownTop(0);
    }
  }, [selected]);

  return (
    <Property1DefaultContainer ref={containerRef}>
      {items.map((item, index) => {
        if (typeof item === 'string') {
          return (
            <FilterButton
              key={index}
              isSelected={selectedDropdown === item}
              onClick={() => console.log(`Selected item: ${item}`)}
            >
              <Http>{item}</Http>
            </FilterButton>
          );
        } else {
          const isItemSelected = selected === index;
          const iconType = isItemSelected ? 'chevron-left' : 'chevron-right';
          const iconPosition = item.iconPosition || 'right';

          return (
            <React.Fragment key={index}>
              <FilterButton
                onClick={(event) => handleIconClick(index, event)}
                isSelected={isItemSelected || selectedDropdown === item.text}
              >
                {iconPosition === 'left' && (
                  <GeneralIcon viewBox={svgs[iconType].viewBox}>
                    {svgs[iconType].paths.map((path: any, pathIndex: number) => (
                      <path
                        key={pathIndex}
                        d={path.d}
                        stroke={isItemSelected ? '#118AB2' : path.stroke}
                        strokeLinecap={path.strokeLinecap}
                        strokeLinejoin={path.strokeLinejoin}
                        fill={path.fill}
                      />
                    ))}
                  </GeneralIcon>
                )}
                <Http>{item.text}</Http>
                {iconPosition === 'right' && (
                  <GeneralIcon viewBox={svgs[iconType].viewBox}>
                    {svgs[iconType].paths.map((path: any, pathIndex: number) => (
                      <path
                        key={pathIndex}
                        d={path.d}
                        stroke={isItemSelected ? '#118AB2' : path.stroke}
                        strokeLinecap={path.strokeLinecap}
                        strokeLinejoin={path.strokeLinejoin}
                        fill={path.fill}
                      />
                    ))}
                  </GeneralIcon>
                )}
              </FilterButton>
              {isItemSelected && item.dropdownOptions && (
                <Dropdown isVisible={isItemSelected} top={dropdownTop}>
                  {item.dropdownOptions.map((option, optionIndex) => (
                    <FilterButton1
                      key={optionIndex}
                      isSelected={selectedDropdown === option}
                      onClick={() => handleDropdownSelect(option, index)}
                    >
                      {option}
                    </FilterButton1>
                  ))}
                </Dropdown>
              )}
            </React.Fragment>
          );
        }
      })}
    </Property1DefaultContainer>
  );
};
