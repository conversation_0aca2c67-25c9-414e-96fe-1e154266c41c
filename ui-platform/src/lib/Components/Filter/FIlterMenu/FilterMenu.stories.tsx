import type { <PERSON><PERSON>, <PERSON> } from '@storybook/react';
import React from 'react';
import { FilterMenu, FilterMenuProps } from './FilterMenu';

const meta: Meta<typeof FilterMenu> = {
  title: 'Components/FilterMenu',
  component: FilterMenu,
  argTypes: {
    items: {
      control: 'array',
      description: 'Array of strings or objects with text and optional icon keys',
    },
  },
};

export default meta;

const Template: Story<FilterMenuProps> = (args) => <FilterMenu {...args} />;

export const Default = Template.bind({});
Default.args = {
  items: [
    'Simple Text',
    { text: 'Item 1', icon: 'chevron', dropdownOptions: ['Option 1.1', 'Option 1.2', 'Option 1.3'] },
    { text: 'Item 2', icon: 'chevron-right', dropdownOptions: [] },
    // { text: 'Item 2', icon: 'chevron-right', dropdownOptions: ['Option 2.1', 'Option 2.2'] },
    { text: 'Item 3', icon: 'chevron-right', dropdownOptions: ['Option 3.1', 'Option 3.2', 'Option 3.3'] },
    'Another Text',
  ],
};
