import React from 'react';
import styled from 'styled-components';
import Filter, { FilterSelectorItem } from '../Filter';
import { FilterCondition } from '../../../Engine/models/filter-condition';

export interface WorkFlowFilterData {
  buttonText: string;
  items: FilterSelectorItem[];
  width?: string | number;
  minWidth?: string | number;
  maxWidth?: string | number;
  searchable?: boolean;
  autoFocus?: boolean;
}

export interface WorkFlowFilterProps {
  filtersData: WorkFlowFilterData[];
  addFilterCondition: (condition: FilterCondition) => void;
  filterConditions?: FilterCondition[];
}

/**
 * Renders a set of filters as a horizontal list
 * Each filter is rendered as a separate Filter component with dynamic width
 * When a filter is selected, the addFilterCondition function is called
 * with the selected filter condition as an argument
 */
export const WorkFlowFilter: React.FC<WorkFlowFilterProps> = ({
  filtersData = [],
  addFilterCondition,
  filterConditions = [],
}) => {
  return (
    <NavContainer>
      {filtersData?.map((filter, index) => {
        // Set even larger default width and minWidth
        const minWidth = filter.minWidth || '280px';
        const maxWidth = filter.maxWidth || 'auto';
        const width = filter.width || '280px';
        
        // Get all selected filter names for all instances
        const selectedFilterNames = filterConditions
          .map(fc => fc.name)
          .filter(Boolean);
        
        return (
          <FilterWrapper 
            key={index}
            style={{
              width,
              minWidth,
              maxWidth,
            }}
          >
            <Filter
              onSelect={(item: FilterSelectorItem) => {
                if (item.filterCondition) {
                  addFilterCondition({
                    ...item.filterCondition,
                    name: item.text || item.filterCondition.name,
                  });
                }
              }}
              filter={filter}
              searchable={filter.searchable}
              autoFocus={filter.autoFocus}
              selectedFilterNames={selectedFilterNames}
            />
          </FilterWrapper>
        );
      })}
    </NavContainer>
  );
};

const NavContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 1px;
  width: 100%;
`;

const FilterWrapper = styled.div`
  flex: 0 1 auto;
  margin: 1px;
`;
