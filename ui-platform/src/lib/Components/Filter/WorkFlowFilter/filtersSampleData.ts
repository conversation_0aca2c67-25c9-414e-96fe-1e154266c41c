import { WorkFlowFilterProps } from './WorkFlowFilter';

export const filtersSampleData: WorkFlowFilterProps['filtersData'] = [
  // {
  //   buttonText: 'Filter 1',
  //   items: [
  //     'Skills',
  //     { text: 'SP/Team lead', icon: 'chevron-right' },
  //     { text: 'Appointment time', icon: 'chevron-right' },
  //   ],
  // },
  // { buttonText: 'Filter 2', items: ['Option A', 'Option B', 'Option C'] },
  // { buttonText: 'Filter 3', items: ['Option X', 'Option Y', 'Option Z'] },
  // { buttonText: 'Filter 4', items: ['Option L', 'Option M', 'Option N'] },
  // { buttonText: 'Filter 5', items: ['Option 11', 'Option 22', 'Option 33'] },

  
];
