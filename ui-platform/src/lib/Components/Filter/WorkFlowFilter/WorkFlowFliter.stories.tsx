import { <PERSON>a, <PERSON> } from '@storybook/react';
import React from 'react';
import { WorkFlowFilter, WorkFlowFilterProps } from './WorkFlowFilter';

const meta: Meta<typeof WorkFlowFilter> = {
  title: 'Components/WorkFlowFilter',
  component: WorkFlowFilter,
  argTypes: {
    filtersData: {
      control: 'array',
      description:
        'Array of filter data objects to display in the workflow filter',
    },
  },
};

export default meta;

const Template: Story<WorkFlowFilterProps> = (args) => (
  <WorkFlowFilter {...args} />
);

export const Default = Template.bind({});
Default.args = {
  filtersData: [
    {
      buttonText: 'Filter 1',
      items: [
        'Skills',
        { text: 'SP/Team lead', icon: 'chevron-right' },
        { text: 'Appointment time', icon: 'chevron-right' },
      ],
      searchable: true,
    },
    { 
      buttonText: 'Filter 2', 
      items: ['Option A', 'Option B', 'Option C'],
      searchable: true 
    },
    { 
      buttonText: 'Filter 3', 
      items: ['Option X', 'Option Y', 'Option Z'],
      searchable: true 
    },
    { 
      buttonText: 'Filter 4', 
      items: ['Option L', 'Option M', 'Option N'],
      searchable: true 
    },
    { 
      buttonText: 'Filter 5', 
      items: ['Option 11', 'Option 22', 'Option 33'],
      searchable: true 
    },
  ],
};
