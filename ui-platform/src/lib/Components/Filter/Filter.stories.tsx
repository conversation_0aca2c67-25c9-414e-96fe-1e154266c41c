import React from 'react';
import type { Meta, StoryFn } from '@storybook/react';
import Filter, { FilterProps } from './Filter';

const meta: Meta<typeof Filter> = {
  title: 'Components/CompleteFilter',
  component: Filter,
  argTypes: {
    filter: {
      control: 'object',
      description: 'Filter data'
    },
    onSelect: {
      action: 'selected'
    }
  },
};

export default meta;

const Template: StoryFn<FilterProps> = (args: FilterProps) => <Filter {...args} searchable={true}/>;

export const Default = Template.bind({});
Default.args = {
  filter: {
    buttonText: 'Job details',
    items: [
      { text: 'Text here' },
      { text: 'Skills', icon: 'chevron-right' },
      { text: 'SP/Team lead', icon: 'chevron-right' },
      { text: 'Appointment time', icon: 'chevron-right' },
    ]
  },
  onSelect: (item) => console.log('Selected:', item),
  searchable: true
};
