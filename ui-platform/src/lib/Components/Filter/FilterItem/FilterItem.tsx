import React from 'react';
import styled from 'styled-components';
import { Icon } from '../../Icons';

interface FilterItemProps {
  itemText: string
  itemWidth?: string; 
  onClick: () => void;
  selected?: boolean;
}

const StyledFilterContainer = styled.div<any>`
  display: grid;
  grid-template-columns: 80% 20%;
  align-items: center;
  gap: ${(props) => props.theme.GapXs};
  height: 21px;
  width: ${(props) => props.itemWidth};
  background-color: ${(props) => props.theme.ColorsCardColorJobCardPrimary};
  border: 2px solid
    ${(props) => (props.selected ? '#118ab2' : 'transparent')};
  border-radius: ${(props) => props.theme.RadiusXxs};
  transition: background 0.18s, border 0.18s;
  &:hover {
    background: rgba(255,255,255,0.08);
    border-color: ${(props) => (props.selected ? '#118ab2' : 'transparent')};
  }
`;

const StyledFilterText = styled.div`
  color: ${(props) => props.theme.ColorsTypographySecondary};
  text-align: center;
  
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize2}px;
  font-style: normal;
  font-weight: ${(props) => props.theme.FontWeightsInter5};
  line-height: normal;
  border-radius: ${(props) => props.theme.RadiusXxs};
  padding: ${(props) => props.theme.RadiusXxs} ${(props) => props.theme.RadiusSm};
`;

/**
 * FilterItem component
 * 
 * @param {string} itemText - The text to display in the filter item
 * @param {string} [itemWidth] - The width of the filter item, defaults to 'auto'
 * @param {() => void} onClick - Called when the filter item is clicked
 * @param {boolean} [selected] - Indicates whether the filter item is selected
 * 
 * @example
 * <FilterItem itemText="Filter Item" itemWidth="200px" onClick={() => console.log('Filter Item clicked')} />
 */

export const FilterItem: React.FC<FilterItemProps> = ({ itemWidth, itemText, onClick, selected }) => {
  return (
    <StyledFilterContainer itemWidth={itemWidth} selected={selected}>
      <StyledFilterText>{itemText}</StyledFilterText>
      <Icon type="close" size={18} onClick={onClick}/>
    </StyledFilterContainer>
  );
};
