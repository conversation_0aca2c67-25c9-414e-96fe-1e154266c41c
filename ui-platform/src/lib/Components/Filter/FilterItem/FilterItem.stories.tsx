import type { Meta, StoryObj } from '@storybook/react';
import { FilterItem } from './FilterItem';

const meta: Meta<typeof FilterItem> = {
  component: FilterItem,
  title: 'Components/FilterItem',
  argTypes: {
    itemWidth: { control: 'text' }
  },
};
export default meta;
type Story = StoryObj<typeof FilterItem>;

export const Default: Story = {
  args: {
    itemWidth: '100px',
    itemText: "Filter Item"
  },
};