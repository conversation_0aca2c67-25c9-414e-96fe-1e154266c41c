import React, { useRef, useState } from 'react';
import styled from 'styled-components';
import { FilterCondition } from '../../Engine/models/filter-condition';
import { svgs } from '../Icons/svgs';
import useOutsideClick from './UseOutsideClick';
import { WorkFlowFilterData } from './WorkFlowFilter/WorkFlowFilter';

export type FilterSelectorItem = {
  text?: string;
  icon?: 'chevron-right' | 'chevron-left';
  iconPosition?: 'left' | 'right';
  filterCondition?: FilterCondition;
  items?: FilterSelectorItem[];
};

export interface FilterProps {
  filter: WorkFlowFilterData;
  onSelect: (item: FilterSelectorItem) => void;
  searchable?: boolean;
  autoFocus?: boolean;
  selectedFilterNames?: string[];
}

const Filter: React.FC<FilterProps> = ({
  filter,
  onSelect,
  searchable,
  autoFocus,
  selectedFilterNames = [],
}) => {
  const [isMenuVisible, setIsMenuVisible] = useState(false);
  const [selectedPath, setSelectedPath] = useState<number[]>([]);
  const [currentItems, setCurrentItems] = useState<FilterSelectorItem[]>(
    filter?.items || []
  );
  const [searchTerm, setSearchTerm] = useState('');
  const menuRef = useRef<HTMLDivElement>(null);

  const isActive = isMenuVisible;

  const toggleMenu = () => {
    if (!isMenuVisible) {
      // Reset to root menu when opening
      setCurrentItems(filter?.items || []);
      setSelectedPath([]);
      setSearchTerm('');
    }
    setIsMenuVisible(!isMenuVisible);
  };

  // Search functionality
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    const term = event.target.value;
    setSearchTerm(term);

    if (!term) {
      let itemsToFilter = filter?.items || [];
      if (selectedPath.length > 0) {
        let items = filter?.items || [];
        for (const index of selectedPath) {
          items = items[index].items || [];
        }
        itemsToFilter = items;
      }
      setCurrentItems(itemsToFilter);
      return;
    }

    let baseItems = filter?.items || [];
    if (selectedPath.length > 0) {
      let items = filter?.items || [];
      for (const index of selectedPath) {
        items = items[index].items || [];
      }
      baseItems = items;
    }

    const flattenMatches = (
      items: FilterSelectorItem[]
    ): FilterSelectorItem[] => {
      const matches: FilterSelectorItem[] = [];

      items.forEach((item) => {
        // Check if this item matches
        const currentMatches = item.text
          ?.toLowerCase()
          .includes(term.toLowerCase());

        // Check if any children match
        let childMatches: FilterSelectorItem[] = [];
        if (item.items && item.items.length > 0) {
          childMatches = flattenMatches(item.items);
        }

        // Add this item if it matches or has matching children
        if (currentMatches) {
          matches.push(item);
        } else if (childMatches.length > 0) {
          // If children match but parent doesn't, create a copy of parent with only matching children
          matches.push({
            ...item,
            items: childMatches,
          });
        }
      });

      return matches;
    };

    const searchResults = flattenMatches(baseItems);
    setCurrentItems(searchResults);
  };

  const handleItemClick = (item: FilterSelectorItem, index: number) => {
    if (item.items && item.items.length > 0) {
      // Navigate to submenu
      setSelectedPath([...selectedPath, index]);
      setCurrentItems(item.items);
    } else {
      // Select leaf item
      onSelect(item);
      setIsMenuVisible(false);
      setSelectedPath([]);
    }
  };

  const handleBack = () => {
    if (selectedPath.length > 0) {
      // Remove last index from path
      const newPath = selectedPath.slice(0, -1);
      setSelectedPath(newPath);

      // Navigate back to parent menu
      let items = filter?.items || [];
      for (const index of newPath) {
        items = items[index].items || [];
      }
      setCurrentItems(items);
    }
  };

  // Use custom hook to detect clicks outside the filter menu
  useOutsideClick(menuRef, () => {
    setIsMenuVisible(false);
    setSelectedPath([]);
  });

  return (
    <Container ref={menuRef}>
      {filter?.buttonText && (
        <FilterButton onClick={toggleMenu} isSelected={isActive}>
          <HttpText isSelected={isActive}>{filter?.buttonText}</HttpText>
          <ArrowsIcon
            isSelected={isActive}
            viewBox={
              svgs[isMenuVisible ? 'chevron-up' : 'chevron-down'].viewBox
            }
          >
            <path
              d={svgs[isMenuVisible ? 'chevron-up' : 'chevron-down'].paths[0].d}
              stroke={
                isActive ? '#118AB2' : svgs['chevron-down'].paths[0].stroke
              }
              strokeLinecap={svgs['chevron-down'].paths[0].strokeLinecap}
              strokeLinejoin={svgs['chevron-down'].paths[0].strokeLinejoin}
              fill={svgs['chevron-down'].paths[0].fill}
            />
          </ArrowsIcon>
        </FilterButton>
      )}

      {isMenuVisible && (
        <FilterMenu>
          {selectedPath.length > 0 && (
            <BackButton onClick={handleBack}>
              <GeneralIcon viewBox={svgs['chevron-left'].viewBox}>
                <path
                  d={svgs['chevron-left'].paths[0].d}
                  stroke={svgs['chevron-left'].paths[0].stroke}
                  strokeLinecap={svgs['chevron-left'].paths[0].strokeLinecap}
                  strokeLinejoin={svgs['chevron-left'].paths[0].strokeLinejoin}
                  fill={svgs['chevron-left'].paths[0].fill}
                />
              </GeneralIcon>
              <HttpText>Back</HttpText>
            </BackButton>
          )}

          {searchable && (
            <SearchContainer>
              <SearchIcon viewBox="0 0 24 24">
                <circle cx="11" cy="11" r="8" stroke="currentColor" strokeWidth="2" fill="none" />
                <line x1="21" y1="21" x2="16.65" y2="16.65" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
              </SearchIcon>
              <SearchInput
                type="text"
                placeholder="Search..."
                value={searchTerm}
                onChange={handleSearch}
                autoFocus={autoFocus}
              />
            </SearchContainer>
          )}

          {currentItems.map((item, index) => (
            <FilterItem
              key={index}
              onClick={() => handleItemClick(item, index)}
              hasSubMenu={!!(item.items && item.items.length > 0)}
              selected={selectedFilterNames.includes(item.text || '')}
            >
              <HttpText isSelected={selectedFilterNames.includes(item.text || '')}>{item.text}</HttpText>
              {item.items && item.items.length > 0 && (
                <GeneralIcon viewBox={svgs['chevron-right'].viewBox}>
                  <path
                    d={svgs['chevron-right'].paths[0].d}
                    stroke={svgs['chevron-right'].paths[0].stroke}
                    strokeLinecap={svgs['chevron-right'].paths[0].strokeLinecap}
                    strokeLinejoin={
                      svgs['chevron-right'].paths[0].strokeLinejoin
                    }
                    fill={svgs['chevron-right'].paths[0].fill}
                  />
                </GeneralIcon>
              )}
            </FilterItem>
          ))}
        </FilterMenu>
      )}
    </Container>
  );
};

const Container = styled.div`
  width: 100%;
  position: relative;
  display: grid;
  grid-template-columns: 1fr;
`;

const FilterButton = styled.div<{ isSelected: boolean }>`
  grid-column: 1 / -1;
  border-radius: 2px;
  min-height: 32px;
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  text-align: left;
  font-size: 14px;
  box-sizing: border-box;
  background-color: ${(props: any) =>
    props.isSelected
      ? props.theme.ColorsBackgroundHover
      : props.theme.ColorsCardColorJobCardPrimary};
  color: ${(props) => (props.isSelected ? '#118ab2' : props.theme.ColorsTypographyPrimary)};
  cursor: pointer;
  transition: background-color 0.2s ease, border 0.2s ease;
  white-space: normal;
  word-wrap: break-word;
  border: 1.5px solid ${(props) => (props.isSelected ? '#118ab2' : 'transparent')};

  &:hover {
    background-color: ${(props: any) => props.theme.ColorsBackgroundHover};
  }
`;

const HttpText = styled.div<{ isSelected?: boolean }>`
  font-weight: ${(props) => props.theme.FontWeightsInter5};
  white-space: normal;
  word-wrap: break-word;
  line-height: 1.2;
  color: ${(props) => (props.isSelected ? '#118ab2' : 'inherit')};
`;

const ArrowsIcon = styled.svg<{ isSelected: boolean }>`
  width: 20px;
  height: 20px;
  margin-left: 4px;
  transition: transform 0.2s ease;
`;

const FilterMenu = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 100%;
  background: rgba(30, 32, 36, 0.98);
  backdrop-filter: blur(8px);
  border: 1.5px solid ${(props) => props.theme.ColorsControllersTertiary};

  margin-top: 6px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.32);
  z-index: 1000;
  max-height: 340px;
  overflow-y: auto;
  color: ${(props) => props.theme.ColorsTypographySecondary};
  padding: 0 0 8px 0;
`;

const FilterItem = styled.div<{ hasSubMenu?: boolean; selected?: boolean }>`
  display: grid;
  grid-template-columns: 1fr ${(props) => (props.hasSubMenu ? 'auto' : '')};
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background 0.18s, border 0.18s, color 0.18s;
  background: transparent;
  border-radius: 6px;
  margin: 2px 8px;
  font-size: 15px;
  font-weight: 500;
  color: ${(props) => (props.selected ? '#118ab2' : props.theme.ColorsTypographyPrimary)};
  border: 2px solid ${(props) => (props.selected ? '#118ab2' : 'transparent')};

  &:hover {
    background: rgba(255,255,255,0.08);
    color: ${(props) => (props.selected ? '#118ab2' : props.theme.ColorsTypographyPrimary)};
    border-color: ${(props) => (props.selected ? '#118ab2' : 'transparent')};
  }
`;

const BackButton = styled(FilterItem)`
  border-bottom: 1px solid ${(props) => props.theme.ColorsControllersTertiary};
  color: ${(props) => props.theme.ColorsTypographySecondary};
  padding: 8px;
  grid-template-columns: auto 1fr;
  gap: 8px;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
`;

const GeneralIcon = styled.svg`
  width: 20px;
  height: 20px;
`;

const SearchContainer = styled.div`
  position: relative;
  padding: 8px;
  border-bottom: 1px solid ${(props) => props.theme.ColorsControllersTertiary};
  background: rgba(46, 50, 54, 0.93);
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 8px 36px 8px 36px;
  border: 1px solid ${(props) => props.theme.ColorsControllersTertiary};
  border-radius: 8px;
  background-color: ${(props) => props.theme.ColorsCardColorJobCardPrimary};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-size: 15px;
  box-sizing: border-box;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: ${(props) => props.theme.ColorsStrokesFocus};
  }

  &::placeholder {
    color: ${(props) => props.theme.ColorsTypographyTertiary};
    opacity: 1;
  }
`;

const SearchIcon = styled.svg`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  pointer-events: none;
  fill: ${(props) => props.theme.ColorsTypographyTertiary};
`;

export default Filter;
