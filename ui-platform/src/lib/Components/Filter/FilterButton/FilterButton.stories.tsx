import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import TypeTextIconStateHover from './FilterButton';

const meta: Meta<typeof TypeTextIconStateHover> = {
  component: TypeTextIconStateHover,
  title: 'Components/FilterButton',
  argTypes: {
    text: { control: 'text', defaultValue: 'Default Text' },
    iconPosition: {
      control: 'radio',
      options: ['left', 'right'],
      defaultValue: 'right',
    },
    isSelected: {
      control: 'boolean',
      defaultValue: false,
    },
  },
};

export default meta;

type Story = StoryObj<typeof TypeTextIconStateHover>;

export const Overview: Story = (args) => {
  const [isSelected, setIsSelected] = React.useState<boolean>(args.isSelected);

  const handleIconClick = (selected: boolean) => {
    setIsSelected(selected);
  };

  return (
    <TypeTextIconStateHover
      text={args.text}
      iconPosition={args.iconPosition}
      isSelected={args.isSelected}
      onIconClick={handleIconClick}
      //   onIconClick={() => {
      //     // Handle icon click to update isSelected in Storybook
      //     args.onIconClick && args.onIconClick(!args.isSelected);
      //   }}
    />
  );
};

Overview.args = {
  text: 'Text',
  iconPosition: 'right',
  isSelected: false,
};
