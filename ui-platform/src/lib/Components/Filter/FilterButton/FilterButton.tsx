import React, { FunctionComponent, useState } from 'react';
import styled, { css } from 'styled-components';
import { svgs } from '../../Icons/svgs';

export type TypeTextIconStateHoverProps = {
  text: string;
  iconPosition: 'left' | 'right';
  isSelected?: boolean;
  onIconClick?: (selected: boolean) => void;
};

const commonStyles = css`
  width: 100%;
  position: relative;
  border-radius: 2px;
  background-color: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
  height: 32px;
  overflow: hidden;
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  padding: 8px;
  box-sizing: border-box;
  text-align: center;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter}, sans-serif;
`;

export const TypeTextIconStatehover = styled.div<{
  iconPosition: 'left' | 'right';
}>`
  ${commonStyles}
  justify-content: space-between;
`;

const HttpText = styled.div`
  position: relative;
  font-weight: ${(props) => props.theme.FontWeightsInter5};
`;

const ArrowsIcon = styled.svg<{ isSelected: boolean }>`
  width: 24px;
  height: 24px;
  position: relative;
  cursor: pointer;
  justify-self: end;
`;

/**
 * A component that renders a button with a text label and an icon. The button
 * state is determined by the isSelected prop. When the button is clicked, the
 * state is toggled and the onIconClick callback function is called with the
 * new state.
 *
 * @param text - The text to be rendered.
 * @param iconPosition - The position of the icon, either 'left' or 'right'.
 * @param isSelected - The initial state of the button.
 * @param onIconClick - The callback function to be called when the button is clicked.
 */
const TypeTextIconStateHover: FunctionComponent<
  TypeTextIconStateHoverProps
> = ({ text, iconPosition, isSelected = false, onIconClick }) => {       
          const [internalSelected, setInternalSelected] = useState<boolean>(isSelected);
          
  /**
   * Handles the icon click event by toggling the internalSelected state
   * and calling the onIconClick callback function if provided.
   */
          const handleIconClick = () => {
              const newSelected = !internalSelected;
              setInternalSelected(newSelected);
              if (onIconClick) {
                  onIconClick(newSelected);
                }
            };
            
            const iconType = isSelected ? 'chevron-up' : 'chevron-down';
            const icon = svgs[iconType];
  
  return (
    <TypeTextIconStatehover iconPosition={iconPosition}>
      {iconPosition === 'left' && (
        <ArrowsIcon
          isSelected={isSelected}
          viewBox={icon.viewBox}
          onClick={handleIconClick}
        >
          <path
            d={icon.paths[0].d}
            stroke={isSelected ? '#118AB2' : icon.paths[0].stroke}
            strokeLinecap={icon.paths[0].strokeLinecap}
            strokeLinejoin={icon.paths[0].strokeLinejoin}
            fill={icon.paths[0].fill}
          />
        </ArrowsIcon>
      )}
      <HttpText>{text}</HttpText>
      {iconPosition === 'right' && (
        <ArrowsIcon
          isSelected={isSelected}
          viewBox={icon.viewBox}
          onClick={handleIconClick}
        >
          <path
            d={icon.paths[0].d}
            stroke={isSelected ? '#118AB2' : icon.paths[0].stroke}
            strokeLinecap={icon.paths[0].strokeLinecap}
            strokeLinejoin={icon.paths[0].strokeLinejoin}
            fill={icon.paths[0].fill}
          />
        </ArrowsIcon>
      )}
    </TypeTextIconStatehover>
  );
};

export default TypeTextIconStateHover;
