import React, { useState, useRef, useEffect } from 'react';
import ViewSwitcherIcon from './ViewSwitcherIcon';

const dropdownStyles: React.CSSProperties = {
  position: 'absolute',
  top: '40px',
  left: 0,
  background: '#23272b',
  color: '#cfd2d6',
  border: '1px solid #444',
  borderRadius: '4px',
  minWidth: '150px',
  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
  zIndex: 1000,
  padding: '8px 0',
};

const optionStyles: React.CSSProperties = {
  padding: '10px 16px',
  cursor: 'pointer',
  fontSize: '15px',
  background: 'transparent',
  border: 'none',
  textAlign: 'left',
  width: '100%',
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  color: '#ffffff',
};

const optionHoverStyles: React.CSSProperties = {
  ...optionStyles,
  background: '#31363b',
};


export type ViewType = 'detailed' | 'list';

export interface ViewSwitcherProps {
  initialView?: ViewType;
  onViewChange?: (view: ViewType) => void;
  buttonStyle?: React.CSSProperties;
}

export const ViewSwitcher: React.FC<ViewSwitcherProps> = ({ initialView = 'detailed', onViewChange, buttonStyle }) => {
  const [open, setOpen] = useState(false);
  const [hovered, setHovered] = useState<number | null>(null);
  const [view, setView] = useState<ViewType>(initialView);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }
    if (open) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [open]);

  const handleSelect = (selectedView: ViewType) => {
    setView(selectedView);
    setOpen(false);
    if (onViewChange) onViewChange(selectedView);
  };

  return (
    <div style={{ position: 'relative', display: 'inline-block' }} ref={ref}>
      <button
        style={{
          background: 'none',
          border: 'none',
          borderRadius: '50%',
          width: 36,
          height: 36,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          outline: 'none',
          ...buttonStyle,
        }}
        onClick={() => setOpen((v) => !v)}
        aria-haspopup="listbox"
        aria-expanded={open}
        title={view === 'detailed' ? 'Detailed Grid View' : 'List View'}
    >
        <ViewSwitcherIcon />
      </button>
      {open && (
        <div style={dropdownStyles}>
          <button
            style={hovered === 0 ? optionHoverStyles : optionStyles}
            onMouseEnter={() => setHovered(0)}
            onMouseLeave={() => setHovered(null)}
            tabIndex={0}
            onClick={() => handleSelect('detailed')}
          >
            Detailed Grid View
          </button>
          <button
            style={hovered === 1 ? optionHoverStyles : optionStyles}
            onMouseEnter={() => setHovered(1)}
            onMouseLeave={() => setHovered(null)}
            tabIndex={0}
            onClick={() => handleSelect('list')}
          >
            List View
          </button>
        </div>
      )}
    </div>
  );
};

export default ViewSwitcher;

export interface ListViewJob {
  id: number;
  skill: string;
  stateTextDislplay: string;
  claim: {
    applicant: {
      first_name: string;
      surname: string;
    };
  };
}

interface ListViewProps {
  jobs: ListViewJob[];
}

export const ListView: React.FC<ListViewProps> = ({ jobs }) => (
  <div style={{ width: '100%', padding: 0, margin: 0 }}>
    <ul style={{ color: '#fff', listStyle: 'none', padding: 0, margin: 0 }}>
      {jobs.map((job) => (
        <li key={job.id} style={{
          background: '#444',
          borderRadius: 4,
          boxShadow: '0 2px 8px rgba(0,0,0,0.10)',
          marginBottom: 4,
          display: 'flex',
          alignItems: 'center',
          minHeight: 40,
          padding: '16px 24px',
        }}>
          <div style={{ flex: 1, textAlign: 'left', fontWeight: 100, fontSize: 14}}>{job.claim.applicant.first_name} {job.claim.applicant.surname}</div>
          <div style={{ flex: 1, textAlign: 'center', fontWeight: 100, fontSize: 14 }}>{job.skill}</div>
          <div style={{ flex: 1, textAlign: 'right', fontWeight: 100, fontSize: 14 }}>{job.stateTextDislplay}</div>
        </li>
      ))}
    </ul>
  </div>
);
