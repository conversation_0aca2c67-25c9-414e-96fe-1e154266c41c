import { ComponentPropsWithRef, ComponentType } from 'react';
import styled from 'styled-components';
export type ListProps = ComponentPropsWithRef<'ul'>;

/**
 * Styled component for rendering an unordered list.
 *
 * @component
 * @param {ListProps} props - The properties passed to the component are the same as HTML li element attributes.
 * @param {ReactNode} props.children - The children to be rendered inside the list.
 * @param {...ComponentPropsWithRef<'ul'>} props.rest - Additional properties to be spread on the component from HTML li element attributes.
 * @return {JSX.Element} - The rendered unordered list.
 */
export const List = styled(({ children, ...rest }: ListProps) => (
  // Render an unordered list with the provided children.
  <ul data-testid="list" {...rest}>
    {children}
  </ul>
))`
  all: unset; // Reset all CSS properties to their default values.
`;

export type ItemOptionsType<T extends object> = Partial<Omit<T, 'children'>> & {
  listId?: string;
};

/**
 * Generates an array of JSX elements by mapping over an array of items and rendering each item as a component.
 *
 * @template T - The type of the items in the array.
 * @param {ComponentType<T>} Component - The component to render for each item.
 * @return {((items: T | T[], listItemOptions?: ItemOptionsType<T>) => JSX.Element[])} - A function that takes an array of items and optional list item options, and returns an array of JSX elements.
 */
export const withItems =
  <T extends object>(
    Component: ComponentType<T>
  ): ((
    items: T | T[],
    listItemOptions?: ItemOptionsType<T>
  ) => JSX.Element[]) =>
  (items: T | T[], listItemOptions?: ItemOptionsType<T>): JSX.Element[] => {
    const itemsList = Array.isArray(items) ? [...items] : [items];
    const itemTemplate = (item: T, idx: number) => {
      return {
        ...listItemOptions,
        ...item,
        key: idx,
      } satisfies T & { listItem?: string };
    };

    return itemsList.map((item: T, idx: number) => {
      const { listId, key, ...updatedItem } = itemTemplate(item, idx);
      return (
        <Component
          data-testid={`list-item-#${idx}`}
          id={listId ? `list-${listId}_list-item-${idx}` : `list-item-${idx}`}
          key={key}
          {...(updatedItem as T)}
        />
      );
    });
  };
