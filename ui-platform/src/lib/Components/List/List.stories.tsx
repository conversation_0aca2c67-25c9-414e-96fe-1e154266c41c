import type { Meta, StoryObj } from '@storybook/react';
import styled from 'styled-components';
import { List, ListItem, withItems } from '../';
import { MenuListItem, MenuListItemProps } from '../Menu';

const meta: Meta<typeof List> = {
  component: List,
  title: 'Components/Lists/List',
};
export default meta;
type Story = StoryObj<typeof List>;

const items = [
  <ListItem key={'1'} children={'List Item 1'} />,
  <ListItem key={'2'} children={'List Item 2'} />,
  <ListItem key={'3'} children={'List Item 3'} />,
  <ListItem key={'4'} children={'List Item 4'} />,
  <ListItem key={'5'} children={'List Item 5'} />,
];

export const Overview: Story = {
  args: {
    children: items,
  },
};

const MenuItems = [
  {
    label: 'Switch Accounts',
    menuItems: {
      name: 'Profile Settings',
      icon: 'bell-02',
      onClick: () => prompt('Enter account you want to switch to'),
    },
  },
  {
    label: 'Logout',
    menuItems: {
      name: 'Access',
      icon: 'bell-02',
      onClick: () => alert('You have been logged out'),
    },
  },
  {
    label: 'No Action',
    menuItems: {
      name: 'Disabled Action',
      icon: 'x-xircle',
      disabled: true,
    },
  },
] as MenuListItemProps[];

const MenuList = styled(List)`
  box-shadow: 0px 0px 23px 5px rgba(7, 14, 17, 0.25);
  /* background-color: rgba(40, 48, 51, 0.84); */
  backdrop-filter: blur(9px);
  border-radius: 4px;
  padding: 8px;
`;

const MenuListItems = withItems(MenuListItem);

export const Menu: Story = {
  render: (args) => <MenuList {...args}>{MenuListItems(MenuItems)}</MenuList>,
  args: {},
};

export const MenuWithPresets: Story = {
  render: (args) => (
    <MenuList {...args}>
      {MenuListItems(MenuItems, {
        listId: 'menu-options',
        role: 'menuitem',
        style: {
          background: 'black',
          borderRadius: '15px',
          padding: '1rem',
          margin: '.5rem 0',
          width: '400px',
        },
      })}
    </MenuList>
  ),
  args: {},
};
