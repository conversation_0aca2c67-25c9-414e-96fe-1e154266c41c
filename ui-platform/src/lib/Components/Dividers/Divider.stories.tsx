import type { Meta, StoryObj } from '@storybook/react';
import { Divider } from './Divider';

const meta: Meta<typeof Divider> = {
  component: Divider,
  title: 'Components/Divider',
};
export default meta;
type Story = StoryObj<typeof Divider>;

export const DefaultDividerStory: Story = {
  argTypes: {
    size: {
      control: 'select',
      options: ['small', 'medium', 'large'],
    },
    background: {
      control: 'select',
      options: ['primary', 'secondary', 'error', 'success'],
    },
    type: {
      control: 'select',
      options: ['default', 'tabActive', 'tabSmll'],
    },
    state: {
      control: 'select',
      options: ['default', 'grey'],
    },
  },
  args: {
    size: 'medium',
    background: 'primary',
    type: 'tabActive',
    state: 'grey',
  },
};
