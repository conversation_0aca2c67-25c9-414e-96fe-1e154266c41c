import React, { FunctionComponent } from 'react';
import styled from 'styled-components';
import { Divider, DividerProps } from '../Divider';

export interface DividerWithTextProps extends DividerProps {
  text: string;
}

/**
 * A `DividerWithText` is a divider with a text in the middle.
 *
 * It takes in a `text` prop, a `className` prop and any other props that
 * can be passed to the `Divider` component.
 *
 * @param {string} text - The text that will be rendered in the middle of the divider.
 * @param {string} className - The class name to be applied to the outermost element.
 * @param {DividerProps} dividerProps - Any other props that can be passed to the `Divider` component.
 * @return {JSX.Element} The rendered divider with text.
 */
const DividerWithText: FunctionComponent<DividerWithTextProps> = ({
  text,
  className,
  ...dividerProps
}: DividerWithTextProps) => {
  console.log('dividerProps', dividerProps);
  return (
    <DividerContainer {...{ className }}>
      <StyledDivider {...dividerProps} />
      <TextContainer>{text}</TextContainer>
      <StyledDivider {...dividerProps} />
    </DividerContainer>
  );
};

export default DividerWithText;

const DividerContainer = styled.div`
  position: relative;
  border-radius: 4px;
  width: 100%;
  overflow: hidden;
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  justify-content: center;
  padding: 0px 8px;
  box-sizing: border-box;
  gap: 9px;
  max-width: 337px;
  text-align: center;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter}, sans-serif;
`;

const StyledDivider = styled(Divider)`
  max-height: 100%;
`;

const TextContainer = styled.div`
  position: relative;
  font-weight: ${(props) => props.theme.FontWeightsInter6};
  color: inherit;
  font-size: inherit;
`;
