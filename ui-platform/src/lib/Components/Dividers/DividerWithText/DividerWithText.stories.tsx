import { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import DividerWithText, { DividerWithTextProps } from './DividerWithText';

const meta: Meta<typeof DividerWithText> = {
  title: 'Components/DividerWithText',
  component: DividerWithText,
  argTypes: {
    text: { control: 'text' },
    // dividerProps: { control: 'object' },
    size: { control: 'select', options: ['small', 'medium', 'large'] },
    background: { control: 'select', options: ['primary', 'secondary'] },
    type: { control: 'select', options: ['default', 'primary'] },
    height: { control: 'select', options: ['default', 'small'] },
  },
};
export default meta;
type Story = StoryObj<DividerWithTextProps>;

export const Overview: Story = {
  args: {
    text: 'or',
    size: 'medium',
    background: 'primary',
    type: 'default',
    height: 'default',
  },
};
