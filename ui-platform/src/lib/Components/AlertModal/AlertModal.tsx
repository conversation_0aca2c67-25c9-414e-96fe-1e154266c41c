import React, { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import styled from 'styled-components';
import { ActionConfig } from '../../Engine';
import { ViewRenderer } from '../../Engine/components/ViewRenderer';
import { ModalConfig } from '../../Engine/models/modal-config';
import { AlertPopup } from '../AlertPopup/Popup';
import { FormButton } from '../Buttons/FormButton/FormButton';
import { Heading } from '../Heading/Heading';
import { Icon } from '../Icons';
import { ScrollableContent as Content } from '../Scrollbar/Scrollbar';

/**
 * Styled component for the modal mask that overlays the screen and the scrollable content area.
 * Styled component for the modal content container, the top-right button and the heading within the modal.
 *
 * @component
 *
 * AlertModal component displays a modal with optional form and a heading.
 * The modal can display form content when passed in the props.
 *
 * @param {AlertModalProps} props - Props for AlertModal.
 * @returns JSX.Element
 */
const AlertMask = styled.div`
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.45);
  display: grid;
  align-items: center;
  justify-content: center;
  gap: ${(props) => props.theme.GapXxl};
  z-index: 10;
  color: ${(props) => props.theme.ColorsIconColorPrimary};
`;

const Container = styled(AlertPopup)`
  width: auto;
  height: auto;
  min-width: 336px;
  min-height: 234px;
  display: grid;
  justify-items: center;
  grid-auto-flow: row;
  gap: ${(props) => props.theme.GapLg};
  padding: 4rem;
  position: relative;
  overflow: auto;
  z-index: 20;

  > div:not(:first-child) {
    width: 100%;
  }
`;

const ModalHeading = styled(Heading)`
  margin: unset;
`;

const TopRightButtonWrapper = styled.div`
  position: absolute;
  top: 15px;
  right: 15px;
`;

const ScrollableContent = styled(Content)`
  // max-height: 800px;
  overflow-y: auto;

  // & > .simplebar-track.simplebar-vertical {
  //   width: 4px;
  //   height: 90%;
  // }

  // & > .simplebar-track.simplebar-horizontal {
  //   height: 4px;
  //   width: 90%;
  // }
`;

export interface AlertModalProps extends ModalConfig {
  onClose: () => void;
  setModalState?: any;
  formData?: (data: Record<string, any>) => void;
  componentMap: any;
  submit: any;
  fetcher: any;
  callClientAction: (config: ActionConfig) => void;
  showCloseBtn?: boolean;
}

/**
 * A modal window that displays an alert message.
 *
 * @param {boolean} display
 *   Whether the modal window should be displayed or not.
 *   Defaults to false.
 *
 * @param {'warning' | 'error' | 'success'} type
 *   The type of the alert message.
 *   Defaults to 'warning'.
 *
 * @param {(data: Record<string, any>) => void} formData
 *   A function that is called when the form is submitted.
 *   The function is given the form data as an argument.
 *   If not provided, the form is not rendered.
 *
 * @param {string} heading
 *   The heading of the alert message.
 *   If not provided, the heading is not rendered.
 *
 * @param {1 | 2 | 3 | 4 | 5 | 6} headingLevel
 *   The level of the heading.
 *   Defaults to 1.
 *
 * @param {'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'span'} headingType
 *   The type of the heading element.
 *   Defaults to 'h1'.
 *
 * @param {(state: boolean) => void} setModalState
 *   A function that is called when the modal window is closed.
 *   The function is given the display state as an argument.
 *   If not provided, the function is not called.
 *
 * @param {() => void} onClose
 *   A function that is called when the modal window is closed.
 *   If not provided, the function is not called.
 *
 * @param {...ViewRendererProps} props
 *   Additional props that are passed to the ViewRenderer component.
 */
export const AlertModal: React.FC<AlertModalProps> = ({
  display = false,
  type = 'warning',
  formData,
  heading,
  headingLevel,
  headingType,
  setModalState,
  onClose,
  componentMap,
  submit,
  fetcher,
  callClientAction,
  showCloseBtn = true,
  ...props
}) => {
  // TODO: THE FUNCTIONALITY BELOW SHOULD BE ON AN SCREEN LEVEL
  const methods = useForm();

  return (
    display && (
      <AlertMask>
        <Container type={type}>
          {showCloseBtn && (
            <TopRightButtonWrapper>
              <FormButton onClick={onClose} width="35px" height="35px">
                <Icon type="close" size={20} />
              </FormButton>
            </TopRightButtonWrapper>
          )}

          {heading && (
            <ModalHeading level={headingLevel} type={headingType}>
              {heading}
            </ModalHeading>
          )}
          {/* <ScrollableContent> */}
          {formData && (
            <FormProvider {...methods}>
              <form onSubmit={methods.handleSubmit(formData)}>
                <ViewRenderer
                  callClientAction={callClientAction}
                  viewConfig={props}
                  clientDataObject={{}}
                  componentMap={componentMap}
                  fetcher={fetcher}
                  submit={submit}
                />
              </form>
            </FormProvider>
          )}
          {!formData && (
            <ViewRenderer
              callClientAction={callClientAction}
              viewConfig={props}
              clientDataObject={{}}
              componentMap={componentMap}
              fetcher={fetcher}
              submit={submit}
            />
          )}
          {/* </ScrollableContent> */}
        </Container>
      </AlertMask>
    )
  );
};
