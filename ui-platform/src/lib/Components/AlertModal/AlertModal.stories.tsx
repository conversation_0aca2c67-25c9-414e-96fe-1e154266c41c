import type { <PERSON>a, StoryObj } from '@storybook/react';
import { ButtonRow, FormBuilder, Instructions } from '../../Fragments';
import { AlertModal } from './AlertModal';

const components = { Instructions, ButtonRow, FormBuilder };

const meta: Meta<typeof AlertModal> = {
  component: AlertModal,
  title: 'Components/AlertModal/AlertModal',
};

export default meta;
type Story = StoryObj<typeof AlertModal>;

export const Overview: Story = {
  args: {
    display: true,
    type: 'warning',
    heading: 'Error',
    headingType: 'page-heading',
    layout: {},
    onEnter: [],
    onLeave: [],
    fragments: [
      { component: 'Instructions', layout: {}, props: { message: 'Message' } },
      {
        component: 'ButtonRow',
        layout: { marginTop: '80px' },
        props: {
          buttons: [
            {
              btnValue: 'decision 1',
              onClick: () => {
                console.log('Clicked decision 1');
              },
            },
            {
              btnValue: 'decision 2',
              onClick: () => {
                console.log('Clicked decision 2');
              },
            },
          ],
        },
      },
    ],
    componentMap: components,
    onClose: () => console.log('closing...'),
    navs: [],
    fetcher: { state: 'idle', data: null },
    submit: () => {},
  },
};

export const ChangePassword: Story = {
  args: {
    display: true,
    type: 'warning',
    heading: 'Change Password',
    headingType: 'page-heading',
    layout: {},
    onEnter: [],
    onLeave: [],
    fragments: [
      {
        component: 'FormBuilder',
        layout: { width: '55%', margin: '0 auto' },
        props: {
          config: {
            style: {},
            controls: [
              {
                type: 'plain-text',
                name: 'old_password',
                label: 'Text',
                css: { wrapper: { width: '100%' } },
              },
              {
                type: 'plain-text',
                name: 'new_password',
                label: 'Text',
                css: { wrapper: { width: '100%' } },
                instructions:
                  'Password minimum characters 12, one uppercase, one lowercase, one special character, one number',
              },
              {
                type: 'plain-text',
                name: 'confirm_password',
                label: 'Text',
                css: { wrapper: { width: '100%' } },
              },
            ],
          },
        },
      },
      {
        component: 'ButtonRow',
        layout: {},
        props: {
          buttons: [
            {
              btnValue: 'decision 1',
              onClick: () => {
                console.log('Clicked decision 1');
              },
            },
            {
              btnValue: 'decision 2',
              onClick: () => {
                console.log('Clicked decision 2');
              },
            },
          ],
        },
      },
    ],
    componentMap: components,
    onClose: () => console.log('closing...'),
    navs: [],
    fetcher: { state: 'idle', data: null },
    submit: () => {},
  },
};

export const AddNewReminder: Story = {
  args: {
    display: true,
    type: 'warning',
    heading: 'Add New Reminder',
    headingType: 'page-heading',
    layout: { padding: '2rem' },
    onEnter: [],
    onLeave: [],
    fragments: [
      {
        component: 'FormBuilder',
        layout: { alignSelf: 'center', height: '450px' },
        props: {
          config: {
            style: {
              display: 'grid',
              gridTemplateColumns: 'repeat(2, 1fr)',
              gridTemplateRows: 'repeat(4, 1fr)',
              rowGap: '8px',
              columnGap: '15px',
              height: '75%',
            },
            controls: [
              {
                type: 'plain-text',
                name: 'Text',
                label: 'Text',
                css: { wrapper: { gridColumn: 1, gridRow: 1 } },
              },
              {
                type: 'plain-text',
                name: 'Channel',
                label: 'Channel',
                css: { wrapper: { gridColumn: 2, gridRow: 1 } },
              },
              {
                type: 'plain-text',
                name: 'date',
                label: 'Date',
                css: { wrapper: { gridColumn: 1, gridRow: 2 } },
              },
              {
                type: 'plain-text',
                name: 'time',
                label: 'Time',
                css: { wrapper: { gridColumn: 2, gridRow: 2 } },
              },
              {
                type: 'plain-text',
                name: 'notes',
                label: 'Reminder message',
                css: {
                  wrapper: {
                    gridColumn: 1,
                    gridColumnEnd: 3,
                    gridRow: 3,
                    gridRowEnd: 4,
                  },
                },
              },
              {
                type: 'plain-text',
                name: 'link_to_claim',
                label: 'Link to claim',
                placeholder: 'Type to search',
                css: {
                  wrapper: {
                    gridColumn: 1,
                    gridColumnEnd: 3,
                    gridRow: 4,
                  },
                },
              },
            ],
          },
        },
      },
      {
        component: 'ButtonRow',
        layout: {},
        props: {
          buttons: [
            {
              btnValue: 'decision 1',
              onClick: () => {
                console.log('Clicked decision 1');
              },
            },
            {
              btnValue: 'decision 2',
              onClick: () => {
                console.log('Clicked decision 2');
              },
            },
          ],
        },
      },
    ],
    componentMap: components,
    onClose: () => console.log('closing...'),
    navs: [],
    fetcher: { state: 'idle', data: null },
    submit: () => {},
  },
};

export const ManageStaffLeave: Story = {
  args: {
    display: true,
    type: 'warning',
    heading: 'Manage Staff Leave',
    headingType: 'page-heading',
    layout: {
      display: 'grid',
      justifyItems: 'center',
    },
    onEnter: [],
    onLeave: [],
    fragments: [
      {
        component: 'FormBuilder',
        layout: { alignSelf: 'center' },
        props: {
          config: {
            style: {},
            controls: [
              {
                type: 'select',
                name: 'Text',
                label: 'Text',
                options: {
                  source: 'literal',
                  data: [
                    { value: 'option 1', label: 'Option 1' },
                    { value: 'option 2', label: 'Option 2' },
                    { value: 'option 3', label: 'Option 3' },
                  ],
                },
                css: { wrapper: { width: '100%' } },
              },
            ],
          },
        },
      },
      {
        component: 'Instructions',
        layout: { fontWeight: '600' },
        props: {
          message: 'Select the date range of the leave',
        },
      },
      {
        component: 'FormBuilder',
        layout: { alignSelf: 'center' },
        props: {
          config: {
            style: {},
            controls: [
              {
                type: 'select',
                name: 'Text',
                label: 'Text',
                options: {
                  source: 'literal',
                  data: [
                    { value: 'option one', label: 'Option one' },
                    { value: 'option two', label: 'Option two' },
                    { value: 'option three', label: 'Option three' },
                  ],
                },
                css: { wrapper: { width: '100%' } },
              },
            ],
          },
        },
      },
      {
        component: 'ButtonRow',
        layout: {},
        props: {
          buttons: [
            {
              btnValue: 'decision 1',
              onClick: () => {
                console.log('Clicked decision 1');
              },
            },
            {
              btnValue: 'decision 2',
              onClick: () => {
                console.log('Clicked decision 2');
              },
            },
          ],
        },
      },
    ],
    componentMap: components,
    onClose: () => console.log('closing...'),
    navs: [],
    fetcher: { state: 'idle', data: null },
    submit: () => {},
  },
};
