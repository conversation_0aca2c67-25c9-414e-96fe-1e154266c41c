import { CSSProperties } from 'react';

/**
 * BetterSureLogo component
 * The logo consists of different colors
 * and shapes, with hex colors used for styling the elements. The SVG has a
 * predefined width and height, and it is rendered as an inline SVG.
 *
 * @returns {ReactElement} The BetterSure logo SVG
 */
export const BetterSureLogo = ({
  width,
  height,
}: {
  width: CSSProperties['width'];
  height: CSSProperties['height'];
}) => {
  return (
    <svg
      width={width || '123.06'}
      height={(width && height ? height : 'auto') || '59.29'}
      viewBox="0 0 194 90"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M163.388 34.963C163.23 34.963 163.072 34.963 162.946 34.963C162.946 35.1526 162.946 35.3105 162.946 35.5C162.946 44.0282 156.028 50.9456 147.5 50.9456C138.972 50.9456 132.054 44.0282 132.054 35.5C132.054 26.9718 138.972 20.0544 147.5 20.0544C153.596 20.0544 158.871 23.5921 161.398 28.7406C162.03 28.8669 162.693 28.9301 163.388 28.9301C165.63 28.9301 167.652 28.0141 169.263 26.6243C165.757 18.0329 157.323 12 147.5 12C134.518 12 124 22.5181 124 35.5C124 48.4819 134.518 59 147.5 59C160.482 59 171 48.4819 171 35.5C171 34.6788 170.968 33.8575 170.874 33.0363C168.631 34.2366 166.104 34.963 163.388 34.963Z"
        fill="#1C2F5C"
      />
      <path
        d="M163.5 4C158.537 4 154.123 6.31776 151.287 9.95535C152.963 10.2129 154.606 10.6314 156.153 11.2108C158.119 9.47248 160.696 8.41018 163.5 8.41018C169.623 8.41018 174.585 13.3676 174.585 19.4839C174.585 25.6002 169.623 30.5576 163.5 30.5576C157.764 30.5576 153.059 26.2118 152.479 20.6428C151.061 20.1599 149.579 19.8702 148 19.8058C148.161 28.2399 155.057 35 163.5 35C172.072 35 179 28.0467 179 19.4839C179 10.9211 172.072 4 163.5 4Z"
        fill="#F58220"
      />
      <path
        d="M24.9382 73.8368C27.9962 71.3783 27.4168 68.4022 27.4168 68.4022C26.9339 63.2911 21.0754 63 21.0754 63L11 63.0324V86H21.848C27.8674 84.9648 27.9962 79.789 27.9962 79.789C28.125 75.4219 24.9382 73.8368 24.9382 73.8368ZM15.925 67.2053H20.2063C20.2063 67.2053 21.2363 67.1406 22.0089 67.9817C22.0089 67.9817 22.5561 68.661 22.6205 69.5668C22.6205 69.5668 22.8458 71.7665 20.3672 72.1547H15.925V67.2053ZM20.335 81.3741H15.925V76.263H20.2063C20.2063 76.263 21.6226 76.2307 22.3952 77.0717C22.3952 77.0717 23.0068 77.6217 23.0068 78.8186C23.0068 78.8186 23.1034 81.3741 20.335 81.3741Z"
        fill="#1C2F5C"
      />
      <path
        d="M39.742 68C34.3557 68 30 72.244 30 77.5C30 82.756 34.3557 87 39.742 87C42.8342 87 45.4942 85.7268 47.2896 83.5395L43.7985 80.634C43.7985 80.634 42.3687 82.854 38.8111 82.6254C38.8111 82.6254 35.6856 82.5601 35.1204 78.8385H48.9853C49.2846 72.2113 44.9622 68 39.742 68ZM35.1869 75.5412C35.7854 73.6151 37.3148 72.1787 39.5758 72.2113C41.9032 72.2766 43.4992 73.5172 44.0644 75.5412H35.1869Z"
        fill="#1C2F5C"
      />
      <path
        d="M87.7404 68C82.3549 68 78 72.244 78 77.5C78 82.756 82.3549 87 87.7404 87C90.8321 87 93.4916 85.7268 95.2867 83.5395L91.7961 80.634C91.7961 80.634 90.3667 82.854 86.8096 82.6254C86.8096 82.6254 83.6847 82.5601 83.1195 78.8385H96.9821C97.3146 72.2113 92.9597 68 87.7404 68ZM83.186 75.5412C83.7844 73.6151 85.3136 72.1787 87.5742 72.2113C89.9012 72.2766 91.4969 73.5172 92.0621 75.5412H83.186Z"
        fill="#1C2F5C"
      />
      <path
        d="M50 71.3016L56.4824 64H57.3568V68.5714H62V72.4127H57.4774V79.365C57.4774 79.365 57.4171 81.8412 59.7688 81.873H62V85.9999H58.2613C58.2613 85.9999 54.1608 86.0634 53.196 81.7777C53.196 81.7777 53.0151 80.7619 53.0151 79.9047V72.4444H50.0302V71.3016H50Z"
        fill="#1C2F5C"
      />
      <path
        d="M64 71.3016L70.4824 64H71.3568V68.5714H76V72.4127H71.4774V79.365C71.4774 79.365 71.4171 81.8412 73.7688 81.873H76V85.9999H72.2613C72.2613 85.9999 68.1608 86.0634 67.196 81.7777C67.196 81.7777 67.0151 80.7619 67.0151 79.9047V72.4444H64.0301V71.3016H64Z"
        fill="#1C2F5C"
      />
      <path
        d="M100 86V68H102.866L103.659 70.2336C103.659 70.2336 105.061 68 107.652 68H110V72.5H107.622C107.622 72.5 104.482 72.3029 104.482 76.4416V86H100Z"
        fill="#1C2F5C"
      />
      <path
        d="M127.353 69.9131H130.287C130.287 69.9131 130.419 65.3211 126.298 63.7042C122.144 62.1197 118.353 63.7042 118.353 63.7042C118.353 63.7042 114.793 65.0624 114.463 68.9753C114.134 72.8882 116.837 74.117 116.837 74.117C116.837 74.117 118.386 75.2812 122.177 76.0249C122.177 76.0249 125.441 76.51 126.925 77.8358C126.925 77.8358 129.463 80.2288 126.496 82.9775C126.496 82.9775 124.716 84.5298 121.155 83.98C121.155 83.98 117.166 83.4303 117.068 79.4851H114.002C114.002 79.4851 113.738 85.2412 119.87 86.664C126.001 88.0869 129.298 84.5298 129.298 84.5298C129.298 84.5298 132.133 81.7164 130.485 77.4155C130.485 77.4155 129.562 75.1518 126.265 74.117C126.265 74.117 123.529 73.3732 122.639 73.1792C122.639 73.1792 119.573 72.4678 118.749 71.7563C118.749 71.7563 116.309 70.2041 118.09 67.3908C118.09 67.3908 119.375 65.5798 123.067 65.7415C123.002 65.8385 127.089 65.9356 127.353 69.9131Z"
        fill="#1C2F5C"
      />
      <path
        d="M142.442 86.9667C146.822 86.9667 150.031 84.2667 150 78.4C150 78.3667 150 78.3 150 78.2667V68.0333H147.316C147.316 68.0333 147.316 77.8 147.316 77.8333C147.316 77.9667 147.316 78.0667 147.316 78.2C147.316 81.5 146.298 84.1 142.164 84.1C138.277 84.1 137.629 80.2333 137.598 77.7333C137.598 77.6333 137.598 77.5667 137.598 77.4667V68H135.007V78.4C135.007 78.4 134.513 86.5 142.01 87L142.442 86.9667Z"
        fill="#1C2F5C"
      />
      <path
        d="M156 68.1604V86H158.631V75.5401C158.631 75.5401 158.9 70.6631 163.056 70.6631H165V68C165 68 162.728 68.0321 161.95 68.1604C161.95 68.1604 159.827 68.6096 158.272 70.8235L157.884 68.1283H156.03V68.1604H156Z"
        fill="#1C2F5C"
      />
      <path
        d="M175.312 68C170.171 68 166 72.244 166 77.5C166 82.756 169.783 86.9674 175.312 87C179.257 87 182.167 84.5842 183.557 81.0911L180.647 81.0584C178.416 84.5189 175.053 84.1924 175.053 84.1924C169.395 84.0619 168.845 78.61 168.845 78.61H183.977C184.333 72.5052 180.453 68 175.312 68ZM168.878 75.8677C169.46 72.8643 171.885 70.677 175.021 70.677C178.157 70.677 180.647 72.8969 181.261 75.8677H168.878Z"
        fill="#1C2F5C"
      />
    </svg>
  );
};
