import { CSSProperties } from 'react';

interface Props {
  theme: 'light' | 'dark';
  style?: {
    width: CSSProperties['width'];
    height: CSSProperties['height'];
  };
}

/**
 * SilLogo
 *
 * A reusable component for rendering the MultiChoice logo.
 * The logo consists of different colors
 * and shapes, with hex colors used for styling the elements. The SVG has a
 * predefined width and height, and it is rendered as an inline SVG.
 *
 * @return {ReactElement} The Sil logo.
 */
export const SilLogo = ({ theme = 'dark', style }: Props) => {
  const { width, height } = style!;
  return (
    <svg
      width={width || '208px'}
      height={(width && height ? height : 'auto') || '53px'}
      viewBox="0 0 208 53"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      // xmlns:xlink="http://www.w3.org/1999/xlink"
    >
      <defs>
        <linearGradient
          x1="19.5842026%"
          y1="4.6136695%"
          x2="80.0861375%"
          y2="75.9797616%"
          id="linearGradient-1"
        >
          <stop stop-color="#005CA4" offset="2%"></stop>
          <stop stop-color="#002445" offset="94%"></stop>
        </linearGradient>
        <linearGradient
          x1="17.0163875%"
          y1="6.91399663%"
          x2="80.2789021%"
          y2="70.8461462%"
          id="linearGradient-2"
        >
          <stop stop-color="#7BBBE8" offset="1%"></stop>
          <stop stop-color="#78B7E5" offset="11%"></stop>
          <stop stop-color="#6FAADC" offset="22%"></stop>
          <stop stop-color="#6095CC" offset="32%"></stop>
          <stop stop-color="#4C78B7" offset="43%"></stop>
          <stop stop-color="#4A76B5" offset="44%"></stop>
          <stop stop-color="#3E72B2" offset="48%"></stop>
          <stop stop-color="#1E67AB" offset="56%"></stop>
          <stop stop-color="#005CA4" offset="62%"></stop>
          <stop stop-color="#004176" offset="79%"></stop>
          <stop stop-color="#002445" offset="99%"></stop>
        </linearGradient>
        <linearGradient
          x1="0%"
          y1="49.9631268%"
          x2="100%"
          y2="49.9631268%"
          id="linearGradient-3"
        >
          <stop stop-color="#FFFFFF" offset="0%"></stop>
          <stop stop-color="#FDFDFD" offset="2%"></stop>
          <stop stop-color="#DFDFDF" offset="42%"></stop>
          <stop stop-color="#CDCDCD" offset="76%"></stop>
          <stop stop-color="#C6C6C6" offset="100%"></stop>
        </linearGradient>
        <linearGradient
          x1="-0.0178954903%"
          y1="50.528307%"
          x2="100%"
          y2="50.528307%"
          id="linearGradient-4"
        >
          <stop stop-color="#C6C6C6" offset="0%"></stop>
          <stop stop-color="#CDCDCD" offset="24%"></stop>
          <stop stop-color="#DFDFDF" offset="58%"></stop>
          <stop stop-color="#FDFDFD" offset="98%"></stop>
          <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient
          x1="14679%"
          y1="17588%"
          x2="20249%"
          y2="17588%"
          id="linearGradient-5"
        >
          <stop stop-color="#C6C6C6" offset="0%"></stop>
          <stop stop-color="#CDCDCD" offset="24%"></stop>
          <stop stop-color="#DFDFDF" offset="58%"></stop>
          <stop stop-color="#FDFDFD" offset="98%"></stop>
          <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient
          x1="17104%"
          y1="20884%"
          x2="20340%"
          y2="20884%"
          id="linearGradient-6"
        >
          <stop stop-color="#C6C6C6" offset="0%"></stop>
          <stop stop-color="#CDCDCD" offset="24%"></stop>
          <stop stop-color="#DFDFDF" offset="58%"></stop>
          <stop stop-color="#FDFDFD" offset="98%"></stop>
          <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient
          x1="20122%"
          y1="18609%"
          x2="20370%"
          y2="18609%"
          id="linearGradient-7"
        >
          <stop stop-color="#C6C6C6" offset="0%"></stop>
          <stop stop-color="#CDCDCD" offset="24%"></stop>
          <stop stop-color="#DFDFDF" offset="58%"></stop>
          <stop stop-color="#FDFDFD" offset="98%"></stop>
          <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient
          x1="0%"
          y1="50%"
          x2="100%"
          y2="50%"
          id="linearGradient-8"
        >
          <stop stop-color="#C6C6C6" offset="0%"></stop>
          <stop stop-color="#D8D8D8" offset="9%"></stop>
          <stop stop-color="#EEEEEE" offset="22%"></stop>
          <stop stop-color="#FBFBFB" offset="34%"></stop>
          <stop stop-color="#FFFFFF" offset="46%"></stop>
        </linearGradient>
        <linearGradient
          x1="16060%"
          y1="22479%"
          x2="18441%"
          y2="22479%"
          id="linearGradient-9"
        >
          <stop stop-color="#C6C6C6" offset="0%"></stop>
          <stop stop-color="#CDCDCD" offset="24%"></stop>
          <stop stop-color="#DFDFDF" offset="58%"></stop>
          <stop stop-color="#FDFDFD" offset="98%"></stop>
          <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient
          x1="13444%"
          y1="21359%"
          x2="16532%"
          y2="21359%"
          id="linearGradient-10"
        >
          <stop stop-color="#C6C6C6" offset="0%"></stop>
          <stop stop-color="#CDCDCD" offset="24%"></stop>
          <stop stop-color="#DFDFDF" offset="58%"></stop>
          <stop stop-color="#FDFDFD" offset="98%"></stop>
          <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient
          x1="16815%"
          y1="25211%"
          x2="17747%"
          y2="25211%"
          id="linearGradient-11"
        >
          <stop stop-color="#C6C6C6" offset="0%"></stop>
          <stop stop-color="#CDCDCD" offset="24%"></stop>
          <stop stop-color="#DFDFDF" offset="58%"></stop>
          <stop stop-color="#FDFDFD" offset="98%"></stop>
          <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient
          x1="12560%"
          y1="20550%"
          x2="16947%"
          y2="20550%"
          id="linearGradient-12"
        >
          <stop stop-color="#C6C6C6" offset="0%"></stop>
          <stop stop-color="#CDCDCD" offset="24%"></stop>
          <stop stop-color="#DFDFDF" offset="58%"></stop>
          <stop stop-color="#FDFDFD" offset="98%"></stop>
          <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient
          x1="20109%"
          y1="16813%"
          x2="20109%"
          y2="16301%"
          id="linearGradient-13"
        >
          <stop stop-color="#FFFFFF" offset="0%"></stop>
          <stop stop-color="#FDFDFD" offset="2%"></stop>
          <stop stop-color="#DFDFDF" offset="42%"></stop>
          <stop stop-color="#CDCDCD" offset="76%"></stop>
          <stop stop-color="#C6C6C6" offset="100%"></stop>
        </linearGradient>
        <linearGradient
          x1="13565%"
          y1="16931%"
          x2="14805%"
          y2="16931%"
          id="linearGradient-14"
        >
          <stop stop-color="#C6C6C6" offset="0%"></stop>
          <stop stop-color="#CDCDCD" offset="24%"></stop>
          <stop stop-color="#DFDFDF" offset="58%"></stop>
          <stop stop-color="#FDFDFD" offset="98%"></stop>
          <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient
          x1="20.2370425%"
          y1="3.08283266%"
          x2="79.8856471%"
          y2="75.1403526%"
          id="linearGradient-15"
        >
          <stop stop-color="#FFFFFF" offset="54%"></stop>
          <stop stop-color="#FBFBFB" offset="66%"></stop>
          <stop stop-color="#EEEEEE" offset="78%"></stop>
          <stop stop-color="#D8D8D8" offset="91%"></stop>
          <stop stop-color="#C6C6C6" offset="100%"></stop>
        </linearGradient>
      </defs>
      <g
        id="Symbols"
        stroke="none"
        stroke-width="1"
        fill="none"
        fill-rule="evenodd"
      >
        <g id="StandardBankLogo" transform="translate(0.000000, -1.000000)">
          <g>
            <g
              id="Standard_Bank-Copy"
              transform="translate(53.575758, 0.307692)"
            >
              <path
                d="M90.5925541,31.2522222 C89.9789995,32.0251508 89.0585737,32.487372 88.0758442,32.5160684 C86.6621645,32.5160684 85.7932468,31.3835897 85.7932468,28.6384615 C85.7932468,25.7619658 86.7387013,24.7064957 88.2064069,24.7064957 C89.0796303,24.7339137 89.9190218,25.0526176 90.5925541,25.6124786 L90.5925541,31.2522222 Z M93.7440693,34.8761538 L93.7440693,16.3035043 L90.5925541,16.7564957 L90.5925541,23.5513675 C89.7281385,22.8401709 88.755671,22.0202564 87.0808658,22.0202564 C84.1454545,22.0202564 82.4661472,24.4754701 82.4661472,28.6701709 C82.4661472,32.747094 84.3795671,35.1071795 87.2384416,35.1071795 C88.6244661,35.0626575 89.924048,34.4187675 90.8041558,33.3405128 L91.0157576,34.8444444 L93.7440693,34.8761538 Z M82.0384416,24.9103419 L82.0384416,22.0700855 C79.3641558,22.097265 77.9234632,23.9409402 77.4777489,24.4165812 L77.2436364,22.3328205 L74.5153247,22.3328205 L74.5153247,34.8897436 L77.6668398,34.8897436 L77.6668398,26.6362393 C78.1935931,26.1605983 79.3731602,24.9465812 82.0519481,24.9194017 L82.0384416,24.9103419 Z M68.4329004,31.8864103 C67.8410296,32.4700563 67.0587465,32.8177345 66.231342,32.8648718 C65.3669264,32.8648718 64.5520346,32.4934188 64.5520346,31.1253846 C64.5520346,29.4357265 65.7586147,29.0144444 68.4329004,28.8287179 L68.4329004,31.8864103 Z M71.4763636,34.8942735 L71.4763636,27.0303419 C71.4763636,24.2580342 71.3998268,22.0700855 67.4244156,22.0700855 C65.7542795,22.1274134 64.1102496,22.5040584 62.5800866,23.1799145 L63.1293506,24.9918803 C64.1781198,24.6831996 65.2609142,24.5066935 66.3529004,24.4664103 C68.1897835,24.4664103 68.4238961,25.1277778 68.4238961,26.5773504 L68.4238961,27.2387179 C65.5920346,27.397265 61.4995671,27.3157265 61.4995671,31.2476923 C61.4995671,33.9112821 63.0212987,35.1524786 64.9617316,35.1524786 C66.3374105,35.0917526 67.6328639,34.4840246 68.5634632,33.4628205 L68.8020779,34.8852137 L71.4763636,34.8942735 Z M56.2635498,31.2703419 C55.6499952,32.0432705 54.7295693,32.5054917 53.7468398,32.534188 C52.3331602,32.534188 51.4642424,31.4017094 51.4642424,28.6565812 C51.4642424,25.7800855 52.409697,24.7246154 53.8774026,24.7246154 C54.7506259,24.7520334 55.5900175,25.0707373 56.2635498,25.6305983 L56.2635498,31.2703419 Z M59.4150649,34.8942735 L59.4150649,16.3216239 L56.2635498,16.7746154 L56.2635498,23.5694872 C55.3991342,22.8582906 54.4266667,22.0383761 52.7518615,22.0383761 C49.8164502,22.0383761 48.1371429,24.4935897 48.1371429,28.6882906 C48.1371429,32.7652137 50.0505628,35.1252991 52.9094372,35.1252991 C54.2954618,35.0807772 55.5950437,34.4368871 56.4751515,33.3586325 L56.6867532,34.8625641 L59.4150649,34.8942735 Z M45.6609524,34.8942735 L45.6609524,26.7132479 C45.6609524,24.6838462 45.5574026,22.0700855 42.1222511,22.0700855 C40.3213853,22.0700855 38.6105628,23.3611111 37.822684,23.9952991 L37.5885714,22.3328205 L34.8602597,22.3328205 L34.8602597,34.8897436 L38.0117749,34.8897436 L38.0117749,25.8299145 C38.5115152,25.5128205 39.7406061,24.6974359 40.8976623,24.6974359 C42.311342,24.6974359 42.4689177,25.698547 42.4689177,27.2840171 L42.4689177,34.8806838 L45.6609524,34.8942735 Z M29.0029437,31.8864103 C28.4110729,32.4700563 27.6287898,32.8177345 26.8013853,32.8648718 C25.9369697,32.8648718 25.1220779,32.4934188 25.1220779,31.1253846 C25.1220779,29.4357265 26.328658,29.0144444 29.0029437,28.8287179 L29.0029437,31.8864103 Z M32.0464069,34.8942735 L32.0464069,27.0303419 C32.0464069,24.2580342 31.9698701,22.0700855 27.9944589,22.0700855 C26.3227849,22.1267312 24.6771539,22.5033929 23.1456277,23.1799145 L23.6948918,24.9918803 C24.7436712,24.6832452 25.82646,24.5067401 26.9184416,24.4664103 C28.7553247,24.4664103 28.9894372,25.1277778 28.9894372,26.5773504 L28.9894372,27.2387179 C26.1575758,27.397265 22.0651082,27.3157265 22.0651082,31.2476923 C22.0651082,33.9112821 23.5868398,35.1524786 25.5272727,35.1524786 C26.9029516,35.0917526 28.198405,34.4840246 29.1290043,33.4628205 L29.3631169,34.8852137 L32.0464069,34.8942735 Z M21.3627706,34.2600855 L20.7054545,32.4481197 C20.0398775,32.687737 19.3410361,32.8207238 18.6344589,32.8422222 C17.324329,32.8422222 17.24329,31.9996581 17.24329,30.9668376 L17.24329,24.4709402 L20.7099567,24.4709402 L21.0251082,22.3328205 L17.2748052,22.3328205 L17.2748052,18.3510256 L14.5464935,18.8040171 L14.0962771,22.3147009 L11.9982684,22.7088034 L11.9982684,24.4482906 L14.0962771,24.4482906 L14.0962771,30.5183761 C14.0962771,32.2352137 14.150303,33.5805983 15.0642424,34.3959829 C15.5909957,34.8489744 16.4148918,35.1616392 17.5809524,35.1616392 C18.9020761,35.168045 20.2058864,34.8591012 21.3852814,34.2600855 L21.3627706,34.2600855 Z M11.7911688,29.6169231 C11.7911688,26.6090598 9.92727273,25.54 7.33402597,24.7110256 C5.00190476,23.9726496 3.55670996,23.5513675 3.55670996,21.9930769 C3.55670996,20.3034188 4.97038961,19.5922222 6.57316017,19.5922222 C7.92184686,19.612318 9.25835388,19.8525056 10.5305628,20.3034188 L11.2644156,18.0882906 C9.71369351,17.4230003 8.0473116,17.0735052 6.36155844,17.06 C2.61125541,17.06 0.306147186,18.9852137 0.306147186,22.2286325 C0.306147186,27.2115385 5.62770563,26.8717949 7.6987013,28.457265 C8.23071119,28.8260094 8.53818086,29.4430298 8.51359307,30.0925641 C8.51359307,31.5693162 7.23047619,32.6247863 5.20900433,32.6247863 C3.81103461,32.622715 2.43446537,32.2791719 1.19757576,31.6236752 L0.0945454545,33.7889744 C0.225108225,33.9203419 2.2195671,35.1479487 5.38909091,35.1479487 C9.50857143,35.1298291 11.8136797,33.0732479 11.8136797,29.6169231 L11.7911688,29.6169231 Z M154.226147,34.9033333 L149.021645,28.3349573 L153.910996,22.3418803 L150.552381,22.3418803 L146.050216,27.9363248 L146.050216,16.3306838 L142.898701,16.7836752 L142.898701,34.9033333 L146.050216,34.9033333 L146.050216,29.0144444 L150.588398,34.9033333 L154.226147,34.9033333 Z M140.030823,34.9033333 L140.030823,26.7223077 C140.030823,24.692906 139.927273,22.0791453 136.492121,22.0791453 C134.691255,22.0791453 132.980433,23.3701709 132.192554,24.004359 L131.958442,22.3418803 L129.23013,22.3418803 L129.23013,34.8988034 L132.381645,34.8988034 L132.381645,25.8389744 C132.881385,25.5218803 134.110476,24.7064957 135.267532,24.7064957 C136.681212,24.7064957 136.84329,25.7076068 136.84329,27.2930769 L136.84329,34.8897436 L140.030823,34.9033333 Z M123.372814,31.8864103 C122.780943,32.4700563 121.99866,32.8177345 121.171255,32.8648718 C120.30684,32.8648718 119.491948,32.4934188 119.491948,31.1253846 C119.491948,29.4357265 120.698528,29.0144444 123.372814,28.8287179 L123.372814,31.8864103 Z M126.416277,34.8942735 L126.416277,27.0303419 C126.416277,24.2580342 126.33974,22.0700855 122.364329,22.0700855 C120.691145,22.1262689 119.043954,22.502938 117.510996,23.1799145 L118.06026,24.9918803 C119.109029,24.6831996 120.191823,24.5066935 121.28381,24.4664103 C123.120693,24.4664103 123.354805,25.1277778 123.354805,26.5773504 L123.354805,27.2387179 C120.522944,27.397265 116.430476,27.3157265 116.430476,31.2476923 C116.430476,33.9112821 117.952208,35.1524786 119.892641,35.1524786 C121.26832,35.0917526 122.563773,34.4840246 123.494372,33.4628205 L123.728485,34.8852137 L126.416277,34.8942735 Z M111.275498,29.6984615 C111.275498,30.9894872 110.753247,31.7822222 109.965368,32.1763248 C109.177489,32.5704274 108.3671,32.5477778 106.948918,32.5477778 L105.742338,32.5477778 L105.742338,27.0303419 L107.421645,27.0303419 C108.97039,27.0303419 109.519654,27.1073504 110.068918,27.4017949 C110.861299,27.7777778 111.307013,28.5342735 111.307013,29.6939316 L111.275498,29.6984615 Z M111.063896,22.0474359 C111.063896,24.054188 110.118442,24.7925641 107.079481,24.7925641 L105.728831,24.7925641 L105.728831,19.6239316 L107.408139,19.6239316 C108.979394,19.6239316 109.560173,19.7009402 110.13645,20.0452137 C110.780532,20.4972328 111.143948,21.2542951 111.095411,22.042906 L111.063896,22.0474359 Z M114.665628,30.1197436 C114.665628,27.4561538 113.224935,26.1107692 110.61368,25.8435043 C113.02684,25.4494017 114.260433,24.0315385 114.260433,21.6759829 C114.260433,17.3499145 110.537143,17.2955556 107.340606,17.2955556 L102.52329,17.2955556 L102.52329,34.8942735 L106.822857,34.8942735 C109.888831,34.8942735 110.807273,34.7900855 111.856277,34.3688034 C113.634908,33.7444482 114.789269,32.0123903 114.688139,30.1197436 L114.665628,30.1197436 Z"
                id="Shape"
                fill={theme === 'dark' ? '#fff' : '#000'}
                fill-rule="nonzero"
              ></path>
              <rect
                id="Rectangle"
                x="0.424242424"
                y="0.692307692"
                width="165"
                height="53"
              ></rect>
            </g>
            <g id="_3d_Logo-Copy" transform="translate(0.000000, 1.000000)">
              <path
                d="M22.168658,52.5107692 C9.87324675,48.4338462 2.12952381,39.7047009 0.936450216,28.5339316 C0.432207792,23.8047009 1.27411255,13.1322222 1.83688312,7.21162393 C2.02268889,4.61275828 3.87649339,2.4421741 6.40207792,1.86632479 C11.9571084,0.687294286 17.6169781,0.0801774862 23.2941991,0.0543589744 C28.965411,0.08118672 34.6192071,0.688299259 40.1683117,1.86632479 C42.7009915,2.43524832 44.5635038,4.60745931 44.7515152,7.21162393 L44.7875325,7.60119658 C45.5258874,15.5874359 46.2012121,24.3347009 45.6204329,28.6335897 C43.8195671,41.7975214 33.5141126,49.5935043 24.4602597,52.4971795 L23.2987013,52.8776923 L22.168658,52.5107692 Z"
                id="Shape"
                fill="url(#linearGradient-1)"
              ></path>
              <path
                d="M41.3838961,7.20709402 C41.3457875,6.16457181 40.6256739,5.27366831 39.6190476,5.02367521 C34.3069842,3.89221192 28.8949422,3.30335592 23.4652814,3.26606838 C18.0326463,3.30339422 12.6176218,3.89224649 7.30251082,5.02367521 C6.2976654,5.27538837 5.57972264,6.16587775 5.5421645,7.20709402 C5.13246753,11.6735897 4.16,23.2022222 4.6417316,27.8182051 C5.7582684,38.2370085 13.2678788,45.1270085 22.2271861,48.4791453 C22.6713997,48.6452422 23.1216162,48.7962393 23.5778355,48.9321368 C23.6678788,48.9004274 26.6753247,48.0623932 26.6753247,48.0623932 C27.2696104,47.7634188 29.2145455,46.8347863 29.804329,46.4452137 C35.5670996,42.6717949 40.5194805,34.9709402 42.2708225,27.8182051 C43.3738528,23.3064103 41.7980952,11.6690598 41.3838961,7.20709402 Z"
                id="Shape"
                fill="url(#linearGradient-2)"
              ></path>
              <image
                id="Bitmap"
                opacity="0.5"
                // eslint-disable-next-line react/style-prop-object
                style={{ mixBlendMode: 'multiply' }}
                x="0.346666667"
                y="2.29666667"
                width="42.5184416"
                height="50.2276923"
                xlinkHref="data:image/png;base64,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"
              ></image>
              <path
                d="M32.8387879,39.4464957 C35.9609567,36.2696422 37.9087297,32.113505 38.3584416,27.6687179 L38.9302165,27.1613675 C38.6870996,32.4613675 36.3954978,35.8588034 33.3880519,38.8983761"
                id="Shape"
                fill="url(#linearGradient-3)"
              ></path>
              <path
                d="M38.0748052,8.15384615 C38.0748052,8.15384615 26.4457143,5.29547009 13.465974,7.43811966 L12.91671,7.99529915 C16.266327,7.51331936 19.6449111,7.26358541 23.0285714,7.24786325 C27.8888133,7.27681392 32.7353579,7.76976435 37.5030303,8.72008547 L38.0748052,8.15384615 Z"
                id="Shape"
                fill="url(#linearGradient-4)"
              ></path>
              <path
                d="M38.4890043,13.0869231 L37.9217316,13.6576923 C36.2559307,14.5908547 34.0543723,15.7142735 30.9298701,15.7142735 C30.7047619,15.7142735 30.4751515,15.7142735 30.241039,15.7142735 C25.9144589,15.4877778 23.4202597,13.0552137 19.8095238,12.8649573 C19.5724098,12.8649573 19.3367965,12.8649573 19.102684,12.8649573 C17.1177495,12.8387698 15.1595249,13.3270534 13.4164502,14.2828205 L13.9657143,13.725641 C15.7087889,12.7698739 17.6670136,12.2815903 19.6519481,12.3077778 C19.889062,12.3077778 20.1246753,12.3077778 20.3587879,12.3077778 C23.9605195,12.4980342 26.5222511,14.8490598 30.8488312,15.0755556 C31.0829437,15.0755556 31.3125541,15.0755556 31.5376623,15.0755556 C33.9851638,15.0570045 36.3816554,14.369638 38.4709957,13.0869231"
                id="Shape"
                fill="url(#linearGradient-5)"
              ></path>
              <path
                d="M38.8987013,27.1794872 L38.3494372,27.7366667 C37.2779221,28.7377778 34.5811255,30.6267521 31.4070996,30.6267521 C28.0980087,30.6267521 26.0585281,29.2949573 24.329697,28.7513675 L24.878961,28.194188 C26.6077922,28.7377778 28.6607792,29.9200855 31.9698701,29.9200855 C34.5239014,29.8552002 36.9749773,28.8937251 38.8987013,27.2021368"
                id="Shape"
                fill="url(#linearGradient-6)"
              ></path>
              <path
                d="M38.4664935,13.1005128 L37.9172294,13.6576923 C38.1063203,16.2125641 38.2909091,19.0437607 38.3674459,21.5986325 L38.4124675,24.1308547 L39.0112554,23.5917949 C39.0112554,23.5917949 39.0337662,17.1366667 38.4664935,13.1005128 Z"
                id="Shape"
                fill="url(#linearGradient-7)"
              ></path>
              <path
                d="M36.0488312,41.5257265 L35.4995671,42.082906 C33.7953173,43.7867742 31.8795696,45.2621309 29.7998268,46.4723932 L30.3490909,45.9152137 C32.4288336,44.7049514 34.3445814,43.2295947 36.0488312,41.5257265 Z"
                id="Shape"
                fill="url(#linearGradient-8)"
              ></path>
              <polygon
                id="Shape"
                fill="url(#linearGradient-9)"
                points="30.3490909 45.9152137 29.7998268 46.4678632 19.6294372 26.3731624 20.1832035 25.8159829"
              ></polygon>
              <polygon
                id="Shape"
                fill="url(#linearGradient-10)"
                points="21.7544589 44.1666667 21.0926407 44.7238462 7.85177489 18.0834188 8.40103896 17.4129915"
              ></polygon>
              <path
                d="M27.2245887,47.5278632 L26.6753247,48.0850427 C25.4860784,48.624888 24.2687209,49.0997106 23.0285714,49.5074359 L23.5778355,48.9502564 C24.7578874,48.3886108 25.9766275,47.9132488 27.2245887,47.5278632"
                id="Shape"
                fill="url(#linearGradient-11)"
              ></path>
              <path
                d="M23.6228571,48.9230769 L23.0285714,49.4938462 C13.3849351,46.322906 5.19549784,39.3694872 4.01142857,28.2983761 C3.51619048,23.6325641 4.49766234,11.9906838 4.91186147,7.46076923 C4.95605496,6.85162491 5.20907663,6.27681573 5.62770563,5.83452991 L6.1769697,5.27735043 C5.75669554,5.71898332 5.50205067,6.29386446 5.45662338,6.90358974 C5.03792208,11.4335043 4.06095238,23.0663248 4.55619048,27.7411966 C5.74025974,38.8077778 13.9792208,45.7340171 23.618355,48.9230769"
                id="Shape"
                fill="url(#linearGradient-12)"
              ></path>
              <path
                d="M38.2143723,9.92051282 L37.6651082,10.4776923 C37.6110823,9.87521368 37.5435498,9.25461538 37.5030303,8.73820513 L38.0748052,8.16290598 C38.0748052,8.16290598 38.2143723,9.29991453 38.2143723,9.92051282 Z"
                id="Shape"
                fill="url(#linearGradient-13)"
              ></path>
              <path
                d="M13.9567101,11.66 C13.9568163,11.3525476 13.8092335,11.0639998 13.5605195,10.8853846 L9.58510823,7.87299145 C9.49886981,7.81379812 9.39686143,7.78221747 9.29246753,7.78237821 C9.16082553,7.78136327 9.03429234,7.83359428 8.9412987,7.92735043 L8.39203463,8.48452991 C8.48502827,8.39077377 8.61156146,8.33854276 8.74320346,8.33957265 C8.84755555,8.33967707 8.94948382,8.37123293 9.03584416,8.43017094 L13.0112554,11.4425641 C13.2575665,11.6359755 13.3992787,11.9345667 13.3939394,12.2488889 L13.3939394,14.2465812 L13.9567101,13.6803419 L13.9567101,11.66 Z"
                id="Shape"
                fill="url(#linearGradient-14)"
              ></path>
              <path
                d="M41.6945455,6.92623932 C41.654438,5.86753261 40.9180554,4.96546074 39.8936797,4.72017094 C34.5238029,3.57699248 29.0529602,2.98206861 23.564329,2.94444444 C18.0742203,2.98225708 12.6019032,3.57717725 7.23047619,4.72017094 C6.20695661,4.96683856 5.47122746,5.86810995 5.42961039,6.92623932 C5.01090909,11.4561538 4.02943723,23.0935043 4.52917749,27.7638462 C5.65922078,38.3140171 13.1553247,45.1088889 22.2046753,48.4836752 C22.6488889,48.6497721 23.0991053,48.8007692 23.5553247,48.9366667 L23.8704762,48.8279487 L23.892987,48.8279487 C25.0185281,48.442906 26.1440693,48.0035043 27.2020779,47.5188034 L10.8051948,15.2929915 L9.19341991,14.0880342 C8.94061548,13.901733 8.79034181,13.6060532 8.78822511,13.2907692 L8.78822511,8.28974359 C8.78821398,8.01380295 9.0092241,7.78940903 9.2834632,7.78692308 C9.3878571,7.78674738 9.48986548,7.81832804 9.5761039,7.87752137 L13.5515152,10.8899145 C13.7978262,11.0833259 13.9395384,11.3819171 13.9341991,11.6962393 L13.9341991,13.6939316 C15.6771998,12.7379732 17.635475,12.249677 19.6204329,12.2760684 C19.8590476,12.2760684 20.0706494,12.2760684 20.3272727,12.2760684 C23.9290043,12.4663248 26.4322078,14.894359 30.7587879,15.1253846 C30.9929004,15.1253846 31.2090043,15.1253846 31.447619,15.1253846 C34.5721212,15.1253846 36.7736797,14.0019658 38.4394805,13.0688034 C38.6285714,15.6236752 38.8131602,18.4503419 38.889697,21.0097436 L38.9392208,23.5419658 C36.9672727,25.1591453 33.7482251,26.5770085 31.911342,26.5770085 C28.8678788,26.5770085 26.8013853,24.6336752 23.92,24.6336752 C22.5715811,24.6297447 21.2528664,25.0320573 20.1336797,25.7888034 L30.3040693,45.8835043 C31.5652974,45.1404154 32.7692623,44.303045 33.9058009,43.3784615 C38.5295238,39.6186325 41.8116017,34.4318803 42.5274459,27.7276068 C43.0902165,23.1025641 42.1087446,11.4380342 41.6945455,6.92623932 Z M21.6419048,44.1666667 C21.3237518,44.0277493 21.0086003,43.884302 20.6964502,43.7363248 C13.9432035,40.4566667 9.09437229,34.9890598 8.27948052,27.3516239 C8.05887446,25.2588034 8.17593074,21.4219658 8.40103896,17.5217094 L21.6419048,44.1666667 Z M31.4881385,11.8321368 C31.2585281,11.8321368 31.0379221,11.8321368 30.7858009,11.8321368 C26.4592208,11.605641 24.0685714,9.35880342 20.4578355,9.16854701 C20.2552381,9.16854701 20.0571429,9.16854701 19.8590476,9.16854701 C18.7684521,9.16249235 17.6827118,9.31506671 16.6354978,9.62153846 L13.4569697,7.45623932 C16.808373,6.97847148 20.1884757,6.73327711 23.5733333,6.72239316 C28.4401931,6.74613638 33.2939905,7.2314745 38.070303,8.17196581 C38.070303,8.17196581 38.1603463,9.33162393 38.2098701,9.93410256 C36.1751916,11.1529215 33.8559871,11.8078018 31.4881385,11.8321368 Z M31.5466667,40.5517949 L26,29.6347009 C25.7134923,29.0958585 25.333884,28.6126713 24.878961,28.2077778 C26.6077922,28.7558974 28.6472727,30.0876923 31.9563636,30.0876923 C35.1303896,30.0876923 37.8091775,28.1987179 38.8987013,27.1976068 L38.8987013,27.3561538 C38.2954113,32.8962393 35.5670996,37.2993162 31.5466667,40.5517949 Z"
                id="Shape"
                stroke="#FFFFFF"
                stroke-width="0.5"
                fill="url(#linearGradient-15)"
                fill-rule="nonzero"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  );
};
