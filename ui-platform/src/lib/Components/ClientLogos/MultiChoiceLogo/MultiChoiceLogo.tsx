/**
 * MultiChoiceLogo
 *
 * A reusable component for rendering the MultiChoice logo.
 * The logo consists of different colors
 * and shapes, with hex colors used for styling the elements. The SVG has a
 * predefined width and height, and it is rendered as an inline SVG.
 *
 * @return {ReactElement} The MultiChoice logo.
 */

import { CSSProperties } from 'react';

export const MultiChoiceLogo = ({
  width,
  height,
}: {
  width: CSSProperties['width'];
  height: CSSProperties['height'];
}) => {
  return (
    <svg
      width={width || '102'}
      height={(width && height ? height : 'auto') || '60'}
      viewBox="0 0 180 109"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_10_224)">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M43.414 73.479H39.1L39.089 85.799L34.407 89.275V73.475L30.178 73.469L30.189 91.19H34.056L39.089 87.49V91.19H43.415L43.414 73.479Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M54.768 73.478H50.468V91.198H59.549V90.015H54.768V73.478Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M23.106 91.1879H18.752L18.741 72.1139L14.059 75.5509V91.1889H9.85198V72.0819L5.18098 75.5469V91.1849H0.815979V69.5819H5.18098V73.5169L10.492 69.5759L14.059 69.5819V73.5559L19.432 69.5759H23.112L23.106 91.1879Z"
          fill="white"
        />
        <path d="M81.768 73.478H77.459V91.193H81.768V73.478Z" fill="white" />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M59.439 73.4729L73.539 73.4789V74.6619H68.639V91.1939H64.325L64.336 74.6619H59.436L59.439 73.4729Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M88.795 72.4539C88.795 70.6589 90.771 69.5779 92.809 69.5779H99.444V73.4779H97.751V71.1109H93.091V89.6639C93.091 89.6639 94.133 89.6579 95.124 89.6639C95.7668 89.6827 96.3932 89.4591 96.8787 89.0374C97.3643 88.6158 97.6735 88.027 97.745 87.3879C97.745 87.3879 97.739 86.1539 97.745 86.1589L99.438 86.1759V87.3989C99.438 89.3989 97.377 91.1989 95.129 91.1989H88.784V72.4589L88.795 72.4539Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M104.3 73.478H108.609V81.378L113.286 77.851L113.303 73.48H117.651V91.198H113.303V79.581L108.609 83.046V91.198H104.3V73.478Z"
          fill="white"
        />
        <path d="M149.366 73.478H145.063V91.193H149.366V73.478Z" fill="white" />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M160.593 74.6621V90.0111L161.844 90.0051C163.107 90.0111 164.103 89.2461 164.103 88.2901V86.8411H165.637V88.2901C165.643 90.0051 163.797 91.1901 161.821 91.1901H156.29V75.6601C156.29 74.4141 158.022 73.4751 159.002 73.4751H165.637V76.8211H164.103V74.6581H160.593V74.6621Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M170.902 73.478H180.002V74.661H175.212V80.436H179.028V81.598H175.212V90.006H180.002V91.198H170.902V73.478Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M128.913 74.6619H133.607V90.0109H128.913V74.6619ZM134.913 73.4729H127.677C126.058 73.4729 124.671 74.4579 124.671 75.7209V88.8899C124.671 90.0169 125.871 91.1999 127.677 91.1999H135.047C136.355 91.1999 137.912 90.1189 137.912 88.8789V75.7259C137.912 74.5199 136.525 73.4729 134.906 73.4729"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M89.469 0L76.369 13.871L89.469 64.056V0Z"
          fill="#CC632D"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M24.543 13.73L26.693 30.092L86.633 64.192L24.543 13.73Z"
          fill="#83227A"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M86.629 64.193L46.593 20.388L24.543 13.73L86.629 64.193Z"
          fill="#E5007D"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M84.523 64.6351L23.3 31.5481L0 32.1091L84.523 64.6351Z"
          fill="#636AAF"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M0 32.1111L5.82 44.4531L84.52 64.6371L0 32.1111Z"
          fill="#4C1256"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M55.904 3.47095L50.633 19.2709L88.278 64.0909L55.904 3.47095Z"
          fill="#B61F29"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M88.293 64.0909L72.138 14.4259L55.923 3.47095L88.293 64.0909Z"
          fill="#E30613"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M89.529 0L102.506 13.871L89.529 64.056V0Z"
          fill="#EC6608"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M154.054 13.73L152.118 30.092L92.41 64.192L154.054 13.73Z"
          fill="#52AE32"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M92.409 64.1931L132.009 20.3931L154.056 13.7351L92.409 64.1931Z"
          fill="#608D33"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M94.514 64.6351L156.014 31.5481L179.606 31.9611L94.514 64.6351Z"
          fill="#006991"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M179.604 31.9629L173.478 44.4529L94.514 64.6369L179.604 31.9629Z"
          fill="#009CB4"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M122.56 3.47095L127.684 19.2709L90.753 64.0909L122.56 3.47095Z"
          fill="#FBBB21"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M90.735 64.0909L106.855 14.4259L122.542 3.47095L90.735 64.0909Z"
          fill="#E28F2A"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M31.801 99.7639V108.477H34.909V107.469H32.956V104.514H34.7V103.506H32.956V100.766H34.909V99.7659L31.801 99.7639Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M40.949 101.785L40.977 101.762L43.01 108.477H44.08V99.7639H42.932V102.872L43.204 106.36L43.181 106.383L41.149 99.7639H40.039V108.477H41.194V105.277L41.018 102.356L40.949 101.785Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M50.731 103.624V100.742H50.916C51.794 100.719 51.895 101.41 51.895 102.242C51.895 103.125 51.595 103.674 50.729 103.623L50.731 103.624ZM49.574 108.475H50.729V104.214H50.746L52.32 108.477H53.52L51.92 104.236C52.877 104.06 53.12 102.815 53.12 101.994C53.12 99.8089 51.801 99.7629 50.957 99.7629H49.576L49.574 108.475Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M68.471 99.8991C68.1677 99.7161 67.8168 99.6273 67.463 99.6441C65.096 99.6441 64.876 102.514 64.876 104.258C64.876 106.081 65.193 108.589 67.548 108.589C67.875 108.603 68.1986 108.518 68.477 108.346V107.333C68.2613 107.447 68.0208 107.505 67.777 107.503C66.197 107.503 66.101 105.329 66.101 104.18C66.101 103.087 66.112 100.732 67.732 100.732C67.9934 100.745 68.2479 100.821 68.474 100.953V99.9001L68.471 99.8991Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M73.444 99.7639V108.477H74.599V104.163H76.343V108.477H77.498V99.7639H76.343V103.164H74.599V99.7639H73.444Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M90.537 101.785L90.56 101.762L92.598 108.477H93.674V99.7639H92.519V102.872L92.796 106.36L92.768 106.383L90.741 99.7639H89.631V108.477H90.78V105.277L90.604 102.356L90.537 101.785Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M101.214 103.873V104.881H102.182V105.357C102.182 107.435 101.661 107.503 101.182 107.503C100.672 107.503 100.163 107.158 100.163 104.129C100.163 102.029 100.231 100.729 101.182 100.729C101.895 100.729 102.06 101.295 102.182 101.873L103.282 101.426C103.226 100.912 102.973 100.439 102.576 100.106C102.179 99.774 101.669 99.608 101.153 99.643C99.143 99.643 98.953 101.698 98.953 104.143C98.953 106.606 99.196 108.593 101.263 108.593C103.584 108.593 103.341 105.683 103.341 104.018V103.876L101.214 103.873Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M115.002 99.7639V108.477H118.102V107.469H116.154V99.7639H115.002Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M123.989 108.511V99.7639H122.834V108.477L123.989 108.511Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M84.238 108.511V99.7639H83.089V108.477L84.238 108.511Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M59.31 108.511V99.7639H58.155V108.477L59.31 108.511Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M128.919 99.7639L130.606 108.477H131.529L133.239 99.7639H132.139L131.612 102.838L131.148 105.771L131.091 106.315H131.069L130.078 99.7639H128.919Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M138.095 99.7639V108.477H141.195V107.469H139.247V104.514H140.991V103.506H139.247V100.766H141.195V99.7659L138.095 99.7639Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M149.008 99.933C148.634 99.7289 148.211 99.6291 147.785 99.644C147.504 99.644 147.227 99.704 146.971 99.8199C146.715 99.9359 146.487 100.105 146.302 100.316C146.117 100.528 145.98 100.776 145.899 101.045C145.818 101.314 145.795 101.597 145.832 101.875C145.832 104.162 148.272 104.508 148.272 106.24C148.272 106.891 147.972 107.457 147.253 107.457C146.832 107.436 146.43 107.277 146.109 107.004V108.17C146.474 108.45 146.923 108.598 147.383 108.589C147.67 108.584 147.953 108.519 148.214 108.4C148.475 108.281 148.708 108.109 148.9 107.896C149.092 107.682 149.237 107.431 149.328 107.159C149.419 106.887 149.452 106.599 149.427 106.313C149.428 105.821 149.317 105.335 149.102 104.893C148.888 104.451 148.574 104.063 148.187 103.76L147.309 102.803C147.087 102.539 146.971 102.202 146.981 101.857C146.971 101.715 146.99 101.572 147.038 101.438C147.085 101.303 147.159 101.18 147.255 101.075C147.351 100.97 147.468 100.886 147.598 100.828C147.728 100.769 147.869 100.738 148.011 100.736C148.37 100.746 148.716 100.878 148.99 101.11L149.008 99.933Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_10_224">
          <rect width="180" height="108.589" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
