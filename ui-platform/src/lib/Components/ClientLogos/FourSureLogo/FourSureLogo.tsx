import { CSSProperties } from 'react';

interface Props {
  theme?: 'light' | 'dark';
  style?: {
    width: CSSProperties['width'];
    height: CSSProperties['height'];
  };
}
/**
 * A FourSure logo component.
 *
 * @param {Object} props
 * @prop {string} theme - The theme of the logo. One of 'light' or 'dark'.
 * @returns {ReactElement} A `svg` element representing the logo.
 */
export const FourSureLogo = ({ theme = 'dark', style }: Props) => {
  const { width, height } = style || {};
  return (
    <svg
      version="1.1"
      id="Layer_1"
      xmlns="http://www.w3.org/2000/svg"
      // xlink="http://www.w3.org/1999/xlink"
      x="0px"
      y="0px"
      viewBox="76.14 319.445 443 203"
      enableBackground="new 76.14 319.445 443 203"
      xmlSpace="preserve"
      {...{
        width: width || 'auto',
        height: width && height ? height : 'auto',
      }}
    >
      <g>
        <path
          fill="#75AE42"
          d="M187.071,507.053h-22.183v-35.717H92.723l94.349-137.097L187.071,507.053L187.071,507.053z
       M164.888,451.154v-49.223l-33.893,49.223H164.888z"
        />
        <g>
          <path
            fill={theme === 'dark' ? '#FFF' : '#000'}
            d="M230.051,450.977c6.053,0,11.115-0.66,16.087-5.168c4.318-3.916,5.998-9.363,5.998-15.705
        c0-2.539-0.346-4.877-1.041-7c-0.694-2.121-1.778-4.076-3.249-5.854c-1.472-1.784-3.375-3.455-5.708-5.01
        c-2.333-1.557-5.11-3.045-8.331-4.459l-12.415-5.259c-17.607-7.58-26.413-18.67-26.413-33.271c0-9.836,3.693-18.073,11.082-24.69
        c7.388-6.674,16.581-10.015,27.579-10.015c14.83,0,18.026-0.14,40.743-0.14v18.827c0,0-36.521-0.09-40.91-0.09
        c-5.389,0-9.858,1.559-13.415,4.68c-3.554,3.057-5.332,6.906-5.332,11.557c0,6.398,4.666,11.557,13.998,15.464l12.831,5.357
        c10.442,4.304,18.079,9.562,22.913,15.761c4.833,6.205,7.249,13.815,7.249,22.822c0,12.068-3.943,22.039-11.832,29.918
        c-7.943,7.933-17.921,12.595-29.698,12.595c-11.164,0-8.188,0-8.188,0l0.006-20.312L230.051,450.977z"
          />
          <path
            fill={theme === 'dark' ? '#FFF' : '#000'}
            d="M292.797,334.407v80.928c0,11.545,1.807,19.983,5.416,25.317c5.386,7.713,12.97,11.574,22.745,11.574
        c9.832,0,17.441-3.861,22.83-11.574c3.611-5.158,5.416-13.606,5.416-25.317v-80.928h19.414v86.556
        c0,14.169-4.223,25.837-12.666,35.011c-9.5,10.217-21.162,15.324-34.994,15.324s-25.468-5.107-34.909-15.324
        c-8.445-9.174-12.667-20.842-12.667-35.011v-86.556H292.797z"
          />
          <path
            fill={theme === 'dark' ? '#FFF' : '#000'}
            d="M352.999,410.852l-3.714-76.444h23.839c17.812,0,30.676,3.389,38.592,10.165
        c8.733,7.541,13.098,17.498,13.098,29.871c0,9.662-2.734,17.97-8.207,24.922s-12.688,11.4-21.654,13.347l31.998,58.151h-24.885
        L373.3,415.008l-6.301,0.103 M369.634,397.067h6.459c19.268,0,28.899-7.452,28.899-22.358c0-13.963-9.371-20.946-28.114-20.946
        h-7.244V397.067L369.634,397.067z"
          />
          <path
            fill={theme === 'dark' ? '#FFF' : '#000'}
            d="M504.258,353.762h-54.704v32.791h53.113v19.354h-53.113v45.605h54.704v19.351H428.96V364.234v-29.827
        h75.298V353.762z"
          />
          <rect
            x="193"
            y="450.891"
            fill={theme === 'dark' ? '#FFF' : '#000'}
            width="23.32"
            height="20.406"
          />
        </g>
      </g>
      src
    </svg>
  );
};
