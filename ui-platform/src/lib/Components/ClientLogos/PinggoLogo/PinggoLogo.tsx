import { CSSProperties } from 'react';

/**
 * Renders the Pinggo logo as an SVG.
 * The logo consists of different colors
 * and shapes, with hex colors used for styling the elements. The SVG has a
 * predefined width and height, and it is rendered as an inline SVG.
 *
 * @returns {JSX.Element} The Pinggo logo.
 */
export const PinggoLogo = ({
  width,
  height,
}: {
  width: CSSProperties['width'];
  height: CSSProperties['height'];
}) => {
  return (
    <svg
      width={width || '58'}
      height={(width && height ? height : 'auto') || '63'}
      viewBox="0 0 92 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.1482 81.207C14.7123 81.207 17.1593 78.7483 17.1593 74.9472C17.1593 71.1178 14.7123 68.6591 10.1207 68.6591H2.27112V88.4417H6.10657V81.207H10.1482ZM10.052 72.2907C12.1416 72.2907 13.2276 73.1809 13.2276 74.9472C13.2276 76.7276 12.1416 77.6037 10.052 77.6037H6.10657V72.2907H10.052ZM22.7449 74.7493H19.1569V88.4417H22.7449V74.7493ZM20.9303 67.8961C19.6381 67.8961 18.6483 68.9417 18.6483 70.27C18.6483 71.5983 19.6381 72.6156 20.9303 72.6156C22.2225 72.6156 23.2261 71.5983 23.2261 70.27C23.2261 68.9417 22.2225 67.8961 20.9303 67.8961ZM32.9612 74.3819C31.449 74.3819 29.9918 74.9048 29.0432 76.3602V74.7493H25.5515V88.4417H29.1395V80.9385C29.1395 78.8754 30.4179 77.8439 31.9576 77.8439C33.5935 77.8439 34.5558 78.932 34.5558 80.9102V88.4417H38.1438V79.7233C38.1438 76.5439 35.9855 74.3819 32.9612 74.3819ZM46.1397 74.3819C42.8954 74.3819 40.0085 77.1657 40.0085 81.3765C40.0085 85.5733 42.8954 88.3569 46.1397 88.3569C47.9818 88.3569 49.329 87.6504 50.2363 86.5059V87.4667C50.2363 89.8265 48.6692 91.0983 46.4421 91.0983C44.8475 91.0983 43.3765 90.6037 42.043 89.6993L40.7646 92.7091C42.263 93.7972 44.49 94.3624 46.5933 94.3624C50.9924 94.3624 53.7831 91.7059 53.7831 87.3113V74.7493H50.2638V76.2754C49.3565 75.1167 47.9955 74.3819 46.1397 74.3819ZM47.047 77.7733C49.0403 77.7733 50.3875 79.3276 50.3875 81.3765C50.3875 83.4113 49.0403 84.9656 47.047 84.9656C44.9987 84.9656 43.6927 83.3548 43.6927 81.3765C43.6927 79.3841 44.9987 77.7733 47.047 77.7733ZM66.139 68.2917C60.2277 68.2917 55.9799 72.5733 55.9799 78.5504C55.9799 84.9374 60.7089 88.795 66.0428 88.795C71.5004 88.795 75.4458 84.57 75.4458 78.5787C75.4458 78.2113 75.4321 77.8439 75.3908 77.4624H66.5377V80.7548H71.4316C70.923 83.5667 69.0946 85.1211 65.9603 85.1211C62.6335 85.1211 59.9528 82.5917 59.9528 78.5504C59.9528 74.6928 62.5098 72.0363 66.029 72.0363C67.9949 72.0363 69.5895 72.8276 70.6755 74.1135L73.4387 71.3298C71.7341 69.4222 69.1909 68.2917 66.139 68.2917ZM84.0448 74.3819C79.907 74.3819 76.8551 77.4624 76.8551 81.5885C76.8551 85.7146 79.907 88.795 84.0448 88.795C88.1965 88.795 91.2621 85.7146 91.2621 81.5885C91.2621 77.4624 88.1965 74.3819 84.0448 74.3819ZM84.0448 77.8156C86.0657 77.8156 87.5916 79.37 87.5916 81.5885C87.5916 83.807 86.0657 85.3613 84.0448 85.3613C82.024 85.3613 80.5118 83.807 80.5118 81.5885C80.5118 79.37 82.024 77.8156 84.0448 77.8156Z"
        fill="#298BB1"
      />
      <mask
        id="mask0"
        // style="mask-type:alpha"
        style={{ maskType: 'alpha' }}
        maskUnits="userSpaceOnUse"
        x="10"
        y="9"
        width="71"
        height="47"
      >
        <path
          d="M58.126 54.7729C70.1177 54.7729 79.839 44.7806 79.839 32.4545C79.839 20.1284 70.1177 10.1361 58.126 10.1361C46.1342 10.1361 36.413 20.1284 36.413 32.4545C36.413 44.7806 46.1342 54.7729 58.126 54.7729Z"
          fill="#C4C4C4"
          stroke="black"
        />
        <path
          d="M10.5747 24.5711C19.7417 28.8831 30.5213 27.2229 38.0562 20.3385L41.7643 16.9504L57.8704 55.3164C41.8093 55.3164 26.7189 47.4128 17.2789 34.0565L10.5747 24.5711Z"
          fill="#C4C4C4"
        />
      </mask>
      <g mask="url(#mask0)">
        <path
          d="M50.2974 21.7119L66.9809 43.2883L38.2013 70.6781L20.0367 51.3896L50.2974 21.7119Z"
          fill="black"
          fill-opacity="0.04"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M7.25122 9.59259H80.8965V55.3164H7.25122V9.59259ZM71.6547 32.7172V32.7173C71.6547 40.2641 65.6597 46.3819 58.2646 46.3819H58.2645C50.8694 46.3819 44.8745 40.2641 44.8745 32.7173V32.7172C44.8745 25.1705 50.8694 19.0526 58.2645 19.0526H58.2646C65.6597 19.0526 71.6547 25.1705 71.6547 32.7172Z"
          fill="url(#paint0_linear)"
        />
        <path
          d="M58.2647 39.0241C61.6778 39.0241 64.4447 36.2004 64.4447 32.7173C64.4447 29.2342 61.6778 26.4106 58.2647 26.4106C54.8516 26.4106 52.0847 29.2342 52.0847 32.7173C52.0847 36.2004 54.8516 39.0241 58.2647 39.0241Z"
          fill="url(#paint1_linear)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear"
          x1="76.664"
          y1="10.6437"
          x2="41.2345"
          y2="71.7579"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#00FFD1" />
          <stop offset="1" stop-color="#3E5DAB" />
        </linearGradient>
        <linearGradient
          id="paint1_linear"
          x1="53.0863"
          y1="36.9218"
          x2="63.0356"
          y2="28.7118"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#22A8BD" />
          <stop offset="1" stop-color="#13CFC6" />
        </linearGradient>
      </defs>
    </svg>
  );
};
