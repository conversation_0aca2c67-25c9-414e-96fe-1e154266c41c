import { CSSProperties } from 'react';

/**
 * DStvLogo component
 * The logo consists of different colors
 * and shapes, with hex colors used for styling the elements. The SVG has a
 * predefined width and height, and it is rendered as an inline SVG.
 *
 * @returns {ReactElement} The DStv logo SVG
 */
export const DstvLogo = ({
  width,
  height,
}: {
  width: CSSProperties['width'];
  height: CSSProperties['height'];
}) => {
  return (
    <svg
      width={width || '70px'}
      height={(width && height ? height : 'auto') || '14px'}
      viewBox="0 0 70 14"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
    >
      <title>dstv logo - trans</title>
      <g
        id="Page-1"
        stroke="none"
        stroke-width="1"
        fill="none"
        fill-rule="evenodd"
      >
        <g id="Login-Screen" transform="translate(-648.000000, -570.000000)">
          <image
            id="dstv-logo---trans"
            x="623"
            y="545"
            width="120"
            height="64"
            xlinkHref="data:image/png;base64,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"
          ></image>
        </g>
      </g>
    </svg>
  );
};
