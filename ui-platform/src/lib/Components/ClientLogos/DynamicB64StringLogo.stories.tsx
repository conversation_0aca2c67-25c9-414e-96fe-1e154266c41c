import { Meta, StoryObj } from '@storybook/react';
import B64String<PERSON>ogo from './DynamicB64StringLogo';
import { imageData } from '../../Assets/imageData';

const meta: Meta<typeof B64StringLogo> = {
  title: 'Components/ClientLogos/DynamicB64StringLogo',
  component: B64StringLogo,
};

export default meta;

type Story = StoryObj<typeof B64StringLogo>;

export const Default: Story = {
  argTypes: {
    id_string: {
      control: 'select',
      options: ['vodapay', 'builders', 'game', 'takealot', 'sil']
    }
  },
  args: {
    id_string: 'vodapay',
    maximumWidth: 200
  }
};
