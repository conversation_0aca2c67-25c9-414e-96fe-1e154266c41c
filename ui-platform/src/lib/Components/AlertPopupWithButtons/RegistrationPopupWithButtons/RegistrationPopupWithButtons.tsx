import styled from "styled-components";
import { AlertPopup } from "../../AlertPopup/Popup";

const FormContainer = styled.form`
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-rows: 1fr auto;
    grid-template-columns: 1fr;
    justify-items: center;
    align-items: center;
`;

const Form = styled.form`
    width: 600px;
    height: 500px;
    justify-content: center;
    align-items: center;
    display: grid;
    grid-template-rows: auto auto 1fr;
    justify-items: center;
    align-items: center;
    z-index: 10000;
    gap: 20px;
`;

const Button = styled.button`
    width: 124px;
    padding: 8px;
    background: ${(props) => props?.theme.ColorsBackgroundInverse};
    color: ${(props) => props?.theme.ColorsTypographyInverse};
    box-shadow: 0px 0px 3px ${(props) => props.theme.ColorsInputsInverse};
    border-radius: 104px;
    border: 1px solid ${(props) => props?.theme.ColorsStrokesInverse};
    cursor: pointer;
`;

const ButtonContainer = styled.div`
  display: flex;
  flex-direction: row;
  gap: 10px;
`;

/**
 * RegistrationPopupWithButtons
 *
 * A popup with an error message and two buttons, e.g. "ALERT".
 *
 * @returns {JSX.Element} a JSX element representing the popup
 */
export const RegistrationPopupWithButtons = () => {
    return (
        <FormContainer>
        <Form>
            <h1>Error</h1>
            <p>Message</p>
            <ButtonContainer>
            <Button type="submit">ALERT</Button>
            <Button type="submit">ALERT</Button>
            </ButtonContainer>
        </Form>
        <AlertPopup type="registration" />
        </FormContainer>
    );
};
