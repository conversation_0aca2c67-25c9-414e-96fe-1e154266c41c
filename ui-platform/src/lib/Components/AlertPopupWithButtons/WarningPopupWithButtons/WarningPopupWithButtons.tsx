import styled from 'styled-components';
import { additionalFontStyling } from '../../../Utilities';
import { AlertPopup } from '../../AlertPopup/Popup';
import { TextButton } from '../../Buttons/TextButton/TextButton';
import { PlainTextInput } from '../../Inputs/PlainTextInput/PlainTextInput';

interface PopupProps {
  useFontTransformer?: boolean;
}

const FormContainer = styled(AlertPopup)`
  width: 600px;
  height: 400px;
  gap: 0px;
  border-radius: var(--radiusmd);
  opacity: 0px;
  border-radius: 12px;
  padding: 32px 24px 24px 24px;
  display: grid;
  grid-template-rows: auto auto 1fr;
  grid-template-columns: 1fr;
  justify-items: center;
  align-items: center;
`;

const Wrapper = styled.div`
  width: 100%;
  display: grid;
  place-items: center;
`;

export const FormHeading = styled.h1<PopupProps>`
  all: unset;
  font-size: ${(props) => props.theme.FontSize8}px;
  line-height: 57.24px;
  letter-spacing: -0.09em;
  color: ${(props) => props?.theme.ColorsButtonColorModuleNavigationInverse};
  text-align: center;
  ${(props) =>
    additionalFontStyling(
      props.theme.FontWeightsInter2,
      props.useFontTransformer
    )}
`;

const ButtonContainer = styled.div`
  width: 284px;
  height: 38px;
  display: grid;
  justify-content: space-between;
  gap: var(--spacinglg);
  box-sizing: border-box;
  border: none;
  margin-top: 82px;
`;

const Button = styled(TextButton)<PopupProps>`
  width: 133px;
  height: 38px;
  padding: 8px 16px 8px 16px;
  gap: var(--gapsm);
  background: ${(props) =>
    props?.theme.ColorsButtonColorModuleNavigationDefault};
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
  box-shadow: 0px 0px 3px ${(props) => props.theme.ColorsInputsInverse};
  border-radius: 104px;
  cursor: pointer;
  ${(props) =>
    additionalFontStyling(
      props.theme.FontWeightsInter3,
      props.useFontTransformer
    )}
`;

/**
 * WarningPopupWithButtons
 *
 * A popup with an error message and two buttons, e.g. "DECISION".
 *
 * @returns {JSX.Element} a JSX element representing the popup
 */
export const WarningPopupWithButtons = () => {
  return (
    <FormContainer type="warning">
      <Wrapper>
        <FormHeading>Error</FormHeading>
      </Wrapper>

      <Wrapper>
        {/* TODO: fix this. take control, name and type etc as props and bind */}
        <PlainTextInput
          placeholder="Message"
          label="Error"
          type="text"
          name=""
        />
      </Wrapper>
      <ButtonContainer>
        <Button btnValue="DECISION" />
        <Button btnValue="DECISION" />
      </ButtonContainer>
    </FormContainer>
  );
};
