import styled from "styled-components";
import { AlertPopup } from "../../AlertPopup/Popup";

const FormContainer = styled.form`
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-rows: 1fr auto;
    justify-items: center;
    align-items: center;
`;

const Form = styled.form`
    width: 300px;
    height: 300px;
    display: grid;
    grid-template-rows: auto auto 1fr;
    justify-items: center;
    align-items: center;
    z-index: 10000;

    h1, p {
        margin: 10px;
    }
`;

const Button = styled.button`
    width: 124px;
    padding: 8px;
    background: ${(props) => props?.theme.ColorsButtonColorModuleNavigationDefault};
    color: ${(props) => props?.theme.ColorsTypographyPrimary};
    box-shadow: 0px 0px 3px ${(props) => props.theme.ColorsInputsInverse};
    border-radius: 104px;
    cursor: pointer;
`;

/**
 * ErrorPopupWithButtons
 *
 * A popup with an error message and a single button, e.g. "DECISION".
 *
 * @returns {JSX.Element} a JSX element representing the popup
 */
export const ErrorPopupWithButtons = () => {
    return (
        <FormContainer>
            <Form>
                <h1>Error</h1>
                <p>Message</p>
                <Button type="submit">DECISION</Button>
            </Form>
            <AlertPopup type="error" />
        </FormContainer>
    );
};
