import React, { useState } from 'react';
import styled from 'styled-components';
import { PaginationBar } from '../../Controllers/Pagination/PaginationBar';
import {
  PaginationPageCount,
  useListPagination2,
} from '../../Controllers/Pagination/PaginationPageCount';
import { List, withItems } from '../../List/List';
import { ListItem, ListItemProps } from '../../ListItem/ListItem';
import { JobImageItem, JobImageItemProps } from '../JobImageItem/JobImageItem';
import { JobImageViewer } from '../JobImageViewer/JobImageViewer';

export interface JobImageListItemProps extends Omit<ListItemProps, 'children'> {
  jobImageItems: any[];
  usecase: string;
  mediaType: string;
}

const ListWrapper = styled(List)<{ usecase?: string }>`
  width: 100%;
  padding-inline-start: unset;
  margin: unset;
  margin: 24px 0;

  /* Conditional grid layout for DocumentView usecase */
  ${(props) =>
    props.usecase === 'DocumentView'
      ? `
        display: grid;
        grid-template-columns: repeat(2, 1fr); /* 2 columns */
        grid-template-rows: repeat(3, auto); /* 3 rows */
      `
      : `
        display: grid;
        grid-auto-flow: column;
      `}
`;

const Component = styled(
  ({ jobImageItems, usecase, ...rest }: JobImageListItemProps) => {
    // I removed ...rest from the bottom const
    const { pages, currentPage, pageItems, ...res } = useListPagination2({
      items: jobImageItems,
      itemsPerPage: 6,
    });

    console.log(jobImageItems);

    return (
      <ListItem {...rest}>
        <ListWrapper usecase={usecase}>
          {withItems(JobImageItem)(pageItems)}
        </ListWrapper>
        {usecase === 'DocumentView' && (
          <PaginationBar
            paginationItems={
              <PaginationPageCount
                pages={pages}
                currentPage={currentPage}
                {...res}
              />
            }
          />
        )}
      </ListItem>
    );
  }
)`
  width: 100%;
  height: 100%;
  position: relative;
  display: grid;
  grid-template-rows: repeat(2, auto);
  text-align: left;
  font-size: ${(props) => props.theme.FontSize2}px;
  color: ${(props) => props.theme.ColorsTypographySecondary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  list-style-type: none;
`;

/**
 * A component that displays a list of job image items and supports opening an image viewer
 * when an item is clicked.
 * @param {JobImageItemProps[]} jobImageItems - The list of job image items to display.
 * @param {ReactNode} [children] - The children elements to display.
 * @return {ReactElement} A React element that displays the list of job image items and an
 * image viewer when an item is clicked.
 */
export const JobImageListItem: React.FC<JobImageListItemProps> = ({
  jobImageItems,
  ...rest
}) => {
  const [viewerOpen, setViewerOpen] = useState(false);
  const [currentItem, setCurrentItem] = useState<any | null>(null);

  /**
   * Handles the click event of an item in the list.
   * @param {JobImageItemProps} item - The item that was clicked.
   * Sets the `currentItem` state to the item that was clicked and opens the image viewer.
   */

  const handleItemClick = (item: JobImageItemProps) => {
    setCurrentItem(item);
    setViewerOpen(true);
  };

  /**
   * Closes the image viewer and resets the current item to null.
   * This is called when the user closes the image viewer.
   */
  const handleClose = () => {
    setViewerOpen(false);
    setCurrentItem(null);
  };

  /**
   * Handles the next button click event in the viewer.
   * Updates the `currentItem` state with the next item in the list.
   * If the current item is the last item in the list, sets the `currentItem` to the first item in the list.
   */
  const handleNext = () => {
    if (Array.isArray(jobImageItems)) {
      const currentIndex = jobImageItems.findIndex(
        (item: any) =>
          item.name === currentItem?.name &&
          item.TeamMemberName === currentItem?.TeamMemberName &&
          item.date === currentItem?.date &&
          item.time === currentItem?.time
      );
      const nextIndex = (currentIndex + 1) % jobImageItems.length;
      setCurrentItem(jobImageItems[nextIndex]);
    }
  };

  /**
   * Handles the previous button click event in the viewer.
   * Updates the `currentItem` state with the previous item in the list.
   * If the current item is the first item in the list, sets the `currentItem` to the last item in the list.
   */
  const handlePrevious = () => {
    if (Array.isArray(jobImageItems)) {
      const currentIndex = jobImageItems.findIndex(
        (item: any) =>
          item.name === currentItem?.name &&
          item.TeamMemberName === currentItem?.TeamMemberName &&
          item.date === currentItem?.date &&
          item.time === currentItem?.time
      );
      const previousIndex =
        (currentIndex - 1 + jobImageItems.length) % jobImageItems.length;
      setCurrentItem(jobImageItems[previousIndex]);
    }
  };

  const itemsArray = Array.isArray(jobImageItems)
    ? jobImageItems
    : [jobImageItems];

  return (
    <>
      <Component
        {...rest}
        jobImageItems={itemsArray.map((item: JobImageItemProps) => ({
          ...item,
          onClick: () => handleItemClick(item),
        }))}
      />
      {viewerOpen && currentItem && (
        <JobImageViewer
          src={currentItem.src || ''}
          name={currentItem.name}
          TeamMemberName={currentItem.TeamMemberName}
          date={currentItem.date}
          time={currentItem.time}
          mediaType={currentItem.mediaType}
          onClose={handleClose}
          onNext={handleNext}
          onPrevious={handlePrevious}
          icon={currentItem.icon}
        />
      )}
    </>
  );
};
