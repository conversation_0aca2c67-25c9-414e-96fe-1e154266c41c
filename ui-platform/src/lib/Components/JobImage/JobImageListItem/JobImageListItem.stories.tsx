import type { <PERSON>a, StoryObj } from '@storybook/react';
import { JobImageItemProps } from '../JobImageItem/JobImageItem';
import { JobImageListItem } from './JobImageListItem';

const meta: Meta<typeof JobImageListItem> = {
  component: JobImageListItem,
  title: 'Components/JobImage/JobImageListItem',
  argTypes: {
    jobImageItems: {
      control: 'object',
    },
  },
};
export default meta;
type Story = StoryObj<typeof JobImageListItem>;

export const Overview: Story = {
  args: {
    jobImageItems: [
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Image Purpose',
        TeamMemberName: 'Team Member Name',
        date: '2024-06-24',
        time: '11:52',
        mediaType: 'image',
      },
    ],
  },
};

export const WithAction: Story = {
  args: {
    jobImageItems: [
      {
        src: 'https://picsum.photos/1336/1080',
        name: 'Decoder Serial Number',
        mediaType: 'image',
        onClick: () =>
          alert('This will navigate to the enlarged job image/document'),
        TeamMemberName: '',
        date: '',
        time: '',
      },
    ],
  },
};

const JobImageItems: JobImageItemProps[] = [
  {
    src: 'https://picsum.photos/1336/1080',
    name: 'Decoder Serial Number',
    TeamMemberName: 'Team Member Name',
    date: '2024-06-24',
    time: '11:52',
    mediaType: 'image',
  },
  {
    src: 'https://picsum.photos/1336/1090',
    name: 'Assessor BOQ Report',
    TeamMemberName: 'Team Member Name',
    date: '2024-06-24',
    time: '11:52',
    mediaType: 'image',
  },
  {
    src: 'https://picsum.photos/1336/1070',
    name: 'Internal Assessor Report',
    TeamMemberName: 'Team Member Name',
    date: '2024-06-24',
    time: '11:52',
    mediaType: 'image',
  },
  {
    src: 'https://picsum.photos/1336/1060',
    name: 'SP Invalid Work Report',
    TeamMemberName: 'Team Member Name',
    date: '2024-06-24',
    time: '11:52',
    mediaType: 'image',
  },
  {
    src: 'https://picsum.photos/1336/1050',
    name: 'Work Stopped, Pay Customer Out',
    TeamMemberName: 'Team Member Name',
    date: '2024-06-24',
    time: '11:52',
    mediaType: 'image',
  },
];

export const WithMultipleItems: Story = {
  args: {
    jobImageItems: JobImageItems,
  },
};

export const WithMultipleItems_DocumentView: Story = {
  args: {
    jobImageItems: JobImageItems,
    usecase: 'DocumentView',
  },
};
