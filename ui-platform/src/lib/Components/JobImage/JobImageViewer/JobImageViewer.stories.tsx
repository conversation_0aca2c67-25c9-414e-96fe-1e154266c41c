import type { Meta, StoryObj } from '@storybook/react';
import { JobImageViewer } from './JobImageViewer';

const meta: Meta<typeof JobImageViewer> = {
  component: JobImageViewer,
  title: 'Components/JobImage/JobImageViewer',
  argTypes: {
    src: { control: 'text' },
    name: { control: 'text' },
    TeamMemberName: { control: 'text' },
    date: { control: 'text' },
    time: { control: 'text' },

    mediaType: {
      control: { type: 'radio' },
      options: ['image', 'icon'],
    },
  },
};
export default meta;
type Story = StoryObj<typeof JobImageViewer>;

export const Overview: Story = {
  args: {
    src: 'https://picsum.photos/1336/1080',
    name: 'Image Purpose',
    TeamMemberName: 'Team Member Name',
    date: '2024-06-24',
    time: '11:52',
    mediaType: 'image',
  },
};
