import React from 'react';
import styled from 'styled-components';
import { IconButton } from '../../Buttons/IconButton/IconButton';
import { Icon, IconTypes } from '../../Icons';
import { Heading } from '../../Heading/Heading';

export interface JobImageViewerProps {
  src: string;
  name?: string;
  TeamMemberName?: string;
  mediaType?: 'image' | 'icon';
  onClose: () => void;
  onNext?: () => void;
  onPrevious?: () => void;
  icon?: IconTypes;
  date?: string;
  time?: string;
}

const AlertMask = styled.div`
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  padding: 10px;
  gap: ${(props) => props.theme.GapXl};
  background-color: ${(props) => props.theme.ColorsOverlayAlert};
  display: grid;
  place-items: center;
  z-index: 10;
`;

const Container = styled.div`
  width: auto;
  height: auto;
  width: 790px;
  height: 883px;
  padding: ${(props) => props.theme.SpacingXl};
  gap: ${(props) => props.theme.GapXl};
  display: grid;
  grid-template-rows: auto 1fr auto;
`;

const ImagePanel = styled.div`
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  justify-content: center;
  max-width: 726px;
  max-height: 720px;
  gap: 0;
  position: relative;
`;

const ImageDocHolder = styled.div`
  width: 646px;
  height: 720px;
  gap: 0;
  border-radius: 16px;
  display: grid;
  place-items: center;
`;

const NavigationButton = styled(IconButton)`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: transparent;
  border: 1px solid white;
  border-radius: 140px;
  height: 40px;
  width: 40px;
`;

const Footer = styled.div`
  width: 726px;
  padding: 4px 2px;
  margin-top: 24px;
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  z-index: 4;
`;

const FooterContent = styled.div`
  display: grid;
  grid-template-rows: auto auto auto;
  align-items: center;
  justify-items: center;
  text-align: center;
  margin: 0 20px;
`;

const FooterHeading = styled(Heading)`
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter0};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize6}px;
`;

const NameSubHeading = styled(Heading)`
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  line-height: 16.94px;
`;

const DateAndTimeFooter = styled.div`
  display: grid;
  grid-template-columns: auto auto;
  width: 100%;
  padding-top: 4px;
`;

const DateText = styled.div`
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  text-align: right;
  padding-right: 8px;
`;

const TimeText = styled.div`
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  text-align: left;
  padding-left: 8px;
`;

const StyledIconButton = styled(IconButton)`
  background-color: transparent;
  border: 1px solid white;
  border-radius: 140px;
  height: 40px;
  width: 40px;
`;

const CenteredFooter = styled.div`
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
  align-items: center;
`;

/**
 * JobImageViewer component renders an image or icon along with metadata and navigation controls.
 *
 * @param {JobImageViewerProps} props - The properties for the JobImageViewer component.
 * @returns {JSX.Element} The rendered JobImageViewer component.
 */
export const JobImageViewer: React.FC<JobImageViewerProps> = ({
  src,
  name,
  TeamMemberName,
  mediaType,
  onClose,
  onNext,
  onPrevious,
  icon = 'file-07',
  date,
  time,
}) => {

  console.log('image', src)

  return (
    <AlertMask>
      <Container>
        <ImagePanel>
          {onPrevious && (<NavigationButton icon="chevron-left" onClick={onPrevious} />)}
          
          <ImageDocHolder>
            {src.startsWith('http') ? (
              <img
                src={src}
                alt={name}
                style={{
                  width: '100%',
                  height: '100%',
                  borderRadius: '16px',
                }}
              />
            ) : (
              <Icon
                type={icon}
                size={24}
                width="100%"
                height="100%"
                color="black"
              />
            )}
          </ImageDocHolder>
          {onNext && (<NavigationButton icon="chevron-right" onClick={onNext} />)}
        </ImagePanel>
        <Footer>
        {name ? (
            <>
              {onClose && (<StyledIconButton icon="x-xircle" onClick={onClose} />)}
              <FooterContent>
                <FooterHeading>{name}</FooterHeading>
                <NameSubHeading>{TeamMemberName}</NameSubHeading>
                <DateAndTimeFooter>
                  <DateText>{date}</DateText>
                  <TimeText>{time}</TimeText>
                </DateAndTimeFooter>
              </FooterContent>
            </>
          ) : (
            <CenteredFooter>
              {onClose && (<StyledIconButton icon="x-xircle" onClick={onClose} />)}
            </CenteredFooter>
          )}
        </Footer>
      </Container>
    </AlertMask>
  );
};
