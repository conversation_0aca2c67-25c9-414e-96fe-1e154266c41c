import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { KeyValuePair } from './KeyValuePair';

const meta: Meta<typeof KeyValuePair> = {
  component: KeyValuePair,
  title: 'Components/KeyValuePair',
};
export default meta;

type Story = StoryObj<typeof KeyValuePair>;

export const DefaultKeyValuePair: Story = {
  argTypes: {
    numbering: {
      control: 'boolean',
    },
    index: {
      control: 'number',
    },
    keyName: {
      control: 'text',
    },
    value: {
      control: 'object',
    },
    styleClasses: {
      control: 'text',
    },
  },
  args: {
    numbering: true,
    index: 0,
    keyName: 'Example Key',
    value: 'Example Value',
    styleClasses: 'align-default size-medium color-default text-default',
  },
};
