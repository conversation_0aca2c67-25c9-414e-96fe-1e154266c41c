import React from 'react';
import styled from 'styled-components';
import { from } from 'rxjs';
import { KeyValueList } from '../../../Fragments/KeyValueList/KeyValueList';
import { Heading } from '../../Heading/Heading';
import { svgs } from '../../Icons';

const TeamProfileSectionContainer = styled.div`
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-rows: auto auto 1fr;
  text-align: center;
  font-size: 14px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};;
`;

const FilterSettings = styled.div`
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  gap: 12px;
  font-size: 16.8px;
  color: ${(props) => props.theme.ColorsUtilityColorFocus};
`;

const ModuleTabComponent = styled.div`
  display: grid;
  grid-auto-flow: column;
  gap: 12px;
  align-items: flex-end;
  justify-content: center;
`;

const MobuleTab = styled.div`
  display: grid;
  place-items: center;
  min-width: 58px;
  min-height: 32px;
`;

const Content = styled.div`
  display: grid;
  place-items: center;
  gap: 10px;
  min-width: 58px;
  min-height: 32px;
  color: #a5a5a5;
`;

const TextContainer = styled.div`
  display: grid;
  place-items: center;
  padding: 0px 8px;
`;

const Tab = styled.div`
  position: relative;
  font-weight: 300;
`;

const DividerLine = styled.div`
  align-self: stretch;
  position: relative;
  height: 2px;
`;

const GlowLine = styled.div`
  position: absolute;
  width: 100%;
  top: 0px;
  right: 0px;
  left: 0px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0),
    rgba(17, 138, 178, 0.49) 50%,
    rgba(255, 255, 255, 0)
  );
  height: 2px;
`;

const ExpandIcon = styled.svg`
  width: 24px;
  height: 24px;
  border-radius: 41.36px;
  overflow: hidden;
`;

const Header = styled.div`
  display: grid;
  place-items: center;
//   padding: 0px 0px 16px;
  font-size: 47.3px;
`;

const HeadingComponent = styled.div`
  width: 100%;
  display: grid;
  place-items: center;
  max-width: 920px;
`;

/**
 * `TeamProfileSection` displays a section that includes:
 * - A filter settings area with tabs and an expand icon.
 * - A header with a main title and sub-titles.
 * - Multiple sections displaying key-value lists.
 * 
 * The component uses design tokens for consistent styling and CSS Grid for layout.
 *
 * @component
 * @example
 * return (
 *   <TeamProfileSection />
 * );
 */
const TeamProfileSection: React.FC = () => {
  const data$ = from([
    {
      property1: 'Value1',
      property2: 'Value2',
      property3: 'Value3',
    },
  ]);

  return (
    <TeamProfileSectionContainer>
      <FilterSettings>
        <ModuleTabComponent>
          <MobuleTab>
            <Content>
              <TextContainer>
                <Tab>Overview</Tab>
              </TextContainer>
              {/* <DividerLine>
                <GlowLine />
              </DividerLine> */}
            </Content>
          </MobuleTab>
          <MobuleTab>
            <Content>
              <TextContainer>
                <Tab>Photo</Tab>
              </TextContainer>
            </Content>
          </MobuleTab>
        </ModuleTabComponent>
        <ExpandIcon viewBox={svgs['dots-vertical'].viewBox}>
          {svgs['dots-vertical'].paths.map((path: any, index: any) => (
            <path
              key={index}
              d={path.d}
              stroke={path.stroke}
              strokeWidth={path.strokeWidth}
              strokeLinecap={path.strokeLinecap}
              strokeLinejoin={path.strokeLinejoin}
              fill={path.fill}
            />
          ))}
        </ExpandIcon>
      </FilterSettings>
      <Header>
        <HeadingComponent>
          <Heading type="page-heading">Job Allocation</Heading>
        </HeadingComponent>
      </Header>
      <HeadingComponent>
        <Heading level={4}>Claim Details</Heading>
      </HeadingComponent>
      <KeyValueList
        data$={data$}
        width="auto"
        itemMargin="10px"
        align="default"
        colouredHeading={{
          headingString: 'Coloured Heading',
          headingColour: 'default',
        }}
        colour="default"
        size="medium"
        textTransform="default"
        numbering={false}
      />
      <HeadingComponent>
        <Heading level={4}>On-site Details</Heading>
      </HeadingComponent>
      <KeyValueList
        data$={data$}
        width="auto"
        itemMargin="10px"
        align="default"
        colouredHeading={{
          headingString: 'Coloured Heading',
          headingColour: 'default',
        }}
        colour="default"
        size="medium"
        textTransform="default"
        numbering={false}
      />
      <HeadingComponent>
        <Heading level={4}>Customer Details</Heading>
      </HeadingComponent>
      <KeyValueList
        data$={data$}
        width="auto"
        itemMargin="10px"
        align="default"
        colouredHeading={{
          headingString: 'Coloured Heading',
          headingColour: 'default',
        }}
        colour="default"
        size="medium"
        textTransform="default"
        numbering={false}
      />
    </TeamProfileSectionContainer>
  );
};

export default TeamProfileSection;
