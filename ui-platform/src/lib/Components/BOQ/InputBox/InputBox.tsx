import React, { FunctionComponent, useState, useEffect, useRef } from 'react';
import styled from 'styled-components';

const InputBoxContainer = styled.div`
  // width: 100%;
  position: relative;
  height: auto;
  display: grid;
  grid-template-rows: auto 1fr auto; /* Adjusted to place subHeading below the input box */
  gap: 4px;
  text-align: left;
  font-size: 14px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
`;

const Heading = styled.div`
  font-size: 14px;
  align-self: stretch;
  position: relative;
  font-weight: ${(props) => props.theme.FontWeightsInter1};
`;

const SubHeading = styled.div`
  font-size: ${(props) => props.theme.FontSize2}px;
  color: ${(props) => props.theme.ColorsStrokesGrey};
  margin-top: 15px;
`;

const InputBox1 = styled.div`
  border-radius: 4px;
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  border: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
  display: grid;
  align-items: stretch;
  padding: 5px;
  height: 28px;

`;

const InputHere = styled.textarea`
  width: 100%;
  height: 100%;
  background: none;
  border: none;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  outline: none;
  resize: none; /* Prevents manual resizing */
  font-family: inherit;
  font-size: inherit;
  box-sizing: border-box;
  padding: 8px;
  overflow-y: hidden; /* Hides scroll until needed */
  flex: 1; /* Ensures the textarea takes up the full available space */
`;

export interface InputBoxProps {
  initialValue?: string;
  heading?: string;
  subHeading?: string;
  onInputChange?: (value: string) => void;
}

/**
 * InputBox component with dynamic resizing textarea and optional heading/subheading
 * 
 * @param {InputBoxProps} props - Props to configure the InputBox component
 * 
 * Handle changes to the textarea and update the input value state
 * 
 * @param {React.ChangeEvent<HTMLTextAreaElement>} e - The change event
 */
const InputBox: FunctionComponent<InputBoxProps> = ({
  initialValue = '',
  heading = 'Quote Number',
  subHeading,
  onInputChange,
}) => {
  const [inputValue, setInputValue] = useState(initialValue);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (textAreaRef.current) {
      textAreaRef.current.style.height = 'auto'; // Reset height
      textAreaRef.current.style.height = `${textAreaRef.current.scrollHeight}px`; // Adjust height based on content
    }
  }, [inputValue]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value);
    onInputChange && onInputChange(e.target.value);
  };

  return (
    <InputBoxContainer>
      {heading && <Heading>{heading}</Heading>}
      <InputBox1>
        <InputHere
          ref={textAreaRef}
          value={inputValue}
          onChange={handleChange}
        />
      </InputBox1>
      {subHeading && <SubHeading>{subHeading}</SubHeading>}
    </InputBoxContainer>
  );
};

export default InputBox;
