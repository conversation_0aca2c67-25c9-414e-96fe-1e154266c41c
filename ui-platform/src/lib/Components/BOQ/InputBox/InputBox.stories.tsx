import { Meta, StoryObj } from '@storybook/react';
import InputBox from './InputBox';

const meta: Meta<typeof InputBox> = {
  title: 'BOQ/InputBox',
  component: InputBox,
  argTypes: {
    heading: { control: 'text' },
    subHeading: { control: 'text' },
    initialValue: { control: 'text' },
  },
};

export default meta;

type Story = StoryObj<typeof InputBox>;

export const Default: Story = {
  args: {
    initialValue: '6578',
    heading: 'Invoice Number',
    subHeading: 'Optional Subheading',
  },
};
