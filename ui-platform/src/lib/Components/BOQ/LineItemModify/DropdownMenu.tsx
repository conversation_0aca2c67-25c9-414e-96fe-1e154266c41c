import React, { forwardRef, useRef, useState } from 'react';
import styled from 'styled-components';
import { CreateCustomItem } from '../../../Fragments/CreateCustomItem/CreateCustomItem';
import {
  TextButton,
  TextButtonProps,
} from '../../Buttons/TextButton/TextButton';
import { IconTypes, svgs } from '../../Icons';
import SelectionBox from '../../Inputs/CheckBoxes/CheckBoxMolecule/CheckBoxMolecule';
import { ScrollableContent as Content } from '../../Scrollbar/Scrollbar';
import { useAppStore } from '../../../Engine';

export interface Item {
  id: number;
  description: string;
  quantity?: number;
  compulsoryItem?: boolean;
  unitPrice?: number;
  iconName?: IconTypes;
  dropdownOptions?: Item[];
}

const DropdownMenu = styled.div<{ width?: number; isChild: boolean }>`
  position: absolute;
  top: 45px;
  right: 0px;
  left: 0px;
  border-radius: 4px;
  background-color: ${(props) => props.theme.ColorsButtonColorModuleNavigationActivatedStroke};
  border: 1px solid ${(props) => props.theme.ColorsUtilityColorFocus};
  box-sizing: border-box;
  z-index: 1;
  width: 100%;
  // width: 400px;
  // padding-bottom: 5px;
  overflow: visible;

  div {
    color: ${(props) => props.theme.ColorsStrokesGrey};
    // display: grid;
    align-items: center;
    grid-template-columns: auto 1fr;
    // align-items: self-start;
    // padding: 0.8rem 0.7rem;
    // margin: 10px 18px;
    padding: ${(props) => props.theme.SpacingXs};
    outline: none;
    cursor: pointer;
    white-space: nowrap;
    overflow: visible;
    text-overflow: ellipsis;
  }

  ${(props) =>
    props.isChild &&
    `
    // position: relative;
    top: 0;
    position: absolute;
    z-index: 11;
    overflow: visible;
    right: 0;
    left: unset;
    translate: 100%;
`}
`;

const FilterContainer = styled.div`
  display: grid;
  align-items: center;
  // width: 100%;
  position: relative;
  text-align: start;
  && {
    padding-bottom: 0;
  }
`;

const FilterInput = styled.input<{ width: number }>`
  border: none;
  background-color: unset;
  transition: color 1s ease, background-color 1s ease;
  cursor: text;
  // width: 100%;
  padding: ${(props) => props.theme.SpacingSm};
  outline: none;
  color: ${(props) => props.theme.ColorsStrokesGrey};
`;

const SearchIcon = styled.svg`
  width: 24px;
  height: 24px;
  position: absolute;
  right: 8px;
  z-index: 9;
  cursor: pointer;
`;

const ScrollableContent = styled(Content)`
  max-height: 100px;
  overflow-y: auto;

  & > .simplebar-track.simplebar-vertical {
    width: 4px;
    height: 90%;
  }

  & > .simplebar-track.simplebar-horizontal {
    height: 4px;
    width: 90%;
  }

  & > .simplebar-wrapper {
    padding: 0;
  }
  & > .simplebar-wrapper > .simplebar-mask > .simplebar-offset {
    padding: 0;
  }
  &
    > .simplebar-wrapper
    > .simplebar-mask
    > .simplebar-offset
    > .simplebar-content-wrapper {
    padding-top: 0;
    padding-bottom: 0;
  }
`;

const SecondDropdownMenu = styled(DropdownMenu)`
  position: absolute;
  top: 0px;
  left: 100%;
  //   margin-left: 8px;
  //   width: 150px;
  z-index: 11;
  overflow: visible;
  padding: 8px;
  gap: 4px;
  cursor: pointer;
`;

const ButtonWrapper = styled.div`
  // display: grid;
  // justify-items: center;
  justify-items: end;
  display: block;
  padding: ${(props) => props.theme.SpacingSm} 0;
`;

const DropdownIcon = styled(SearchIcon)<{ isSelected: boolean }>`
  transform: ${({ isSelected }) => (isSelected ? 'rotate(180deg)' : 'none')};
`;
interface Props {
  items: Item[];
  selectedItems: Item[];
  multiSelect?: boolean;
  isSelected?: boolean;
  isChild?: boolean;
  handleItemClick: (item: Item) => void;
  filterPlaceholder?: string;
  searchIcon: any;
  filter: string;
  inputWidth: number;
  handleFilterChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  buttonProps?: TextButtonProps;
  onClose?: () => void;
  createCustomItemButton?: boolean;
}

/**
 * DropdownMenuComponent - A component that displays a dropdown menu with items and filtering capabilities.
 *
 * @param props - The props for the component.
 * @param ref - The ref for the component.
 * @returns The rendered component.
 */
const DropdownMenuComponent = forwardRef(
  (props: Props, ref: React.ForwardedRef<HTMLDivElement>) => {
    const {
      items,
      multiSelect = false,
      handleItemClick,
      handleFilterChange,
      filter,
      selectedItems,
      isSelected = false,
      filterPlaceholder = '|Search',
      searchIcon,
      isChild = false,
      inputWidth,
      buttonProps,
      onClose,
      createCustomItemButton,
    } = props;

    const setBOQState = useAppStore((state: any) => state?.setState);

    const [itemId, setItemId] = useState(-1);
    const [dropdownItems, setDropdownItems] = useState(items);

    const [isCustomItemOpen, setCustomItemOpen] = useState(false);

    const handleOpenCustomItem = () => {
      setCustomItemOpen(true);
      setBOQState({ showCreateCustomItemModal: true });
      // close the dropdown-menu
      onClose && onClose();
    };

    const onItemSelect = (item: Item) => {
      if (item.dropdownOptions) {
        console.log('item inside:', item);

        setItemId((prev) => {
          return prev === item.id ? -1 : item.id;
        });
      }
      handleItemClick(item);
    };

    return (
      <DropdownMenu width={300} ref={ref} isChild={isChild}>
        <FilterContainer>
          <FilterInput
            type="text"
            width={inputWidth}
            value={filter}
            onChange={handleFilterChange}
            placeholder={filterPlaceholder}
          />
          <SearchIcon viewBox={searchIcon.viewBox}>
            <path
              d={searchIcon.paths[0].d}
              stroke={searchIcon.paths[0].stroke}
              strokeLinecap={searchIcon.paths[0].strokeLinecap}
              strokeLinejoin={searchIcon.paths[0].strokeLinejoin}
              fill={searchIcon.paths[0].fill}
            />
          </SearchIcon>
        </FilterContainer>
        <ScrollableContent>
          {dropdownItems.map((item, index) => {
            const icon = item.iconName ? svgs[item.iconName] : undefined;
            return multiSelect ? (
              <SelectionBox
                key={index}
                checked={selectedItems.some((i) => i.id === item.id)}
                name={item.description}
                onChange={() => handleItemClick(item)}
              />
            ) : (
              <FilterContainer key={index}>
                <div
                  onClick={() => onItemSelect(item)}
                  className={item.compulsoryItem ? 'compulsory' : ''}
                >
                  {item.description}
                  {item.dropdownOptions && (
                    <DropdownIcon
                      isSelected={itemId === item.id}
                      viewBox={svgs['chevron-right'].viewBox}
                    >
                      <path
                        d={svgs['chevron-right'].paths[0].d}
                        stroke={
                          isSelected
                            ? '#c4c4c4'
                            : svgs['chevron-right'].paths[0].stroke
                        }
                        strokeLinecap={
                          svgs['chevron-right'].paths[0].strokeLinecap
                        }
                        strokeLinejoin={
                          svgs['chevron-right'].paths[0].strokeLinejoin
                        }
                        fill={svgs['chevron-right'].paths[0].fill}
                      />
                    </DropdownIcon>
                  )}
                  {!item.dropdownOptions && icon && (
                    <SearchIcon viewBox={icon.viewBox}>
                      <path
                        d={icon.paths[0].d}
                        stroke={icon.paths[0].stroke}
                        strokeLinecap={(icon.paths[0] as any).strokeLinecap}
                        strokeLinejoin={(icon.paths[0] as any).strokeLinejoin}
                        fill={icon.paths[0].fill}
                      />
                    </SearchIcon>
                  )}
                </div>
                {itemId === item.id && item.dropdownOptions && (
                  //   <SecondDropdownMenu width={100} isChild={true}>
                  <DropdownMenuComponent
                    isChild
                    inputWidth={100}
                    ref={ref}
                    items={item.dropdownOptions || []}
                    selectedItems={item.dropdownOptions || []}
                    handleItemClick={handleItemClick}
                    handleFilterChange={handleFilterChange}
                    searchIcon={svgs['search-sm']}
                    filter={filter}
                    buttonProps={buttonProps}
                  />
                  //   </SecondDropdownMenu>
                )}
              </FilterContainer>
            );
          })}
        </ScrollableContent>
        {createCustomItemButton && (
          <ButtonWrapper>
            <TextButton
              btnValue="Create Custom Item"
              actiontype="alternative"
              size="small"
              onClick={handleOpenCustomItem}
            />
          </ButtonWrapper>
        )}
      </DropdownMenu>
    );
  }
);

export default DropdownMenuComponent;
