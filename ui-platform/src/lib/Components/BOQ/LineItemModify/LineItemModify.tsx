import React, { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import { IconTypes, svgs } from '../../Icons/svgs';
import DropdownMenuComponent from './DropdownMenu';
import { TextButtonProps } from '../../Buttons/TextButton/TextButton';
import { useAppStore } from '../../../Engine';

const LineItemModifyContainer = styled.div`
  gap: 1px;
  transition: color 1s ease, background-color 1s ease;
  position: relative;
  width: 100%;
`;

const LineItemModifyItem = styled.div`
  display: grid;
  align-items: center;
  justify-content: center;
  min-width: 95px;
  background-color: ${(props) => props.theme.ColorsStrokesInverse};
  border-right: 1px solid ${(props) => props.theme.ColorsStrokesInverse};
  cursor: pointer;
  grid-template-columns: 1fr;

  &:last-of-type {
    border-right: none;
  }
`;

const ActiveZone = styled.div`
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: self-start;
  width: 100%;

  &.left {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }
`;

const SearchItemsContainer = styled.div`
  position: relative;
  display: grid;
  align-items: center;
  // width: 60%;
  /* width: 400px; */
  width: 100%;
`;

const SearchItems = styled.input<{ dropdownOpen: boolean }>`
  height: 32px;
  z-index: 8;
  outline: unset;
  text-align: left;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  border: 1px solid
    ${({ dropdownOpen }) => (dropdownOpen ? '#118ab2' : '#8B8B8B')};
  box-sizing: border-box;
  width: 100%;
  padding: ${(props) => props.theme.SpacingSm};
  gap: ${(props) => props.theme.GapSm};
  border-radius: 4px;
  overflow: visible;
  text-overflow: ellipsis;
  white-space: nowrap;

  ::placeholder {
    color: ${({ dropdownOpen }) => (dropdownOpen ? '#118ab2' : '#8B8B8B')};
    transition: color 0.3s ease;
  }
`;

const DropdownIcon = styled.svg<{ isSelected: boolean }>`
  width: 24px;
  height: 24px;
  position: absolute;
  right: 8px;
  z-index: 9;
  cursor: pointer;
`;

export interface Item {
  id: number;
  description: string;
  quantity?: number;
  compulsoryItem?: boolean;
  unitPrice?: number;
  iconName?: IconTypes;
  dropdownOptions?: Item[];
}

interface LineItemModifyProps {
  items: Item[];
  onSelect?: (item: Item | Item[]) => void;
  isSelected?: boolean;
  multiSelect?: boolean;
  iconType?: string;
  placeholder?: string;
  buttonProps?: TextButtonProps;
  createCustomItemButton?: boolean;
}

/**
 * LineItemModify component provides a searchable and selectable list of items with dropdown functionality.
 *
 * @param {LineItemModifyProps} props - The props for the component.
 * @returns {React.FC<LineItemModifyProps>} - The rendered component.
 */
const LineItemModify: React.FC<LineItemModifyProps> = ({
  items,
  onSelect,
  isSelected = false,
  multiSelect = false,
  placeholder = 'Search billable items...',
  buttonProps,
  createCustomItemButton,
}) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [sideDropdownOpen, setSideDropdownOpen] = useState<
    'chevron-right' | 'chevron-left'
  >('chevron-right');
  const [filter, setFilter] = useState('');
  const [filteredItems, setFilteredItems] = useState<Item[]>([]);
  const [selectedItems, setSelectedItems] = useState<Item[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputContainerRef = useRef<HTMLDivElement>(null);

  const setBOQState = useAppStore((state: any) => state?.setState);

  useEffect(() => {
    console.log('Items updated check:', items);
    setFilteredItems(items);
  }, [items]);

  useEffect(() => {
    setBOQState({ showCreateCustomItemModal: false });

    const updateDropdownWidth = () => {
      if (inputContainerRef.current) {
        const width = inputContainerRef.current.offsetWidth;
        if (dropdownRef.current) {
          dropdownRef.current.style.width = `${width}px`;
        }
      }
    };

    updateDropdownWidth();
    window.addEventListener('resize', updateDropdownWidth);

    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputContainerRef.current &&
        !inputContainerRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
        setSideDropdownOpen('chevron-right');
      }
    };
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      window.removeEventListener('resize', updateDropdownWidth);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilter(e.target.value);
    setFilteredItems(
      items.filter((item) =>
        item.description.toLowerCase().includes(e.target.value.toLowerCase())
      )
    );
  };

  const handleItemClick = (item: Item) => {
    console.log('Item clicked:', item);
    if (multiSelect) {
      setSelectedItems((prev) => {
        const isSelected = prev.some((i) => i.id === item.id);
        if (isSelected) {
          return prev.filter((i) => i.id !== item.id);
        }
        return [...prev, item];
      });
      return;
    }

    if (item.dropdownOptions) {
      setSideDropdownOpen('chevron-right');
      return;
    }

    setDropdownOpen(false);
    onSelect && onSelect(item);
    setFilter('');
  };

  // useEffect(() => {
  //   console.log('multiSelect && onSelect');
  //   if (multiSelect && onSelect) {
  //     onSelect(selectedItems);
  //   }
  // }, [selectedItems, onSelect, multiSelect]);

  const handleInputClick = () => {
    setDropdownOpen((prev) => !prev);
    setSideDropdownOpen('chevron-right');
  };

  const renderSelectedItems = () => {
    if (multiSelect && selectedItems.length > 0) {
      return selectedItems.map((item) => item.description).join(', ');
    }
    return '';
  };

  const iconType = dropdownOpen ? 'chevron-up' : 'chevron-down';
  const icon = svgs[iconType];

  return (
    <LineItemModifyContainer>
      <LineItemModifyItem>
        <ActiveZone className="left" ref={inputContainerRef}>
          <SearchItemsContainer>
            <SearchItems
              type="text"
              dropdownOpen={dropdownOpen}
              onClick={handleInputClick}
              placeholder={renderSelectedItems() || placeholder}
              readOnly
            />
            <DropdownIcon
              isSelected={isSelected}
              viewBox={icon.viewBox}
              onClick={handleInputClick}
            >
              <path
                d={icon.paths[0].d}
                stroke={isSelected ? '#c4c4c4' : icon.paths[0].stroke}
                strokeLinecap={icon.paths[0].strokeLinecap}
                strokeLinejoin={icon.paths[0].strokeLinejoin}
                fill={icon.paths[0].fill}
              />
            </DropdownIcon>
          </SearchItemsContainer>
          {dropdownOpen && (
            <DropdownMenuComponent
              inputWidth={inputContainerRef.current?.offsetWidth || 0}
              ref={dropdownRef}
              items={filteredItems}
              selectedItems={selectedItems}
              handleItemClick={handleItemClick}
              handleFilterChange={handleFilterChange}
              searchIcon={svgs['search-sm']}
              filter={filter}
              multiSelect={multiSelect}
              buttonProps={buttonProps}
              onClose={() => {
                setDropdownOpen(false);
              }}
              createCustomItemButton={createCustomItemButton}
            />
          )}
        </ActiveZone>
      </LineItemModifyItem>
    </LineItemModifyContainer>
  );
};

export default LineItemModify;
