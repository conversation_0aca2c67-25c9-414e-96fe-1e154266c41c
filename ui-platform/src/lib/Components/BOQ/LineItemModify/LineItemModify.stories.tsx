import React from 'react';
import { Meta, StoryObj } from '@storybook/react';
import LineItemModify from './LineItemModify';
import { TextButtonProps } from '../../Buttons/TextButton/TextButton';

const meta: Meta<typeof LineItemModify> = {
  title: 'BOQ/LineItemModify',
  component: LineItemModify,
  argTypes: {
    multiSelect: { control: 'boolean', defaultValue: false },
    buttonProps: {
      control: {
        type: 'object',
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof LineItemModify>;

export const SingleSelect: Story = {
  args: {
    items: [
      {
        id: 1,
        description: 'Certificate of Compliance',
        quantity: 1,
        unitPrice: 100,
      },
      { id: 2, description: 'Labour-Geyser', quantity: 2, unitPrice: 200 },
      { id: 3, description: 'Fittings', quantity: 5, unitPrice: 50 },
    ],
    placeholder: 'Search billable items...',
    multiSelect: false,
  },
};

export const MultiSelect: Story = {
  args: {
    items: [
      {
        id: 1,
        description: 'Certificate of Compliance',
        quantity: 1,
        unitPrice: 100,
        iconName: 'search-sm',
        dropdownOptions: [
          {
            id: 1,
            description: 'Certificate of Compliance',
            quantity: 1,
            unitPrice: 100,
          },
        ],
      },
      {
        id: 2,
        description: 'Labour-Geyser',
        quantity: 2,
        unitPrice: 200,
        iconName: 'search-sm',
      },
      { id: 3, description: 'Fittings ttt', quantity: 5, unitPrice: 50 },
    ],
    placeholder: 'Select multiple items...',
    multiSelect: true,
  },
};

// export const WithButton: Story = {
//   args: {
//     items: [
//       { id: 1, description: 'Item 1' },
//       { id: 2, description: 'Item 2' },
//       { id: 3, description: 'Item 3' },
//     ],
//     buttonProps: {
//       btnValue: 'create custom item',
//       size: 'large',
//       actiontype: 'alternative',
//       onClick: () => alert('Button Clicked!'),
//     } as TextButtonProps,
//   },
// };

export const WithButton: Story = {
  render: (args) => {
    
   

    return (
      <>
        <LineItemModify {...args}  />
        
      </>
    );
  },
  args: {
    items: [
      { id: 1, description: 'Item 1' },
      { id: 2, description: 'Item 2' },
      { id: 3, description: 'Item 3' },
    ],
    buttonProps: {
      btnValue: 'Create Custom Item',
      size: 'large',
      actiontype: 'alternative',
    } as TextButtonProps,
  },
};

