import { Meta, <PERSON> } from '@storybook/react';
import CombinedComponent from './CompleteBOQ';

// Sample data for the story
const dataTemp = [
  {
    id: 2,
    name: 'Electric Geyser Consignment',
    compulsory_items: [775, 769],
    optional_items: [1072, 774],
  },
];

const getItems = [
  { id: 775, name: 'TEST', price: 283.8 },
  { id: 1072, name: 'fittings', price: 183.0 },
  { id: 774, name: 'Certificate of Compliance', price: 193.0 },
  { id: 769, name: 'Geyser labour', price: 2183.0 },
];

// Preparing compulsory and optional items
interface Item {
  id: number;
  description: string | undefined;
  quantity: number;
  compulsoryItem: boolean;
  unitPrice: number | undefined;
}

let compulsory: Item[] = [];
let optional: Item[] = [];

dataTemp.forEach((item) => {
  const compulsory_items = item.compulsory_items
    .map((productId) => {
      const obj = getItems.find((product) => product.id === productId);
      if (!obj) return null;
      return {
        id: obj.id,
        description: obj?.name,
        quantity: 1,
        compulsoryItem: true,
        unitPrice: obj?.price,
      };
    })
    .filter((item) => item !== null) as Item[];

  const optional_items = item.optional_items
    .map((productId) => {
      const obj = getItems.find((product) => product.id === productId);
      if (!obj) return null;
      return {
        id: obj.id,
        description: obj?.name,
        quantity: 1,
        compulsoryItem: false,
        unitPrice: obj?.price,
      };
    })
    .filter((item) => item !== null) as Item[];

  compulsory = [...compulsory, ...compulsory_items];
  optional = [...optional, ...optional_items];
});

const meta: Meta<typeof CombinedComponent> = {
  title: 'BOQ/Combined',
  component: CombinedComponent,
  parameters: {
    componentSubtitle: 'A comprehensive BOQ component for managing compulsory and optional items.',
    layout: 'padded',
    docs: {
      description: {
        component: `
<div class="documentation-wrapper" style="max-width: 1200px; margin: 0 auto; padding: 20px; font-family: system-ui, -apple-system, sans-serif;">
  <div style="background: linear-gradient(90deg, #2C5282, #2B6CB0); padding: 2px; border-radius: 8px; margin-bottom: 30px;">
    <div style="background: white; padding: 20px; border-radius: 6px;">
      <h1 style="color: #2C5282; margin-top: 0;">Complete BOQ Component</h1>
      <p style="color: #4A5568; font-size: 1.1em; line-height: 1.5;">
        This component organizes and displays compulsory and optional items for a Bill of Quantities (BOQ).
      </p>
    </div>
  </div>

  <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
    <div style="background: #EDF2F7; padding: 20px; border-radius: 8px;">
      <h3 style="color: #2C5282; margin-top: 0;">🎯 Key Features</h3>
      <ul style="list-style-type: none; padding: 0; margin: 0;">
        <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">✓ Segregation of compulsory and optional items</li>
        <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">✓ Dynamic pricing and quantity management</li>
        <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">✓ Configurable column names</li>
      </ul>
    </div>

    <div style="background: #EDF2F7; padding: 20px; border-radius: 8px;">
      <h3 style="color: #2C5282; margin-top: 0;">⚡ Component Structure</h3>
      <ul style="list-style-type: none; padding: 0; margin: 0;">
        <li style="margin-bottom: 8px;">🔷 LineItemsTable</li>
        <li style="margin-bottom: 8px;">🔷 InvoiceSummary</li>
        <li style="margin-bottom: 8px;">🔷 InvoiceSummaryNotes</li>
      </ul>
    </div>
  </div>
</div>`
      },
    },
  },
};

export default meta;

const Template: Story<{
  lineItemsTable: any;
  invoiceSummaryNotes: any;
  invoiceSummary: any;
}> = (args) => <CombinedComponent {...args} />;

export const CombinedStory = Template.bind({});
CombinedStory.args = {
  lineItemsTable: {
    compulsoryItems: compulsory,
    optionalItems: optional,
    columnNames: {
      description: 'Description',
      quantity: 'Quantity',
      unitPrice: 'Unit Price',
      total: 'Total',
    },
  },
  invoiceSummaryNotes: {
    notes: 'These are some preloaded notes from an endpoint.',
  },
  invoiceSummary: {
    vat: 15,
  },
};
