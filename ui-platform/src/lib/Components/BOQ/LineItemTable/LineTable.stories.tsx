import React from 'react';
import { Meta, Story } from '@storybook/react';
import LineItemsTable, { LineItemsTableProps } from './LineItemTable';

export default {
  title: 'BOQ/LineItemsTable',
  component: LineItemsTable,
  argTypes: {
    columnNames: {
      control: 'object',
      defaultValue: {
        description: 'Description',
        quantity: 'Quantity',
        unitPrice: 'Unit Price',
        total: 'Total',
      },
    },
  },
} as Meta;

const Template: Story<LineItemsTableProps> = (args) => (
  <LineItemsTable {...args} />
);

const dataTemp = [
  {
    id: 2,
    name: 'Electric Geyser Consignment',
    skills: [1],
    compulsory_items: [775, 769],
    optional_items: [1072, 774],
    type: 'consignment',
    claim_type: [],
    includes_after_hours_items: false,
    allow_custom_items: false,
  },
];

const getItems = [
  {
    id: 775,
    name: 'TEST',
    price: 283.80,
    measurement: '100l',
    item_type: 2,
    is_custom_items: true,
    is_after_hours: false,
    skill_categories: [
      {
        id: 15,
        name: 'Custom',
      },
      {
        id: 5,
        name: 'Demolition',
      },
    ],
    year: 2022,
  },
  {
    id: 1072,
    name: 'fittings',
    price: 183.0,
    measurement: '100l',
    item_type: 2,
    is_custom_items: true,
    is_after_hours: false,
    skill_categories: [
      {
        id: 15,
        name: 'Custom',
      },
      {
        id: 5,
        name: 'Demolition',
      },
    ],
    year: 2022,
  },
  {
    id: 774,
    name: 'Certificate of Compliance',
    price: 193.0,
    measurement: '100l',
    item_type: 2,
    is_custom_items: true,
    is_after_hours: false,
    skill_categories: [
      {
        id: 15,
        name: 'Custom',
      },
      {
        id: 5,
        name: 'Demolition',
      },
    ],
    year: 2022,
  },
  {
    id: 769,
    name: 'Geyser labour',
    price: 2183.00,
    measurement: '100l',
    item_type: 2,
    is_custom_items: true,
    is_after_hours: false,
    skill_categories: [
      {
        id: 15,
        name: 'Custom',
      },
      {
        id: 5,
        name: 'Demolition',
      },
    ],
    year: 2022,
  },
];

interface Item {
  id: number;
  description: string | undefined;
  quantity: number;
  compulsoryItem: boolean;
  unitPrice: number | undefined;
}

// const ts = [{
//   description: 'Item 1',
//   quantity: 2,
//   compulsory_item: true,
//   unitPrice: 50,
// }]

let compulsory: Item[] = [];
let optional: Item[] = [];

dataTemp.forEach((item) => {
  const compulsory_items = item.compulsory_items
    .map((productId) => {
      const obj = getItems.find((product) => product.id === productId);
      if (!obj) return false;
      return {
        id: obj.id,
        description: obj?.name,
        quantity: 1,
        compulsoryItem: true,
        unitPrice: obj?.price,
      };
    })
    .filter((item) => item) as Item[];

  const optional_items = item.optional_items
    .map((productId) => {
      const obj = getItems.find((product) => product.id === productId);
      if (!obj) return false;
      return {
        id: obj.id,
        description: obj?.name,
        quantity: 1,
        compulsoryItem: false,
        unitPrice: obj?.price,
      };
    })
    .filter((item) => item) as Item[];

  compulsory = [...compulsory, ...compulsory_items];
  optional = [...optional, ...optional_items];
});

console.log('optional: ', optional);

export const Default = Template.bind({});
Default.args = {
  compulsoryItems: compulsory,
  optionalItems: optional,
  columnNames: {
    description: 'Description',
    quantity: 'Quantity',
    unitPrice: 'Unit Price',
    total: 'Total',
  },
  onItemsChange: (items: any) => console.log(items),
};
