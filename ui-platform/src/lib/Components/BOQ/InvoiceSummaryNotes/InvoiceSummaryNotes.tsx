import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

const Form = styled.form`
  width: 90%;
  display: grid;
  grid-template-columns: 1fr;
  grid-gap: 10px;
`;

const Label = styled.label`
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter1};
`;

const TextareaWrapper = styled.div`
  position: relative;
`;

const Textarea = styled.textarea`
  resize: none;
  outline: none;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  height: 56px;
  transition: all 1s ease;
  line-height: 1.5rem;
  width: 100%;
  border-radius: 4px;
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  border: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
  padding: 8px;
  text-align: left;
`;

export interface InvoiceSummaryNotesProps {
  notes?: string;
  onNotesChange?: (value: string) => void;
}

/**
   * <PERSON>les changes to the textarea content.
   * @param {Object} props - The component props.
   * @param {string} [props.notes] - The initial notes value.
   * 
   * @returns {React.ReactElement} The rendered invoice summary notes form.
   * 
   * @param {React.ChangeEvent<HTMLTextAreaElement>} event - The textarea change event.
   */
const InvoiceSummaryNotes: React.FC<InvoiceSummaryNotesProps> = ({ notes, onNotesChange }) => {
  const [noteContent, setNoteContent] = useState(notes || '');

  useEffect(() => {
    if (notes) {
      setNoteContent(notes);
    }
  }, [notes]);

  const handleChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { value } = event.target;
    setNoteContent(value);
    onNotesChange && onNotesChange(value);
  };

  return (
    <Form className="invoice-summary__notes" noValidate>
      <Label>Notes</Label>
      <TextareaWrapper>
        <Textarea
          maxLength={600}
          placeholder=""
          value={noteContent}
          onChange={handleChange}
        />
      </TextareaWrapper>
    </Form>
  );
};

export default InvoiceSummaryNotes;
