import React from 'react';
import { Meta, StoryObj } from '@storybook/react';
import InvoiceSummaryNotes, { InvoiceSummaryNotesProps } from './InvoiceSummaryNotes'; 

const meta: Meta<typeof InvoiceSummaryNotes> = {
  title: 'BOQ/InvoiceSummaryNotes',
  component: InvoiceSummaryNotes,
  parameters: {
  },
};

export default meta;

type Story = StoryObj<typeof InvoiceSummaryNotes>;

export const Default: Story = {
  args: {

  },
};

export const WithNotes: Story = {
  args: {
    notes: '',
  } as InvoiceSummaryNotesProps,
};
