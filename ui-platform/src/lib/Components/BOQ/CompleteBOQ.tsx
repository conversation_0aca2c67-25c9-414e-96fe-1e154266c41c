import React, { useState, useEffect } from 'react';
import LineItemsTable, {
  LineItem,
  LineItemsTableProps,
} from './LineItemTable/LineItemTable';
import InvoiceSummaryNotes, {
  InvoiceSummaryNotesProps,
} from './InvoiceSummaryNotes/InvoiceSummaryNotes';
import InvoiceSummary, {
  InvoiceSummaryProps,
} from './InvoiceSummary/InvoiceSummary';
import styled from 'styled-components';
import DateComponent from './DateComponent/DateComponent';
import InputBox from './InputBox/InputBox';

const Wrapper = styled.div`
  display: grid;
  justify-content: center;
  padding: 40px;
`;

const Container = styled.div`
  gap: 20px;
  max-width: 1072px;
  width: 100%;
`;

const TableContainer = styled.div`
  margin-top: 8px;
  flex: 1;
`;

const BottomContainer = styled.div`
  display: flex;
  justify-content: space-between;
`;

const NotesContainer = styled.div`
  flex: 1;
`;

const SummaryContainer = styled.div`
  flex: 1;
  text-align: right;
`;

const DatePickerInputContainer = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 20px;
`;

const DatePickerContainer = styled.div`
  flex: 1;
  text-align: left;
`;

const InvoiceInput = styled.div`
  flex: 1;
`;

interface CombinedProps {
  lineItemsTable: LineItemsTableProps;
  invoiceSummaryNotes: InvoiceSummaryNotesProps;
  invoiceSummary: InvoiceSummaryProps;
}

/**
 * Renders a combination of components for a BOQ: LineItemsTable, InvoiceSummaryNotes, InvoiceSummary, DatePicker, and InvoiceInput.
 * It dynamically calculates the subtotal from LineItemsTable.
 *
 * @param {CombinedProps} props - Contains lineItemsTable, invoiceSummaryNotes, invoiceSummary, with optional layout.
 * @return {JSX.Element} The rendered components with the specified layout.
 * 
 * Handles the change of the items in the LineItemsTable.
 * It updates the items state and recalculates the subtotal.
 * @param {LineItem[]} updatedItems - The updated items.
 */
const CombinedComponent: React.FC<CombinedProps> = (props) => {
  const [items, setItems] = useState<LineItem[]>([
    ...props.lineItemsTable.compulsoryItems,
    ...props.lineItemsTable.optionalItems,
  ]);
  const [subTotal, setSubTotal] = useState(0);

  useEffect(() => {
    const newSubTotal = items.reduce(
      (acc, item) => acc + (item.unitPrice || 0) * (item.quantity || 0),
      0
    );
    setSubTotal(newSubTotal);
  }, [items]);

  const handleItemChange = (updatedItems: LineItem[]) => {
    setItems(updatedItems);
    const newSubTotal = updatedItems.reduce(
      (acc, item) => acc + (item.unitPrice || 0) * (item.quantity || 0),
      0
    );
    setSubTotal(newSubTotal);
  };

  return (
    <Wrapper>
      <Container>
        <DatePickerInputContainer>
          <DatePickerContainer>
            <DateComponent />
          </DatePickerContainer>
          <InvoiceInput>
            <InputBox />
          </InvoiceInput>
        </DatePickerInputContainer>
        <TableContainer>
          <LineItemsTable
            {...props.lineItemsTable}
            items={items}
            onItemsChange={handleItemChange}
          />
        </TableContainer>
        <BottomContainer>
          <NotesContainer>
            <InvoiceSummaryNotes {...props.invoiceSummaryNotes} />
          </NotesContainer>
          <SummaryContainer>
            <InvoiceSummary {...props.invoiceSummary} subTotal={subTotal} />
          </SummaryContainer>
        </BottomContainer>
      </Container>
    </Wrapper>
  );
};

export default CombinedComponent;
