import React from 'react';
import { Meta, Story } from '@storybook/react';
import InvoiceSummary, { InvoiceSummaryProps } from './InvoiceSummary';
import styled from 'styled-components';

const StyledWrapper = styled.div`
  height: 135px !important; 
  min-height: 135px !important; 
`;

export default {
  title: 'BOQ/InvoiceSummary',
  component: InvoiceSummary,
  parameters: {
    docs: {
      description: {
        component: `### Invoice Summary

The \`InvoiceSummary\` component provides a summary of invoice details, including:

- **Subtotal**: The total cost before tax.
- **VAT**: The percentage of VAT applied.
- **Excess Amount** (Optional): Any additional amount deducted from the total due.

This component is designed to handle calculations dynamically and present clear financial data.
        `,
      },
    },
  },
  argTypes: {
    subTotal: {
      description: 'The total amount before VAT is applied.',
      control: { type: 'number' },
    },
    vat: {
      description: 'The VAT percentage applied to the subtotal.',
      control: { type: 'number' },
    },
    excessAmount: {
      description: 'An optional amount deducted from the total due.',
      control: { type: 'number' },
    },
  },
} as Meta;

const Template: Story<InvoiceSummaryProps> = (args) => (
  <StyledWrapper>
    <InvoiceSummary {...args} />
  </StyledWrapper>
);

export const Default = Template.bind({});
Default.args = {
  subTotal: 544.83,
  vat: 15,
};

Default.parameters = {
  docs: {
    description: {
      story: 'This is the default usage of the `InvoiceSummary` component, showing subtotal and VAT.',
    },
  },
};

export const WithExcess = Template.bind({});
WithExcess.args = {
  subTotal: 1000.0,
  vat: 15,
  excessAmount: 50.0,
};

WithExcess.parameters = {
  docs: {
    description: {
      story: 'An example with an additional excess amount applied, showing the balance due.',
    },
  },
};
