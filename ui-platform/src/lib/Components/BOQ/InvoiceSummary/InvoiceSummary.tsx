import React from 'react';
import styled from 'styled-components';

export interface InvoiceSummaryProps {
  subTotal: number;
  vat: number;
  excessAmount?: number;
}

const InvoiceSummaryTotals = styled.div`
  position: relative;
  top: 42px;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 183px;
  gap: 16px;
  text-align: right;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
`;

const Descriptors = styled.div`
  display: grid;
  grid-template-rows: repeat(auto, auto);
  gap: 0;
  justify-items: end;
  align-items: center;
`;

const Amounts = styled.div`
  border: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
  border-radius: 4px;
  box-sizing: border-box;
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  padding: 8px;
  display: grid;
  grid-template-rows: repeat(auto, auto);
  gap: 16px;
  justify-items: center;
  align-items: center;
  text-align: center;
`;

const formatCurrency = (amount: number) => {
  return `R ${amount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`;
};

/**
 * InvoiceSummary component to display a breakdown of subtotal, VAT, and optionally excess amounts.
 *
 * @param {InvoiceSummaryProps} props - The props for configuring the component
 * @returns {React.FC} The rendered invoice summary
 */
const InvoiceSummary: React.FC<InvoiceSummaryProps> = ({
  subTotal,
  vat,
  excessAmount,
}) => {
  const vatAmount = (subTotal * vat) / 100;
  const total = subTotal + vatAmount;
  const balanceDue = total - (excessAmount || 0);

  return (
    <InvoiceSummaryTotals>
      <Descriptors>
        <div>Subtotal</div>
        <div>Add: VAT &#64; {vat}%</div>
        {/* {excessAmount !== undefined && <div>Less Excess</div>} */}
        {/* <div>{excessAmount !== undefined ? 'Balance Due' : 'Total Due'}</div> */}
        <div>Less Excess</div>
        <div>Balance Due</div>
      </Descriptors>
      <Amounts>
        <div>{formatCurrency(subTotal)}</div>
        <div>{formatCurrency(vatAmount) || 15}</div>
        {/* {excessAmount !== undefined && (
          <div>{formatCurrency(excessAmount)}</div>
        )} */}
        <div>{formatCurrency(excessAmount || 0)}</div>
        <div>{formatCurrency(balanceDue)}</div>
      </Amounts>
    </InvoiceSummaryTotals>
  );
};

export default InvoiceSummary;
