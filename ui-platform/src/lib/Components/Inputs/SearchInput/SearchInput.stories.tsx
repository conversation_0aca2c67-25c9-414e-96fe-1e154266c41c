import type { <PERSON>a, StoryObj } from '@storybook/react';
import React from 'react'
import { SearchInput, ISearchInputProps } from './SearchInput';

const meta: Meta<typeof SearchInput> = {
  component: SearchInput,
  title: 'Components/Inputs/SearchInput',
};
export default meta;

type Story = StoryObj<typeof SearchInput>;

const SearchInputWrapper: React.FC<ISearchInputProps>= (args) => {
    const [value, setValue] = React.useState('');
  
    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      setValue(event.target.value);
    };
  
    return (
      <div>
        <SearchInput {...args} onChange={handleChange} />
        <p>Current Value: {value}</p>
      </div>
    );
  };

export const Overview: Story = {
  args: {
      placeholder: "Search..."
  },
  render: (args) => <SearchInputWrapper {...args} />
};
