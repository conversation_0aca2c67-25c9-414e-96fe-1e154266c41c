import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Switch } from './Switch';
import { background } from '@storybook/theming';

const meta: Meta<typeof Switch> = {
  component: Switch,
  title: 'Components/Inputs/Switch',
};
export default meta;
type Story = StoryObj<typeof Switch>;

export const Overview: Story = {
  args: {
    background: 'primary',
    name: 'switch',
    label: '',
  },
};
