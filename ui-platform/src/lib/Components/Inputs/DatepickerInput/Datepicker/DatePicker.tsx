import { useMemo, useState } from 'react';
import {
  DateRang<PERSON>,
  DayPicker,
  Matcher,
} from 'react-day-picker';
import 'react-day-picker/dist/style.css';
import styled from 'styled-components';

export interface IDatePickerProps {
  mode?: any;
  selected?: Date | DateRange | Date[];
  numberOfMonths?: number;
  onSelect?: (date?: Date | DateRange | Date[]) => void;
  disablePast?: boolean;
  disableFuture?: boolean;
  disabledDates?: string[];
  weekendSelectable?: boolean;
}

const StyledDayPicker = styled(DayPicker)<{ selectedColor?: string }>`
  background-color: ${(props) =>
    props?.theme.ColorsButtonColorModuleActionsPrimary};
  border-radius: ${(props) => props?.theme.RadiusXs};
  padding: ${(props) => props?.theme.SpacingLg};
  width: max-content;

  .rdp-month {
  }

  /* .rdp-table {
    visibility: collapse;
  } */

  .rdp-button_reset {
    color: ${(props) => props?.theme.ColorsIconColorTertiary};
  }

  .rdp-day_selected {
    background-color: ${(props) =>
      props?.theme.ColorsUtilityColorFocus} !important;
    color: #fff;
  }
`;

/**
 * Renders a date picker component with customizable options.
 *
 * @param {IDatePickerProps} props - The properties for the date picker.
 * @param {'single' | 'multiple' | 'range'} props.mode - The mode of the date picker ('single' or 'range').
 * @param {Date | Date[] | DateRange | undefined} props.selected - The selected date or date range.
 * @param {(date: Date | Date[] | DateRange | undefined) => void} props.onSelect - The callback function when a date is selected.
 * @param {number} [props.numberOfMonths=1] - The number of months to display in the date picker.
 * @param {boolean} [props.disablePast=true] - Whether to disable past dates.
 * @param {boolean} [props.disableFuture=false] - Whether to disable future dates.
 * @param {string[]} [props.disabledDates=[]] - An array of disabled dates in string format.
 * @param {boolean} [props.weekendSelectable=false] - Whether weekends are selectable.
 * @return {JSX.Element} The rendered date picker component.
 */
export const DatePicker = ({
  mode = 'single',
  selected,
  onSelect,
  numberOfMonths = 1,
  disablePast = true,
  disableFuture = false,
  disabledDates = [],
  weekendSelectable = false,
}: IDatePickerProps) => {
  const [selectedDate, setSelectedDate] = useState<
    Date | Date[] | DateRange | undefined
  >(selected);

  const weekendDisabled = useMemo(() => {
    if (!weekendSelectable) {
      return { dayOfWeek: [0, 6] };
    }
  }, [weekendSelectable]);

  const pastDisabled = useMemo(() => {
    if (disablePast) {
      return { before: new Date() };
    }
  }, [disablePast]);

  const futureDisabled = useMemo(() => {
    if (disableFuture) {
      return { after: new Date() };
    }
  }, [disableFuture]);

  const disabledDays = useMemo(() => {
    return disabledDates.map((date) => new Date(date));
  }, [disabledDates]);

  const disabledMatchers = useMemo(() => {
    const matchers = [
      weekendDisabled,
      pastDisabled,
      futureDisabled,
      ...(disabledDays || []),
    ].filter((item) => !!item);
    return matchers;
  }, [weekendDisabled, pastDisabled, futureDisabled, disabledDays]);

  const handleSelect = (date: any) => {
    setSelectedDate(date);
    if (onSelect) {
      onSelect(date);
    }
  };

  return (
    <StyledDayPicker
      mode={mode}
      selected={selectedDate}
      disabled={disabledMatchers as Matcher[]}
      onSelect={handleSelect}
      numberOfMonths={numberOfMonths}
    />
  );
};
