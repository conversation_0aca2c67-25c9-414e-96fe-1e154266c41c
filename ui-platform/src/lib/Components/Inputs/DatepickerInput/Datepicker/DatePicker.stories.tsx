import type { Meta, StoryObj } from '@storybook/react';
import { DatePicker } from './DatePicker';

const meta: Meta<typeof DatePicker> = {
  component: DatePicker,
  title: 'Components/Inputs/DatePicker',
};
export default meta;
type Story = StoryObj<typeof DatePicker>;

export const Overview: Story = {
  args: {
    mode: 'single',
    selected: new Date(),
    onSelect: (date) => console.log('Selected Date', date),
  },
};

export const EnabledWeekeds: Story = {
  args: {
    mode: 'single',
    numberOfMonths: 2,
    weekendSelectable: true,
  },
};

export const PastDisabled: Story = {
  args: {
    mode: 'single',
    numberOfMonths: 2,
    disablePast: true,
    disableFuture: false,
  },
};

export const FutureDisabled: Story = {
  args: {
    mode: 'single',
    numberOfMonths: 2,
    disableFuture: true,
    disablePast: false,
  },
};

export const DisabledDates: Story = {
  args: {
    mode: 'single',
    numberOfMonths: 2,
    disablePast: false,
    disabledDates: ['2024-07-01', '2024-07-02'],
  },
};

export const MultipleMonths: Story = {
  args: {
    mode: 'single',
    numberOfMonths: 2,
    weekendSelectable: false,
    disableFuture: true,
    disablePast: false,
  },
};

export const MultipleSelectMode: Story = {
  args: {
    mode: 'multiple',
    numberOfMonths: 2,
    onSelect: (date) => console.log('Selected Date', date),
  },
};
