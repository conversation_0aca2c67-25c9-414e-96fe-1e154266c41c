import { Meta, StoryObj } from '@storybook/react';
import { DatepickerInput } from './DatepickerInput';

const meta: Meta<typeof DatepickerInput> = {
  title: 'Components/Inputs/DatePicker/DatepickerInput',
  component: DatepickerInput,
};
export default meta;
type Story = StoryObj<typeof DatepickerInput>;

export const SingleDate: Story = {
  args: {
    mode: 'single',
    selected: new Date(),
    numberOfMonths: 1,
    icon: 'alarm-clock',
    iconPosition: 'right',
    name: 'date',
    placeholder: 'Date',
    fieldError: {
      type: 'required',
      message: 'This field is required',
    },
    instructions: 'Select a date',
  },
};
export const MultipleDates: Story = {
  args: {
    mode: 'multiple',
    // selected: new Date(),
    numberOfMonths: 1,
    icon: 'alarm-clock',
    iconPosition: 'right',
    placeholder: 'Dates',
    name: 'date',
  },
};
export const DateRange: Story = {
  args: {
    mode: 'range',
    // selected: new Date(),
    placeholder: 'Period',
    numberOfMonths: 1,
    icon: 'alarm-clock',
    iconPosition: 'right',
    name: 'date',
  },
};
