import { useEffect, useRef, useState } from 'react';
import { DateRange } from 'react-day-picker';
import {
  Control,
  Controller,
  FieldError,
  RegisterOptions,
} from 'react-hook-form';
import styled, { css } from 'styled-components';
import { Icon, IconTypes } from '../../Icons';
import { DatePicker, IDatePickerProps } from './Datepicker/DatePicker';
type Props = {
  name: string;
  placeholder?: string;
  icon?: IconTypes;
  className?: string;
  iconPosition?: 'left' | 'right' | 'none';
  onDateChange?: (date?: Date | DateRange | Date[]) => void;
  fieldError?: FieldError | undefined | null;
  // type: HTMLInputTypeAttribute | undefined;
  rules?: RegisterOptions;
  control?: Control;
  label?: string;
  instructions?: string;
  value?: string;
  state?: 'default' | 'display-only';
  onFocus?: (ev: any) => void;
  secondaryName?: string;
  setSecondaryValue?: (name: string, value: string) => void;
} & IDatePickerProps;

const Container = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${(props) => props.theme.SpacingXs};
  transition: color 1s ease, background-color 1s ease;
  position: relative;
  width: 100%;
`;

const InputContainer = styled.div`
  height: auto;
  min-height: 37px;
  display: grid;
  align-items: center;
  justify-content: center;
  min-width: 95px;
  /* background-color: #181818; */
  /* border-right: 1px solid #272727; */
  cursor: pointer;
  grid-template-columns: 1fr;
  /* margin-top: ${(props) => props.theme.SpacingXs}; */

  &:last-of-type {
    border-right: none;
  }
`;

const Instruction = styled.div<{ error?: boolean }>`
  margin-top: ${(props) => props.theme.SpacingXs};
  color: ${({ error, theme }) =>
    error ? theme.ColorsUtilityColorError : theme.ColorsTypographySecondary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize2}px;
`;
const ActiveZone = styled.div`
  display: grid;
  grid-template-rows: 1fr auto;
  gap: ${(props) => props.theme.SpacingSm};
  align-items: self-start;
  width: 100%;

  &.left {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }
`;

const DateInputContainer = styled.div<{
  dropdownOpen: boolean;
  state: 'default' | 'display-only';
  error?: boolean;
}>`
  height: 37px;
  overflow: hidden;
  z-index: 8;
  outline: unset;
  text-align: left;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  border: 1px solid
    ${(props) =>
      props?.dropdownOpen
        ? props.theme.ColorsStrokesFocus
        : props.error
        ? props.theme.ColorsUtilityColorError
        : props.theme.ColorsStrokesGrey};
  box-sizing: border-box;
  width: 100%;
  padding: ${(props) => props.theme.SpacingSm};
  border-radius: ${(props) => props.theme.RadiusXs};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: grid;
  grid-template-areas: 'left-icon date-display right-icon dropdown-icon';
  grid-template-rows: 1fr;
  grid-template-columns: auto 1fr auto auto;
  align-content: center;

  ${({ state, dropdownOpen }) =>
    state === 'display-only'
      ? !dropdownOpen
        ? css`
            border: 1px solid ${(props) => props.theme.ColorsControllersDefault};
            background: ${(props) => props.theme.ColorsInputsNonEditable};
          `
        : css`
            background: ${(props) => props.theme.ColorsInputsPrimary};
            border: 1px solid ${(props) => props.theme.ColorsStrokesFocus};
          `
      : ''}
`;

const DateInput = styled.input<{ dropdownOpen: boolean }>`
  all: unset;
  overflow: hidden;
  outline: unset;
  background-color: transparent;
  text-align: left;
  box-sizing: border-box;
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;

  ::placeholder {
    color: ${(props) => props.theme.ColorsTypographyPrimary};
    transition: color 0.3s ease;
  }
`;

const DropdownIcon = styled(Icon)`
  grid-area: dropdown-icon;
  padding: 0 ${(props) => props.theme.SpacingXs};
`;

const DropdownMenu = styled.div<{ width: number }>`
  // width: ${(props) => props.width}px;
  margin: 0 !important;
  position: absolute;
  /* top: 40px; */
  right: 0px;
  left: 0px;
  border-radius: ${(props) => props.theme.RadiusXs};
  background-color: ${(props) => props.theme.ColorsOverlaySurfaceOverlay};
  border: 1px solid ${(props) => props.theme.ColorsStrokesFocus};
  box-sizing: border-box;
  overflow: hidden;
  display: grid;
  grid-template-rows: auto;
  justify-items: center;
  text-align: self-start;
  color: ${(props) => props.theme.ColorsIconColorTertiary};
  padding-bottom: 5px;
  z-index: 1;
  width: 100%;

  div {
    color: ${(props) => props.theme.ColorsTypographyPrimary};
    // display: grid;
    align-items: center;
    grid-template-columns: 1fr auto;
    // align-items: self-start;
    // padding: 0.8rem 0.7rem;
    // margin: 10px 18px;
    outline: none;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
`;

const StyledInstuctionContainer = styled.div`
  margin: unset;
`;

const IconWrapper = styled(Icon)`
  display: grid;
  grid-auto-flow: column;
  place-items: center;
  align-self: center;
`;
const Label = styled.label`
  margin-bottom: ${(props) => props.theme.SpacingXs};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
`;

const LeftIconContainer = styled.span<{ position: 'left' | 'right' | 'none' }>`
  grid-area: left-icon;
  padding: 0
    ${(props) => (props?.position === 'left' ? props.theme.SpacingXs : 0)};
`;
const RightIconContainer = styled.span<{ position: 'left' | 'right' | 'none' }>`
  grid-area: right-icon;
  padding: 0
    ${(props) => (props?.position === 'right' ? props.theme.SpacingXs : 0)};
`;

/**
 * A reusable datepicker input component that allows users to select a date or a range of dates.
 *
 * @param {object} props - The component props.
 * @param {string} props.mode - The datepicker mode, either 'single', 'multiple', or 'range'.
 * @param {Date|Date[]|DateRange|undefined} props.selected - The currently selected date or dates.
 * @param {number} props.numberOfMonths - The number of months to display in the datepicker.
 * @param {boolean} props.disablePast - Whether to disable past dates.
 * @param {Date[]} props.disabledDates - An array of dates to disable.
 * @param {boolean} props.disableFuture - Whether to disable future dates.
 * @param {boolean} props.weekendSelectable - Whether weekends are selectable.
 * @param {string} props.placeholder - The input placeholder text.
 * @param {function} props.onDateChange - A callback function called when the date changes.
 * @param {string} props.instructions - The input instructions.
 * @param {string} props.label - The input label.
 * @param {string} props.position - The position of the icon, either 'left', 'right', or 'none'.
 * @param {object} props.icon - The icon component.
 * @param {string} props.name - The input name.
 * @param {object} props.control - The input control.
 * @param {object} props.rules - The input rules.
 * @param {object} props.fieldError - The input field error.
 * @return {JSX.Element} The datepicker input component.
 */
export const DatepickerInput = ({
  mode = 'single',
  selected,
  numberOfMonths,
  disablePast,
  disabledDates,
  disableFuture,
  weekendSelectable,
  placeholder,
  instructions,
  label,
  iconPosition: position = 'none',
  icon,
  name,
  control,
  rules,
  state = 'default',
  onFocus,
  fieldError,
  secondaryName,  
  setSecondaryValue,
}: Props) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const updateDropdownWidth = () => {
      if (inputContainerRef.current) {
        const width = inputContainerRef.current.offsetWidth;
        if (dropdownRef.current) {
          dropdownRef.current.style.width = `${width}px`;
        }
      }
    };

    updateDropdownWidth();
    window.addEventListener('resize', updateDropdownWidth);

    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputContainerRef.current &&
        !inputContainerRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      window.removeEventListener('resize', updateDropdownWidth);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const displaySelected = (value: any) => {
    if (mode === 'single') {
      return (value as Date)?.toDateString();
    }
    if (mode === 'multiple') {
      return Array.isArray(value)
        ? (value as Date[])?.map((date) => date?.toDateString()).join(', ')
        : '';
    }
    return `${(value as DateRange)?.from?.toDateString()} - ${(
      value as DateRange
    )?.to?.toDateString()}`;
  };

  const handleInputClick = () => {
    setDropdownOpen((prev) => !prev);
  };

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({
        field: { onChange: onDateChange, value = selected, name },
        fieldState: { error = fieldError },
        formState,
      }) => (
        <Container data-testid="date-picker-input">
          {!!label && <Label>{label}</Label>}
          <InputContainer data-testid="date-picker-input-wrapper">
            <ActiveZone
              data-testid="date-picker-input-active-zone"
              className={'left'}
              ref={inputContainerRef}
            >
              <DateInputContainer
                data-testid="date-picker-input-container"
                dropdownOpen={dropdownOpen}
                onClick={handleInputClick}
                state={state}
                error={!!error?.message}
              >
                <LeftIconContainer
                  data-testid="date-picker-input-left-icon"
                  position={position}
                >
                  {position === 'left' && !!icon && (
                    <IconWrapper type={icon} width={24} height={24} />
                  )}
                </LeftIconContainer>
                <DateInput
                  dropdownOpen={dropdownOpen}
                  type="text"
                  placeholder={value ? displaySelected(value) : placeholder}
                  name={name}
                  readOnly
                />
                <RightIconContainer position={position}>
                  {position === 'right' && !!icon && (
                    <IconWrapper type={icon} width={24} height={24} />
                  )}
                </RightIconContainer>
                <DropdownIcon
                  type={dropdownOpen ? 'chevron-up' : 'chevron-down'}
                />
              </DateInputContainer>
              <div>
                {dropdownOpen && (
                  <DropdownMenu
                    data-testid="date-picker-dropdown"
                    width={inputContainerRef.current?.offsetWidth || 0}
                    ref={dropdownRef}
                  >
                    <DatePicker
                      data-testid="date-picker"
                      mode={mode}
                      {...{
                        numberOfMonths,
                        disablePast,
                        disabledDates,
                        disableFuture,
                        weekendSelectable,
                      }}
                      selected={value}
                      onSelect={(selectedDate) => {
                        // Update the normal field (store full selected date)
                        onDateChange(selectedDate);
                        // If provided, update the secondary field (with date only)
                        if (secondaryName && setSecondaryValue && selectedDate instanceof Date) {
                          const dateOnly = selectedDate.toISOString().split('T')[0];
                          setSecondaryValue(secondaryName, dateOnly);
                        }
                      }}
                    />
                  </DropdownMenu>
                )}
              </div>
            </ActiveZone>
          </InputContainer>
          {error ? (
            <Instruction error={!!error?.message}>{error?.message}</Instruction>
          ) : (
            <StyledInstuctionContainer>
              {instructions && <Instruction>{instructions}</Instruction>}
            </StyledInstuctionContainer>
          )}
        </Container>
      )}
    />
  );
};
