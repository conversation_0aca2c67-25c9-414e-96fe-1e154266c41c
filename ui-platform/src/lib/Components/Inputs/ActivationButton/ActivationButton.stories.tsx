import type { Meta, StoryObj } from '@storybook/react';
import { ActivationButton } from './ActivationButton';

const meta: Meta<typeof ActivationButton> = {
  component: ActivationButton,
  title: 'Components/Inputs/ActivationButton',
};
export default meta;
type Story = StoryObj<typeof ActivationButton>;

export const Overview: Story = {
  args: {
    currentState: 1,
    name: 'activation-button',
  },
};

export const NoneSelected: Story = {
  args: {
    label: 'Multichoice',
    name: 'activation-button',
  },
};

export const WithChangeHandler: Story = {
  args: {
    name: 'activation-button',
    onChange: (state) => console.log(state),
  },
};
