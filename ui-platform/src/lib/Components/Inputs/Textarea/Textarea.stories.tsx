import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Icon } from '../../Icons';
import { Textarea } from './Textarea';

const meta: Meta<typeof Textarea> = {
  component: Textarea,
  title: 'Components/Inputs/TextInput/Textarea',
  argTypes: {
    error: {
      control: {
        type: 'boolean',
      },
      description: 'Toggle error state',
    },
    position: {
      control: {
        type: 'select',
        options: ['left', 'right', 'none'],
      },
      description: 'Position of the icon',
    },
    // onChange: { action: 'changed' },
  },
};
export default meta;

type Story = StoryObj<typeof Textarea>;

export const Overview: Story = {
  args: {
    placeholder: 'Text Here',
    icon: <Icon type="user-01" />,
    position: 'none',
    error: { type: 'required', message: 'This field is required' },
    name: 'username',
  },
};

export const WithIconLeft: Story = {
  args: {
    placeholder: 'Text Here',
    icon: <Icon type="user-01" />,
    position: 'left',
    error: { type: 'required', message: 'This field is required' },
    name: 'username',
  },
};

export const WithIconRight: Story = {
  args: {
    placeholder: 'Text Here',
    icon: <Icon type="user-01" />,
    position: 'right',
    error: { type: 'required', message: 'This field is required' },
    name: 'username',
  },
};

export const WithErrorState: Story = {
  args: {
    placeholder: 'Text Here',
    icon: <Icon type="user-01" />,
    position: 'none',
    error: { type: 'required', message: 'This field is required' },
    name: 'username',
  },
};

export const WithLabelAndInstruction: Story = {
  args: {
    placeholder: 'Text Here',
    icon: <Icon type="user-01" />,
    position: 'none',
    error: { type: 'required', message: 'This field is required' },
    label: 'Username',
    instructions: 'Enter your username.',
    name: 'username',
  },
};
