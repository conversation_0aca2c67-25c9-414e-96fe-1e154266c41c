import React, {
  HTMLInputTypeAttribute,
} from 'react';
import {
  Control,
  Controller,
  FieldError,
  RegisterOptions,
} from 'react-hook-form';
import styled, { css } from 'styled-components';

interface TextareaProps {
  placeholder?: string;
  icon?: React.ReactNode;
  position?: 'left' | 'right' | 'none';
  error?: FieldError | undefined | null;
  type: HTMLInputTypeAttribute | undefined;
  rules?: RegisterOptions;
  name: string;
  control: Control;
  underline?: boolean;
  label?: string;
  instructions?: string;
  value?: string;
  className?: string;
  rows?: number;
  cols?: number;
  minLength?: number;
  maxLength?: number;
  readonly?: boolean;
  state?: 'default' | 'display-only';
  onFocus?: (ev: any) => void;
}

const Container = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  width: 100%;
  gap: ${(props) => props.theme.SpacingXs};
`;

const Label = styled.label`
  margin-bottom: ${(props) => props.theme.SpacingXs};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
`;

const StyledTextareaWrapper = styled.div<{
  error?: any;
  hasIconLeft: boolean;
  state?: 'default' | 'display-only';
}>`
  display: grid;
  grid-template-columns: ${(props) =>
    props.hasIconLeft ? 'auto 1fr' : '1fr auto'};
  /* width: 100%; */
  height: auto;
  padding: ${(props) => props.theme.SpacingSm} ${(props) => props.theme.GapSm};
  align-items: center;
  gap: ${(props) => props.theme.GapSm};
  border-radius: ${(props) => props.theme.RadiusXs};
  border: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
  background: ${(props) => props.theme.ColorsInputsPrimary};

  ${({ state, theme }) =>
    state === 'display-only' &&
    css`
      border: 1px solid ${(theme) => theme.theme.ColorsControllersDefault};
      background: ${(theme) => theme.theme.ColorsInputsNonEditable};
    `}

  ${({ error, theme }) =>
    error &&
    css`
      border-color: red;
    `}

  &:focus-within {
    border-color: ${(props) => props.theme.ColorsStrokesFocus};
    background: ${(props) => props.theme.ColorsInputsPrimary};
  }
`;

const StyledTextarea = styled.textarea`
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
  font-style: normal;
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  line-height: ${(props) => props.theme.LineHeights0};
  width: 100%;
  outline: none;
  border: none;
  background: inherit;
  resize: none;

  &::placeholder {
    color: ${(props) => props.theme.ColorsTypographyPrimary};
  }
`;

const IconWrapper = styled.div`
  display: grid;
  place-items: center;
  align-self: baseline;
`;

const Instruction = styled.div<{ error?: boolean }>`
  margin-top: ${(props) => props.theme.SpacingXs};
  color: ${({ error, theme }) =>
    error ? theme.ColorsUtilityColorError : theme.ColorsTypographySecondary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize2}px;
`;

const StyledInstuctionContainer = styled.div`
  margin: unset;
`;

/**
 * Renders a textarea component.
 *
 * @param {TextareaProps} props - The props for the component.
 * @param {string} [props.placeholder] - The placeholder for the component.
 * @param {ReactNode} [props.icon] - The icon to place in the component.
 * @param {string} [props.position] - The position of the icon. 'left' or 'right'.
 * @param {boolean} [props.error] - If the component should be in an error state.
 * @param {string} [props.label] - The label for the component.
 * @param {string} props.name - The name of the field in the form.
 * @param {ControllerProps} props.control - The control props for the component.
 * @param {string} [props.className] - The class name for the component.
 * @param {string} [props.instructions] - The instructions for the component.
 * @param {number} [props.rows] - The number of rows for the component.
 * @param {number} [props.cols] - The number of cols for the component.
 * @param {number} [props.minLength] - The minimum length for the component.
 * @param {number} [props.maxLength] - The maximum length for the component.
 * @param {boolean} [props.readonly] - If the component should be readonly.
 * @param {'default' | 'display-only'} [props.state] - The state of the component.
 * @param {(event: React.FocusEvent<HTMLTextAreaElement>) => void} [props.onFocus] - The on focus event handler.
 * @returns {ReactElement} The textarea component.
 */
export const Textarea = ({
  placeholder,
  icon,
  position = 'none',
  error,
  label,
  name,
  rules,
  control,
  className,
  instructions,
  rows = 3,
  cols = 20,
  minLength,
  maxLength,
  readonly,
  state,
  onFocus,
}: TextareaProps) => {

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({
        field: { onChange, value, name: nm },
        fieldState: { error: fieldError },
        formState,
      }) => (
        <Container data-testid="textarea-container" className={className}>
          {label && <Label>{label}</Label>}
          <StyledTextareaWrapper
            data-testid="textarea-wrapper"
            error={error}
            hasIconLeft={position === 'left'}
            state={state}
          >
            {position === 'left' && (
              <IconWrapper data-testid="textarea-left-icon">{icon}</IconWrapper>
            )}
            <StyledTextarea
              data-testid="textarea"
              name={nm}
              placeholder={placeholder}
              value={value || ''}
              onChange={onChange}
              rows={rows}
              cols={cols}
              minLength={minLength}
              maxLength={maxLength}
              readOnly={readonly}
              onFocus={onFocus}
            />
            {position === 'right' && (
              <IconWrapper data-testid="textarea-right-icon">
                {icon}
              </IconWrapper>
            )}
          </StyledTextareaWrapper>
          {fieldError ? (
            <Instruction
              data-testid="textarea-error"
              error={!!fieldError?.message}
            >
              {fieldError?.message}
            </Instruction>
          ) : (
            <StyledInstuctionContainer data-testid="textarea-instructions-container">
              {instructions && (
                <Instruction data-testid="textarea-instructions">
                  {instructions}
                </Instruction>
              )}
            </StyledInstuctionContainer>
          )}
        </Container>
      )}
    />
  );
};
