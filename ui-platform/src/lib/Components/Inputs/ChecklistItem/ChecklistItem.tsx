import { useCallback, useState } from 'react';
import { Control, Controller, RegisterOptions } from 'react-hook-form';
import styled, { useTheme } from 'styled-components';
import { ActionConfig } from '../../../Engine/models/action.config';
import { Icon, svgs } from '../../Icons';

type CheckVal = 'valid' | 'invalid' | 'unchecked';
type Props = {
  name: string;
  label: string;
  rules?: RegisterOptions;
  control?: Control;
  currentValue?: CheckVal;
  onCheckItem?: (val?: CheckVal) => void;
  onInvalid?: ActionConfig[];
  _callClientAction?: any;
  invalidCount?: number;
  setInvalidCount?: any;
};

/**
 * ItemContainer styled to arrange checkboxes and text using grid.
 * @component
 */
const ItemContainer = styled.div`
  width: 100%;
  height: 25px;
  padding-top: ${(props) => props.theme.SpacingXs};
  padding-bottom: ${(props) => props.theme.SpacingSm};
  padding-left: ${(props) => props.theme.SpacingXs};
  padding-right: ${(props) => props.theme.SpacingSm};
  display: grid;
  grid-template-columns: repeat(2, auto) 1fr;
  align-items: center;
  gap: 4px;
  border-radius: 6px 0px 0px 0px;
  background-color: transparent;
`;

/**
 * Checkbox container for rendering both the CheckIcon and CrossIcon.
 * @component
 */
const CheckboxContainer = styled.div<{ background?: string }>`
  width: 16px;
  position: relative;
  border-radius: 2px;
  border: 1px solid ${(props) => props?.theme.ColorsStrokesDefault};
  box-sizing: border-box;
  height: 16px;
  display: grid;
  justify-items: center;
  align-content: center;
  left: 8px;
  top: 4px;
  z-index: 1;
  background: ${(props) =>
    props?.background ? props.background : 'transparent'};
`;

/**
 * CheckIcon logic is toggled by `checked` state.
 */
const CheckIcon = styled(Icon)<{ checked: boolean }>`
  width: 100%;
  height: 100%;
  margin: auto;
`;

/**
 * CrossIcon logic mimics the CheckIcon behavior.
 */
const CrossIcon = styled(Icon)<{ checked: boolean }>`
  width: 100%;
  height: 100%;
  margin: auto;
`;

/**
 * Text component styled for each checklist item.
 * @component
 */
const ItemText = styled.span`
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
  text-transform: uppercase;
  margin-left: 12px;
  margin-top: 8px;
`;

/**
 * ChecklistItem component that renders a single checklist item with a checkbox and an optional x button.
 * The component uses the React Hook Form library to manage the state of the checkbox.
 * The component accepts a label, name, control, rules, currentValue, onCheckItem, and onInvalid as props.
 * The onCheckItem prop is called when the user clicks on the checkbox.
 * The onInvalid prop is called when the user clicks on the x button.
 * The component renders a grid with three columns: a checkbox, an x button, and the label.
 * The checkbox is rendered using the CheckIcon component.
 * The x button is rendered using the CrossIcon component.
 * The label is rendered using the ItemText component.
 * The component uses the useTheme hook to get the theme object.
 * The component uses the useClientAction hook to call the actions defined in the onInvalid prop.
 * @param {string} label - The label to be displayed next to the checkbox.
 * @param {string} name - The name of the field.
 * @param {Control} control - The control object from React Hook Form.
 * @param {RegisterOptions} rules - The rules object from React Hook Form.
 * @param {CheckVal} currentValue - The current value of the field.
 * @param {(val?: CheckVal) => void} onCheckItem - The function to be called when the user clicks on the checkbox.
 * @param {ActionConfig[]} onInvalid - The actions to be called when the user clicks on the x button.
 * @returns {JSX.Element} The rendered ChecklistItem component.
 */
export function ChecklistItem({
  label: itemDescription,
  name,
  control,
  rules,
  currentValue,
  onCheckItem,
  onInvalid,
  _callClientAction,
  invalidCount = 0,
  setInvalidCount,
}: Props) {
  const [checklistValue, setChecklistValue] = useState<CheckVal>();
  const theme = useTheme();

  // console.log('ChecklistItem: Line 102', { name, currentValue });

  const handleToggle = useCallback(
    async (val: CheckVal, onChange: (val?: CheckVal) => void) => {
      setChecklistValue((prev) => (val === prev ? 'unchecked' : val));
      const newValue = checklistValue === val ? 'unchecked' : val;
      if (newValue === 'invalid') {
        if (invalidCount === 0) {
          for (const cf of onInvalid || []) {
            await _callClientAction?.(cf);
          }
        }
        setInvalidCount(invalidCount + 1);
      }
      onChange(newValue);
      onCheckItem && onCheckItem(newValue);
    },
    [
      _callClientAction,
      checklistValue,
      invalidCount,
      onCheckItem,
      onInvalid,
      setInvalidCount,
    ]
  );

  const colors = {
    pass: theme.ColorsUtilityColorSuccess,
    fail: theme.ColorsUtilityColorError,
    notValidated: theme.ColorsControllersDefault,
    default: theme.ColorsStrokesDefault,
  };

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      defaultValue={currentValue}
      render={({ field: { onChange, value } }) => {
        return (
          <ItemContainer>
            <CheckboxContainer
              onClick={() => handleToggle('valid', onChange)}
              background={value === 'valid' ? colors.pass : ''}
            >
              <CheckIcon
                type="check"
                // color={value === 'valid' ? colors.pass : colors.notValidated}
                color={value === 'valid' ? colors.default : colors.notValidated}
                size={12}
                strokeWidth="3px"
                checked={value === 'valid'}
              />
            </CheckboxContainer>
            <CheckboxContainer
              onClick={() => handleToggle('invalid', onChange)}
              background={value === 'invalid' ? colors.fail : ''}
            >
              <CrossIcon
                type="x"
                // color={value === 'invalid' ? colors.fail : colors.notValidated}
                color={
                  value === 'invalid' ? colors.default : colors.notValidated
                }
                size={12}
                checked={value === 'invalid'}
                strokeWidth="2px"
              />
            </CheckboxContainer>
            <ItemText>{itemDescription}</ItemText>
          </ItemContainer>
        );
      }}
    />
  );
}
