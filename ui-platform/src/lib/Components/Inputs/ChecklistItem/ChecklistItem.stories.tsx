import { Meta, StoryObj } from '@storybook/react';
import { ChecklistItem } from './ChecklistItem';

const meta: Meta<typeof ChecklistItem> = {
  title: 'Components/Inputs/ChecklistItem',
  component: ChecklistItem,
};

export default meta;

type Story = StoryObj<typeof ChecklistItem>;

export const Default: Story = {
  args: {
    name: 'name',
    label: 'text',
    onCheckItem: (val) => console.log('Checked: ' + val),
  },
};
