import {
  Box,
  FormControlLabel,
  FormGroup,
  FormLabel,
  Radio,
  RadioGroup,
  RadioProps,
  Typography,
} from '@mui/material';
import React, { ChangeEvent } from 'react';
import {
  Controller,
  FieldError,
  FieldValues,
  Path,
  UseControllerProps,
} from 'react-hook-form';
// import { useTokensTheme } from '../../../contexts/tokens-context/TokensContext';
import { styled } from '@mui/material/styles';
import { ComponentProps } from 'react';
import { css, useTheme } from 'styled-components';
import { IFormRadioButton } from '../../../models/IFormRadioButton';
import { additionalFontStyling } from '../../../Utilities/fontTransformer';

const StyledRadioRoot = styled(Radio)({
  paddingLeft: '0.5rem',
  '&:hover': {
    backgroundColor: 'transparent',
  },
});

const RadioIcon = styled('span')({
  borderRadius: '50%',
  width: 16,
  height: 16,
  boxShadow:
    'inset 0 0 0 1px rgba(16,22,26,.2), inset 0 -1px 0 rgba(16,22,26,.1)',
  backgroundColor: '#f5f8fa',
  backgroundImage:
    'linear-gradient(180deg,hsla(0,0%,100%,.8),hsla(0,0%,100%,0))',
  'input:hover ~ &': {
    backgroundColor: '#ebf1f5',
  },
  'input:disabled ~ &': {
    boxShadow: 'none',
    background: 'rgba(206,217,224,.5)',
  },
});

const CheckedRadioIcon = styled(RadioIcon)<{
  color?: string;
  background?: string;
}>(({ background, color }) => ({
  backgroundColor: background || '#137cbd',
  backgroundImage:
    'linear-gradient(180deg,hsla(0,0%,100%,.1),hsla(0,0%,100%,0))',
  '&:before': {
    display: 'block',
    width: 16,
    height: 16,
    backgroundImage: color
      ? `radial-gradient(${color}, ${color} 28%, transparent 32%)`
      : 'radial-gradient(#fff,#fff 28%,transparent 32%)',
    content: '""',
  },
  'input:hover ~ &': {
    backgroundColor: background || '#106ba3',
  },
}));

/* eslint-disable-next-line */
export interface FormRadioButtonsProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends Path<TFieldValues> = Path<TFieldValues>
> extends UseControllerProps<TFieldValues, TName> {
  label: string;
  error: FieldError | undefined | null;
  data: IFormRadioButton[];
  instructions?: string;
  returnBoolean?: boolean;
  size?: 'small' | 'medium';
  border?: boolean;
  columns?: number;
  mobileColumns?: number;
  mobileBreakpoint?: number;
  onSelect?: (e: ChangeEvent) => any;
}

const StyledRadio: React.FC<RadioProps> = (props: RadioProps) => {
  const theme = useTheme();
  return (
    <StyledRadioRoot
      {...props}
      checkedIcon={
        <CheckedRadioIcon
          background={theme.ColorsBackgroundInverse}
          color={theme.ColorsControllersDefault}
        />
      }
      icon={<RadioIcon />}
    />
  );
};

/**
 * A component that renders a radio button group based on the provided options.
 *
 * @typedef { import("react-hook-form").ControllerProps & {
 *   label: string;
 *   error: import("react-hook-form").FieldError | undefined | null;
 *   data: IFormRadioButton[];
 *   instructions?: string;
 * } } FormRadioButtonsProps
 *
 * @param {{ name: string; control: import("react-hook-form").ControllerProps["control"]; label: string; error: import("react-hook-form").FieldError | undefined | null; data: IFormRadioButton[]; instructions?: string; }} props
 * @returns { import("react").ReactElement }
 */
export function FormRadioButtons<
  TFieldValues extends FieldValues = FieldValues,
  TName extends Path<TFieldValues> = Path<TFieldValues>
>({
  name,
  control,
  label,
  error,
  data,
  instructions,
  returnBoolean,
  border,
  size,
  columns = 3,
  mobileColumns = 1,
  mobileBreakpoint = 600,
  onSelect,
}: FormRadioButtonsProps<TFieldValues, TName>) {
  // const tokensTheme = useTokensTheme().theme;
  const tokensTheme = useTheme();

  const formGroupDefaultStyles = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: tokensTheme.GapSm,
  } as const;
  const formLabelErrorStyles = {
    float: 'right',
    color: '#B22411',
    fontSize: '14px',
  } as const;

  /**
   * Generates a list of FormControlLabel components based on the provided data array.
   * Each FormControlLabel is rendered with the corresponding label and value from the data array.
   * The color, fontSize and fontWeight of the label are set based on the theme.
   * The control is set to a Radio component with the color set to white.
   * The key of each FormControlLabel is set to the index of the array.
   * @returns {JSX.Element[]}
   */
  const generateRadioOptions = (size: 'small' | 'medium' = 'medium') => {
    return data.map((singleOption: IFormRadioButton, index: number) => {
      return (
        <FormControlLabel
          key={index}
          value={singleOption.value}
          label={singleOption.label}
          sx={{
            color: tokensTheme.ColorsTypographyPrimary,
            ...tokensTheme.DesktopButtonsModuleNavigationMedium,
            fontWeight: additionalFontStyling(
              tokensTheme.DesktopButtonsModuleNavigationMedium.fontWeight,
              false
            ),
            textTransform:
              tokensTheme.DesktopButtonsModuleNavigationMedium.textCase,
            marginLeft: '1px',
          }}
          control={
            <StyledRadio
              sx={{ color: tokensTheme.ColorsStrokesDefault }}
              size={size}
            />
          }
        />
      );
    });
  };

  function changeHandler(
    event: React.ChangeEvent<HTMLInputElement>,
    value: string,
    onChange?: (value: string | boolean) => void
  ) {
    if (onChange) {
      if (returnBoolean) {
        const selectedValue = event.target.value;
        console.log({ selectedValue });
        onChange(selectedValue === 'true');
      } else {
        onChange(value);
      }
    }
  }

  return (
    <FormGroup sx={formGroupDefaultStyles}>
      <FormLabel
        sx={{
          color: tokensTheme.ColorsTypographyPrimary,
          fontSize: tokensTheme.DesktopFormsHeading.fontSize,
          fontWeight: additionalFontStyling(
            tokensTheme.DesktopFormsHeading.fontWeight,
            false
          ),
        }}
      >
        {label}
      </FormLabel>
      <Controller
        name={name}
        control={control}
        render={({
          field: { onChange, value },
          fieldState: { error },
          formState,
        }) => {
          return (
            <RadioGroup
              sx={{
                display: 'grid',
                borderRadius: tokensTheme.RadiusSm,
                border: border
                  ? `1px solid ${tokensTheme.ColorsControllersSecondary}`
                  : 'none',
                gridTemplateColumns: {
                  xs: `repeat(${mobileColumns}, 1fr)`,
                  sm: `repeat(${columns}, 1fr)`,
                },
                gap: 1,
                width: '100%',
                [`@media (max-width: ${mobileBreakpoint}px)`]: {
                  gridTemplateColumns: `repeat(${mobileColumns}, 1fr)`,
                },
              }}
              value={value || (typeof value === 'boolean' ? false : '')}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                changeHandler(event, event.target.value, onChange);
                if (onSelect) {
                  onSelect(event);
                }
              }}
            >
              {generateRadioOptions(size)}
            </RadioGroup>
          );
        }}
      />
      <Box component="div" sx={{ height: '17px' }}>
        <Typography
          sx={{
            color: tokensTheme.ColorsTypographyPrimary,
            ...tokensTheme.DesktopFormsSubText,
            fontWeight: additionalFontStyling(
              tokensTheme.DesktopFormsSubText.fontWeight,
              false
            ),
            textTransform: tokensTheme.DesktopFormsSubText.textCase,
          }}
        >
          {instructions}
        </Typography>
      </Box>
    </FormGroup>
  );
}
