import type { <PERSON>a, StoryObj } from '@storybook/react';
import { FormRadioButtons } from './FormRadioButtons';

const meta: Meta<typeof FormRadioButtons> = {
  component: FormRadioButtons,
  title: 'Components/Inputs/FormRadioButtons',
};
export default meta;
type Story = StoryObj<typeof FormRadioButtons>;

export const Overview: Story = {
  args: {
    label: 'Text Label',
    error: null,
    data: [
      {
        value: 'Radio Option One',
        label: 'Radio Option One',
      },
      {
        value: 'Radio Option Two',
        label: 'Radio Option Two',
      },
      {
        value: 'Radio Option One',
        label: 'Radio Option One',
      },
      {
        value: 'Radio Option Two',
        label: 'Radio Option Two',
      },
      {
        value: 'Radio Option One',
        label: 'Radio Option One',
      },
      {
        value: 'Radio Option Two',
        label: 'Radio Option Two',
      },
    ],
    name: 'Text Name',
  },
};

export const Overview_onSelect: Story = {
  args: {
    label: 'Text Label',
    error: null,
    onSelect: (e: any) => {
      console.log(Number(e.target.value));
      console.log(e);
    },
    data: [
      {
        value: 1,
        label: 'Radio Option One',
      },
      {
        value: 2,
        label: 'Radio Option Two',
      },
      {
        value: 3,
        label: 'Radio Option three',
      },
      {
        value: 4,
        label: 'Radio Option four',
      },
      {
        value: 5,
        label: 'Radio Option five',
      },
      {
        value: 6,
        label: 'Radio Option six',
      },
    ],
    name: 'Text Name',
  },
};
