import type { <PERSON>a, StoryObj } from '@storybook/react';
import { HeadingTextInput } from './HeadingTextInput';
import { Icon } from '../../Icons';

const meta: Meta<typeof HeadingTextInput> = {
  component: HeadingTextInput,
  title: 'Components/Inputs/TextInput/HeadingTextInput',
  argTypes: {
    error: {
      control: {
        type: 'boolean',
      },
      description: 'Toggle error state',
    },
    // onChange: { action: 'changed' },
  },
};
export default meta;
type Story = StoryObj<typeof HeadingTextInput>;

export const Overview: Story = {
  args: {
    name: 'first_name',
    label: 'Text Here',
    placeholder: 'Input Here',
    icon: <Icon type="user-01" />,
    position: 'none',
    error: {type: 'required', message: 'This field is required'},
  },
};

export const WithUnderline: Story = {
  args: {
    name: 'first_name',
    label: 'Text Here',
    placeholder: 'Input Here',
    icon: <Icon type="user-01" />,
    position: 'none',
    error: {type: 'required', message: 'This field is required'},
    underline: true
  },
};

export const WithIconRight: Story = {
  args: {
    name: 'first_name',
    label: 'Text Here',
    placeholder: 'Input Here',
    icon: <Icon type="user-01" />,
    position: 'right',
    error: {type: 'required', message: 'This field is required'},
  },
};

export const WithIconLeft: Story = {
  args: {
    name: 'first_name',
    label: 'Text Here',
    placeholder: 'Input Here',
    icon: <Icon type="user-01" />,
    position: 'left',
    error: {type: 'required', message: 'This field is required'},
  },
};

export const WithErrorStateAndTextAndNoIcon: Story = {
  args: {
    name: 'first_name',
    label: 'Text Here',
    placeholder: 'Input Here',
    icon: <Icon type="user-01" />,
    position: 'none',
    error: {type: 'required', message: 'This field is required'},
  },
};

export const WithErrorStateAndTextAndRightIcon: Story = {
  args: {
    name: 'first_name',
    label: 'Text Here',
    placeholder: 'Input Here',
    icon: <Icon type="user-01" />,
    position: 'right',
    error: {type: 'required', message: 'This field is required'},
  },
};

export const WithErrorStateAndTextAndLeftIcon: Story = {
  args: {
    name: 'first_name',
    label: 'Text Here',
    placeholder: 'Input Here',
    icon: <Icon type="user-01" />,
    position: 'left',
    error: {type: 'required', message: 'This field is required'},
  },
};
