import React, {
  ChangeEvent,
  HTMLInputTypeAttribute,
  ReactNode,
  useState,
} from 'react';
import styled, { css } from 'styled-components';
import {
  Controller,
  FieldError,
  FieldValues,
  UseControllerProps,
  Path,
  RegisterOptions,
  Control,
} from 'react-hook-form';

interface StyledHeadingInputContainerProps {
  error?: FieldError | undefined | null;
  theme: any;
  hasIconLeft?: boolean;
}

const StyledHeadingInputContainer = styled.div<StyledHeadingInputContainerProps>`
  display: grid;
  grid-template-columns: ${(props) =>
    props.hasIconLeft ? 'auto 1fr' : '1fr auto'};
  height: auto;
  padding: ${(props) => props.theme.SpacingSm};
  align-items: center;

  border-radius: ${(props) => props.theme.RadiusXs};
  border: 1px solid ${(props) => props.theme.ColorsStrokesFocus};
  background: ${(props) => props.theme.ColorsInputsInverse};

  ${({ error, theme }) =>
    error &&
    css`
      border-color: ${theme.ColorsUtilityColorError} };
      background: ${theme.ColorsUtilityColorError}
    `}

  &:focus-within {
    border-color: ${(props) => props.theme.ColorsStrokesFocus};
  }
`;

const StyledHeadingInputWrapper = styled.div`
  display: grid;
  grid-template-rows: auto auto;
  align-items: start;
  width: 100%;
`;

const StyledHeading = styled.h3`
  all: unset;
  color: ${(props) => props.theme.ColorsTypographyDisabled};

  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
  font-style: normal;
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  line-height: normal;
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  width: 100%;
`;


const ErrorText = styled.span`
  color: red;
  font-size: 12px;
  margin-left: auto;
`;

const StyledInput = styled.input<{ underline?: boolean }>`
  width: 100%;
  outline: none;
  border: none;
  color: ${(props) => props.theme.ColorsStrokesInverse};

  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
  font-style: normal;
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  line-height: normal;

  ${({ underline }) =>
    underline &&
    css`
      border-bottom: 1px solid ${(props) => props.theme.ColorsStrokesFocus};
      padding-bottom: 4px;
    `}
`;

const IconWrapper = styled.div<{ position?: 'left' | 'right' }>`
  display: grid;
  place-items: center;
  ${(props) =>
    props.position === 'right' &&
    css`
      margin-left: ${(props) => props.theme.GapXs};
    `}
  ${(props) =>
    props.position === 'left' &&
    css`
      margin-right: ${(props) => props.theme.GapXs};
    `}
`;

interface HeadingTextInputProps {
  label: string;
  placeholder: string;
  icon?: ReactNode;
  position?: 'left' | 'right' | 'none';
  error?: FieldError | undefined | null;
  type: HTMLInputTypeAttribute | undefined;
  rules?: RegisterOptions;
  name: string;
  control: Control;
  underline?: boolean;
}

/**
 * Renders a text input component with a heading label and optional icon.
 *
 * @param {HeadingTextInputProps} props - The props for the component.
 * @param {string} props.label - The label for the component.
 * @param {string} props.placeholder - The placeholder for the component.
 * @param {ReactNode} [props.icon] - The icon for the component.
 * @param { 'left' | 'right' | 'none' } [props.position] - The position of the icon.
 * @param {FieldError | undefined | null} [props.error] - The error for the component.
 * @param {string} props.name - The name of the field in the form.
 * @param {RegisterOptions} [props.rules] - The validation rules for the form.
 * @param {Control} props.control - The control props for the component.
 * @param {HTMLInputTypeAttribute | undefined} [props.type] - The type of input.
 * @param {boolean} [props.underline] - Whether to underline the input.
 * @returns {ReactElement} The text input component.
 */
export const HeadingTextInput: React.FC<HeadingTextInputProps> = ({
  label,
  placeholder,
  icon,
  position,
  error,
  name,
  rules,
  control,
  type,
  underline,
}: HeadingTextInputProps) => {

  // const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
  //   const newValue = e.target.value;
  //   setValue(newValue);
  //   if (onChange) {
  //     onChange(e);
  //   }
  // };

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({
        field: { onChange, value },
        fieldState: { error: fieldError },
        formState,
      }) => (
        <StyledHeadingInputContainer
          error={error}
          hasIconLeft={position === 'left'}
        >
          {position === 'left' && (
            <IconWrapper position="left">{icon}</IconWrapper>
          )}
          <StyledHeadingInputWrapper>
            <StyledHeading>
              {label}
              {fieldError && <ErrorText>{fieldError?.message}</ErrorText>}
            </StyledHeading>
            <StyledInput
              placeholder={placeholder}
              type={type}
              onChange={onChange}
              value={value || ''}
              underline={underline}
            />
          </StyledHeadingInputWrapper>
          {position === 'right' && (
            <IconWrapper position="right">{icon}</IconWrapper>
          )}
        </StyledHeadingInputContainer>
      )}
    />
  );
};
