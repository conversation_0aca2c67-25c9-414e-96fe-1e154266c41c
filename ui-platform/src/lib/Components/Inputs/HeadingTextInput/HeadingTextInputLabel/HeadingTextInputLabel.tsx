import React from 'react';
import styled from 'styled-components';

type TextLabelProps = {
  headingTextLabel: string;
}

export const HeadingLabelTextWrapper = styled(({ ...rest }) => <div {...rest}></div>)`
  display: grid;
  grid-template-rows: auto auto;
  gap: 4px;
  align-self: stretch;
`;

export const LabelText = styled(({ ...rest }) => <div {...rest}></div>)`
  color: ${(props) => props?.theme.ColorsTypographyDisabled};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
  font-style: normal;
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  line-height: normal;
`;

/**
 * A React component that renders a heading text label for a heading text input.
 * It renders a heading text label with a smaller font size and a lighter color
 * than the default typography. It also renders a gap between the label and the
 * input field.
 *
 * @param {string} headingTextLabel The label text to render in the heading text
 *   label.
 * @returns {React.ReactElement} A React element representing the heading text
 *   label.
 */
export const HeadingTextInputLabel = ({ headingTextLabel }: TextLabelProps) => {
  return (
    <HeadingLabelTextWrapper>
      <LabelText>{headingTextLabel}</LabelText>
    </HeadingLabelTextWrapper>
  );
};

