import React from 'react';
import { Meta } from '@storybook/react';

const meta: Meta = {
  title: 'Components/Inputs/AddFile',
};

export default meta;

export const Overview: React.FC = () => {
  const onSelect = (files: FileList) => {
    // Mock function
    console.log('Selected files:', files);
  };

  const AddFile = React.lazy(() => import('./AddFile'));

  return (
    <React.Suspense fallback={<div>Loading...</div>}>
      <AddFile onSelect={onSelect} />
    </React.Suspense>
  );
};
