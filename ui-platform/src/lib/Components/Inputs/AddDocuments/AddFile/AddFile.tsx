import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import FileMolecule from '../FileMolecule/FileMolecule';

export interface AddFileProps {
  onSelect: (files: FileList) => void;
  dragAndDropMessage?: string;
}

const AddFileContainer = styled.div`
  border: 1px dashed ${(props) => props.theme.ColorsStrokesGrey};
  border-radius: 5px;
  display: grid;
  grid-template-rows: auto auto auto 1fr;
  grid-template-columns: 1fr;
  gap: 20px;
  justify-items: center;
  align-items: center;
  padding: 76px 73px;
  box-sizing: border-box;
  height: auto;
  max-width: 830px;
  min-height: 400px;
  margin: 0 auto;
`;

const HiddenInput = styled.input`
  display: none;
`;

const DragAndDrop = styled.div`
  text-align: center;
  font-size: ${(props) => props.theme.FontSize6}px;
  font-weight: ${(props) => props.theme.FontWeightsInter0};
`;

const BrowseButton = styled(({ ...rest }) => <label {...rest}></label>)`
  display: grid;
  place-items: center;
  box-shadow: 0px 0px 3px ${(props) => props.theme.ColorsControllersInverse};
  border-radius: 104px;
  background-color: ${(props) =>
    props?.theme.ColorsButton_colorModule_navigationPrimary};
  width: 100%;
  height: 38px;
  min-width: 124px;
  max-width: 235px;
  text-align: center;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  margin-top: 25px 0;
  cursor: pointer;
`;

const ButtonLabel = styled(({ ...rest }) => <div {...rest}></div>)`
  font-size: ${(props) => props.theme.FontSize3}px;
  text-transform: uppercase;
  font-family: ${(props) => props.theme.FontFamiliesInter};
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const FileGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 32px;
  width: 100%;
  justify-items: center;
`;

/**
 * A component that allows users to add files to a list of files. The component
 * includes a drop zone that listens for drag and drop events, and a "Browse"
 * button that opens a file picker dialog when clicked. When a user drags and
 * drops a file or selects a file from the file picker, the component updates
 * the list of files and calls the onSelect callback with the new files as an
 * argument.
 *
 * @param {{onSelect: (files: FileList) => void, dragAndDropMessage?: string}}
 *   props
 * @param {function} props.onSelect - The function to call when the user adds a
 *   file.
 * @param {string} [props.dragAndDropMessage='Drag and drop files, paste
 *   screenshots, or browse'] - The text to display in the drop zone.
 * @returns {ReactElement} The AddFile component.
 */
const AddFile: React.FC<AddFileProps> = ({
  onSelect,
  dragAndDropMessage = 'Drag and drop files, paste screenshots, or browse',
}) => {
  const [files, setFiles] = useState<File[]>([]);
  const [isDragging, setIsDragging] = useState(false);

  const updateFiles = (newFiles: File[]) => {
    setFiles((prevFiles) => {
      const allFiles = [...prevFiles, ...newFiles];
      const dataTransfer = new DataTransfer();
      allFiles.forEach((file) => dataTransfer.items.add(file));
      onSelect(dataTransfer.files);
      return allFiles;
    });
  };

  useEffect(() => {
  /**
   * Handles the paste event by iterating over the pasted items and if the kind is 'file',
   * add it to the list of files. If the length of the filesArray is more than 0, then update
   * the component state with the new files and call the onSelect callback with the new files
   * as an argument
   * @param event the ClipboardEvent
   */
    const handlePaste = (event: ClipboardEvent) => {
      if (event.clipboardData) {
        const items = event.clipboardData.items;
        const filesArray: File[] = [];
        for (let i = 0; i < items.length; i++) {
          if (items[i].kind === 'file') {
            const file = items[i].getAsFile();
            if (file) {
              filesArray.push(file);
            }
          }
        }
        if (filesArray.length > 0) {
          updateFiles(filesArray);
        }
      }
    };

    document.addEventListener('paste', handlePaste);

    return () => {
      document.removeEventListener('paste', handlePaste);
    };
  }, [onSelect]);

  /**
   * Handles the drag over event when a user drags a file over the component.
   * Prevents the default event behavior and sets the isDragging state to true.
   * @param {React.DragEvent<HTMLDivElement>} event - The drag over event.
   */
  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(true);
  };

  /**
   * Handles the drag leave event when a user drags a file out of the component.
   * Sets the isDragging state to false.
   */
  const handleDragLeave = () => {
    setIsDragging(false);
  };

  /**
   * Handles the drop event when a user drags and drops a file into the component.
   * @param event The drag event.
   */
  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(false);

    const fileList = event.dataTransfer.files;
    if (fileList) {
      const filesArray = Array.from(fileList);
      updateFiles(filesArray);
    }
  };

  /**
   * Handles a change event on the file input element.
   *
   * This function is called when the user selects a file from their
   * file system, either by clicking on the "Browse" button or by
   * dragging and dropping a file onto the drop zone.
   *
   * @param {React.ChangeEvent<HTMLInputElement>} event - The change event
   *   that was triggered by the user.
   */
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const fileList = event.target.files;

    if (fileList) {
      const filesArray = Array.from(fileList);
      updateFiles(filesArray);
    }
  };

  return (
    <AddFileContainer
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <HiddenInput
        type="file"
        id="file-input"
        multiple
        onChange={handleFileChange}
      />
      <DragAndDrop>{dragAndDropMessage}</DragAndDrop>
      <BrowseButton htmlFor="file-input">
        <ButtonLabel>Browse</ButtonLabel>
      </BrowseButton>
      <FileGrid>
        {files.map((file, index) => (
          <FileMolecule
            key={index}
            title={file.name}
            uploadDate={new Date().toLocaleString()}
            file={file}
          />
        ))}
      </FileGrid>
    </AddFileContainer>
  );
};

export default AddFile;
