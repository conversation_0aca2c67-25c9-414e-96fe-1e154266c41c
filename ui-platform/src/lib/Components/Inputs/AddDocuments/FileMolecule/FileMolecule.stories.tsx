import type { Meta, StoryObj } from '@storybook/react';
import FileMolecule from './FileMolecule';

const meta: Meta<typeof FileMolecule> = {
  component: FileMolecule,
  title: 'Components/Inputs/FileMolecule',
};
export default meta;
type Story = StoryObj<typeof FileMolecule>;

export const Overview: Story = {
  args: {title: 'Sample Document',
  uploadDate: '2024-05-16',
  previewSrc: 'https://via.placeholder.com/100', // Placeholder image URL for testing
  file: new File([''], 'sample-document.pdf'),
},
};
