import React, { useEffect, useState } from 'react';
import styled from 'styled-components';

const DocumentMolecule = styled(({ ...rest }) => <div {...rest}></div>)`
  background: ${(props) => props?.theme.ColorsBackgroundInverse};
  border-radius: 6px;
  text-overflow: ellipsis;
  font-family: ${(props) => props.theme.FontFamiliesInter};
  max-width: 146px;
  display: grid;
  grid-template-rows: auto 1fr;
  grid-template-columns: 1fr;
  gap: 5px;
`;

const FilePreview = styled.img`
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  width: 100%;
  height: 100px;
  position: relative;
  font-size: ${(props) => props.theme.FontSize2}px;
  font-family: ${(props) => props.theme.FontFamiliesInter};
  object-fit: cover;
  grid-row: 1;
  grid-column: 1;
`;

const DocumentPlaceholderIcon = styled.img`
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  width: 100%;
  height: 100px;
  object-fit: cover;
  grid-row: 1;
  grid-column: 1;
`;

const DocumentDetails = styled.div`
  padding: 0 5px 5px;
  grid-row: 2;
  grid-column: 1;
`;

const Title = styled(({ ...rest }) => <div {...rest}></div>)`
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter1};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  color: ${(props) => props?.theme.ColorsTypographyInverse};
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120px;
`;

const DateText = styled(({ ...rest }) => <div {...rest}></div>)`
  text-transform: uppercase;
  font-size: ${(props) => props.theme.FontSize2}px;
  font-family: ${(props) => props.theme.FontFamiliesInter};
  color: ${(props) => props?.theme.ColorsTypographyInverse};
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
`;

interface FileMoleculeProps {
  title: string;
  uploadDate: string;
  previewSrc?: string;
  file: File;
}

/**
 * Renders a box with a default image or the preview of the uploaded image,
 * and two lines of text with the name and the last modified date of the file.
 *
 * @param {FileMoleculeProps} props
 * @returns {JSX.Element}
 */
const FileMolecule: React.FC<FileMoleculeProps> = ({
  title,
  uploadDate,
  previewSrc,
  file,
}) => {
  const [filePreviewSrc, setFilePreviewSrc] = useState<string | undefined>(
    previewSrc
  );

  useEffect(() => {
    if (file && file.type) {
      const fileUrl = URL.createObjectURL(file);
      setFilePreviewSrc(fileUrl);

      // Cleanup the object URL when the component unmounts
      return () => {
        URL.revokeObjectURL(fileUrl);
      };
    }
  }, [file]);

  /**
   * Format a given date string into a human-readable format.
   *
   * The format is "dd MMM yyyy, hh:mm a".
   *
   * @param dateString A date string in the ISO 8601 format.
   * @returns A formatted date string.
   */
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return dateString;
    }

    const options: Intl.DateTimeFormatOptions = {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    };
    return date ? date.toLocaleString('en-GB', options) : '';
  };

  return (
    <DocumentMolecule>
      {file.type.startsWith('image/') ? (
        filePreviewSrc && (
          <FilePreview src={filePreviewSrc} alt="File Preview" />
        )
      ) : (
        <DocumentPlaceholderIcon
          src="https://img.icons8.com/ios-glyphs/30/document--v1.png"
          alt="Document Placeholder"
        />
      )}
      <DocumentDetails>
        <Title>{title}</Title>
        <DateText>{formatDate(uploadDate)}</DateText>
      </DocumentDetails>
    </DocumentMolecule>
  );
};

export default FileMolecule;
