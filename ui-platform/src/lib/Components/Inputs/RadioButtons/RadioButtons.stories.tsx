import type { <PERSON>a, StoryObj } from '@storybook/react';
import RadioButtons, { RadioButtonProps } from './RadioButtons';

const meta: Meta = {
  component: RadioButtons,
  title: 'Components/Inputs/RadioButtons',
};
export default meta;

type Story = StoryObj<typeof RadioButtons>;

export const Overview: Story = {
  args: {
    options: [
      { label: 'Option 1', value: 'option1' },
      { label: 'Option 2', value: 'option2' },
    ],
    textPosition: 'right',
  },
} as StoryObj<RadioButtonProps>;
