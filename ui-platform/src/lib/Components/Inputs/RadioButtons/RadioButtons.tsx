import { FunctionComponent, useState } from 'react';
import styled from 'styled-components';
import { svgs } from '../../Icons/svgs';

export interface RadioButtonProps {
  checkedValue?: string;
  onChange?: (value: string) => void;
  options: { label: string; value: string }[];
  textPosition?: 'left' | 'right';
}

const RadioButtonContainer = styled.div<{ checked: boolean }>`
  width: 16px;
  height: 16px;
  position: relative;
  cursor: pointer;
`;

const RadioIcon = styled.svg<{ checked: boolean }>`
  width: 16px;
  height: 16px;
  position: absolute;
`;

const Text = styled.div<{
  position: 'left' | 'right';
}>`
  position: relative;
  text-transform: uppercase;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  z-index: 0;
  ${(props) =>
    props.position === 'left' ? 'margin-right: 8px;' : 'margin-left: 8px;'}
  &.property1left {
    position: relative;
    border-radius: 4px;
    width: 100%;
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;
    gap: 12px;
    text-align: left;
    font-size: ${(props) => props.theme.FontSize3}px;
    color: ${(props) => props?.theme.ColorsTypographyPrimary};
    font-family: ${(props) => props.theme.FontFamiliesInter};
  }
  &.property1right {
    position: relative;
    border-radius: 4px;
    width: 100%;
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    gap: 12px;
    text-align: left;
    font-size: ${(props) => props.theme.FontSize3}px;
    color: ${(props) => props?.theme.ColorsTypographyPrimary};
    font-family: ${(props) => props.theme.FontFamiliesInter};
  }
`;

const SelectionBox = styled.div`
  display: grid;
  grid-template-columns: 1fr;;
  gap: 12px;
`;

const OptionContainer = styled.div`
  display: grid;
  grid-template-columns: auto 1fr;
  align-items: center;
  gap: 12px;
`;

/**
 * A single radio button molecule component.
 *
 * @remarks
 * This component is used to render a single radio button option. It renders a
 * radio button and a text label with the option's label. The component also
 * handles the click event of the radio button and updates the internal state with
 * the new selected value. If the onChange callback is provided, it is called with
 * the new selected value.
 *
 * @param {RadioButtonProps} props - The component props
 * @prop {string | null} checkedValue - The current selected value
 * @prop {(value: string) => void} onChange - The callback to be called when the
 *  selected value changes
 * @prop {RadioButtonOption[]} options - The options to be rendered as radio buttons
 * @prop {'left' | 'right'} textPosition - The position of the text label
 * @returns {JSX.Element} - The rendered radio button molecule component
 */
const RadioMolecule: FunctionComponent<RadioButtonProps> = ({
  checkedValue,
  onChange,
  options,
  textPosition,
}) => {
  const [selectedValue, setSelectedValue] = useState<string | null>(
    checkedValue || null
  );




  /**
   * Handles the click event of a radio button, updates the internal state with
   * the new selected value and calls the onChange callback if provided.
   * @param {string} value - The value of the selected radio button
   */
  const handleClick = (value: string) => {
    setSelectedValue(value);
    if (onChange) {
      onChange(value);
    }
  };

  return (
    <SelectionBox>
      {options.map((option) => (
        <OptionContainer
          key={option.value}
          onClick={() => handleClick(option.value)}
        >
          {textPosition === 'left' && (
            <Text
              position={textPosition}
              className={`property1${textPosition}`}
            >
              {option.label}
            </Text>
          )}
          <RadioButtonContainer checked={selectedValue === option.value}>
            <RadioIcon
              viewBox={
                svgs[
                  selectedValue === option.value
                    ? 'RadioSelected'
                    : 'RadioDeselected'
                ].viewBox
              }
              checked={selectedValue === option.value}
            >
              <path
                d={
                  svgs[
                    selectedValue === option.value
                      ? 'RadioSelected'
                      : 'RadioDeselected'
                  ].paths[0].d
                }
                fill={
                  svgs[
                    selectedValue === option.value
                      ? 'RadioSelected'
                      : 'RadioDeselected'
                  ].paths[0].fill
                }
              />
            </RadioIcon>
          </RadioButtonContainer>
          {textPosition === 'right' && (
            <Text
              position={textPosition}
              className={`property1${textPosition}`}
            >
              {option.label}
            </Text>
          )}
        </OptionContainer>
      ))}
    </SelectionBox>
  );
};

export default RadioMolecule;
