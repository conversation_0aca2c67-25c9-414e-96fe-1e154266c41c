import { Meta, StoryObj } from '@storybook/react';
import styled from 'styled-components';
import { DropdownInput } from './DropdownInput';

const meta: Meta<typeof DropdownInput> = {
  title: 'Components/Inputs/DropdownInput',
  component: DropdownInput,
  argTypes: {
    multiSelect: { control: 'boolean', defaultValue: false },
  },
};

export default meta;

type Story = StoryObj<typeof DropdownInput>;

export const SingleSelect: Story = {
  args: {
    items: [
      {
        value: 1,
        label: 'Certificate of Compliance',
      },
      { value: 2, label: 'Labour-Geyser' },
      { value: 3, label: 'Fittings' },
    ],
    placeholder: 'Search billable items...',
    multiSelect: false,
    dropdownScroll: true,
    valueProp: 'value',
    labelProp: 'label',
  },
};

export const MultiSelect: Story = {
  args: {
    selected: [
      {
        value: 2,
        label: 'Labour-Geyser',
        iconName: 'search-sm',
      },
      { value: 3, label: 'Fittings ttt' },
    ],
    items: [
      {
        value: 1,
        label: 'Certificate of Compliance',
        iconName: 'search-sm',
        dropdownOptions: [
          {
            id: 1,
            description: 'Certificate of Compliance',
          },
        ],
      },
      {
        value: 2,
        label: 'Labour-Geyser',
        iconName: 'search-sm',
      },
      { value: 3, label: 'Fittings ttt' },
    ],
    labelProp: 'label',
    valueProp: 'value',
    placeholder: 'Select multiple items...',
    multiSelect: true,
    dropdownScroll: true,
    onSelect: (selectedItem) => console.log(selectedItem),
    error: [
      {
        type: 'required',
        message: 'This field is required',
      },
    ],
  },
};

const Dropdown = styled(DropdownInput)`
  .dropdown__input-container {
    width: 500px;
  }
  .dropdown__input-search {
    background: red;
    height: 50px;
  }
  .dropdown__menu {
    width: 500px;
    background: blue;
  }
  .dropdown__menu-content {
    background: green;
    text-align: right;
    color: yellow;
  }
`;

export const CustomStyling: Story = {
  render: (args) => <Dropdown {...args} />,
  args: {
    items: [
      {
        value: 1,
        label: 'Certificate of Compliance',
      },
      {
        value: 2,
        label: 'Labour-Geyser',
      },
      { value: 3, label: 'Fittings ttt' },
    ],
    placeholder: 'Select multiple items...',
    multiSelect: false,
    dropdownScroll: false,
    valueProp: 'value',
    labelProp: 'label',
    onSelect: (selectedItem) =>
      console.log('The selected item is: ', selectedItem),
    error: {
      type: 'required',
      message: 'This field is required',
    },
  },
};
