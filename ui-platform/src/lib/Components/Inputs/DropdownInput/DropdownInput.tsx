import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  Control,
  Controller,
  FieldError,
  RegisterOptions,
} from 'react-hook-form';
import styled, { css } from 'styled-components';
import { IconTypes, svgs } from '../../Icons/svgs';
import { DropdownDefaultMenuComponent } from './DropdownDefaultMenu';
import { DropdownMenuComponent } from './DropdownMenu';

const DropdownInputContainer = styled.div`
  display: grid;
  grid-auto-flow: row;
  gap: 1px;
  transition: color 1s ease, background-color 1s ease;
  position: relative;
  // width: 98%;
`;

const DropdownInputItem = styled.div`
  display: grid;
  align-items: center;
  justify-content: center;
  min-width: 95px;
  /* background-color: #181818; */
  /* border-right: 1px solid #272727; */
  cursor: pointer;
  grid-template-columns: 1fr;

  &:last-of-type {
    border-right: none;
  }
`;

const ActiveZone = styled.div`
  display: grid;
  grid-template-rows: 1fr auto;
  gap: ${(props) => props.theme.SpacingSm};
  align-items: self-start;
  width: 100%;

  &.left {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }
`;

const SearchItemsContainer = styled.div`
  position: relative;
  display: grid;
  align-items: center;
  width: 100%;
`;

const DropdownTextControl = styled.input<{
  dropdownOpen: boolean;
  state: 'default' | 'display-only';
}>`
  height: 40px;
  z-index: 8;
  outline: unset;
  text-align: left;
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  border: 1px solid
    ${(props) =>
      props?.dropdownOpen
        ? props.theme.ColorsStrokesFocus
        : props.theme.ColorsStrokesGrey};
  box-sizing: border-box;
  width: 100%;
  padding: ${(props) => props.theme.SpacingSm};
  /* gap: ${(props) => props.theme.GapSm}; */
  border-radius: ${(props) => props.theme.RadiusXs};
  overflow: visible;
  text-overflow: ellipsis;
  white-space: nowrap;

  ${({ state, dropdownOpen }) =>
    state === 'display-only'
      ? !dropdownOpen
        ? css`
            border: 1px solid ${(props) => props.theme.ColorsControllersDefault};
            background: ${(props) => props.theme.ColorsInputsNonEditable};
          `
        : css`
            background: ${(props) => props.theme.ColorsInputsPrimary};
            border: 1px solid ${(props) => props.theme.ColorsStrokesFocus};
          `
      : ''}

  ::placeholder {
    color: ${(props) => props.theme.ColorsTypographyPrimary};
    transition: color 0.3s ease;
  }
`;

const DropdownIcon = styled.svg<{ isSelected: boolean }>`
  width: 24px;
  height: 24px;
  position: absolute;
  right: 8px;
  z-index: 9;
  cursor: pointer;
`;

interface DropdownInputProps {
  items: any[];
  onSelect?: (item: any[]) => void;
  isSelected?: boolean;
  multiSelect?: boolean;
  iconType?: string;
  placeholder?: string;
  name: string;
  label?: string;
  labelProp: string;
  valueProp: string;
  instructions?: string;
  error?: FieldError | FieldError[];
  rules?: RegisterOptions;
  control?: Control;
  value?: string;
  className?: string;
  dropdownScroll?: boolean;
  state?: 'default' | 'display-only';
  isOpen?: boolean;
  onFocus?: (focusSetter: Function) => void;
  selected?: any[];
}

const Label = styled.label`
  text-align: start;
  margin-bottom: ${(props) => props.theme.SpacingXs};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
`;

const Instruction = styled.div<{ error?: boolean }>`
  margin-top: ${(props) => props.theme.SpacingXs};
  color: ${({ error, theme }) =>
    error ? theme.ColorsUtilityColorError : theme.ColorsTypographySecondary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize2}px;
  text-align: start;
`;

const StyledInstuctionContainer = styled.div`
  margin: unset;
`;

/**
 * A dropdown input component that allows users to select one or multiple items from a list.
 *
 * The component provides a text input field for filtering the list of items.
 * The component also provides a dropdown menu that displays the filtered items.
 * The user can then select one or multiple items from the dropdown menu.
 *
 * The component will call the onSelect prop with the selected item(s) when the user selects an item.
 *
 * The component will also update the value of the input field with the selected item(s) when the user selects an item.
 *
 * The component can be used in multiSelect mode to allow users to select multiple items from the list.
 *
 * The component can also be used in non-multiSelect mode to allow users to select a single item from the list.
 *
 * The component will display an error message if the user selects an item that is not in the list of items.
 *
 * The component will also display an error message if the user selects an item that is already selected.
 *
 * The component will close the dropdown menu when the user clicks outside of the component.
 *
 * @param {Object[]} items - The list of items to display in the dropdown menu.
 * @param {Function} onSelect - The function to call when the user selects an item.
 * @param {boolean} isSelected - Whether the component should display the selected item in the input field.
 * @param {boolean} multiSelect - Whether the component should allow users to select multiple items from the list.
 * @param {string} placeholder - The placeholder text to display in the input field.
 * @param {string} name - The name of the input field.
 * @param {string} label - The label to display above the input field.
 * @param {string} labelProp - The property of the items to use as the label for the items in the dropdown menu.
 * @param {string} valueProp - The property of the items to use as the value for the items in the dropdown menu.
 * @param {string} instructions - The instructions to display below the input field.
 * @param {FieldError | FieldError[]} error - The error message to display if the user selects an item that is not in the list of items.
 * @param {RegisterOptions} rules - The validation rules for the input field.
 * @param {Control} control - The control object from React Hook Form.
 * @param {string} className - The class name to add to the component.
 * @param {boolean} isOpen - Whether the component should be open by default.
 * @param {'default' | 'display-only'} state - The state of the component. If 'default', the component will be interactive. If 'display-only', the component will not be interactive.
 * @param {Function} onFocus - The function to call when the component is focused.
 */
export const DropdownInput: React.FC<DropdownInputProps> = ({
  items,
  onSelect,
  isSelected = false,
  multiSelect = false,
  placeholder = '',
  name = 'dropdown-input',
  label,
  labelProp,
  valueProp,
  instructions,
  error,
  rules,
  dropdownScroll = false,
  control,
  className,
  isOpen = false,
  state = 'default',
  onFocus,
  selected,
}) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [sideDropdownOpen, setSideDropdownOpen] = useState<
    'chevron-right' | 'chevron-left'
  >('chevron-right');
  const [filter, setFilter] = useState('');
  const [filteredItems, setFilteredItems] = useState<any[]>([]);
  const [selectedItems, setSelectedItems] = useState<any[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputContainerRef = useRef<HTMLDivElement>(null);

  // useEffect(() => {
  //   console.log('focussing...');
  //   if (dropdownOpen && onFocus) {
  //     onFocus(setDropdownOpen);
  //   }
  // }, [dropdownOpen, isOpen]);

  useEffect(() => {
    setFilteredItems(items);
  }, [items]);

  useEffect(() => {
    /**
     * Updates the width of the dropdown to match the width of the input container
     */
    const updateDropdownWidth = () => {
      if (inputContainerRef.current) {
        const width = inputContainerRef.current.offsetWidth;
        if (dropdownRef.current) {
          dropdownRef.current.style.width = `${width}px`;
        }
      }
    };

    updateDropdownWidth();
    window.addEventListener('resize', updateDropdownWidth);

    /**
     * Handles click outside of the dropdown by closing the dropdown and resetting the chevron orientation to right.
     * This is used to close the dropdown when the user clicks outside of it.
     * @param {MouseEvent} event - The event triggered by the user's click.
     */
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputContainerRef.current &&
        !inputContainerRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
        setSideDropdownOpen('chevron-right');
      }
    };
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      window.removeEventListener('resize', updateDropdownWidth);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  /**
   * Handles the change event of the input filter box.
   * Filters the items displayed in the dropdown based on the input value.
   * The filter is case-insensitive and looks for the value in all the labels of the items.
   * @param {React.ChangeEvent<HTMLInputElement>} e The change event of the input field.
   */
  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilter(e.target.value);
    setFilteredItems(
      items.filter((item) =>
        item[labelProp]
          ?.toUpperCase()
          ?.includes(e?.target?.value?.toUpperCase())
      )
    );
  };

  /**
   * Handles the click of an item in the dropdown list.
   *
   * If the component is in multiSelect mode, it will toggle the selection of the item.
   * If the component is not in multiSelect mode, it will set the selected item to the one clicked.
   *
   * If the item has the dropdownOptions property, it will open the side dropdown menu.
   *
   * If the onSelect prop is provided, it will be called with the selected item.
   * @param {Object} item The item clicked.
   */
  const handleItemClick = (item: any) => {
    if (multiSelect) {
      setSelectedItems((prev) => {
        const isSelected = prev.some((i) => i[valueProp] === item[valueProp]);
        if (isSelected) {
          return prev.filter((i) => i[valueProp] !== item[valueProp]);
        }
        return [...prev, item];
      });
      return;
    } else {
      setSelectedItems([item]);
    }

    if (item.dropdownOptions) {
      setSideDropdownOpen('chevron-right');
      return;
    }

    setDropdownOpen(false);
    onSelect && onSelect(item);
    // setFilter('');
  };

  useEffect(() => {
    if (onSelect) {
      onSelect(selectedItems);
    }
  }, [selectedItems]);

  useEffect(() => {
    if (selected) {
      setSelectedItems(selected);
    }
  }, [selected]);

  /**
   * Handles the input click event. Toggles the dropdown open and
   * sets the side dropdown open to 'chevron-right'.
   */
  const handleInputClick = () => {
    setDropdownOpen((prev) => !prev);
    setSideDropdownOpen('chevron-right');
  };

  const iconType = dropdownOpen || isOpen ? 'chevron-up' : 'chevron-down';
  const icon = svgs[iconType];

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      defaultValue={selectedItems}
      render={({
        field: { onChange, value, name: nm },
        fieldState: { error: fieldError = error },
        formState,
      }) => (
        <DropdownInputContainer
          dropdown-select-input="dropdown-select-input-container"
          className={className}
        >
          {label && <Label htmlFor={nm}>{label}</Label>}
          <DropdownInputItem data-testid="dropdown-select-input-wrapper">
            <ActiveZone
              data-testid="dropdown-select-input-active-zone"
              className="left"
              ref={inputContainerRef}
            >
              <SearchItemsContainer
                data-testid="dropdown-select-input-field-container"
                className="dropdown__input-container"
              >
                <DropdownTextControlWrapper
                  className="dropdown__input-search"
                  state={state}
                  dataTestId="dropdown-select-input-field"
                  dropdownOpen={dropdownOpen || isOpen}
                  onClick={handleInputClick}
                  placeholder={placeholder}
                  value={value}
                  labelProp={labelProp}
                  selectedItems={selectedItems}
                  setSelectedItems={setSelectedItems}
                  name={nm}
                  readOnly
                />

                <DropdownIcon
                  isSelected={isSelected}
                  viewBox={icon.viewBox}
                  onClick={handleInputClick}
                >
                  <path
                    d={icon.paths[0].d}
                    stroke={isSelected ? '#c4c4c4' : icon.paths[0].stroke}
                    strokeLinecap={icon.paths[0].strokeLinecap}
                    strokeLinejoin={icon.paths[0].strokeLinejoin}
                    fill={icon.paths[0].fill}
                  />
                </DropdownIcon>
              </SearchItemsContainer>
              <div>
                {dropdownOpen &&
                  (dropdownScroll ? (
                    <DropdownMenuComponent
                      inputWidth={inputContainerRef.current?.offsetWidth || 0}
                      ref={dropdownRef}
                      items={filteredItems}
                      selectedItems={selectedItems}
                      handleItemClick={handleItemClick}
                      handleFilterChange={handleFilterChange}
                      searchIcon={svgs['search-sm']}
                      filter={filter}
                      multiSelect={multiSelect}
                      labelProp={labelProp}
                      valueProp={valueProp}
                    />
                  ) : (
                    <DropdownDefaultMenuComponent
                      inputWidth={inputContainerRef.current?.offsetWidth || 0}
                      ref={dropdownRef}
                      items={filteredItems}
                      selectedItems={selectedItems}
                      handleItemClick={handleItemClick}
                      handleFilterChange={handleFilterChange}
                      searchIcon={svgs['search-sm']}
                      filter={filter}
                      multiSelect={multiSelect}
                      labelProp={labelProp}
                      valueProp={valueProp}
                    />
                  ))}
              </div>
            </ActiveZone>
          </DropdownInputItem>
          {fieldError ? (
            Array.isArray(fieldError) ? (
              fieldError.map((error: FieldError, idx: number) => (
                <Instruction key={idx} error={!!error?.message}>
                  {error?.message}
                </Instruction>
              ))
            ) : (
              <Instruction error={!!fieldError?.message}>
                {fieldError?.message}
              </Instruction>
            )
          ) : (
            <StyledInstuctionContainer>
              {instructions && <Instruction>{instructions}</Instruction>}
            </StyledInstuctionContainer>
          )}
        </DropdownInputContainer>
      )}
    />
  );
};

/**
 * @function DropdownTextControlWrapper
 * @description A wrapper component for the `DropdownTextControl` component.
 * @param {Object} props The props object.
 * @param {string} [props.className] The class name to apply to the element.
 * @param {string} props.state The state of the dropdown.
 * @param {string[]} props.selectedItems The selected items.
 * @param {Function} props.setSelectedItems A function to set the selected items.
 * @param {string} props.dataTestId The data test id.
 * @param {string} props.labelProp The label property.
 * @param {boolean} props.dropdownOpen Whether the dropdown is open.
 * @param {Function} props.onClick A function to call when the dropdown is clicked.
 * @param {string} props.placeholder The placeholder text.
 * @param {string|string[]} props.value The value of the dropdown.
 * @param {string} props.name The name of the dropdown.
 * @param {Function} props.onChange A function to call when the dropdown value changes.
 * @param {boolean} props.readOnly Whether the dropdown is read only.
 * @returns {ReactElement} The rendered element.
 */
const DropdownTextControlWrapper = ({
  className,
  state,
  selectedItems,
  setSelectedItems,
  dataTestId,
  labelProp,
  dropdownOpen,
  onClick,
  placeholder,
  value,
  name,
  onChange,
  readOnly,
}: any) => {
  console.log({ selectedItems });
  const label = useMemo(
    () =>
      selectedItems
        ? selectedItems
            .map((item: any) => item[labelProp]?.toUpperCase())
            .join(', ')
        : [],
    [selectedItems]
  );

  console.log({ name, value, selectedItems, label });

  useEffect(() => {
    setSelectedItems(value || []);
  }, []);
  return (
    <DropdownTextControl
      className={className}
      state={state}
      data-testid={dataTestId}
      dropdownOpen={dropdownOpen}
      onClick={onClick}
      placeholder={label || placeholder}
      readOnly={readOnly}
    />
  );
};
