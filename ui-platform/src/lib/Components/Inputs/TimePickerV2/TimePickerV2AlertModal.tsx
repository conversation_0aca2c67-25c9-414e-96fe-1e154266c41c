import React from 'react';
import styled from 'styled-components';
import { TextButton } from '../../Buttons/TextButton/TextButton';
import { AlertPopup } from '../../AlertPopup/Popup';
import { Heading } from '../../Heading/Heading';
import { FormButton } from '../../Buttons/FormButton/FormButton';
import { Icon } from '../../Icons';


interface ActionButtonsProps {
  closeModal: () => void,
}

const AlertMask = styled.div`
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.45);
  display: grid;
  align-items: center;
  justify-content: center;
  gap: ${(props) => props.theme.GapXxl};
  z-index: 10;
  color: ${(props) => props.theme.ColorsIconColorPrimary};
`;

const Button = styled(TextButton)`
  display: grid;
  grid-auto-flow: column;
  gap: 1rem;
  width: 50%;
`;

const ModalContent = styled(AlertPopup)`
  width: auto;
  height: auto;
  min-width: 336px;
  min-height: 234px;
  display: grid;
  justify-items: center;
  grid-auto-flow: row;
  gap: ${(props) => props.theme.GapLg};
  padding: 4rem;
  position: relative;
  overflow: auto;
  z-index: 20;

  > div:not(:first-child) {
    width: 50%;
  }
`;

const BodyText = styled.p`
  text-align: center;
  margin: 0;
`;

const ModalHeading = styled(Heading)`
  margin: unset;
`;

const TopRightButtonWrapper = styled.div`
  position: absolute;
  top: 15px;
  right: 15px;
`;

const ButtonContainer = styled.div`
  display: grid;
  grid-auto-flow: column;
  grid-template-columns: repeat(1, 1fr);
  grid-template-rows: repeat(1, 1fr);
  gap: 1rem;
  justify-items: center;
  width: 100%;
`;

export const TimePickerV2AlertModal: React.FC<ActionButtonsProps> = ({
  closeModal,
}) => {

  return (
    <AlertMask>
      <ModalContent type={'warning'}>
        <TopRightButtonWrapper>
          <FormButton onClick={closeModal} width="35px" height="35px">
            <Icon type="close" size={20} />
          </FormButton>
        </TopRightButtonWrapper>
        <ModalHeading level={1} type={'page-heading'}>
        Are You sure you selected the correct time?
        </ModalHeading>
        <BodyText>
          The SP will charge an After Hours rate.
          <br />
          The time you have entered falls out of normal working hours
        </BodyText>
        <ButtonContainer>
          <Button btnValue="Close" onClick={closeModal} />
        </ButtonContainer>
      </ModalContent>
    </AlertMask>
  );
};