type MinuteInterval = number | number[];

export function maskTime(
  input: string,
  validMinutes?: MinuteInterval
): { time: string; hours: number; minutes: number } {
  let validMinutesArray: number[];

  // Validate and normalize validMinutes input
  if (validMinutes !== undefined) {
    if (typeof validMinutes === 'number') {
      validMinutesArray = Array.from(
        { length: Math.floor(60 / validMinutes) },
        (_, i) => i * validMinutes
      );
    } else if (Array.isArray(validMinutes)) {
      validMinutesArray = validMinutes
        .filter((num) => num >= 0 && num < 60)
        .sort((a, b) => a - b);
    } else {
      throw new Error(
        'Invalid validMinutes type. Must be a number or array of numbers.'
      );
    }
  } else {
    // Default valid minutes if not provided
    validMinutesArray = [0, 15, 30, 45];
  }

  const hoursRegex = /^\d{2}$/;
  const timeRegex = /^(\d{2}):(\d{2})$/;

  if (timeRegex.test(input)) {
    // Input already has a colon
    const [, hours, minutes] = input.match(timeRegex)!;

    if (
      !hoursRegex.test(hours) ||
      !validMinutesArray.includes(parseInt(minutes))
    ) {
      throw new Error(
        `Invalid time format. Hours must be between 00 and 23, and minutes must be one of: ${validMinutesArray.join(
          ', '
        )}`
      );
    }

    // return input;
  }

  if (
    typeof input !== 'string' ||
    (!input.includes(':') && input.length !== 4) ||
    (input.includes(':') && input.length !== 5)
  ) {
    throw new Error('Input must be a string of exactly 4 characters');
  }

  const hours = input.slice(0, 2);
  const minutes = input.slice(-2);

  if (
    !hoursRegex.test(hours) ||
    parseInt(hours) > 23 ||
    !validMinutesArray.includes(parseInt(minutes))
  ) {
    throw new Error(
      `Invalid time value. Hours must be between 00 and 23, and minutes must be one of: ${validMinutesArray.join(
        ', '
      )}`
    );
  }

  return {
    time: `${hours}:${minutes}`,
    hours: parseInt(hours),
    minutes: parseInt(minutes),
  };
}
