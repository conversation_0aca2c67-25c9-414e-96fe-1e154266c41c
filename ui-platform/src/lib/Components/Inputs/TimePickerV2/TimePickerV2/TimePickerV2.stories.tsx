import { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import { TimePickerV2 } from './TimePickerV2';

const meta: Meta<typeof TimePickerV2> = {
  title: 'Components/Inputs/TimePickerV2/TimePickerV2',
  component: TimePickerV2,
};

export default meta;

type Story = StoryObj<typeof TimePickerV2>;

export const Default: Story = {
  args: {
    onSelect: (time: string) => console.log({ time }),
  },
};
