import { Meta, StoryObj } from '@storybook/react';
import { TimePickerV2Input } from './TimePickerV2Input';

const meta: Meta<typeof TimePickerV2Input> = {
  title: 'Components/Inputs/TimePickerV2/TimePickerV2Input',
  component: TimePickerV2Input,
};
export default meta;
type Story = StoryObj<typeof TimePickerV2Input>;

export const SingleTimeInput: Story = {
  args: {
    icon: 'alarm-clock',
    iconPosition: 'right',
  },
};
