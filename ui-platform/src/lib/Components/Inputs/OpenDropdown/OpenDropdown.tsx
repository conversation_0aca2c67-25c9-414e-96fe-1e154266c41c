import React, { FunctionComponent, useEffect, useState } from 'react';
import styled from 'styled-components';

/**
 * Props for the Dropdown component.
 * @property {string[]} items - List of items to display and filter within the dropdown.
 * @property {string} [placeholder="Type to filter"] - Placeholder text for the input field.
 */
export interface DropdownListProps {
  items: string[];
  placeholder?: string;
}

const SPLISTWrapper = styled.div`
  max-width: 720px;
  position: relative;
  display: grid;
  grid-template-rows: auto;
  gap: 24px;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize5}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
`;

const SearchComponent = styled.div`
  align-self: stretch;
  display: grid;
  grid-template-columns: 1fr;
`;

const InputBox = styled.div`
  border-radius: 4px;
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  border: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
  display: grid;
  align-items: center;
  padding: 8px;
`;

const InputHereWrapper = styled.div`
  display: grid;
  align-items: flex-start;
`;

const InputHere = styled.input` 
  position: relative;
  cursor: text;
  background: transparent;
  border: none;
  outline: none;
  color: inherit;
  font-size: inherit;
  width: 100%;
`;

const DropdownList = styled.div<{ isVisible: boolean }>`
  align-self: stretch;
  display: ${({ isVisible }) => (isVisible ? 'grid' : 'none')};
  grid-template-columns: 1fr;
  gap: 8px;
  font-size: ${(props) => props.theme.FontSize3}px;
`;

const List1 = styled.div`
  height: 228px;
  display: grid;
  grid-template-rows: auto 1fr auto;
  gap: 8px;
`;

const DividerLine = styled.div`
  align-self: stretch;
  height: 2px;
`;

const GlowLine = styled.div`
  position: absolute;
  width: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 0.49) 50%,
    rgba(255, 255, 255, 0)
  );
  height: 2px;
`;

const SPS = styled.div`
  align-self: stretch;
  display: grid;
  grid-template-rows: repeat(auto-fill, minmax(33px, 1fr));
  gap: 4px;
`;

const ClaimClassCard = styled.div`
  align-self: stretch;
  border-radius: 4px;
  background-color: ${(props) => props.theme.ColorsControllersDefault};
  height: 33px;
  display: grid;
  align-items: center;
  padding: 8px 16px;
  box-sizing: border-box;
  cursor: pointer;
`;

const PipesOnly = styled.b`
  align-self: stretch;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

/**
 * Dropdown Component
 *
 * A component that provides an input field for filtering a list of items.
 * Allows users to type and filter items dynamically, with the ability to select an item from the dropdown.
 *
 * @component
 * @example
 * <Dropdown items={['Option 1', 'Option 2']} placeholder="Select an option" />
 *
 * @param {DropdownListProps} props - Props for the Dropdown component.
 * @param {string[]} props.items - List of items to display in the dropdown.
 * @param {string} [props.placeholder="Type to filter"] - Placeholder text for the input field.
 * @returns {JSX.Element} Rendered Dropdown component.
 */
export const Dropdown: FunctionComponent<DropdownListProps> = ({
  items,
  placeholder = 'Type to filter',
}) => {
  const [inputValue, setInputValue] = useState('');
  const [filteredItems, setFilteredItems] = useState(items);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [isDropdownVisible, setDropdownVisible] = useState(true);

  useEffect(() => {
    setFilteredItems(
      items.filter((item) =>
        item.toLowerCase().includes(inputValue.toLowerCase())
      )
    );
  }, [inputValue, items]);

  /**
   * Handles selection of an item from the dropdown.
   * Updates the input value and hides the dropdown.
   * @param {string} item - The selected item from the dropdown.
   */
  const handleSelect = (item: string) => {
    setSelectedItem(item);
    setInputValue(item);
    setDropdownVisible(false); 
  };

  /**
   * Toggles the dropdown visibility and resets the input when clicked.
   */
  const handleInputClick = () => {
    if (selectedItem) {
      setSelectedItem(null);
      setInputValue('');
    }
    setDropdownVisible(true); 
  };

  /**
   * Updates the input value as the user types, triggering filtering.
   * @param {React.ChangeEvent<HTMLInputElement>} event - The input change event.
   */
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value); 
  };

  return (
    <SPLISTWrapper>
      <SearchComponent>
        <InputBox onClick={handleInputClick}>
          <InputHereWrapper>
            <InputHere
              value={inputValue} 
              onChange={handleInputChange} 
              placeholder={selectedItem || placeholder} 
            />
          </InputHereWrapper>
        </InputBox>
      </SearchComponent>

      <DropdownList isVisible={isDropdownVisible}>
        <List1>
          <DividerLine>
            <GlowLine />
          </DividerLine>
          <SPS>
            {filteredItems.map((item) => (
              <ClaimClassCard key={item} onClick={() => handleSelect(item)}>
                <PipesOnly>{item}</PipesOnly>
              </ClaimClassCard>
            ))}
          </SPS>
          <DividerLine>
            <GlowLine />
          </DividerLine>
        </List1>
      </DropdownList>
    </SPLISTWrapper>
  );
};
