import { <PERSON>a, StoryObj } from '@storybook/react';
import {Dropdown} from './OpenDropdown';

const meta: Meta<typeof Dropdown> = {
  title: 'Components/Inputs/OpenDropdown',
  component: Dropdown,
  argTypes: {},
};

export default meta;
type Story = StoryObj<typeof Dropdown>;

export const Default: Story = {
  args: {
    items: [
      'Storm',
      'Fire',
      'Impact',
      'Pipes Only',
      'Flood',
      'Pipes + Damages',
      'Lightning',
    ],
    placeholder: 'Type to filter',
  },
};
