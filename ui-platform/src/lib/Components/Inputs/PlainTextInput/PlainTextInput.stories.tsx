import type { Meta, StoryObj } from '@storybook/react';
import { Icon } from '../../Icons';
import { PlainTextInput } from './PlainTextInput';

const meta: Meta<typeof PlainTextInput> = {
  component: PlainTextInput,
  title: 'Components/Inputs/TextInput/PlainTextInput',
  argTypes: {
    error: {
      control: {
        type: 'boolean',
      },
      description: 'Toggle error state',
    },
    position: {
      control: {
        type: 'select',
        options: ['left', 'right', 'none'],
      },
      description: 'Position of the icon',
    },
    // onChange: { action: 'changed' },
  },
};
export default meta;

type Story = StoryObj<typeof PlainTextInput>;

export const Overview: Story = {
  args: {
    placeholder: 'Text Here',
    icon: <Icon type="user-01" />,
    position: 'none',
    error: { type: 'required', message: 'This field is required' },
    name: 'username',
  },
};

export const WithIconLeft: Story = {
  args: {
    placeholder: 'Text Here',
    icon: <Icon type="user-01" />,
    position: 'left',
    error: { type: 'required', message: 'This field is required' },
    name: 'username',
  },
};

export const WithIconRight: Story = {
  args: {
    placeholder: 'Text Here',
    icon: <Icon type="user-01" />,
    position: 'right',
    error: { type: 'required', message: 'This field is required' },
    name: 'username',
  },
};

export const WithErrorState: Story = {
  args: {
    placeholder: 'Text Here',
    icon: <Icon type="user-01" />,
    position: 'none',
    error: { type: 'required', message: 'This field is required' },
    name: 'username',
  },
};

export const WithLabelAndInstruction: Story = {
  args: {
    placeholder: 'Text Here',
    icon: <Icon type="user-01" />,
    position: 'none',
    error: { type: 'required', message: 'This field is required' },
    label: 'Username',
    instructions: 'Enter your username.',
    name: 'username',
  },
};


export const WithDisplayOnly: Story = {
  args: {
    placeholder: 'Text Here',
    icon: <Icon type="user-01" />,
    position: 'none',
    error: { type: 'required', message: 'This field is required' },
    label: 'Username',
    instructions: 'Enter your username.',
    name: 'username',
    state: 'display-only',
  },
};