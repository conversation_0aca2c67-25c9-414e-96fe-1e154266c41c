import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  Control,
  Controller,
  FieldError,
  RegisterOptions,
  useForm,
} from 'react-hook-form';
import styled, { css } from 'styled-components';
import { Icon } from '../../Icons';
import { IconTypes, svgs } from '../../Icons/svgs';
import { DropdownDefaultMenuComponent } from './DropdownSelectDefaultMenu';
import { DropdownMenuComponent } from './DropdownSelectMenu';

const DropdownInputContainer = styled.div`
  display: grid;
  grid-auto-flow: row;
  /* gap: 1px; */
  gap: ${(props) => props.theme.SpacingXs};
  transition: color 1s ease, background-color 1s ease;
  position: relative;
  // width: 98%;
`;

const DropdownInputItem = styled.div`
  height: 37px;
  display: grid;
  align-items: center;
  justify-content: center;
  min-width: 95px;
  /* background-color: #181818; */
  /* border-right: 1px solid #272727; */
  cursor: pointer;
  grid-template-columns: 1fr;

  &:last-of-type {
    border-right: none;
  }
`;

const ActiveZone = styled.div`
  display: grid;
  grid-template-rows: 1fr auto;
  gap: ${(props) => props.theme.SpacingSm};
  align-items: self-start;
  width: 100%;

  &.left {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }
`;

const SearchItemsContainer = styled.div<{
  dropdownOpen: boolean;
  state: 'default' | 'display-only';
  hasIconLeft: boolean;
  error?: boolean;
}>`
  height: auto;
  min-height: 37px;
  position: relative;
  display: grid;
  grid-template-columns: ${(props) =>
    props.hasIconLeft ? 'auto 1fr' : '1fr auto'};
  gap: ${(props) => props.theme.SpacingXs};
  align-items: center;
  width: 100%;
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  border: 1px solid
    ${(props) =>
      props?.dropdownOpen
        ? props.theme.ColorsStrokesFocus
        : props?.error
        ? props.theme.ColorsUtilityColorError
        : props.theme.ColorsStrokesGrey};
  box-sizing: border-box;
  width: 100%;
  padding: ${(props) => props.theme.SpacingSm};
  border-radius: ${(props) => props.theme.RadiusXs};

  ${({ state, dropdownOpen }) =>
    state === 'display-only'
      ? !dropdownOpen
        ? css`
            border: 1px solid ${(props) => props.theme.ColorsControllersDefault};
            background: ${(props) => props.theme.ColorsInputsNonEditable};
          `
        : css`
            background: ${(props) => props.theme.ColorsInputsPrimary};
            border: 1px solid ${(props) => props.theme.ColorsStrokesFocus};
          `
      : ''}
`;

const DropdownTextControl = styled.input<{
  dropdownOpen: boolean;
  state: 'default' | 'display-only';
}>`
  /* height: 40px; */
  /* z-index: 8; */
  all: unset;
  font-family: inherit;
  /* outline: unset; */
  text-align: left;
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
  /* background-color: ${(props) => props.theme.ColorsInputsPrimary};
  border: 1px solid
    ${(props) =>
    props?.dropdownOpen
      ? props.theme.ColorsStrokesFocus
      : props.theme.ColorsStrokesGrey}; */
  /* box-sizing: border-box; */
  width: 100%;
  /* padding: ${(props) => props.theme.SpacingSm}; */
  /* gap: ${(props) => props.theme.GapSm}; */
  /* border-radius: ${(props) => props.theme.RadiusXs}; */
  overflow: visible;
  text-overflow: ellipsis;
  white-space: nowrap;

  /* ${({ state, dropdownOpen }) =>
    state === 'display-only'
      ? !dropdownOpen
        ? css`
            border: 1px solid ${(props) => props.theme.ColorsControllersDefault};
            background: ${(props) => props.theme.ColorsInputsNonEditable};
          `
        : css`
            background: ${(props) => props.theme.ColorsInputsPrimary};
            border: 1px solid ${(props) => props.theme.ColorsStrokesFocus};
          `
      : ''} */

  ${(props) => props.theme.DesktopFormsInputText}
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  font-style: normal;

  ::placeholder {
    color: ${(props) => props.theme.ColorsTypographyPrimary};
    transition: color 0.3s ease;
  }
`;

const DropdownIcon = styled.svg<{ isSelected: boolean }>`
  width: 24px;
  height: 24px;
  position: absolute;
  right: 8px;
  z-index: 9;
  cursor: pointer;
`;

const IconWrapper = styled(Icon)`
  display: grid;
  grid-auto-flow: column;
  place-items: center;
  align-self: center;

  &.icon__right {
    margin-right: ${(props) => props.theme.SpacingXl};
    padding: 0 ${(props) => props.theme.SpacingSm};
  }
`;

interface DropdownInputProps {
  items: any[];
  isSelected?: boolean;
  multiSelect?: boolean;
  iconType?: string;
  placeholder?: string;
  name: string;
  label?: string;
  labelProp: string;
  valueProp: string;
  instructions?: string;
  error?: FieldError | null;
  rules?: RegisterOptions;
  control?: Control;
  className?: string;
  dropdownScroll?: boolean;
  state?: 'default' | 'display-only';
  isOpen?: boolean;
  onFocus?: (ev?: any) => any;
  icon?: IconTypes;
  position?: 'left' | 'right' | 'none';
  selected?: any[];
  disabled?: boolean;
}

const Label = styled.label`
  text-align: start;
  margin-bottom: ${(props) => props.theme.SpacingXs};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
`;

const Instruction = styled.div<{ error?: boolean }>`
  margin-top: ${(props) => props.theme.SpacingXs};
  color: ${({ error, theme }) =>
    error ? theme.ColorsUtilityColorError : theme.ColorsTypographySecondary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize2}px;
  text-align: start;
`;

const StyledInstuctionContainer = styled.div`
  margin: unset;
`;

/**
 * A dropdown select component that can be used to select one or multiple items
 * from a list of options. It supports displaying a label and instructions, and
 * provides a filter input to narrow down the list of options. It also supports
 * displaying a second dropdown menu when an item is selected.
 *
 * @example
 * <DropdownSelect
 *   items={[
 *     { label: 'Option 1', value: 'option-1' },
 *     { label: 'Option 2', value: 'option-2' },
 *     { label: 'Option 3', value: 'option-3' },
 *   ]}
 *   name="dropdown-input"
 *   label="Select an option"
 *   labelProp="label"
 *   valueProp="value"
 *   instructions="This is an example of a dropdown select component"
 * />
 */

export const DropdownSelect: React.FC<DropdownInputProps> = ({
  items,
  icon,
  position = 'none',
  isSelected = false,
  multiSelect = false,
  placeholder = '',
  name = 'dropdown-input',
  label,
  labelProp,
  valueProp,
  instructions,
  error,
  rules,
  dropdownScroll = false,
  control,
  className,
  isOpen = false,
  state = 'default',
  selected,
  disabled,
  onFocus,
}) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [sideDropdownOpen, setSideDropdownOpen] = useState<
    'chevron-right' | 'chevron-left'
  >('chevron-right');
  const [filter, setFilter] = useState('');
  const [filteredItems, setFilteredItems] = useState<any[]>([]);
  const [selectedItems, setSelectedItems] = useState<any[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputContainerRef = useRef<HTMLDivElement>(null);

  // useEffect(() => {
  //   console.log('focussing...');
  //   if (dropdownOpen && onFocus) {
  //     onFocus(setDropdownOpen);
  //   }
  // }, [dropdownOpen, isOpen]);

  // Handle Array of Non-Object Items in DropdownSelect
  const normalizedItems = useMemo(() => {
    return items.map((item) =>
      typeof item === 'object' ? item : { [labelProp]: item, [valueProp]: item }
    );
  }, [items, labelProp, valueProp]);

  // useEffect(() => {
  //   setFilteredItems(items);
  // }, [items]);

  useEffect(() => {
    setFilteredItems(normalizedItems);
  }, [normalizedItems]);

  useEffect(() => {
    /**
     * Updates the width of the dropdown menu to match the width of the input
     * container. This is needed to ensure that the dropdown menu is not wider than
     * the input container.
     */
    const updateDropdownWidth = () => {
      if (inputContainerRef.current) {
        const width = inputContainerRef.current.offsetWidth;
        if (dropdownRef.current) {
          dropdownRef.current.style.width = `${width}px`;
        }
      }
    };

    updateDropdownWidth();
    window.addEventListener('resize', updateDropdownWidth);

    /**
     * Handles click outside of the dropdown to close it and reset the arrow
     * @param {MouseEvent} event The mouse event
     */
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputContainerRef.current &&
        !inputContainerRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
        setFilteredItems(items);
        setSideDropdownOpen('chevron-right');
      }
    };
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      window.removeEventListener('resize', updateDropdownWidth);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  /**
   * Handles the click of an item in the dropdown list.
   *
   * If multiSelect is true, toggles the selection of the item and updates the
   * selectedItems state.
   *
   * If multiSelect is false, sets the selectedItems to the clicked item and
   * closes the dropdown.
   *
   * If the item has dropdownOptions, sets the side dropdown to open and
   * returns.
   *
   * @param {object} item The item that was clicked.
   */
  const handleItemClick = (item: any) => {
    if (multiSelect) {
      setSelectedItems((prev) => {
        // const isSelected = prev.some((i) => i[valueProp] === item[valueProp]);
        // if (isSelected) {
        //   return prev.filter((i) => i[valueProp] !== item[valueProp]);
        // }
        // return [...prev, item];
        const itemValue = item[valueProp];
        const isSelected = prev.some((i) => i[valueProp] === itemValue);
        if (isSelected) {
          return prev.filter((i) => i[valueProp] !== itemValue);
        }
        return [...prev, item];
      });
      return;
    } else {
      setSelectedItems([item]);
    }

    if (item.dropdownOptions) {
      setSideDropdownOpen('chevron-right');
      return;
    }

    setDropdownOpen(false);
  };

  /**
   * Handles the input click event. Toggles the dropdown open and
   * sets the side dropdown open to 'chevron-right'.
   */
  const handleInputClick = () => {
    if (onFocus) {
      onFocus();
    }
    if (!disabled) {
      setDropdownOpen((prev) => !prev);
      setSideDropdownOpen('chevron-right');
    }
  };

  const iconType = dropdownOpen || isOpen ? 'chevron-up' : 'chevron-down';
  const dropdownIcon = svgs[iconType];

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      defaultValue={selected}
      render={({
        field: { onChange, value, name: nm },
        fieldState: { error: fieldError = error },
        formState,
      }) => {
        /**
         * Handles the change event on the filter input. Updates the filter state and
         * the filtered items based on the search input.
         * @param e The change event from the input element.
         */
        const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
          setFilter(e.target.value);
          console.log({ items, value });
          setFilteredItems(
            items.filter(
              (item) =>
                item[labelProp]
                  ?.toUpperCase()
                  .includes(e?.target?.value?.toUpperCase())
              // Filter out already selected items so that they don't show in filter results
              // && (Array.isArray(value)
              // ? !value?.includes(item[valueProp])
              // : item[valueProp] !== value)
            )
          );
        };

        return (
          <DropdownInputContainer
            dropdown-select-input="dropdown-select-input-container"
            className={className}
          >
            {label && <Label htmlFor={nm}>{label}</Label>}
            <DropdownInputItem data-testid="dropdown-select-input-wrapper">
              <ActiveZone
                data-testid="dropdown-select-input-active-zone"
                className="left"
                ref={inputContainerRef}
              >
                <SearchItemsContainer
                  hasIconLeft={position === 'left'}
                  data-testid="dropdown-select-input-field-container"
                  className="dropdown__input-container"
                  state={state}
                  dropdownOpen={dropdownOpen}
                  error={!!fieldError?.message}
                >
                  {position === 'left' && (
                    <IconWrapper type={icon} width={24} height={24} />
                  )}
                  <DropdownTextControlWrapper
                    className="dropdown__input-search"
                    state={state}
                    dataTestId="dropdown-select-input-field"
                    dropdownOpen={dropdownOpen || isOpen}
                    onClick={handleInputClick}
                    placeholder={placeholder}
                    value={value}
                    onChange={onChange}
                    labelProp={labelProp}
                    valueProp={valueProp}
                    selectedItems={selectedItems}
                    setSelectedItems={setSelectedItems}
                    items={items}
                    multiSelect={multiSelect}
                    name={nm}
                    readOnly
                  />
                  {position === 'right' && icon && (
                    <IconWrapper
                      className="icon__right"
                      type={icon}
                      width={24}
                      height={24}
                    />
                  )}
                  <DropdownIcon
                    isSelected={isSelected}
                    viewBox={dropdownIcon.viewBox}
                    onClick={handleInputClick}
                  >
                    <path
                      d={dropdownIcon.paths[0].d}
                      stroke={
                        isSelected ? '#c4c4c4' : dropdownIcon.paths[0].stroke
                      }
                      strokeLinecap={dropdownIcon.paths[0].strokeLinecap}
                      strokeLinejoin={dropdownIcon.paths[0].strokeLinejoin}
                      fill={dropdownIcon.paths[0].fill}
                    />
                  </DropdownIcon>
                </SearchItemsContainer>
                <div>
                  {dropdownOpen &&
                    (dropdownScroll ? (
                      <DropdownMenuComponent
                        inputWidth={inputContainerRef.current?.offsetWidth || 0}
                        ref={dropdownRef}
                        items={filteredItems}
                        selectedItems={value}
                        handleItemClick={handleItemClick}
                        handleFilterChange={handleFilterChange}
                        searchIcon={svgs['search-sm']}
                        filter={filter}
                        multiSelect={multiSelect}
                        labelProp={labelProp}
                        valueProp={valueProp}
                      />
                    ) : (
                      <DropdownDefaultMenuComponent
                        inputWidth={inputContainerRef.current?.offsetWidth || 0}
                        ref={dropdownRef}
                        items={filteredItems}
                        selectedItems={value}
                        handleItemClick={handleItemClick}
                        handleFilterChange={handleFilterChange}
                        searchIcon={svgs['search-sm']}
                        filter={filter}
                        multiSelect={multiSelect}
                        labelProp={labelProp}
                        valueProp={valueProp}
                      />
                    ))}
                </div>
              </ActiveZone>
            </DropdownInputItem>
            {fieldError ? (
              Array.isArray(fieldError) ? (
                fieldError.map((error: FieldError, idx: number) => (
                  <Instruction key={idx} error={!!error?.message}>
                    {error?.message}
                  </Instruction>
                ))
              ) : (
                <Instruction error={!!fieldError?.message}>
                  {fieldError?.message}
                </Instruction>
              )
            ) : (
              <StyledInstuctionContainer>
                {instructions && <Instruction>{instructions}</Instruction>}
              </StyledInstuctionContainer>
            )}
          </DropdownInputContainer>
        );
      }}
    />
  );
};

/**
 * @function DropdownTextControlWrapper
 * @description A wrapper component for the `DropdownTextControl` component.
 * @param {Object} props The props object.
 * @param {string} [props.className] The class name to apply to the element.
 * @param {string} props.state The state of the dropdown.
 * @param {string[]} props.items The items to display in the dropdown.
 * @param {boolean} [props.multiSelect] Whether the dropdown is multi-select.
 * @param {string|string[]} props.selectedItems The selected items.
 * @param {Function} props.setSelectedItems A function to set the selected items.
 * @param {string} props.dataTestId The data test id.
 * @param {string} props.labelProp The label property.
 * @param {string} props.valueProp The value property.
 * @param {boolean} [props.dropdownOpen] Whether the dropdown is open.
 * @param {Function} [props.onClick] A function to call when the dropdown is clicked.
 * @param {string} [props.placeholder] The placeholder text.
 * @param {string|string[]} [props.value] The value of the dropdown.
 * @param {string} [props.name] The name of the dropdown.
 * @param {Function} [props.onChange] A function to call when the dropdown value changes.
 * @param {boolean} [props.readOnly] Whether the dropdown is read only.
 * @returns {ReactElement} The rendered element.
 */
const DropdownTextControlWrapper = ({
  className,
  state,
  items,
  multiSelect,
  selectedItems,
  setSelectedItems,
  dataTestId,
  valueProp,
  labelProp,
  dropdownOpen,
  onClick,
  placeholder,
  value,
  name,
  onChange,
  readOnly,
}: any) => {
  const label = useMemo(
    () =>
      selectedItems && Array.isArray(selectedItems) && selectedItems[0]
        ? selectedItems
            .map(
              (item: any) =>
                (typeof item[labelProp] === 'string' &&
                  item[labelProp].toUpperCase()) ||
                JSON.stringify(item[labelProp])?.toUpperCase()
            )
            .join(', ')
        : '',
    [selectedItems]
  );
  const selectedItemsStr = useMemo(
    () => JSON.stringify(selectedItems),
    [selectedItems]
  );
  useEffect(() => {
    if (multiSelect) {
      if (value) {
        const arr = value
          ? value.reduce((acc: any[], v: any) => {
              const obj = items.find((item: any) => item[valueProp] === v);
              if (obj) {
                return [...acc, obj];
              } else {
                return acc;
              }
            }, [])
          : [];
        setSelectedItems(arr);
      }
    } else {
      if (value) {
        const obj = items.find((item: any) => item[valueProp] == value);
        setSelectedItems([obj]);
      }
    }
  }, [items, value]);

  useEffect(() => {
    if (multiSelect) {
      if (selectedItems && selectedItems[0]) {
        // if it contains something
        onChange(selectedItems.map((item: any) => item[valueProp]));
      }
    } else {
      if (selectedItems && selectedItems[0]) {
        // if it contains the item
        onChange(selectedItems[0][valueProp]);
      }
    }
  }, [selectedItemsStr]);
  return (
    <DropdownTextControl
      className={className}
      state={state}
      data-testid={dataTestId}
      dropdownOpen={dropdownOpen}
      onClick={onClick}
      placeholder={label || placeholder}
      readOnly={readOnly}
    />
  );
};
