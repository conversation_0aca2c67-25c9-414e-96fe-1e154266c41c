import { Meta, StoryObj } from '@storybook/react';
import styled from 'styled-components';
import { DropdownSelect } from './DropdownSelect';

const meta: Meta<typeof DropdownSelect> = {
  title: 'Components/Inputs/DropdownSelect',
  component: DropdownSelect,
  argTypes: {
    multiSelect: { control: 'boolean', defaultValue: false },
  },
};

export default meta;

type Story = StoryObj<typeof DropdownSelect>;

const units = ['', 'm³', 'm', 'm²', 'unit', 'L', 'km'];

export const NonObjectSingleSelect: Story = {
  args: {
    items: units,
    placeholder: 'Search billable items...',
    valueProp: 'value',
    labelProp: 'label',
    multiSelect: false,
    dropdownScroll: true,
    error: {
      type: 'required',
      message: 'This field is required',
    },
    instructions: 'Select a billable item',
  },
}

export const SingleSelect: Story = {
  args: {
    items: [
      {
        value: 1,
        label: 'Certificate of Compliance',
      },
      { value: 2, label: 'Labour-Geyser' },
      { value: 3, label: 'Fittings' },
    ],
    placeholder: 'Search billable items...',
    valueProp: 'value',
    labelProp: 'label',
    multiSelect: false,
    dropdownScroll: true,
    error: {
      type: 'required',
      message: 'This field is required',
    },
    instructions: 'Select a billable item',
  },
};

export const MultiSelect: Story = {
  args: {
    items: [
      {
        value: 1,
        label: 'Certificate of Compliance',
        iconName: 'search-sm',
        dropdownOptions: [
          {
            id: 1,
            description: 'Certificate of Compliance',
          },
        ],
      },
      {
        value: 2,
        label: 'Labour-Geyser',
        iconName: 'search-sm',
      },
      { value: 3, label: 'Fittings ttt' },
    ],
    labelProp: 'label',
    valueProp: 'value',
    placeholder: 'Select multiple items...',
    multiSelect: true,
    dropdownScroll: true,
    error: [
      {
        type: 'required',
        message: 'This field is required',
      },
    ],
  },
};

const Dropdown = styled(DropdownSelect)`
  .dropdown__input-container {
    width: 500px;
  }
  .dropdown__input-search {
    background: red;
    height: 50px;
  }
  .dropdown__menu {
    width: 500px;
    background: blue;
  }
  .dropdown__menu-content {
    background: green;
    text-align: right;
    color: yellow;
  }
`;

export const CustomStyling: Story = {
  render: (args) => <Dropdown {...args} />,
  args: {
    items: [
      {
        value: 1,
        label: 'Certificate of Compliance',
      },
      {
        value: 2,
        label: 'Labour-Geyser',
      },
      { value: 3, label: 'Fittings ttt' },
    ],
    placeholder: 'Select multiple items...',
    multiSelect: false,
    dropdownScroll: false,
    valueProp: 'value',
    labelProp: 'label',
    error: {
      type: 'required',
      message: 'This field is required',
    },
  },
};

export const SelectWithIcon: Story = {
  args: {
    items: [
      {
        value: 1,
        label: 'Certificate of Compliance',
      },
      { value: 2, label: 'Labour-Geyser' },
      { value: 3, label: 'Fittings' },
    ],
    icon: 'search-sm',
    position: 'right',
    placeholder: 'Search billable items...',
    valueProp: 'value',
    labelProp: 'label',
    multiSelect: false,
    dropdownScroll: true,
  },
};
export const MultiSelectWithIcon: Story = {
  args: {
    items: [
      {
        value: 1,
        label: 'Certificate of Compliance',
      },
      { value: 2, label: 'Labour-Geyser' },
      { value: 3, label: 'Fittings' },
    ],
    icon: 'search-sm',
    position: 'right',
    placeholder: 'Search billable items...',
    valueProp: 'value',
    labelProp: 'label',
    multiSelect: true,
    dropdownScroll: true,
  },
};
