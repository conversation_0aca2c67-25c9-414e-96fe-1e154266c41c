import React from 'react';
import styled from 'styled-components';

type TextLabelProps = {
  labelText: string;
}

export const LabelText = styled(({ ...rest }) => <label {...rest}></label>)`
    color: ${(props) => props?.theme.ColorsTypographyPrimary};
    font-size: ${(props) => props.theme.FontSize3}px;
    font-family: ${(props) => props.theme.FontFamiliesInter};
    font-style: normal;
    font-weight: ${(props) => props.theme.FontWeightsInter1};
    line-height: ${(props) => props.theme.LineHeights0};
`;

/**
 * A component to render a text label for a form input.
 *
 * @example
 * const labelText = 'Your name';
 * <TextLabel labelText={labelText} />
 *
 * @param {string} labelText - The text to display as the label
 * @returns {ReactElement} The text label component
 */
export const TextLabel: React.FC<TextLabelProps> = ({ labelText }) => {
  return <LabelText>{labelText}</LabelText>;
};

