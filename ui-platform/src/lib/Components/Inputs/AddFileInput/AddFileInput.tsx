import React, { useRef, ChangeEvent, HTMLInputTypeAttribute } from 'react';
import { useForm, Control, FieldError } from 'react-hook-form';
import styled, { css } from 'styled-components';
import { Icon } from '../../Icons';
import { PlainTextInput } from '../PlainTextInput/PlainTextInput';

interface AddFileInputProps {
  placeholder?: string;
  icon?: React.ReactNode;
  type: HTMLInputTypeAttribute | undefined;
  position?: 'left' | 'right' | 'none';
  error?: FieldError | undefined | null;
  control?: Control;
  name: string;
  disabled?: boolean;
  label?: string;
  className?: string;
  onFileSelect?: (file: File) => void;
}

const IconText = styled.div`
  text-align: right;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  color: var(--colors-typography-secondary, #c4c4c4);
  font-family: Inter;
`;

const IconTextContainer = styled.div`
  display: grid;
  grid-template-columns: auto auto;
  gap: 4px;
  align-items: center;
  background-color: transparent;
  border: 0.5px solid ${(props) => props.theme.ColorsTypographySecondaryStroke};
  border-radius: ${(props) => props.theme.RadiusXs};
  padding: ${(props) => props.theme.SpacingXs};
  margin-right: 4px;
  margin-bottom: 2px;
`;

const StyledAddFileInput = styled(PlainTextInput)<{
  error?: FieldError | null;
}>`
  border-radius: ${(props) => props.theme.RadiusXs};
  padding: ${(props) => props.theme.SpacingLg};
  position: relative;
  max-width: fit-content;

  .icon-right {
    position: absolute;
    top: 50%;
    right: 16px;
    transform: translateY(-50%);
    display: grid;
    grid-template-columns: auto auto;
    gap: ${(props) => props.theme.GapXxs};
    align-items: center;
    cursor: pointer;
  }

  ${({ error }) =>
    error &&
    css`
      position: relative;
      max-width: fit-content;
      align-items: center;
      display: grid;
      grid-template-columns: auto;
      right: 16px;
    `}
`;

export const AddFileInput: React.FC<AddFileInputProps> = ({
  placeholder = 'Input here',
  icon,
  position = 'right',
  error,
  control,
  name,
  disabled,
  label,
  className,
  onFileSelect,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0 && onFileSelect) {
      onFileSelect(files[0]);
    }
  };

  const iconElement = (
    <div className="icon-right">
      <IconTextContainer>
        <Icon type="file-07" size={16} color="#c4c4c4" />
        <IconText>Add File</IconText>
      </IconTextContainer>
    </div>
  );

  return (
    <div onClick={handleClick} style={{ cursor: 'pointer' }}>
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        onChange={handleFileChange}
        disabled={disabled}
      />

      <StyledAddFileInput
        placeholder={placeholder}
        type="text"
        name={name}
        control={control}
        icon={iconElement}
        position={position}
        className={className}
        disabled={disabled}
        error={error}
      />
    </div>
  );
};
