import type { Meta, StoryObj } from '@storybook/react';
import { AddFileInput } from './AddFileInput';
import { Icon } from '../../Icons';

const meta: Meta<typeof AddFileInput> = {
  component: AddFileInput,
  title: 'Components/Inputs/AddFileInput',
  argTypes: {
    error: {
      control: {
        type: 'boolean',
      },
      description: 'Toggle error state',
    },
    position: {
      control: {
        type: 'select',
        options: ['left', 'right', 'none'],
      },
      description: 'Position of the icon',
    },
    onFileSelect: {
      action: 'file selected',
      description: 'Callback when a file is selected',
    },
  },
};
export default meta;

type Story = StoryObj<typeof AddFileInput>;

export const Overview: Story = {
  args: {
    placeholder: 'Upload a file',
    icon: <Icon type="file-07" />,
    position: 'right',
    error: null,
    name: 'fileUpload',
  },
};

export const WithErrorState: Story = {
  args: {
    placeholder: 'Upload a file',
    icon: <Icon type="file-07" />,
    position: 'right',
    error: { type: 'required', message: 'File is required' },
    name: 'fileUpload',
  },
};

