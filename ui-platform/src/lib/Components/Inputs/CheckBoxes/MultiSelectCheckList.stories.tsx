import type { Meta, StoryObj } from '@storybook/react';
import { MultiSelectCheckList } from './MultiSelectCheckList';

const meta: Meta<typeof MultiSelectCheckList> = {
  component: MultiSelectCheckList,
  title: 'Components/Inputs/MultiSelectCheckList',
};
export default meta;
type Story = StoryObj<typeof MultiSelectCheckList>;

export const Overview: Story = {
  args: {
    items: [
      { label: 'Text 1', value: 'Text 1' },
      { label: 'Text 2', value: 'Text 2' },
      { label: 'Text 3', value: 'Text 3' },
      { label: 'Text 4', value: 'Text 4' },
      { label: 'Text 5', value: 'Text 5' },
      { label: 'Text 6', value: 'Text 6' },
      { label: 'Text 7', value: 'Text 7' },
      { label: 'Text 8', value: 'Text 8' },
      { label: 'Text 9', value: 'Text 9' },
      { label: 'Text 10', value: 'Text 10' },
      { label: 'Text 11', value: 'Text 11' },
      { label: 'Text 12', value: 'Text 12' },
    ],
    labelProp: 'label',
    valueProp: 'value',
    heading: 'Heading here',
    subtext: 'Subtext here',
    name: 'test',
  },
};
