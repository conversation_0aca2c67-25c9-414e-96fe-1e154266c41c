import React, { useState } from 'react';
import styled from 'styled-components';
import { DatePicker } from '../Inputs/DatepickerInput/Datepicker/DatePicker';
import TimePicker from '../TimePicker/TimePicker';

const Container = styled.div`
  display: grid;
  place-items: center;
`;

const DropdownContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(4, minmax(150px, 200px));;
  grid-gap: 20px;
`;

const DropdownWrapper = styled.div`
  position: relative;
`;

const Dropdown = styled.select`
  width: 100%;
  height: 40px;
  margin: 4px;
  padding: 10px;
  background-color: #121212;
  color: #f8f8f8;
  cursor: pointer;
`;

const Wrapper = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 10;
  width: 100%;
`;

/**
 * Appointment Component
 * 
 * A form component for selecting appointment details such as the type of installation, date, and time.
 * 
 * @component
 * @returns {React.FC} Appointment component
 * 
 * Handles input change in the dropdown.
  * @param {React.ChangeEvent<HTMLSelectElement>} e - The event triggered on dropdown change
  * 
  * Toggles the visibility of the date picker and time picker.
  * 
  * Handles the selection of a time from the TimePicker.
   * @param {string} time - The selected time
  */
export const Appointment = () => {
  const [selectedInput, setSelectedInput] = useState('');
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedInput(e.target.value);
  };

  const toggleDatePicker = () => setShowDatePicker(!showDatePicker);
  const toggleTimePicker = () => setShowTimePicker(!showTimePicker);

  const handleDateSelect = (date: Date | null) => {
    if (date) {
      const formattedDate = date.toLocaleDateString();
      setSelectedDate(formattedDate);
      setShowDatePicker(false);
    }
  };

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
    setShowTimePicker(false);
  };

  return (
    <Container>
      <h1>Appointment Time</h1>
      <DropdownContainer>
        <DropdownWrapper>
          <Dropdown value={selectedInput} onChange={handleInputChange}>
            <option value="">Dish installation</option>
          </Dropdown>
        </DropdownWrapper>

        <DropdownWrapper>
          <Dropdown onClick={toggleDatePicker}>
            <option value="" hidden>
              {selectedDate || 'Select Date'}
            </option>
          </Dropdown>
          {showDatePicker && (
            <Wrapper>
              <DatePicker
                mode="single"
                selected={new Date()}
                // onSelect={handleDateSelect}
              />
            </Wrapper>
          )}
        </DropdownWrapper>

        <DropdownWrapper>
          <Dropdown value={selectedInput} onChange={handleInputChange}>
            <option value="">Select Item</option>
          </Dropdown>
        </DropdownWrapper>

        <DropdownWrapper>
          <Dropdown onClick={toggleTimePicker}>
            <option value="" hidden>
              {selectedTime || '--:--'}
            </option>
          </Dropdown>
          {showTimePicker && (
            <Wrapper>
              <TimePicker
              // onSelect={handleTimeSelect}
              />
            </Wrapper>
          )}
        </DropdownWrapper>
      </DropdownContainer>
    </Container>
  );
};
