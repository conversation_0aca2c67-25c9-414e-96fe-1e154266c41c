import { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { ForwardedRef, Fragment, ReactNode, forwardRef, useRef } from 'react';
import styled from 'styled-components';
import { MenuItemConfig } from '../../../Engine/models/menu-list-item.config';
import { withItems } from '../../List/List';
import { MenuItem } from '../../Menu';
import { useContextMenuOptions } from '../hooks/useContextMenuOptions';
import { ContextMenu, ContextMenuProps } from './ContextMenu';

const StyledParentDiv = styled.div`
  background: #ff8900;
  box-shadow: 0 0 8px #ffbe11;
  box-sizing: border-box;
  margin: 5rem auto;
  max-width: 80%;
  min-height: 400px;
  padding: 1rem;
  border-radius: 5px;
`;

const ParentComponent = forwardRef(
  ({ children }: { children: ReactNode }, elRef) => (
    <StyledParentDiv ref={elRef as ForwardedRef<HTMLDivElement>}>
      {children}
    </StyledParentDiv>
  )
);

const ContextMenuItem = styled.div`
  margin: 0.25rem;
  cursor: pointer;
  padding: 0.5rem;
  &:hover {
    background: #cbcdca;
    color: black;
  }
`;

const Item = () => {
  const { hideContextMenu } = useContextMenuOptions();
  const makeAlert = () => {
    alert(`You clicked:  This is the context menu`);
    hideContextMenu && hideContextMenu();
  };
  return (
    <ContextMenuItem onClick={makeAlert} onContextMenu={makeAlert}>
      This is the context menu
    </ContextMenuItem>
  );
};

type ContextMenuArgs = Omit<ContextMenuProps, 'parentElement'>;

const Demo = (props: ContextMenuArgs) => {
  const elRef = useRef(null);
  return (
    <ParentComponent ref={elRef}>
      Right click anywhere inside this box...
      {
        <ContextMenu parentElement={elRef} {...props}>
          {props['children']}
        </ContextMenu>
      }
    </ParentComponent>
  );
};

const ContextMenuItems = () => {
  const { showMenu, hideContextMenu } = useContextMenuOptions();
  const Items: MenuItemConfig[] = [
    {
      icon: 'plus',
      label: 'List Item 1',
      // onClick: () => {
      //   console.log(`The menu is: ${showMenu ? 'open' : 'closing'}`);
      //   hideContextMenu && hideContextMenu();
      //   alert('Clicked List Item 1');
      //   console.log(`The menu is: ${!showMenu ? 'open' : 'closing'}`);
      // },
    },
    {
      icon: 'check-circle',
      label: 'List Item 2',
      // onClick: () => {
      //   console.log(`The menu is: ${showMenu ? 'open' : 'closing'}`);
      //   hideContextMenu && hideContextMenu();
      //   alert('Clicked List Item 2');
      //   console.log(`The menu is: ${!showMenu ? 'open' : 'closing'}`);
      // },
    },
    {
      icon: 'minus-xircle',
      label: 'List Item 3',
      // onClick: () => {
      //   console.log(`The menu is: ${showMenu ? 'open' : 'closing'}`);
      //   hideContextMenu && hideContextMenu();
      //   alert('Clicked List Item 3');
      //   console.log(`The menu is: ${!showMenu ? 'open' : 'closing'}`);
      // },
    },
    {
      icon: 'x-xircle',
      label: 'List Item 4',
      // onClick: () => {
      //   console.log(`The menu is: ${showMenu ? 'open' : 'closing'}`);
      //   hideContextMenu && hideContextMenu();
      //   alert('Clicked List Item 4');
      //   console.log(`The menu is: ${!showMenu ? 'open' : 'closing'}`);
      // },
    },
    {
      icon: 'edit-05',
      label: 'List Item 5',
      // onClick: () => {
      //   console.log(`The menu is: ${showMenu ? 'open' : 'closing'}`);
      //   hideContextMenu && hideContextMenu();
      //   alert('Clicked List Item 5');
      //   console.log(`The menu is: ${!showMenu ? 'open' : 'closing'}`);
      // },
    },
  ];
  return <Fragment>{withItems(MenuItem)(Items)}</Fragment>;
};

const DynamicMenuItems = () => {
  const { showMenu, hideContextMenu } = useContextMenuOptions();
  const Items: MenuItemConfig[] = [
    {
      icon: 'bell-02',
      label: 'Give alert with message then close menu',
      // onClick: () => {
      //   alert('Clicked List Item 1 - Clickable and closes menu');
      //   hideContextMenu && hideContextMenu();
      // },
    },
    {
      icon: 'bell-02',
      label: 'Give alert with message do not close menu',
      // onClick: () => {
      //   alert('Clicked List Item 2 - Clickable and does not close menu');
      // },
    },
    {
      icon: 'minus-xircle',
      label: 'Do nothing',
    },
    {
      icon: 'x-xircle',
      label: 'Close context menu',
      // onClick: () => {
      //   console.log('Clicked List Item 4 - closing context menu');
      //   hideContextMenu && hideContextMenu();
      // },
    },
    {
      icon: 'check-circle',
      label: 'Open up prompt and close menu when done',
      // onClick: () => {
      //   prompt('Clicked List Item 5 - Open up prompt', 'Cool');
      //   hideContextMenu && hideContextMenu();
      // },
    },
  ];
  return <Fragment>{withItems(MenuItem)(Items)}</Fragment>;
};

export default {
  component: ContextMenu,
  title: 'Components/ContextMenu/ContextMenu',
  argTypes: {
    additionalStyling: {
      defaultValue: '',
      control: {
        type: 'text',
      },
    },
    className: {
      defaultValue: '',
      control: {
        type: 'text',
      },
    },
    restrictWithinParentHeight: {
      defaultValue: false,
      control: {
        type: 'boolean',
      },
    },
    restrictWithinParentWidth: {
      defaultValue: false,
      control: {
        type: 'boolean',
      },
    },
  },
} as Meta<typeof ContextMenu>;

type Story = StoryObj<typeof ContextMenu>;

export const Overview: Story = {
  render: (args) => <Demo {...args} />,
  args: {
    children: <Item />,
    restrictWithinParentHeight: true,
    restrictWithinParentWidth: true,
  },
};

export const ContextMenuWithList: Story = {
  render: (args) => <Demo {...args} />,
  args: {
    children: <ContextMenuItems />,
    restrictWithinParentHeight: true,
    restrictWithinParentWidth: true,
  },
};

export const ContextMenuWithListAndHeightOverflow: Story = {
  render: (args) => <Demo {...args} />,
  args: {
    children: <ContextMenuItems />,
    restrictWithinParentHeight: false,
    restrictWithinParentWidth: true,
  },
};

export const ContextMenuWithListAndWidthOverflow: Story = {
  render: (args) => <Demo {...args} />,
  args: {
    children: <ContextMenuItems />,
    restrictWithinParentHeight: true,
    restrictWithinParentWidth: false,
  },
};
export const ContextMenuWithDifferentActions: Story = {
  render: (args) => <Demo {...args} />,
  args: {
    children: <DynamicMenuItems />,
    restrictWithinParentHeight: true,
    restrictWithinParentWidth: false,
  },
};
