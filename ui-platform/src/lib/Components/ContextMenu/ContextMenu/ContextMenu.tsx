import React, {
  CSSProperties,
  ReactNode,
  useCallback,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import styled from 'styled-components';
import { ParentElementType, useParentSize } from '../../../Hooks/useParentSize';
import { IPosition, useContextMenu } from '../hooks/useContextMenu';
import { ContextMenuOptionsProvider } from '../hooks/useContextMenuOptions';

declare type WithChildren<T, C> = T & { children: C };

interface IContextMenu {
  parentElement: ParentElementType;
  className?: string;
  additionalStyling?: string;
  id?: string;
  key?: string;
  restrictWithinParentWidth?: boolean;
  restrictWithinParentHeight?: boolean;
}

export type ContextMenuProps = WithChildren<IContextMenu, ReactNode>;

/**
 * Renders a context menu component.
 *
 * @param {ContextMenuProps} props - The props for the context menu component.
 * @param {string} props.additionalStyling - Additional styling for the context menu as css string literal.
 * @param {ReactNode} props.children - The children of the context menu.
 * @param {string} props.className - The class name for the context menu.
 * @param {React.RefObject<HTMLElement>} props.parentElement - The parent element reference.
 * @param {boolean} props.restrictWithinParentHeight - Whether to restrict the context menu within the parent height.
 * @param {boolean} props.restrictWithinParentWidth - Whether to restrict the context menu within the parent width.
 * @return {JSX.Element} The rendered context menu component.
 */
export const ContextMenu = ({
  additionalStyling,
  children,
  className = '',
  parentElement,
  restrictWithinParentHeight = false,
  restrictWithinParentWidth = false,
  ...rest
}: ContextMenuProps) => {
  const [intrinsicWidth, setIntrinsicWidth] = useState<number>(0);
  const [intrinsicHeight, setIntrinsicHeight] = useState<number>(0);

  const { parentBottomOffset, parentRightOffset } =
    useParentSize(parentElement);

  const { anchorPosition, hideContextMenu, showContextMenu, showMenu } =
    useContextMenu({
      elementRef: parentElement,
    });

  const element = useRef<HTMLDivElement | null>(null);

  const setMenuActive = () => {
    if (element.current) {
      showMenu ? element.current.focus() : element.current.blur();
    }
  };

  const contextMenuClasses = [className, showMenu ? 'active' : '']
    .filter((x) => !!x)
    .join(' ');

  const ContextMenuDiv = styled.div`
    box-shadow: 0px 12px 23px 5px rgba(7, 14, 17, 0.25);
    backdrop-filter: blur(9px);
    border-radius: 4px;
    background-color: rgba(40, 48, 51, 0.93);
    border: 0.5px solid #696969;
    box-sizing: border-box;
    overflow: hidden;
    ${additionalStyling};

    display: block;
    position: absolute;
    height: 0;
    opacity: 0;
    transition: all 0.2s ease-in-out;
    visibility: hidden;

    &.active {
      height: auto;
      opacity: 1;
      visibility: visible;
    }
  `;

  const getIntrinsicSize = useCallback((menu: HTMLDivElement | null) => {
    if (!menu) return;

    setIntrinsicHeight(menu.getBoundingClientRect().height);
    setIntrinsicWidth(menu.getBoundingClientRect().width);
  }, []);

  useLayoutEffect(() => {
    getIntrinsicSize(element.current);
  });

  const setPosition = useMemo(() => {
    const newPosition: IPosition = {
      positionX: 0,
      positionY: 0,
    };

    const verticallyWithinParentBounds =
      anchorPosition.positionY + intrinsicHeight <= parentBottomOffset;
    const verticallyOverlapsParentBounds =
      anchorPosition.positionY + intrinsicHeight > parentBottomOffset;
    const horizontallyWithinParentBounds =
      anchorPosition.positionX + intrinsicWidth <= parentRightOffset;
    const horizontallyOverlapsParentBounds =
      anchorPosition.positionX + intrinsicWidth > parentRightOffset;

    if (restrictWithinParentHeight) {
      if (verticallyWithinParentBounds) {
        newPosition.positionY = anchorPosition.positionY;
        console.log('Clicked within div height', anchorPosition.positionY);
      }

      if (verticallyOverlapsParentBounds) {
        newPosition.positionY = anchorPosition.positionY - intrinsicHeight;
        console.log('Clicked inside div height', anchorPosition.positionY);
      }
    } else {
      newPosition.positionY = anchorPosition.positionY;
    }

    if (restrictWithinParentWidth) {
      if (horizontallyWithinParentBounds) {
        newPosition.positionX = anchorPosition.positionX;
        console.log('Clicked within div width', anchorPosition.positionX);
      }

      if (horizontallyOverlapsParentBounds) {
        newPosition.positionX = anchorPosition.positionX - intrinsicWidth;
        console.log('Clicked inside div width', anchorPosition.positionX);
      }
    } else {
      newPosition.positionX = anchorPosition.positionX;
    }

    return newPosition;
  }, [
    anchorPosition.positionY,
    anchorPosition.positionX,
    intrinsicHeight,
    parentBottomOffset,
    intrinsicWidth,
    parentRightOffset,
    restrictWithinParentHeight,
    restrictWithinParentWidth,
  ]);

  const style: CSSProperties = {
    left: setPosition.positionX,
    top: setPosition.positionY,
  };

  return (
    <ContextMenuDiv
      className={contextMenuClasses}
      data-testid="context-menu"
      {...rest}
      ref={element}
      style={style}
      onContextMenu={(e) => {
        e.preventDefault();
        setMenuActive();
        // showContextMenu(e as unknown as MouseEvent);
      }}
      onClick={(e) => {
        e.preventDefault();
      }}
    >
      <ContextMenuOptionsProvider
        {...{ showMenu, showContextMenu, hideContextMenu }}
      >
        {children}
      </ContextMenuOptionsProvider>
    </ContextMenuDiv>
  );
};
