import { Dispatch, ReactNode, createContext, useContext } from 'react';

type MenuOptions = {
  hideContextMenu?: () => void;
  showContextMenu?: (event: MouseEvent) => void;
  showMenu?: boolean;
  setShowMenu?: Dispatch<React.SetStateAction<boolean>>;
};

export const ContextMenuOptions = createContext<MenuOptions>({
  showMenu: true,
  /**
   * Shows the context menu.
   * @param {MouseEvent} event The event that triggered the context menu.
   */
  showContextMenu: function () {
    return;
  },
  /**
   * Hides the context menu.
   */
  hideContextMenu: function () {
    return;
  },
});

export const useContextMenuOptions = () => useContext(ContextMenuOptions);

/**
 * Provides the context menu options to its children.
 *
 * @param {ReactNode} children - The children of the provider.
 * @param {boolean} showMenu - Whether the context menu is shown.
 * @param {function} showContextMenu - Function to show the context menu.
 * @param {function} hideContextMenu - Function to hide the context menu.
 * @return {JSX.Element} The ContextMenuOptionsProvider component.
 */

export const ContextMenuOptionsProvider = ({
  children,
  showMenu,
  showContextMenu,
  hideContextMenu,
}: Omit<MenuOptions, 'setShowMenu'> & { children: ReactNode }) => {
  return (
    <ContextMenuOptions.Provider
      value={{ showMenu, showContextMenu, hideContextMenu }}
    >
      {children}
    </ContextMenuOptions.Provider>
  );
};
