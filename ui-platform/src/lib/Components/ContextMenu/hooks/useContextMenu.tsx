import { MutableRefObject, useCallback, useState } from 'react';
import { useClickOutside } from '../../../Hooks/useClickOutside';

export interface IPosition {
  positionX: number;
  positionY: number;
}

const initialPosition: IPosition = {
  positionX: 0,
  positionY: 0,
};

export interface IUseContextMenuProps {
  elementRef: MutableRefObject<null | HTMLElement>;
}

  /**
   * Hook to manage a context menu.
   *
   * @param {MutableRefObject<null | HTMLElement>} elementRef
   *   The reference to the element that will trigger the context menu.
   * @returns {Object} An object with the following properties:
   *   - anchorPosition: The position where the context menu should anchor.
   *   - hideContextMenu: A function to hide the context menu.
   *   - showMenu: A boolean indicating whether the context menu is shown.
   *   - setShowMenu: A function to show or hide the context menu.
   *   - showContextMenu: A function to show the context menu at the given position.
   */
export const useContextMenu = ({ elementRef }: IUseContextMenuProps) => {
  const [anchorPosition, setAnchorPosition] =
    useState<IPosition>(initialPosition);
  const [showMenu, setShowMenu] = useState<boolean>(false);

  const hideContextMenu = () => {
    showMenu && setShowMenu(false);
    console.log('Closing context menu...');
  };

  const showContextMenu = useCallback(
    (event: MouseEvent) => {
      event.preventDefault();
      const newPosition: IPosition = {
        positionX: event.pageX,
        positionY: event.pageY,
      };
      if (!showMenu) {
        setAnchorPosition(newPosition);
        console.log('Setting new position...', newPosition);
      }
      setShowMenu(!showMenu);
      console.log('Closing context menu...', showMenu);
    },
    [setAnchorPosition, setShowMenu, showMenu]
  );

  useClickOutside({
    elementRef,
    eventType: 'contextmenu',
    insideDivCb: (e: MouseEvent) => showContextMenu(e),
  });
  useClickOutside({
    elementRef,
    eventType: 'click',
    insideDivCb: () => hideContextMenu(),
  });

  return {
    anchorPosition,
    hideContextMenu,
    showMenu,
    setShowMenu,
    showContextMenu,
  };
};

export default useContextMenu;
