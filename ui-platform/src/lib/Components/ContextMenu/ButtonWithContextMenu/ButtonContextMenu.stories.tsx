import { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { ComponentType } from 'react';
import { MenuItemConfig } from '../../../Engine/models/menu-list-item.config';
import { Avatar } from '../../Avatars/Avatar/Avatar';
import { withItems } from '../../List/List';
import { MenuItem, ProfileDropdown } from '../../Menu';
import { useContextMenuOptions } from '../hooks/useContextMenuOptions';
import { BaseButtonProps, ButtonContextMenu } from './ButtonContextMenu';

/**
 * A storybook component that renders a single context menu item and logs the
 * context menu state and its dispatch function to the console.
 *
 * @returns a single {@link MenuItem} element.
 */
const SingleItem = (): JSX.Element[] => {
  const { showMenu, setShowMenu } = useContextMenuOptions();
  
  const Item: MenuItemConfig = {
    icon: 'edit-05',
    label: 'Context Menu Item 1',
    // onClick: () => {
    //   console.log(`The menu is: ${showMenu ? 'open' : 'closing'}`);
    //   alert('Clicked List Item 5');
    //   if (setShowMenu) setShowMenu(false);
    // },
  };
  return withItems(MenuItem)(Item);
};
/**
 * Generates an array of {@link MenuItem} elements with multiple context menu items.
 * Logs the state of the context menu and its dispatch function to the console.
 * Each menu item is configured with an icon and a label.
 *
 * @returns An array of {@link MenuItem} elements.
 */
const WithMultipleItems = (): JSX.Element[] => {
  const { showMenu, setShowMenu } = useContextMenuOptions();
  const Items: MenuItemConfig[] = [
    {
      icon: 'bell-02',
      label: 'Item 1 - Close the menu after alert',
      // onClick: () => {
      //   console.log(`The menu is: ${showMenu ? 'open' : 'closing'}`);
      //   alert('Clicked List Item 5');
      //   if (setShowMenu) setShowMenu(false);
      // },
    },
    {
      icon: 'bell-02',
      label: 'Item 2 - Keep menu open after alert',
      // onClick: () => {
      //   console.log(`The menu is: ${showMenu ? 'open' : 'closing'}`);
      //   alert('Clicked List Item 5');
      //   if (setShowMenu) setShowMenu(false);
      // },
    },
    {
      icon: 'minus-xircle',
      label: 'Item 3 - Do nothing',
    },
    {
      icon: 'x-xircle',
      label: 'Item 4 - Close the menu',
      // onClick: () => {
      //   console.log(`The menu is: ${showMenu ? 'open' : 'closing'}`);
      //   if (setShowMenu) setShowMenu(false);
      // },
    },
  ];
  return withItems(MenuItem)(Items);
};

const MenuItems = [
  {
    label: 'Switch Accounts',
    menuItems: {
      label: 'Profile Settings',
      icon: 'bell-02',
      // onClick: (e) => {
      //   console.log('Click event registered:', e);
      //   if (setShowMenu) setShowMenu(false);
      // },
    },
  },
  {
    label: 'Logout',
    menuItems: {
      label: 'Access',
      icon: 'bell-02',
      // onClick: () => {
      //   alert('You have been logged out');
      //   if (setShowMenu) setShowMenu(false);
      // },
    },
  },
  {
    label: 'No Action',
    menuItems: [
      {
        label: 'Disabled Action',
        icon: 'x-xircle',
        disabled: true,
      },
    ],
  },
] as MenuItemConfig[];

/**
 * A component that renders a ProfileButtonDropdown with the given options.
 * The rendered dropdown includes the username and email of a user.
 *
 * @param {object} options - An object with options to pass to the ProfileButtonDropdown component.
 * @return {JSX.Element} The rendered ProfileButtonDropdown component.
 */
const AvatarMenu = (options: any) => {
  return (
    <ProfileDropdown
      items={MenuItems}
      username={'Thabo Mabikho'}
      email={'<EMAIL>'}
      {...options}
    />
  );
};

const b64Image =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAApgAAAKYB3X3/OAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVEiJtZZPbBtFFMZ/M7ubXdtdb1xSFyeilBapySVU8h8OoFaooFSqiihIVIpQBKci6KEg9Q6H9kovIHoCIVQJJCKE1ENFjnAgcaSGC6rEnxBwA04Tx43t2FnvDAfjkNibxgHxnWb2e/u992bee7tCa00YFsffekFY+nUzFtjW0LrvjRXrCDIAaPLlW0nHL0SsZtVoaF98mLrx3pdhOqLtYPHChahZcYYO7KvPFxvRl5XPp1sN3adWiD1ZAqD6XYK1b/dvE5IWryTt2udLFedwc1+9kLp+vbbpoDh+6TklxBeAi9TL0taeWpdmZzQDry0AcO+jQ12RyohqqoYoo8RDwJrU+qXkjWtfi8Xxt58BdQuwQs9qC/afLwCw8tnQbqYAPsgxE1S6F3EAIXux2oQFKm0ihMsOF71dHYx+f3NND68ghCu1YIoePPQN1pGRABkJ6Bus96CutRZMydTl+TvuiRW1m3n0eDl0vRPcEysqdXn+jsQPsrHMquGeXEaY4Yk4wxWcY5V/9scqOMOVUFthatyTy8QyqwZ+kDURKoMWxNKr2EeqVKcTNOajqKoBgOE28U4tdQl5p5bwCw7BWquaZSzAPlwjlithJtp3pTImSqQRrb2Z8PHGigD4RZuNX6JYj6wj7O4TFLbCO/Mn/m8R+h6rYSUb3ekokRY6f/YukArN979jcW+V/S8g0eT/N3VN3kTqWbQ428m9/8k0P/1aIhF36PccEl6EhOcAUCrXKZXXWS3XKd2vc/TRBG9O5ELC17MmWubD2nKhUKZa26Ba2+D3P+4/MNCFwg59oWVeYhkzgN/JDR8deKBoD7Y+ljEjGZ0sosXVTvbc6RHirr2reNy1OXd6pJsQ+gqjk8VWFYmHrwBzW/n+uMPFiRwHB2I7ih8ciHFxIkd/3Omk5tCDV1t+2nNu5sxxpDFNx+huNhVT3/zMDz8usXC3ddaHBj1GHj/As08fwTS7Kt1HBTmyN29vdwAw+/wbwLVOJ3uAD1wi/dUH7Qei66PfyuRj4Ik9is+hglfbkbfR3cnZm7chlUWLdwmprtCohX4HUtlOcQjLYCu+fzGJH2QRKvP3UNz8bWk1qMxjGTOMThZ3kvgLI5AzFfo379UAAAAASUVORK5CYII=';

/**
 * A component that renders a ProfileButtonDropdown with a base64-encoded image.
 * The rendered dropdown includes the username and email of a user.
 *
 * @return {JSX.Element} The rendered ProfileButtonDropdown component.
 */
const AvatarMenuWIthImage = () => {
  return <AvatarMenu image={b64Image} />;
};

export default {
  component: ButtonContextMenu,
  title: 'Components/ContextMenu/ButtonContextMenu',
} as Meta<typeof ButtonContextMenu>;

type Story = StoryObj<typeof ButtonContextMenu>;

export const Overview: Story = {
  args: {
    children: <SingleItem />,
    orientation: 'left',
  },
};

export const MultipleItems: Story = {
  args: {
    children: <WithMultipleItems />,
    orientation: 'left',
  },
};

export const ProfileMenu: Story = {
  args: {
    Button: Avatar as ComponentType<BaseButtonProps>,
    btnProps: {
      size: 'medium',
      onClick: (e) => {
        console.log('Avatar click event registered:', e);
      },
    },
    children: <AvatarMenu />,
    orientation: 'right',
  },
};

export const ProfileMenuWithBase64Image: Story = {
  args: {
    Button: Avatar as ComponentType<BaseButtonProps>,
    btnProps: {
      size: 'medium',
      onClick: (e) => {
        console.log('Avatar click event registered:', e);
      },
    },
    children: <AvatarMenuWIthImage />,
    orientation: 'right',
  },
};
