import {
  ComponentPropsWithoutRef,
  ComponentType,
  CSSProperties,
  ForwardedRef,
  forwardRef,
  MouseEvent,
  ReactNode,
  useEffect,
  useRef,
  useState,
} from 'react';
import styled, { useTheme } from 'styled-components';
import { IconButton } from '../../Buttons/IconButton/IconButton';
import { ContextMenuOptions } from '../hooks/useContextMenuOptions';

declare type WithChildren<T, C> = T & { children: C };

export type BaseButtonProps = {
  [key: string]: any;
  onClick?: (event: MouseEvent<HTMLButtonElement, MouseEvent>) => void;
};

export type Props<T extends BaseButtonProps> = WithChildren<
  {
    Button?: ComponentType<T>;
    btnProps?: T;
    btnClassName?: string;
    dropdownClassName?: string;
    additionalStyling?: string;
    orientation?: 'left' | 'right';
    dropdownGap?: string;
    id?: string;
    key?: string;
  },
  ReactNode
>;

const Container = styled.div<
  ComponentPropsWithoutRef<'div'> & { orientation: 'left' | 'right' }
>`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: ${(props) => (props?.orientation === 'left' ? 'start' : 'end')};
`;

const ContextMenuButton = styled(IconButton)<
  Omit<typeof IconButton, 'active' | 'className' | 'disabled'>
>`
  /* add global styles here for the control button */
  background-color: transparent;
  border-color: transparent;
  &:hover {
    background-color: ${(props) =>
      props?.theme.ColorsButtonColorActionPanelHover};
  }
`;

const ContextMenuDiv = styled(
  forwardRef(
    (
      props: {
        showMenu: boolean;
        children: ReactNode;
        style?: CSSProperties;
        additionalStyling?: string;
        className?: string;
        dropdownGap?: string;
      },
      ref: ForwardedRef<HTMLDivElement>
    ) => {
      return (
        <div style={props.style} className={props.className} ref={ref}>
          {props.children}
        </div>
      );
    }
  )
)`
  top: 100%;
  margin-top: ${(props) => props?.dropdownGap ?? props.theme.GapSm};
  box-shadow: 0px 12px 23px 5px rgba(7, 14, 17, 0.25);
  backdrop-filter: blur(9px);
  border-radius: 4px;
  background-color: rgba(40, 48, 51, 0.84);
  border: 0.5px solid #696969;
  box-sizing: border-box;
  overflow: hidden;
  position: absolute;
  z-index: 2000;
  animation-name: ${(props) => (props.showMenu ? 'slideDown' : 'slideUp')};
  animation-duration: 0.5s;
  animation-timing-function: ease-in-out;
  display: ${(props) => (props.showMenu ? 'auto' : 'none')};
  width: max-content;

  ${(props) => props.additionalStyling}

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
      visibility: hidden;
      display: none;
    }
    to {
      opacity: 1;
      transform: translateY(0);
      visibility: visible;
      display: auto;
    }
  }

  @keyframes slideUp {
    from {
      opacity: 1;
      transform: translateY(0);
      visibility: visible;
      display: auto;
    }
    to {
      opacity: 0;
      transform: translateY(-10px);
      visibility: hidden;
      display: none;
    }
  }
`;

/**
 * Renders a button with a context menu.
 *
 * @template T - The type of the button props.
 * @param {Props<T>} props - The props for the button context menu.
 * @param {string} props.additionalStyling - Additional styling for the context menu.
 * @param {ReactNode} props.children - The children to render inside the context menu.
 * @param {string} props.btnClassName - The class name for the button.
 * @param {string} props.dropdownClassName - The class name for the dropdown.
 * @param {'left' | 'right'} [props.orientation='left'] - The orientation of the context menu.
 * @param {number} props.dropdownGap - The gap between the button and the dropdown.
 * @param {React.ComponentType<T>} props.Button - The button component to render.
 * @param {T} props.btnProps - The props for the button component.
 * @param {string} props.id - The id for the button.
 * @param {string} props.key - The key for the button.
 * @return {JSX.Element} The rendered button with the context menu.
 */
export const ButtonContextMenu = <T extends BaseButtonProps>({
  additionalStyling,
  children,
  btnClassName,
  dropdownClassName,
  orientation = 'left',
  dropdownGap,
  Button,
  btnProps,
  id,
  key,
}: Props<T>) => {
  const [showMenu, setShowMenu] = useState<boolean>(false);
  const dropdownButtonRef = useRef<HTMLDivElement | null>(null);
  const contextMenuRef = useRef<HTMLDivElement | null>(null);

  const theme = useTheme();

  const style: CSSProperties = {
    opacity: showMenu ? 1 : 0,
  };

  const color = theme.ColorsButtonColorActionPanelPrimary;

  useEffect(() => {
    const handleClickOutside = (event: Event) => {
      // if (event instanceof MouseEvent) {
      if (
        contextMenuRef.current &&
        !contextMenuRef.current.contains(event.target as Node) &&
        dropdownButtonRef.current &&
        !dropdownButtonRef.current.contains(event.target as Node)
      ) {
        setShowMenu(false);
      }
    };
    // };
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <Container orientation={orientation} className={btnClassName}>
      <div ref={dropdownButtonRef}>
        {Button && btnProps ? (
          <Button
            {...btnProps}
            key={'btn-dropdown-contextmenu'}
            active={showMenu}
            onClick={(e) => {
              setShowMenu(!showMenu);
              btnProps?.onClick && btnProps?.onClick(e);
            }}
          />
        ) : (
          <ContextMenuButton
            icon="dots-vertical"
            color={color}
            active={showMenu}
            onClick={(ev) => {
              ev.stopPropagation();
              setShowMenu(!showMenu);
            }}
          />
        )}
      </div>
      <ContextMenuDiv
        key={key}
        className={dropdownClassName}
        data-testid="context-menu"
        {...{ dropdownGap, showMenu, orientation, additionalStyling, id }}
        style={style}
        ref={contextMenuRef}
      >
        <ContextMenuOptions.Provider value={{ showMenu, setShowMenu }}>
          {children}
        </ContextMenuOptions.Provider>
      </ContextMenuDiv>
    </Container>
  );
};
