import { NavigateOptions, SubmitOptions } from 'react-router-dom';
// import { ModalConfig } from './modal-config';
import { FetcherSubmitOptions } from 'react-router-dom/dist/dom';
import { FetchConfig } from './fetch.config';

// export interface ActionConfig {
//     type: 'clientAction' | 'serverAction';
//     action: string;
//     payload: any;
// }

export type ActionConfig =
  | NavigateActionConfig
  | ConsoleLogActionConfig
  | LogoutActionConfig
  | TriggerModalActionConfig
  | SubmitActionConfig
  | fetchActionConfig
  | DefaultModalActionConfig
  | ResetFieldActionConfig
  | SubmitAsyncConfig
  | CloseModalActionConfig
  | ConditionalActionConfig
  | ClearStoreActionConfig
  | ClearErrorsActionConfig
  | TriggerFetchCallConfig
  | UpdateStoreActionConfig
  | TimeoutActionConfig
  | ViewJobCardPDFActionConfig;

type NavigateActionConfig = {
  type: 'clientAction';
  action: 'navigate';
  payload: [string] | [string, Record<string, any>];
};

type ConsoleLogActionConfig = {
  type: 'clientAction';
  action: 'log';
  payload: any[];
};

type LogoutActionConfig = {
  type: 'clientAction';
  action: 'logout';
  payload: any[];
};

type TriggerModalActionConfig = {
  type: 'clientAction';
  action: 'triggerModal';
  payload: [any];
};

type SubmitActionConfig = {
  type: 'clientAction';
  action: 'submitAndNavigate';
  payload: [
    { [key: string]: any },
    {
      url: string;
      slicePath?: string;
      bodySlicePath?: string;
      headers?: { [key: string]: any };
      redirect: string;
    },
    SubmitOptions
  ];
};

type fetchActionConfig = {
  type: 'clientAction';
  action: 'submitAndFetch';
  payload: [
    { [key: string]: any },
    {
      url: string;
      slicePath?: string;
      bodySlicePath?: string;
      headers?: { [key: string]: any };
      loader?: string;
    },
    FetcherSubmitOptions
  ];
};

type SubmitAsyncConfig = {
  type: 'clientAction';
  action: 'submitAsync';
  payload: {
    calls: Call[];
    onFinish?: ActionConfig;
    redirect?: string;
  };
};

type TriggerFetchCallConfig = {
  type: 'clientAction';
  action: 'triggerFetchCall';
  payload: FetchConfig[];
};

type DefaultModalActionConfig = {
  type: 'clientAction';
  action: 'defaultModalAction';
  payload: any; // No payload needed for closing the modal
};

type Call = {
  key: string;
  data?: any;
  // file?: File;
  url: string;
};

type ResetFieldActionConfig = {
  type: 'clientAction';
  action: 'resetFields';
  payload: { fields: string[] | { fieldName: string; defaultValue: string }[] }; // No payload needed for closing the modal
};

type CloseModalActionConfig = {
  type: 'clientAction';
  action: 'closeModal';
  payload?: any; // No payload needed for closing the modal
};

type ConditionalActionConfig = {
  type: 'clientAction';
  action: 'conditional';
  payload: {
    condition: string | boolean;
    actions: { whenTrue: ActionConfig[]; whenFalse?: ActionConfig[] };
  };
};

type ClearStoreActionConfig = {
  type: 'clientAction';
  action: 'clearStore';
  payload: string[];
};

type UpdateStoreActionConfig = {
  type: 'clientAction';
  action: 'updateStore';
  payload: Array<Record<string, any>>;
};

type ClearErrorsActionConfig = {
  type: 'clientAction';
  action: 'clearErrors';
  payload?: string;
};

type TimeoutActionConfig = {
  type: 'clientAction';
  action: 'timeout';
  payload: [number, ActionConfig[]];
};

type ViewJobCardPDFActionConfig = {
  type: 'clientAction';
  action: 'viewJobCardPDF';
  payload: {
    jobId: string;
    apiBaseUrl: string,
  pdfUrl: string
  };
};

/**
 * Extended ActionConfig type that includes an optional async property
 * to specify whether the action should be executed asynchronously
 */
export type ExtendedActionConfig = ActionConfig & {
  /**
   * When true, the action will be executed asynchronously and return a Promise
   * When false or undefined, the action will be executed synchronously (default behavior)
   */
  async?: boolean;
  concurrencyLimit?: number;
};