// external modules imports
import Keycloak from 'keycloak-js';
import { useCallback, useMemo } from 'react';
import { useFormContext, UseFormReturn } from 'react-hook-form';
import {
  FetcherWithComponents,
  Location,
  NavigateFunction,
  SubmitFunction,
} from 'react-router-dom';
// internal module imports
import { FetchConfig, useErrorStore } from '..';
import { TemplateLiteralLogger } from '../../Utilities';
import { checkIsOnline } from '../../Utilities/checkNetworkOnline';
import {
  applyTemplateToObject,
  evaluateFormConditionExpression,
  extractValues,
  makeFetchCalls,
  renderTemplateStringOnClient,
} from '../helpers';
import { getPdfUrl, openPdfUrl } from '../helpers/pdf-handler';
import { renderTemplateObject } from '../helpers/render-template';
import { templateFunctions } from '../helpers/render-template-functions';
import { ActionConfig, ExtendedActionConfig } from '../models/action.config';
import { useAppStore } from '../useAppStore';
import { useAsyncLoaderStore } from './useAsyncLoaderStore';
import { useModalStore } from './useModalStore';
import { useNotesStore } from './useNotesStore';
import { useReminderStore } from './useReminderStore';

// instantiate logger for debugging generate control component
const logger = new TemplateLiteralLogger({
  prefix: '🪵[Use Client Actions Async log]:',
  enabled: false,
  options: { style: { backgroundColor: '#efefef', color: '#017812' } },
});

interface Props {
  navigate: NavigateFunction;
  location: Location<any>;
  keycloak?: Keycloak;
  fetcher?: FetcherWithComponents<any>;
  submit?: SubmitFunction;
  formContext?: UseFormReturn;
  envObject?: any;
  tokenPrefix?: 'Bearer' | 'Token';
  debug?: boolean;
}

// Context object to reduce parameter passing
interface ActionContext {
  navigate: NavigateFunction;
  location: Location<any>;
  keycloak?: Keycloak;
  fetcher?: FetcherWithComponents<any>;
  submit?: SubmitFunction;
  formContext?: UseFormReturn;
  envObject?: any;
  tokenPrefix: 'Bearer' | 'Token';
  setState: any;
  setModalState: any;
  setAsyncLoading: any;
  addError: any;
  clearError: any;
  clearAllErrors: any;
  fc: any;
  offlineHandler: () => void;
  debug?: boolean;
}

// Action handler type
type ActionHandler = (
  config: ActionConfig,
  context: ActionContext
) => Promise<void> | void;

// Validation utilities
const validateConfig = (
  config: any,
  functionName: string
): config is ActionConfig => {
  if (config === null || config === undefined) {
    console.warn(
      `${functionName} received null or undefined config, ignoring action`
    );
    return false;
  }

  if (typeof config !== 'object') {
    console.warn(
      `${functionName} received invalid config type, expected object`
    );
    return false;
  }

  if (!('action' in config)) {
    console.warn(
      `${functionName} received config without action property, ignoring action`
    );
    return false;
  }

  if (typeof config.action !== 'string') {
    console.warn(
      `${functionName} received config with invalid action type, expected string`
    );
    return false;
  }

  return true;
};

// Validation for sync execution
const validateConfigSync = (config: any): config is ActionConfig => {
  return validateConfig(config, 'executeClientActionSynchronously');
};

// Validation for async execution
const validateConfigAsync = (config: any): config is ActionConfig => {
  return validateConfig(config, 'executeClientActionAsynchronously');
};

// Error handling utility
const createErrorHandler =
  (addError: any) => (error: unknown, actionName: string) => {
    addError({
      key: `${actionName}-${Date.now()}`,
      message:
        error instanceof Error
          ? `${error.message}: Please try refreshing the page`
          : 'Unknown error: Try refreshing the website',
      source: 'server',
      stackTrace: error instanceof Error ? error.stack : undefined,
    });
  };

// Individual action handlers
const handleNavigate = (config: ActionConfig, context: ActionContext) => {
  if (checkIsOnline(() => context.offlineHandler()) === false) return;

  const currentParams = new URLSearchParams(context.location.search);
  const options =
    config?.payload[1] && Object.keys(config?.payload[1]).length !== 0
      ? Object.keys(config?.payload[1] as Record<string, any>).reduce(
          (acc, key) => {
            if (key !== 'clearParams') {
              if (config?.payload[1]) {
                acc[key] =
                  config.payload[1][key] !== undefined
                    ? config.payload[1][key]
                    : undefined;
              }
            }
            return acc;
          },
          {} as Record<string, any>
        )
      : undefined;

  context.navigate(
    {
      pathname: config.payload[0],
      search: config.payload[1]?.clearParams ? '' : currentParams.toString(),
    },
    options
  );
};

const handleLog = (config: ActionConfig, context: ActionContext) => {
  console.log(...config.payload);
};

const handleLogout = (config: ActionConfig, context: ActionContext) => {
  if (checkIsOnline(() => context.offlineHandler()) === false) return;
  context.keycloak?.logout();
};

const handleTriggerModal = (config: ActionConfig, context: ActionContext) => {
  context.setModalState({ ...config.payload[0], display: true });
};

const handleCloseModal = (config: ActionConfig, context: ActionContext) => {
  context.setModalState({ display: false });
};

const handleDefaultModalAction = (
  config: ActionConfig,
  context: ActionContext
) => {
  if (typeof config.payload === 'function') {
    config.payload();
  }
};

const handleClearErrors = (config: ActionConfig, context: ActionContext) => {
  if (!config.payload) {
    context.clearAllErrors();
    return;
  }
  context.clearError(config.payload);
};

const handleViewJobCardPDF = (config: ActionConfig, context: ActionContext) => {
  const payload = config.payload;
  openPdfUrl(
    context.keycloak?.token as string,
    payload?.jobId,
    payload?.apiBaseUrl,
    payload?.pdfUrl
  );
};

// Action registry for simple actions
const createActionRegistry = (
  context: ActionContext
): Record<string, ActionHandler> => ({
  navigate: handleNavigate,
  log: handleLog,
  logout: handleLogout,
  triggerModal: handleTriggerModal,
  closeModal: handleCloseModal,
  defaultModalAction: handleDefaultModalAction,
  clearErrors: handleClearErrors,
  viewJobCardPDF: handleViewJobCardPDF,
});

// Shared execution function that uses the registry for simple actions
const executeAction = async (
  config: ActionConfig,
  context: ActionContext,
  executeClientAction?: (
    config: ActionConfig,
    debug?: boolean
  ) => Promise<void>,
  debug?: boolean
): Promise<void> => {
  logger.configure({
    enabled: debug,
    options: { style: { color: '#d69f14' } },
  });
  if (!validateConfigAsync(config)) {
    return;
  }

  const { type = 'clientAction', action } = config;

  if (type !== 'clientAction') {
    return;
  }

  const actionRegistry = createActionRegistry(context);
  const handler = actionRegistry[action];

  if (handler) {
    // Use registry for simple actions
    return handler(config, context);
  }

  // Handle complex actions that need special logic
  switch (action) {
    case 'submitAndNavigate':
      return handleSubmitAndNavigate(config, context);
    case 'submitAndFetch':
      return handleSubmitAndFetch(config, context);
    case 'submitAsync':
      return handleSubmitAsync(config, context, executeClientAction);
    case 'triggerFetchCall':
      return handleTriggerFetchCall(config, context);
    case 'resetFields':
      return handleResetFields(config, context);
    case 'conditional':
      return handleConditional(config, context, executeClientAction);
    case 'clearStore':
      return handleClearStore(config, context);
    case 'updateStore':
      return handleUpdateStore(config, context);
    case 'timeout':
      return handleTimeout(config, context, executeClientAction);
    default:
      logger.warn`no handler for the function you called`;
      break;
  }
};

// Synchronous version of executeAction for backward compatibility
const executeActionSync = (
  config: ActionConfig,
  context: ActionContext,
  executeClientActionSynchronously?: (
    config: ActionConfig,
    debug?: boolean
  ) => void | Promise<void>,
  debug?: boolean
): void => {
  logger.configure({
    enabled: debug,
    options: { style: { color: '#d69f14' } },
  });
  if (!validateConfigSync(config)) {
    return;
  }

  const { type = 'clientAction', action } = config;

  if (type !== 'clientAction') {
    return;
  }

  const actionRegistry = createActionRegistry(context);
  const handler = actionRegistry[action];

  if (handler) {
    // Use registry for simple actions
    handler(config, context);
    return;
  }

  // Handle complex actions that need special logic
  switch (action) {
    case 'submitAndNavigate':
      handleSubmitAndNavigate(config, context);
      break;
    case 'submitAndFetch':
      handleSubmitAndFetch(config, context);
      break;
    case 'submitAsync':
      // For sync version, we don't await
      handleSubmitAsync(
        config,
        context,
        executeClientActionSynchronously as any
      );
      break;
    case 'triggerFetchCall':
      // For sync version, we don't await
      handleTriggerFetchCall(config, context);
      break;
    case 'resetFields':
      handleResetFields(config, context);
      break;
    case 'conditional':
      // For sync version, we don't await
      handleConditional(
        config,
        context,
        executeClientActionSynchronously as any
      );
      break;
    case 'clearStore':
      handleClearStore(config, context);
      break;
    case 'updateStore':
      handleUpdateStore(config, context);
      break;
    case 'timeout':
      // For sync version, we don't await
      handleTimeout(config, context, executeClientActionSynchronously as any);
      break;
    default:
      logger.warn`no handler for the function you called`;
      break;
  }
};

// Complex action handlers
const handleSubmitAndNavigate = (
  config: ActionConfig,
  context: ActionContext
) => {
  if (checkIsOnline(() => context.offlineHandler()) === false) return;

  const store = useAppStore.getState();
  const formValues = context.fc?.getValues();
  const transformedPayloadObj = renderTemplateObject(
    { payload: config.payload },
    store,
    templateFunctions(store, formValues)
  );
  const payload = transformedPayloadObj.payload;
  const val = extractValues(payload[0], store);

  try {
    context.submit?.(
      {
        data: JSON.stringify(val),
        url: payload[1].url,
        headers: payload[1].headers || {},
        redirect: payload[1].redirect,
        bodySlicePath: payload[1].bodySlicePath || '',
      },
      payload[2]
    );
  } catch (error) {
    const handleError = createErrorHandler(context.addError);
    handleError(error, 'submit-and-navigate');
  }
};

const handleSubmitAndFetch = (config: ActionConfig, context: ActionContext) => {
  if (checkIsOnline(() => context.offlineHandler()) === false) return;

  const store = useAppStore.getState();
  const formValues = context.formContext?.getValues();
  const transformedPayloadObj = renderTemplateObject(
    { payload: config.payload },
    store,
    templateFunctions(store, formValues)
  );
  const payload = transformedPayloadObj.payload;
  const val = extractValues(payload[0], store);

  if (payload[0].sp_id) {
    (val as any).postData.sp_id = (val as any).sp_id;
  }

  try {
    const response = context.fetcher?.submit(
      {
        data: JSON.stringify(val),
        url: payload[1].url,
        headers: payload[1].headers || {},
        bodySlicePath: payload[1].bodySlicePath || '',
      },
      config.payload[2]
    );
    console.log({ response });
  } catch (error) {
    const handleError = createErrorHandler(context.addError);
    handleError(error, 'submit-and-fetch');
  }

  if (config.payload[1].loader) {
    context.fetcher?.load(config.payload[1].loader);
  }
};

const handleTriggerFetchCall = async (
  config: ActionConfig,
  context: ActionContext
) => {
  if (checkIsOnline(() => context.offlineHandler()) === false) return;

  const fetchCalls: FetchConfig[] = config.payload;
  const request: Request | undefined = undefined;
  if (!context.keycloak) return;

  const result = await makeFetchCalls(
    fetchCalls,
    context.keycloak,
    request,
    context.envObject
  );
  context.setState((state: any) => ({ ...state, ...result }));
};

const handleResetFields = (config: ActionConfig, context: ActionContext) => {
  const fields = config.payload?.fields;
  if (fields) {
    for (const field of fields) {
      if (typeof field === 'string') {
        context.formContext?.resetField(field);
      } else {
        const store = useAppStore.getState();
        const val: Record<string, any> = extractValues(
          { [field.fieldName]: field.defaultValue },
          store
        );
        context.formContext?.resetField(field.fieldName, {
          defaultValue: val[field.fieldName],
        });
      }
    }
  }
};

const handleClearStore = (config: ActionConfig, context: ActionContext) => {
  if (config.payload.length === 0) {
    context.setState({}, true);
    return;
  }
  context.setState(
    config.payload.reduce(
      (acc: any, curr: string) => ({ ...acc, [curr]: undefined }),
      {}
    )
  );
};

const handleUpdateStore = (config: ActionConfig, context: ActionContext) => {
  if (config.payload.length === 0) {
    return;
  }
  context.setState((state: any) => {
    return config.payload.reduce(
      (acc: any, curr: string) => ({
        ...acc,
        ...Object.keys(curr).reduce(
          (acc2, key: string) => ({
            ...acc2,
            [key]: (curr as unknown as Record<string, any>)[key],
          }),
          {}
        ),
      }),
      state
    );
  });
};

const handleConditional = async (
  config: ActionConfig,
  context: ActionContext,
  executeClientAction?: (config: ActionConfig) => Promise<void>
) => {
  if (!executeClientAction) return;

  const executeConditionalActions = async (
    condition: string | boolean,
    actions: { whenTrue: ActionConfig[]; whenFalse?: ActionConfig[] }
  ) => {
    const callConditionalActions = async (
      conditionResult: boolean,
      whenTrue: ActionConfig[],
      whenFalse?: ActionConfig[]
    ) => {
      if (conditionResult) {
        for (const cf of whenTrue || []) {
          await executeClientAction(cf);
        }
      } else {
        if (!whenFalse || whenFalse.length === 0) return;
        for (const cf of whenFalse || []) {
          await executeClientAction(cf);
        }
      }
    };

    if (context.formContext) {
      const conditionResult =
        typeof condition === 'string'
          ? evaluateFormConditionExpression(
              condition,
              useAppStore.getState(),
              context.formContext.watch(),
              context.formContext.formState
            )
          : condition;
      await callConditionalActions(
        conditionResult,
        actions.whenTrue,
        actions.whenFalse
      );
    } else {
      const conditionResult =
        typeof condition === 'string'
          ? evaluateFormConditionExpression(condition, useAppStore.getState())
          : condition;
      await callConditionalActions(
        conditionResult,
        actions.whenTrue,
        actions.whenFalse
      );
    }
  };

  await executeConditionalActions(
    config.payload.condition,
    config.payload.actions
  );
};

const handleTimeout = async (
  config: ActionConfig,
  context: ActionContext,
  executeClientAction?: (config: ActionConfig) => Promise<void>
) => {
  if (!executeClientAction) return;

  const timeout = config.payload[0];
  const actions = config.payload[1];

  return new Promise<void>((resolve) => {
    setTimeout(async () => {
      for (const cf of actions || []) {
        await executeClientAction(cf);
      }
      resolve();
    }, timeout);
  });
};

const handleSubmitAsync = async (
  config: ActionConfig,
  context: ActionContext,
  executeClientAction?: (config: ActionConfig) => Promise<void>
) => {
  if (checkIsOnline(() => context.offlineHandler()) === false) return;

  const store = useAppStore.getState();
  const { getValues } = context.formContext
    ? context.formContext
    : { getValues: () => null };

  const transformedPayloadObj = renderTemplateObject(
    { payload: config.payload },
    store
  );
  const payload = transformedPayloadObj.payload;

  const calls = payload.calls;
  const redirect = payload.redirect;
  const onFinish = payload.onFinish;
  const previousResults: { [key: string]: any } = {};

  const executeCallsSequentially = async () => {
    context.setAsyncLoading(true);
    for (const call of calls) {
      if (call.key === 'addReminder') {
        const reminderData = call.data;
        const addReminder = useReminderStore.getState().addReminder;
        const formValues = context.formContext?.getValues();
        addReminder(reminderData);
        context.setAsyncLoading(false);
        context.setModalState({ display: false });
        if (formValues) {
          console.log('formValues should be here', formValues);
        }
      } else if (call.key === 'addNote') {
        const noteData = call.data;
        const message = noteData.message;
        const jobId = noteData.job_id;
        const addNoteUrlString = noteData.addNoteUrl;
        const token = context.keycloak?.token || '';
        const addNote = useNotesStore.getState().addNote;
        if (!token || !addNoteUrlString) {
          console.error('Missing token or baseUrl');
          return;
        }
        addNote(message, jobId, token, addNoteUrlString);
      } else {
        const callFormData = new FormData();
        const callDataWithStoreValues: any = extractValues(
          call.data,
          getValues()
        );
        const interpolatedData = applyTemplateToObject(
          callDataWithStoreValues,
          previousResults
        );
        for (const k of Object.keys(interpolatedData)) {
          if (callDataWithStoreValues[k] instanceof File) {
            callFormData.append(k, callDataWithStoreValues[k]);
          } else {
            callFormData.append(k, interpolatedData[k]);
          }
        }
        let newUrl = '';
        if (context.envObject) {
          newUrl = renderTemplateStringOnClient(
            { template: call.url },
            context.envObject
          );
        } else {
          newUrl = call.url;
        }

        try {
          const response = await fetch(newUrl, {
            method: 'POST',
            body: callFormData,
            headers: {
              Authorization: `${context.tokenPrefix} ${context.keycloak?.token}`,
              Accept: 'application/json',
            },
          });
          const result = await response.json();
          previousResults[call.key] = result;
        } catch (error) {
          const handleError = createErrorHandler(context.addError);
          handleError(error, 'submit-async');
        }
      }
    }
    if (onFinish && executeClientAction) {
      await executeClientAction(onFinish);
    }
    if (redirect) {
      context.fetcher?.load(redirect);
    }
    context.setAsyncLoading(false);
  };

  await executeCallsSequentially();
};

export const useClientActionAsync = ({
  keycloak,
  fetcher,
  submit,
  formContext,
  envObject,
  navigate,
  location,
  tokenPrefix = 'Bearer',
  debug = true,
}: Props) => {
  const setState = useAppStore(
    (state) => (state as { setState: any }).setState
  );
  const setModalState = useModalStore((state: any) => state.setModalState);
  const setAsyncLoading = useAsyncLoaderStore(
    (state: any) => state.setAsyncLoading
  );
  const { addError, clearError, clearAllErrors } = useErrorStore();
  const fc = useFormContext();

  const error = new Error('You are offline');
  const offlineHandler = useCallback(
    () =>
      addError({
        key: `client-offline-${Date.now()}`,
        message:
          error instanceof Error
            ? `${error.message}: Please check your network connections and try again`
            : 'Unknown error: Try refreshing the website',
        source: 'server',
        stackTrace: error instanceof Error ? error.stack : undefined,
      }),
    [addError]
  );

  // Create action context using useMemo to prevent unnecessary re-renders
  const actionContext = useMemo(
    (): ActionContext => ({
      navigate,
      location,
      keycloak,
      fetcher,
      submit,
      formContext,
      envObject,
      tokenPrefix,
      setState,
      setModalState,
      setAsyncLoading,
      addError,
      clearError,
      clearAllErrors,
      fc,
      offlineHandler,
      debug,
    }),
    [
      navigate,
      location,
      keycloak,
      fetcher,
      submit,
      formContext,
      envObject,
      tokenPrefix,
      setState,
      setModalState,
      setAsyncLoading,
      addError,
      clearError,
      clearAllErrors,
      fc,
      offlineHandler,
      debug,
    ]
  );

  /**
   * Execute a client action and return a Promise
   * This is the core function that handles both synchronous and asynchronous execution
   */
  const executeClientActionAsynchronously = useCallback(
    async (config: ActionConfig, debug?: boolean): Promise<void> => {
      return executeAction(
        config,
        actionContext,
        executeClientActionAsynchronously,
        debug
      );
    },
    [actionContext]
  );

  const callConditionalActions = useCallback(
    async (
      conditionResult: boolean,
      whenTrue: ActionConfig[],
      whenFalse?: ActionConfig[],
      debug?: boolean
    ) => {
      if (conditionResult) {
        for (const cf of whenTrue || []) {
          await executeClientActionAsynchronously(cf, debug);
        }
      } else {
        if (!whenFalse || whenFalse.length === 0) return;
        for (const cf of whenFalse || []) {
          await executeClientActionAsynchronously(cf, debug);
        }
      }
    },
    [executeClientActionAsynchronously]
  );

  const executeConditionalActions = useCallback(
    async (
      condition: string | boolean,
      actions: { whenTrue: ActionConfig[]; whenFalse?: ActionConfig[] }
    ) => {
      if (formContext) {
        const conditionResult =
          typeof condition === 'string'
            ? evaluateFormConditionExpression(
                condition,
                useAppStore.getState(),
                formContext.watch(),
                formContext.formState
              )
            : condition;
        await callConditionalActions(
          conditionResult,
          actions.whenTrue,
          actions.whenFalse
        );
      } else {
        const conditionResult =
          typeof condition === 'string'
            ? evaluateFormConditionExpression(condition, useAppStore.getState())
            : condition;
        await callConditionalActions(
          conditionResult,
          actions.whenTrue,
          actions.whenFalse
        );
      }
    },
    [callConditionalActions, formContext]
  );

  /**
   * Synchronous version of callClientAction that maintains backward compatibility
   * This function will execute the action synchronously unless the config.async is true
   */
  const executeClientActionSynchronously = useCallback(
    (config: ActionConfig, debug?: boolean) => {
      // If the action is configured to be asynchronous, return a Promise
      if ((config as ExtendedActionConfig).async) {
        return executeClientActionAsynchronously(config, debug);
      }

      // Otherwise, execute synchronously using the shared logic
      return executeActionSync(
        config,
        actionContext,
        executeClientActionSynchronously,
        debug
      );
    },
    [actionContext, executeClientActionAsynchronously]
  );

  /**
   * Asynchronous version of callClientAction that always returns a Promise
   * This provides a convenient way to always get a Promise without having to set async: true
   */
  const callClientActionAsync = useCallback(
    (config: ActionConfig): Promise<void> => {
      return executeClientActionAsynchronously(config, debug);
    },
    [executeClientActionAsynchronously, debug]
  );

  /**
   * Execute multiple client actions sequentially (one after another)
   * This ensures actions complete in order and prevents race conditions
   */
  const callClientActionsSequentially = useCallback(
    async (configs: ActionConfig[], debug?: boolean): Promise<void> => {
      for (const config of configs) {
        await executeClientActionAsynchronously(config, debug);
      }
    },
    [executeClientActionAsynchronously]
  );

  /**
   * Execute multiple client actions concurrently (all at the same time)
   * This is faster but may cause race conditions if actions affect the same state
   */
  const callClientActionsConcurrently = useCallback(
    async (configs: ActionConfig[], debug?: boolean): Promise<void> => {
      await Promise.all(
        configs.map((config) =>
          executeClientActionAsynchronously(config, debug)
        )
      );
    },
    [executeClientActionAsynchronously]
  );

  /**
   * Execute multiple client actions with controlled concurrency
   * Limits the number of actions running simultaneously
   */
  const callClientActionsWithLimit = useCallback(
    async (
      configs: ActionConfig[],
      concurrencyLimit = 3,
      debug?: boolean
    ): Promise<void> => {
      logger.configure({
        enabled: debug,
        options: { style: { color: '#d69f14' } },
      });
      // Handle edge cases for concurrency limit
      if (concurrencyLimit <= 0) {
        logger.warn`callClientActionsWithLimit received invalid concurrency limit (${concurrencyLimit}), falling back to sequential execution`;
        // Fall back to sequential execution for invalid limits
        return callClientActionsSequentially(configs, debug);
      }

      // Handle empty array
      if (configs.length === 0) {
        return;
      }

      // Execute actions in batches with the specified concurrency limit
      for (let i = 0; i < configs.length; i += concurrencyLimit) {
        const batch = configs.slice(i, i + concurrencyLimit);
        const batchPromises = batch.map((config) =>
          executeClientActionAsynchronously(config, debug)
        );
        await Promise.all(batchPromises);
      }
    },
    [executeClientActionAsynchronously, callClientActionsSequentially]
  );

  const callClientAction = useCallback(
    (
      config: ActionConfig | ActionConfig[],
      async?: boolean,
      concurrencyLimit?: number,
      debug?: boolean
    ) => {
      logger.configure({
        enabled: debug,
        options: { style: { color: '#d69f14' } },
      });
      // Handle null/undefined inputs gracefully
      if (config === null || config === undefined) {
        logger.warn`callClientAction received null or undefined config, ignoring action`;
        return;
      }

      // Handle array of actions
      if (Array.isArray(config)) {
        // Return early for empty arrays
        if (config.length === 0) return;

        // Case 1: Array with concurrency limit (async execution with controlled concurrency)
        if (concurrencyLimit && concurrencyLimit > 0) {
          return callClientActionsWithLimit(config, concurrencyLimit);
        }

        // Case 2: Array with async=true (sequential async execution)
        if (async === true) {
          return callClientActionsSequentially(config);
        }

        // Case 3: Array with async=false or undefined (concurrent execution - default for arrays)
        return callClientActionsConcurrently(config);
      }

      // Handle single action
      else {
        // Validate that config is a proper object
        if (typeof config !== 'object') {
          logger.warn`callClientAction received invalid config type, expected object or array`;
          return;
        }

        // Case 4: Single action with explicit async property in config
        if (
          'async' in config &&
          (config as ExtendedActionConfig).async === true
        ) {
          return callClientActionAsync(config);
        }

        // Case 5: Single action with async parameter override
        if (async === true) {
          return callClientActionAsync(config);
        }

        // Case 6: Single action - synchronous execution (default for single actions)
        return executeClientActionSynchronously(config);
      }
    },
    [
      executeClientActionSynchronously,
      callClientActionAsync,
      callClientActionsConcurrently,
      callClientActionsSequentially,
      callClientActionsWithLimit,
    ]
  );

  /**
   * Asynchronous wrapper for callClientAction with execution mode options
   * Ensures any action or array of actions executes asynchronously and returns a Promise
   */
  const withAsync = useCallback(
    <T extends ActionConfig | ActionConfig[]>(
      config: T,
      options?: {
        mode?: 'sequential' | 'concurrent';
        concurrencyLimit?: number;
      }
    ): Promise<void> => {
      const { mode = 'sequential', concurrencyLimit } = options || {};

      // For arrays, use the specified execution mode
      if (Array.isArray(config)) {
        if (mode === 'concurrent') {
          if (concurrencyLimit && concurrencyLimit > 0) {
            return callClientActionsWithLimit(config, concurrencyLimit);
          }
          return callClientActionsConcurrently(config);
        }
        return callClientActionsSequentially(config);
      }

      // For single actions, use callClientActionAsync
      return callClientActionAsync(config as ActionConfig);
    },
    [
      callClientActionAsync,
      callClientActionsSequentially,
      callClientActionsConcurrently,
      callClientActionsWithLimit,
    ]
  );

  /**
   * Creates a batch executor for multiple async actions
   * Allows collecting multiple actions and executing them together
   */
  const createBatch = useCallback(() => {
    const actions: ActionConfig[] = [];

    return {
      /**
       * Add an action to the batch
       */
      add: (config: ActionConfig) => {
        actions.push(config);
      },

      /**
       * Execute all collected actions with the specified execution mode
       */
      execute: async (options?: {
        mode?: 'sequential' | 'concurrent';
        concurrencyLimit?: number;
      }) => {
        const { mode = 'sequential', concurrencyLimit } = options || {};

        if (mode === 'concurrent') {
          if (concurrencyLimit && concurrencyLimit > 0) {
            return callClientActionsWithLimit(actions, concurrencyLimit);
          }
          return callClientActionsConcurrently(actions);
        }
        return callClientActionsSequentially(actions);
      },

      /**
       * Get the number of actions in the batch
       */
      size: () => actions.length,

      /**
       * Clear all actions from the batch
       */
      clear: () => {
        actions.length = 0;
      },
    };
  }, [
    callClientActionsSequentially,
    callClientActionsConcurrently,
    callClientActionsWithLimit,
  ]);

  return {
    callClientAction,
    callClientActionAsync,
    callClientActionsSequentially,
    callClientActionsConcurrently,
    callClientActionsWithLimit,
    executeClientActionAsynchronously,
    executeClientActionSynchronously,
    withAsync,
    createBatch,
  };
};
