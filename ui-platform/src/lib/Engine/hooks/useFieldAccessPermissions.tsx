import { useMemo } from 'react';
import { TemplateLiteralLogger } from '../../Utilities';
import { useAppStore } from '../useAppStore';

// instantiate logger for debugging generate control component
const log = TemplateLiteralLogger.createLog(
  {
    prefix: '🪵[Use Field Access Permissions log]:',
    enabled: false,
    options: { style: { backgroundColor: '#efefef', color: '#017812' } },
  },
  'log'
);

export const useFieldAccessPermissions = ({
  fieldAccess,
  fieldAccessKey,
  fieldAccessPath,
}: {
  fieldAccess?: {
    view: { [key: string]: string[] };
    edit: { [key: string]: string[] };
    special: { [key: string]: string[] };
  };
  fieldAccessKey: string;
  fieldAccessPath?: { view?: string; edit?: string; special?: string };
}) => {
  const { fieldAccess: storeFieldAccess }: any = useAppStore((state) => state);
  const permissions = useMemo(
    () => fieldAccess ?? storeFieldAccess,
    [fieldAccess, storeFieldAccess]
  );
  const canView = useMemo(() => {
    if (fieldAccessPath?.view) {
      log`Field ${fieldAccessKey} can be viewed: ${permissions?.view[
        fieldAccessPath?.view
      ]?.includes(fieldAccessKey)}`;
      return permissions?.view[fieldAccessPath?.view]?.includes(fieldAccessKey);
    }
    return true;
  }, [fieldAccessKey, permissions, fieldAccessPath]);

  const canEdit = useMemo(() => {
    if (fieldAccessPath?.edit) {
      log`Field ${fieldAccessKey} can be edited: ${permissions?.edit[
        fieldAccessPath?.edit
      ]?.includes(fieldAccessKey)}`;
      return permissions?.edit[fieldAccessPath?.edit]?.includes(fieldAccessKey);
    }
    return true;
  }, [fieldAccessKey, permissions, fieldAccessPath]);

  const isSpecial = useMemo(() => {
    if (fieldAccessPath?.edit && fieldAccessPath?.special) {
      log`Field ${fieldAccessKey} will require some vetting: ${
        permissions?.edit[fieldAccessPath?.edit]?.includes(fieldAccessKey) &&
        permissions?.special[fieldAccessPath?.special]?.includes(fieldAccessKey)
      }`;
      return (
        permissions?.edit[fieldAccessPath?.edit]?.includes(fieldAccessKey) &&
        permissions?.special[fieldAccessPath?.special]?.includes(fieldAccessKey)
      );
    }
    return false;
  }, [permissions, fieldAccessKey, fieldAccessPath]);

  return { canView, canEdit, isSpecial };
};
