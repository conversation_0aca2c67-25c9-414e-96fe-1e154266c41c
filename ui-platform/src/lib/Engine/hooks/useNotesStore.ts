import { create } from 'zustand';
import { persist } from 'zustand/middleware';


export interface Note {
  id?: number;
  author_name: string;
  author_contact: string;
  author_company: string;
  author_roles: { id: number; description: string; }[];
  timestamp: string;
  message: string;
}

interface NotesState {
  notes: Note[];
  isLoading: boolean;
  error: string | null;
  // fetchNotes: (jobId: number, baseUrl: string) => Promise<void>;
  addNote: (message: string, jobId: number, token: string, addNoteUrl: string) => Promise<void>;
  setNotes: (notes: Note[]) => void;
}

export const useNotesStore = create<NotesState>()(
  persist(
    (set, get) => ({
      notes: [],
      isLoading: false,
      error: null,


      addNote: async (message: string, jobId: number, token?: string | undefined, addNoteUrl?: string | undefined) => {
        set({ isLoading: true, error: null });

        console.log('addNote in useNotesStore', message, jobId, addNoteUrl, token);

        if(!token || !addNoteUrl) {
          set({ error: 'Authentication token is required', isLoading: false });
          return;
        }
        
        // Optimistically add the note to the UI
        const tempNote = {
          message: message,
          author_name: '',
          author_contact: '',
          author_company: '',
          author_roles: [],
          timestamp: new Date().toISOString(),
          id: Date.now(), // temporary ID
        } as Note;
        console.log('tempNote', tempNote);
        set(state => ({ notes: [...state.notes, tempNote] }));

        try {
          const response = await fetch(`${addNoteUrl}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Token ${token}`,
            },
            body: JSON.stringify({ message, job_id: jobId }),
          });

          if (!response.ok) throw new Error('Failed to add note on the job');
          
          const data = await response.json();
          // Update the note with the real server data
          set(state => ({
            notes: state.notes.map(n => 
              n.id === tempNote.id ? data.note : n
            )
          }));
        } catch (error) {
          // Revert the optimistic update
          set(state => ({
            notes: state.notes.filter(n => n.id !== tempNote.id),
            error: error instanceof Error ? error.message : 'Failed to add note: ERROR'
          }));
        } finally {
          set({ isLoading: false });
        }
      },

      setNotes: (notes) => {
        console.log('setNotes in useNotesStore', notes);
        set({ notes, isLoading: false, error: null })
      },
    }),
    {
      name: 'notes-store',
    }
  )
);