# Multiple Async Actions Guide

## The Problem

When you call multiple `callClientActionAsync` functions sequentially without `await`, they execute **concurrently** (in parallel), not sequentially:

```typescript
// ❌ PROBLEMATIC: These all start immediately
callClientActionAsync(actionConfig1);
callClientActionAsync(actionConfig2);
callClientActionAsync(actionConfig3);
callClientActionAsync(actionConfig4);
```

### Issues This Causes:

1. **Race Conditions**: Actions may complete in unpredictable order
2. **State Conflicts**: Multiple actions modifying the same state simultaneously
3. **Resource Contention**: Multiple API calls hitting the server at once
4. **Loading State Issues**: Loading indicators may complete before actions finish
5. **Error Handling**: Difficult to handle errors from multiple concurrent operations

## Solutions

### 🆕 Refactored Factory Function (Recommended)

The `callClientAction` function has been refactored to handle all execution patterns through a single, unified interface:

```typescript
const { callClientAction } = useClientAction({...});

// Case 1: Array with concurrency limit (async execution with controlled concurrency)
await callClientAction(actionArray, false, 2); // limit to 2 concurrent actions

// Case 2: Array with async=true (sequential async execution)
await callClientAction(actionArray, true);

// Case 3: Array with async=false or undefined (concurrent execution - default for arrays)
await callClientAction(actionArray); // or callClientAction(actionArray, false)

// Case 4: Single action with explicit async property in config
await callClientAction({ ...actionConfig, async: true });

// Case 5: Single action with async parameter override
await callClientAction(actionConfig, true);

// Case 6: Single action - synchronous execution (default for single actions)
callClientAction(actionConfig); // returns undefined, executes synchronously
```

**Benefits of the Factory Function:**
- **Unified Interface**: One function handles all execution patterns
- **Clear Precedence**: concurrencyLimit > async parameter > config.async property
- **Type Safety**: Proper TypeScript support for all cases
- **Backward Compatibility**: Existing code continues to work

### 1. Sequential Execution (Manual)

```typescript
// ✅ Execute one after another
const handleSequential = async () => {
  try {
    await callClientActionAsync(actionConfig1);
    await callClientActionAsync(actionConfig2);
    await callClientActionAsync(actionConfig3);
    await callClientActionAsync(actionConfig4);
  } catch (error) {
    console.error('Action failed:', error);
  }
};
```

**Use when**: Actions depend on each other or modify the same state.

### 2. Sequential Execution (Helper Function)

```typescript
// ✅ Use the built-in helper
const { callClientActionsSequentially } = useClientAction({...});

await callClientActionsSequentially([
  actionConfig1,
  actionConfig2,
  actionConfig3,
  actionConfig4
]);
```

**Use when**: You have an array of actions that must execute in order.

### 3. Concurrent Execution (Safe)

```typescript
// ✅ Execute all at once (when safe)
const { callClientActionsConcurrently } = useClientAction({...});

await callClientActionsConcurrently([
  actionConfig1,
  actionConfig2,
  actionConfig3,
  actionConfig4
]);
```

**Use when**: Actions are independent and don't conflict with each other.

### 4. Controlled Concurrency

```typescript
// ✅ Execute with limited concurrency
const { callClientActionsWithLimit } = useClientAction({...});

// Execute max 2 actions at a time
await callClientActionsWithLimit([
  actionConfig1,
  actionConfig2,
  actionConfig3,
  actionConfig4
], 2);
```

**Use when**: You want some parallelism but need to limit resource usage.

### 5. Mixed Approach

```typescript
// ✅ Combine sequential and concurrent as needed
const handleMixed = async () => {
  // Critical setup actions (sequential)
  await callClientActionsSequentially([
    initAction,
    setupAction
  ]);

  // Independent actions (concurrent)
  await callClientActionsConcurrently([
    saveDataAction,
    sendNotificationAction,
    updateUIAction
  ]);

  // Final cleanup (sequential)
  await callClientActionAsync(cleanupAction);
};
```

### 6. Partial Failure Handling

```typescript
// ✅ Handle cases where some actions may fail
const handlePartialFailures = async () => {
  const promises = actions.map(action => callClientActionAsync(action));
  const results = await Promise.allSettled(promises);

  const successful = results.filter(r => r.status === 'fulfilled');
  const failed = results.filter(r => r.status === 'rejected');

  console.log(`${successful.length} succeeded, ${failed.length} failed`);
};
```

## Decision Matrix

| Scenario | Solution | Reason |
|----------|----------|---------|
| Actions modify the same state | Sequential | Prevent race conditions |
| Actions depend on previous results | Sequential | Ensure proper order |
| Independent API calls | Concurrent | Faster execution |
| Large number of actions | Controlled Concurrency | Prevent server overload |
| Mix of dependent/independent | Mixed Approach | Optimize for each type |
| Some actions may fail | Partial Failure Handling | Graceful degradation |

## Enhanced Hook API

The enhanced `useClientAction` hook now returns:

```typescript
const {
  callClientAction,           // Original function (sync/async based on config)
  callClientActionAsync,      // Always async version
  callClientActionsSequentially,  // Execute array sequentially
  callClientActionsConcurrently,   // Execute array concurrently
  callClientActionsWithLimit,      // Execute with concurrency limit
} = useClientAction({...});
```

## Best Practices

1. **Always use `await`** when you need to wait for completion
2. **Choose the right execution pattern** based on your use case
3. **Handle errors appropriately** with try-catch blocks
4. **Set loading states** before starting and clear them after completion
5. **Consider user experience** - don't block the UI unnecessarily
6. **Test thoroughly** - async code can have subtle bugs

## Common Patterns

### Form Submission Workflow
```typescript
// Sequential: validation → submission → navigation
await callClientActionsSequentially([
  validateAction,
  submitAction,
  navigateAction
]);
```

### Data Loading
```typescript
// Concurrent: load multiple independent data sources
await callClientActionsConcurrently([
  loadUserDataAction,
  loadPreferencesAction,
  loadNotificationsAction
]);
```

### Batch Operations
```typescript
// Controlled: process large number of items
await callClientActionsWithLimit(
  items.map(item => createProcessAction(item)),
  5 // Process 5 at a time
);
```
