import React, { useState } from 'react';
import { ActionConfig } from '../models/action.config';
import { ExtendedActionConfig, useClientAction } from './useClientActions';

/**
 * Comprehensive examples demonstrating the refactored callClientAction factory function
 * and different ways to handle multiple async actions
 */
export function MultipleAsyncActionsExamples() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  const {
    callClientAction,
    callClientActionAsync,
    callClientActionsSequentially,
    callClientActionsConcurrently,
    callClientActionsWithLimit,
  } = useClientAction({
    navigate: () => {},
    location: { search: '' } as any,
  });

  // Example action configurations
  const actionConfigs: ActionConfig[] = [
    {
      type: 'clientAction',
      action: 'log',
      payload: ['Action 1: Starting process'],
    },
    {
      type: 'clientAction',
      action: 'log',
      payload: ['Action 2: Processing data'],
    },
    {
      type: 'clientAction',
      action: 'log',
      payload: ['Action 3: Saving results'],
    },
    {
      type: 'clientAction',
      action: 'log',
      payload: ['Action 4: Cleanup'],
    },
  ];

  // ❌ PROBLEMATIC: Concurrent execution without await
  const handleProblematicConcurrent = () => {
    setResult('');
    setLoading(true);

    // These will all start executing immediately and concurrently
    // This can cause race conditions and unpredictable behavior
    callClientActionAsync(actionConfigs[0]);
    callClientActionAsync(actionConfigs[1]);
    callClientActionAsync(actionConfigs[2]);
    callClientActionAsync(actionConfigs[3]);

    // Loading will be set to false immediately, before actions complete!
    setLoading(false);
    setResult('All actions started (but may not be finished!)');
  };

  // ✅ SOLUTION 1: Sequential execution with manual await
  const handleSequentialManual = async () => {
    setResult('');
    setLoading(true);

    try {
      // Execute one after another
      await callClientActionAsync(actionConfigs[0]);
      await callClientActionAsync(actionConfigs[1]);
      await callClientActionAsync(actionConfigs[2]);
      await callClientActionAsync(actionConfigs[3]);

      setResult('Sequential actions completed successfully');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // ✅ SOLUTION 2: Sequential execution with helper function
  const handleSequentialHelper = async () => {
    setResult('');
    setLoading(true);

    try {
      await callClientActionsSequentially(actionConfigs);
      setResult('Sequential actions (helper) completed successfully');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // ✅ SOLUTION 3: Concurrent execution (when safe)
  const handleConcurrentSafe = async () => {
    setResult('');
    setLoading(true);

    try {
      // Safe for independent actions that don't affect the same state
      await callClientActionsConcurrently(actionConfigs);
      setResult('Concurrent actions completed successfully');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // ✅ SOLUTION 4: Controlled concurrency
  const handleControlledConcurrency = async () => {
    setResult('');
    setLoading(true);

    try {
      // Execute max 2 actions at a time
      await callClientActionsWithLimit(actionConfigs, 2);
      setResult('Controlled concurrency actions completed successfully');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // ✅ SOLUTION 5: Mixed approach with error handling
  const handleMixedApproach = async () => {
    setResult('');
    setLoading(true);

    try {
      // Critical actions that must be sequential
      await callClientActionsSequentially([
        actionConfigs[0], // Initialize
        actionConfigs[1], // Setup
      ]);

      // Independent actions that can run concurrently
      await callClientActionsConcurrently([
        actionConfigs[2], // Save data
        actionConfigs[3], // Send notification
      ]);

      setResult('Mixed approach completed successfully');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // ✅ SOLUTION 6: Promise.allSettled for partial failure handling
  const handlePartialFailures = async () => {
    setResult('');
    setLoading(true);

    try {
      const promises = actionConfigs.map((config) =>
        callClientActionAsync(config)
      );
      const results = await Promise.allSettled(promises);

      const successful = results.filter((r) => r.status === 'fulfilled').length;
      const failed = results.filter((r) => r.status === 'rejected').length;

      setResult(`Completed: ${successful} successful, ${failed} failed`);
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // ✅ NEW: Using the refactored callClientAction factory function
  const handleFactorySequential = async () => {
    setResult('');
    setLoading(true);

    try {
      // Case 2: Array with async=true (sequential execution)
      await callClientAction(actionConfigs, true);
      setResult('Factory function (sequential) completed successfully');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleFactoryConcurrent = async () => {
    setResult('');
    setLoading(true);

    try {
      // Case 3: Array with async=false/undefined (concurrent execution)
      await callClientAction(actionConfigs, false);
      setResult('Factory function (concurrent) completed successfully');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleFactoryWithLimit = async () => {
    setResult('');
    setLoading(true);

    try {
      // Case 1: Array with concurrency limit
      await callClientAction(actionConfigs, false, 2);
      setResult(
        'Factory function (concurrency limit 2) completed successfully'
      );
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleFactorySingleAsync = async () => {
    setResult('');
    setLoading(true);

    try {
      // Case 5: Single action with async parameter override
      await callClientAction(actionConfigs[0], true);
      setResult('Factory function (single async) completed successfully');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleFactorySingleSync = () => {
    setResult('');
    setLoading(true);

    try {
      // Case 6: Single action - synchronous execution (default)
      callClientAction(actionConfigs[0]);
      setResult('Factory function (single sync) completed successfully');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h2>Multiple Async Actions Examples</h2>

      <div style={{ marginBottom: '20px' }}>
        <h3>❌ Problematic Approach</h3>
        <button onClick={handleProblematicConcurrent} disabled={loading}>
          Concurrent Without Await (Problematic)
        </button>
        <p>
          Issues: Race conditions, immediate completion, unpredictable order
        </p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>✅ Sequential Solutions</h3>
        <button onClick={handleSequentialManual} disabled={loading}>
          Sequential (Manual Await)
        </button>
        <button onClick={handleSequentialHelper} disabled={loading}>
          Sequential (Helper Function)
        </button>
        <p>Use when: Actions depend on each other or modify the same state</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>✅ Concurrent Solutions</h3>
        <button onClick={handleConcurrentSafe} disabled={loading}>
          Concurrent (All at Once)
        </button>
        <button onClick={handleControlledConcurrency} disabled={loading}>
          Controlled Concurrency (Limit 2)
        </button>
        <p>Use when: Actions are independent and don't conflict</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>✅ Advanced Solutions</h3>
        <button onClick={handleMixedApproach} disabled={loading}>
          Mixed Approach
        </button>
        <button onClick={handlePartialFailures} disabled={loading}>
          Handle Partial Failures
        </button>
        <p>Use when: Complex workflows with different requirements</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>🆕 Refactored Factory Function Examples</h3>
        <button onClick={handleFactorySequential} disabled={loading}>
          Factory: Sequential (async=true)
        </button>
        <button onClick={handleFactoryConcurrent} disabled={loading}>
          Factory: Concurrent (async=false)
        </button>
        <button onClick={handleFactoryWithLimit} disabled={loading}>
          Factory: Concurrency Limit (limit=2)
        </button>
        <button onClick={handleFactorySingleAsync} disabled={loading}>
          Factory: Single Async
        </button>
        <button onClick={handleFactorySingleSync} disabled={loading}>
          Factory: Single Sync
        </button>
        <p>
          Use the unified callClientAction function with different parameters
        </p>
      </div>

      {loading && <div>Loading...</div>}

      {result && (
        <div>
          <h3>Result:</h3>
          <pre>{result}</pre>
        </div>
      )}
    </div>
  );
}

/**
 * Real-world example: Form submission with multiple steps
 */
export function FormSubmissionExample() {
  const { callClientActionsSequentially, callClientActionAsync } =
    useClientAction({
      navigate: () => {},
      location: { search: '' } as any,
    });

  const handleFormSubmission = async () => {
    try {
      // Step 1: Validate form (must be first)
      await callClientActionAsync({
        type: 'clientAction',
        action: 'log',
        payload: ['Validating form...'],
      });

      // Step 2: Submit data sequentially (order matters)
      await callClientActionsSequentially([
        {
          type: 'clientAction',
          action: 'submitAndFetch',
          payload: [
            { formData: 'user details' },
            { url: '/api/users', method: 'POST' },
            { method: 'post' },
          ],
        },
        {
          type: 'clientAction',
          action: 'submitAndFetch',
          payload: [
            { formData: 'preferences' },
            { url: '/api/preferences', method: 'POST' },
            { method: 'post' },
          ],
        },
      ]);

      // Step 3: Navigate to success page
      await callClientActionAsync({
        type: 'clientAction',
        action: 'navigate',
        payload: ['/success'],
      });
    } catch (error) {
      // Handle error
      await callClientActionAsync({
        type: 'clientAction',
        action: 'navigate',
        payload: ['/error'],
      });
    }
  };

  return (
    <button onClick={handleFormSubmission}>
      Submit Form (Sequential Steps)
    </button>
  );
}
