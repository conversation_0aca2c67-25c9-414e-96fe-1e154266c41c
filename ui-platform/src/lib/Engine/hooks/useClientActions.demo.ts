/**
 * Demonstration script showing the difference between concurrent and sequential execution
 * Run this in a browser console or Node.js environment to see the timing differences
 */

// Simulate the async action execution with delays
const simulateAsyncAction = (name: string, delay: number): Promise<void> => {
  console.log(`🚀 Starting ${name} (${delay}ms delay)`);
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log(`✅ Completed ${name}`);
      resolve();
    }, delay);
  });
};

// Demo 1: Concurrent execution (without await)
export async function demoConcurrentExecution() {
  console.log('\n=== DEMO 1: CONCURRENT EXECUTION (WITHOUT AWAIT) ===');
  console.log(
    'callClientActionAsync(action1); callClientActionAsync(action2); callClientActionAsync(action3);'
  );

  const startTime = Date.now();

  // ❌ This is what happens when you don't use await
  // All actions start immediately and run in parallel
  const promise1 = simulateAsyncAction('Action 1', 100);
  const promise2 = simulateAsyncAction('Action 2', 150);
  const promise3 = simulateAsyncAction('Action 3', 80);

  // Wait for all to complete
  await Promise.all([promise1, promise2, promise3]);

  const endTime = Date.now();
  console.log(
    `⏱️  Total time: ${
      endTime - startTime
    }ms (should be ~150ms - the longest action)`
  );
  console.log(
    '❗ Notice: All actions started immediately, completed in parallel\n'
  );
}

// Demo 2: Sequential execution (with await)
export async function demoSequentialExecution() {
  console.log('\n=== DEMO 2: SEQUENTIAL EXECUTION (WITH AWAIT) ===');
  console.log(
    'await callClientActionAsync(action1); await callClientActionAsync(action2); await callClientActionAsync(action3);'
  );

  const startTime = Date.now();

  // ✅ This is what happens when you use await
  // Each action waits for the previous one to complete
  await simulateAsyncAction('Action 1', 100);
  await simulateAsyncAction('Action 2', 150);
  await simulateAsyncAction('Action 3', 80);

  const endTime = Date.now();
  console.log(
    `⏱️  Total time: ${
      endTime - startTime
    }ms (should be ~330ms - sum of all actions)`
  );
  console.log('✅ Notice: Actions executed one after another, in order\n');
}

// Demo 3: Using helper functions
export async function demoHelperFunctions() {
  console.log('\n=== DEMO 3: USING HELPER FUNCTIONS ===');

  const actions = [
    () => simulateAsyncAction('Helper Action 1', 50),
    () => simulateAsyncAction('Helper Action 2', 75),
    () => simulateAsyncAction('Helper Action 3', 60),
  ];

  // Sequential helper
  console.log('📋 callClientActionsSequentially([action1, action2, action3])');
  const sequentialStart = Date.now();
  for (const action of actions) {
    await action();
  }
  const sequentialTime = Date.now() - sequentialStart;
  console.log(`⏱️  Sequential time: ${sequentialTime}ms\n`);

  // Concurrent helper
  console.log('🚀 callClientActionsConcurrently([action1, action2, action3])');
  const concurrentStart = Date.now();
  await Promise.all(actions.map((action) => action()));
  const concurrentTime = Date.now() - concurrentStart;
  console.log(`⏱️  Concurrent time: ${concurrentTime}ms\n`);

  console.log(
    `📊 Speed difference: ${Math.round(
      (sequentialTime / concurrentTime) * 100
    )}% faster with concurrent execution`
  );
}

// Demo 4: Real-world scenarios
export async function demoRealWorldScenarios() {
  console.log('\n=== DEMO 4: REAL-WORLD SCENARIOS ===');

  // Scenario 1: Form submission (must be sequential)
  console.log('📝 Form Submission Workflow (Sequential):');
  const formStart = Date.now();
  await simulateAsyncAction('Validate Form', 50);
  await simulateAsyncAction('Submit Data', 200);
  await simulateAsyncAction('Navigate to Success', 30);
  const formTime = Date.now() - formStart;
  console.log(`⏱️  Form submission time: ${formTime}ms\n`);

  // Scenario 2: Dashboard loading (can be concurrent)
  console.log('📊 Dashboard Loading (Concurrent):');
  const dashboardStart = Date.now();
  await Promise.all([
    simulateAsyncAction('Load User Data', 150),
    simulateAsyncAction('Load Notifications', 100),
    simulateAsyncAction('Load Recent Activity', 120),
    simulateAsyncAction('Load Weather Widget', 80),
  ]);
  const dashboardTime = Date.now() - dashboardStart;
  console.log(`⏱️  Dashboard loading time: ${dashboardTime}ms\n`);

  // Scenario 3: Batch processing with limits
  console.log('🔄 Batch Processing (Controlled Concurrency):');
  const batchStart = Date.now();
  const batchActions = Array.from(
    { length: 10 },
    (_, i) => () => simulateAsyncAction(`Batch Item ${i + 1}`, 30)
  );

  // Process in batches of 3
  const batchSize = 3;
  for (let i = 0; i < batchActions.length; i += batchSize) {
    const batch = batchActions.slice(i, i + batchSize);
    await Promise.all(batch.map((action) => action()));
  }
  const batchTime = Date.now() - batchStart;
  console.log(
    `⏱️  Batch processing time: ${batchTime}ms (10 items, 3 at a time)\n`
  );
}

// Demo 5: Refactored Factory Function
export async function demoFactoryFunction() {
  console.log('\n=== DEMO 5: REFACTORED FACTORY FUNCTION ===');

  // Simulate the callClientAction factory function
  const simulateCallClientAction = async (
    config: any | any[],
    async?: boolean,
    concurrencyLimit?: number
  ): Promise<void> => {
    if (Array.isArray(config)) {
      if (config.length === 0) return;

      // Case 1: Array with concurrency limit
      if (concurrencyLimit && concurrencyLimit > 0) {
        console.log(
          `🔄 Case 1: Array with concurrency limit (${concurrencyLimit})`
        );
        const batchSize = concurrencyLimit;
        for (let i = 0; i < config.length; i += batchSize) {
          const batch = config.slice(i, i + batchSize);
          await Promise.all(
            batch.map((action) =>
              simulateAsyncAction(action.name, action.delay)
            )
          );
        }
        return;
      }

      // Case 2: Array with async=true (sequential)
      if (async === true) {
        console.log('📋 Case 2: Array with async=true (sequential execution)');
        for (const action of config) {
          await simulateAsyncAction(action.name, action.delay);
        }
        return;
      }

      // Case 3: Array with async=false or undefined (concurrent)
      console.log(
        '🚀 Case 3: Array with async=false/undefined (concurrent execution)'
      );
      await Promise.all(
        config.map((action) => simulateAsyncAction(action.name, action.delay))
      );
      return;
    } else {
      // Single action cases
      if (config.async === true) {
        console.log('⚡ Case 4: Single action with explicit async property');
        await simulateAsyncAction(config.name, config.delay);
      } else if (async === true) {
        console.log('🔄 Case 5: Single action with async parameter override');
        await simulateAsyncAction(config.name, config.delay);
      } else {
        console.log('⚡ Case 6: Single action - synchronous execution');
        console.log(`🚀 Starting ${config.name} (sync)`);
        console.log(`✅ Completed ${config.name} (sync)`);
      }
    }
  };

  const actions = [
    { name: 'Factory Action 1', delay: 50 },
    { name: 'Factory Action 2', delay: 75 },
    { name: 'Factory Action 3', delay: 60 },
  ];

  // Test Case 1: Concurrency limit
  console.log('\n--- Testing Case 1: Concurrency Limit ---');
  const case1Start = Date.now();
  await simulateCallClientAction(actions, false, 2);
  console.log(`⏱️  Time: ${Date.now() - case1Start}ms\n`);

  // Test Case 2: Sequential
  console.log('--- Testing Case 2: Sequential ---');
  const case2Start = Date.now();
  await simulateCallClientAction(actions, true);
  console.log(`⏱️  Time: ${Date.now() - case2Start}ms\n`);

  // Test Case 3: Concurrent
  console.log('--- Testing Case 3: Concurrent ---');
  const case3Start = Date.now();
  await simulateCallClientAction(actions, false);
  console.log(`⏱️  Time: ${Date.now() - case3Start}ms\n`);

  // Test Case 4: Single with async property
  console.log('--- Testing Case 4: Single with async property ---');
  await simulateCallClientAction({
    name: 'Single Async',
    delay: 30,
    async: true,
  });

  // Test Case 5: Single with async parameter
  console.log('\n--- Testing Case 5: Single with async parameter ---');
  await simulateCallClientAction(
    { name: 'Single Param Async', delay: 30 },
    true
  );

  // Test Case 6: Single synchronous
  console.log('\n--- Testing Case 6: Single synchronous ---');
  simulateCallClientAction({ name: 'Single Sync', delay: 30 });
  console.log('');
}

// Demo 6: Error handling patterns
export async function demoErrorHandling() {
  console.log('\n=== DEMO 6: ERROR HANDLING PATTERNS ===');

  const failingAction = (name: string): Promise<void> => {
    console.log(`🚀 Starting ${name}`);
    return new Promise((_, reject) => {
      setTimeout(() => {
        console.log(`❌ Failed ${name}`);
        reject(new Error(`${name} failed`));
      }, 50);
    });
  };

  // Pattern 1: Fail fast (Promise.all)
  console.log('⚡ Fail Fast Pattern (Promise.all):');
  try {
    await Promise.all([
      simulateAsyncAction('Success Action 1', 100),
      failingAction('Failing Action'),
      simulateAsyncAction('Success Action 2', 150),
    ]);
  } catch (error) {
    console.log('🛑 Execution stopped on first error\n');
  }

  // Pattern 2: Continue on errors (Promise.allSettled)
  console.log('🔄 Continue on Errors Pattern (Promise.allSettled):');
  const results = await Promise.allSettled([
    simulateAsyncAction('Success Action 1', 50),
    failingAction('Failing Action'),
    simulateAsyncAction('Success Action 2', 75),
  ]);

  const successful = results.filter((r) => r.status === 'fulfilled').length;
  const failed = results.filter((r) => r.status === 'rejected').length;
  console.log(`📊 Results: ${successful} successful, ${failed} failed\n`);
}

// Run all demos
export async function runAllDemos() {
  console.log('🎭 useClientActions Async Execution Patterns Demo');
  console.log('================================================');

  await demoConcurrentExecution();
  await demoSequentialExecution();
  await demoHelperFunctions();
  await demoRealWorldScenarios();
  await demoFactoryFunction();
  await demoErrorHandling();

  console.log('🎉 Demo completed! Key takeaways:');
  console.log('1. Without await: Actions run concurrently (parallel)');
  console.log('2. With await: Actions run sequentially (one after another)');
  console.log('3. Use helper functions for complex scenarios');
  console.log(
    '4. The refactored factory function provides a unified interface'
  );
  console.log('5. Choose the right pattern based on your use case');
  console.log('6. Handle errors appropriately for your workflow');
}

// Uncomment to run the demo
// runAllDemos().catch(console.error);
