import Keycloak from 'keycloak-js';
import { useCallback } from 'react';
import { useFormContext, UseFormReturn } from 'react-hook-form';
import {
  FetcherWithComponents,
  Location,
  NavigateFunction,
  SubmitFunction,
} from 'react-router-dom';
import { FetchConfig, useErrorStore } from '../../Engine';
import { checkIsOnline } from '../../Utilities/checkNetworkOnline';
import {
  applyTemplateToObject,
  evaluateFormConditionExpression,
  extractValues,
  makeFetchCalls,
  renderTemplateStringOnClient,
} from '../helpers';
import { getPdfUrl, openPdfUrl } from '../helpers/pdf-handler';
import { renderTemplateObject } from '../helpers/render-template';
import { templateFunctions } from '../helpers/render-template-functions';
import { ActionConfig } from '../models/action.config';
import { useAppStore } from '../useAppStore';
import { useAsyncLoaderStore } from './useAsyncLoaderStore';
import { useModalStore } from './useModalStore';
import { useNotesStore } from './useNotesStore';
import { useReminderStore } from './useReminderStore';

// interface ContextProps {
//     // callClientAction: (config: ActionConfig) => any;
// }

interface Props {
  navigate: NavigateFunction;
  location: Location<any>;
  keycloak?: Keycloak;
  fetcher?: FetcherWithComponents<any>;
  submit?: SubmitFunction;
  formContext?: UseFormReturn;
  envObject?: any;
  tokenPrefix?: 'Bearer' | 'Token';
}

export const useClientAction = ({
  keycloak,
  fetcher,
  submit,
  formContext,
  envObject,
  navigate,
  location,
  tokenPrefix = 'Bearer',
}: Props) => {
  const setState = useAppStore(
    (state) => (state as { setState: any }).setState
  );
  const setModalState = useModalStore((state: any) => state.setModalState);
  const setAsyncLoading = useAsyncLoaderStore(
    (state: any) => state.setAsyncLoading
  );
  const { addError, clearError, clearAllErrors } = useErrorStore();
  const fc = useFormContext();

  const callConditionalActions = async (
    conditionResult: boolean,
    whenTrue: ActionConfig[],
    whenFalse?: ActionConfig[]
  ) => {
    if (conditionResult) {
      for (const cf of whenTrue || []) {
        await callClientAction(cf);
      }
    } else {
      if (!whenFalse || whenFalse.length === 0) return;
      for (const cf of whenFalse || []) {
        await callClientAction(cf);
      }
    }
  };

  async function executeConditionalActions(
    condition: string | boolean,
    actions: { whenTrue: ActionConfig[]; whenFalse?: ActionConfig[] }
  ) {
    if (formContext) {
      const conditionResult =
        typeof condition === 'string'
          ? evaluateFormConditionExpression(
              condition,
              useAppStore.getState(),
              formContext.watch(),
              formContext.formState
            )
          : condition;
      await callConditionalActions(
        conditionResult,
        actions.whenTrue,
        actions.whenFalse
      );
    } else {
      const conditionResult =
        typeof condition === 'string'
          ? evaluateFormConditionExpression(condition, useAppStore.getState())
          : condition;
      await callConditionalActions(
        conditionResult,
        actions.whenTrue,
        actions.whenFalse
      );
    }
  }

  const error = new Error('You are offline');
  const offlineHandler = () =>
    addError({
      key: `client-offline-${Date.now()}`,
      message:
        error instanceof Error
          ? `${error.message}: Please check your network connections and try again`
          : 'Unknown error: Try refreshing the website',
      source: 'server',
      stackTrace: error instanceof Error ? error.stack : undefined,
    });

  const callClientAction = useCallback(
    (config: ActionConfig) => {
      const { type = 'clientAction', action } = config;
      if (type === 'clientAction') {
        //   const functionArgs: any = {};

        switch (action) {
          case 'navigate': {
            if (checkIsOnline(() => offlineHandler()) === false) break;
            const currentParams = new URLSearchParams(location.search);
            const options =
              config?.payload[1] && Object.keys(config?.payload[1]).length !== 0
                ? Object.keys(config?.payload[1] as Record<string, any>).reduce(
                    (acc, key) => {
                      if (key !== 'clearParams') {
                        if (config?.payload[1]) {
                          acc[key] =
                            config.payload[1][key] !== undefined
                              ? config.payload[1][key]
                              : undefined;
                        }
                      }
                      return acc;
                    },
                    {} as Record<string, any>
                  )
                : undefined;
            navigate(
              {
                pathname: config.payload[0],
                search: config.payload[1]?.clearParams
                  ? ''
                  : currentParams.toString(),
              },
              options
            );
            break;
          }
          case 'log': {
            console.log(...config.payload);
            break;
          }
          case 'logout': {
            if (checkIsOnline(() => offlineHandler()) === false) break;
            keycloak?.logout();
            // console.log('in logout', {keycloak})
            break;
          }
          case 'triggerModal': {
            setModalState({ ...config.payload[0], display: true });
            break;
          }
          case 'closeModal': {
            setModalState({ display: false });
            break;
          }
          case 'submitAndNavigate': {
            if (checkIsOnline(() => offlineHandler()) === false) break;
            const store = useAppStore.getState();
            const formValues = fc?.getValues();
            const transformedPayloadObj = renderTemplateObject(
              { payload: config.payload },
              store,
              templateFunctions(store, formValues)
            );
            const payload = transformedPayloadObj.payload;
            const val = extractValues(payload[0], store);
            console.log({ val, payload, store, config, formValues });
            try {
              submit?.(
                {
                  data: JSON.stringify(val),
                  url: payload[1].url,
                  headers: payload[1].headers || {},
                  redirect: payload[1].redirect,
                  bodySlicePath: payload[1].bodySlicePath || '',
                },
                payload[2]
              );
            } catch (error) {
              addError({
                key: `submit-and-navigate-${Date.now()}`,
                message:
                  error instanceof Error
                    ? `${error.message}: Please try refreshing the page`
                    : 'Unknown error: Try refreshing the website',
                source: 'server',
                stackTrace: error instanceof Error ? error.stack : undefined,
              });
            }
            break;
          }
          case 'submitAndFetch': {
            if (checkIsOnline(() => offlineHandler()) === false) break;
            const store = useAppStore.getState();
            const formValues = formContext?.getValues();
            const transformedPayloadObj = renderTemplateObject(
              { payload: config.payload },
              store,
              templateFunctions(store, formValues)
            );
            const payload = transformedPayloadObj.payload;
            const val = extractValues(payload[0], store);
            // inject sp_id into payload if submission has sp_id
            if (payload[0].sp_id) {
              (val as any).postData.sp_id = (val as any).sp_id;
            }
            try {
              const response = fetcher?.submit(
                {
                  data: JSON.stringify(val),
                  url: payload[1].url,
                  headers: payload[1].headers || {},
                  bodySlicePath: payload[1].bodySlicePath || '',
                },
                config.payload[2]
              );

              console.log({ response });
            } catch (error) {
              addError({
                key: `submit-and-fetch-${Date.now()}`,
                message:
                  error instanceof Error
                    ? `${error.message}: Please try refreshing the page`
                    : 'Unknown error: Try refreshing the website',
                source: 'server',
                stackTrace: error instanceof Error ? error.stack : undefined,
              });
            }
            if (config.payload[1].loader) {
              fetcher?.load(config.payload[1].loader);
            }
            break;
          }
          case 'submitAsync': {
            if (checkIsOnline(() => offlineHandler()) === false) break;
            const store = useAppStore.getState();
            const { getValues } = formContext
              ? formContext
              : { getValues: () => null };
            console.log({ getValues: getValues() });

            const transformedPayloadObj = renderTemplateObject(
              { payload: config.payload },
              store
            );
            const payload = transformedPayloadObj.payload;

            const calls = payload.calls;
            const redirect = payload.redirect;
            const onFinish = payload.onFinish;
            const previousResults: { [key: string]: any } = {};

            const executeCallsSequentially = async () => {
              setAsyncLoading(true);
              for (const call of calls) {
                if (call.key === 'addReminder') {
                  console.log('addReminder', call);
                  console.log('addReminder data', call.data);
                  const reminderData = call.data;
                  // get the reminder store's addReminder function
                  const addReminder = useReminderStore.getState().addReminder;
                  // handle reminder specific logic

                  const formValues = formContext?.getValues();
                  // add the reminder to the store
                  addReminder(reminderData);

                  // close the modal after adding the reminder
                  setAsyncLoading(false);
                  setModalState({ display: false });
                  if (formValues) {
                    // console.log('formValues', formValues);
                    // const reminderData = typeof call.data === 'function' ? call.data(formValues) : renderTemplateObject(call.data, {form: formValues});

                    // // add the reminder to the store
                    // addReminder(reminderData);

                    // // close the modal after adding the reminder
                    // setAsyncLoading(false);
                    // setModalState({ display: false });
                    console.log('formValues should be here', formValues);
                  }
                }
                if (call.key === 'addNote') {
                  console.log('addNote', call);
                  console.log('addNote data', call.data);
                  const noteData = call.data;
                  const message = noteData.message;
                  const jobId = noteData.job_id;
                  const addNoteUrlString = noteData.addNoteUrl;
                  const token = keycloak?.token || '';

                  console.log('E N V O B J E C T', envObject);
                  console.log('M E S S A G E', message);

                  // get the addNote function from the notes store
                  const addNote = useNotesStore.getState().addNote;

                  // provide fallback if token or addNoteUrlString is missing
                  if (!token || !addNoteUrlString) {
                    console.error('Missing token or baseUrl');
                    return;
                  }

                  // call the addNote function from the notes store
                  addNote(message, jobId, token, addNoteUrlString);
                } else {
                  console.log('call.key !== "addReminder" ', call.data);
                  const callFormData = new FormData();
                  const callDataWithStoreValues: any = extractValues(
                    call.data,
                    getValues()
                  );
                  const interpolatedData = applyTemplateToObject(
                    callDataWithStoreValues,
                    previousResults
                  );
                  // callFormData.append('call', JSON.stringify({ ...call, data: interpolatedData }));
                  // const newCall = { ...call, data: interpolatedData };
                  for (const k of Object.keys(interpolatedData)) {
                    if (callDataWithStoreValues[k] instanceof File) {
                      callFormData.append(k, callDataWithStoreValues[k]);
                    } else {
                      callFormData.append(k, interpolatedData[k]);
                    }
                  }
                  //
                  let newUrl = '';
                  if (envObject) {
                    newUrl = renderTemplateStringOnClient(
                      { template: call.url },
                      envObject
                    );
                    console.log({ uurl: call.url, envObject });
                  } else {
                    newUrl = call.url;
                  }

                  // Use fetch directly to handle the request and response
                  try {
                    const response = await fetch(newUrl, {
                      method: 'POST',
                      body: callFormData,
                      headers: {
                        Authorization: `${tokenPrefix} ${keycloak?.token}`,
                        Accept: 'application/json',
                      },
                    });

                    const result = await response.json(); // Assuming the response is in JSON format
                    previousResults[call.key] = result; // Store the result using the call's key
                  } catch (error) {
                    addError({
                      key: `submit-async-${Date.now()}`,
                      message:
                        error instanceof Error
                          ? `${error.message}: Please try refreshing the page`
                          : 'Unknown error: Try refreshing the website',
                      source: 'server',
                      stackTrace:
                        error instanceof Error ? error.stack : undefined,
                    });
                  }
                }
              }
              if (onFinish) {
                callClientAction(onFinish);
              }
              if (redirect) {
                fetcher?.load(redirect);
              }
              setAsyncLoading(false);
            };

            executeCallsSequentially();
            break;
          }

          case 'triggerFetchCall': {
            if (checkIsOnline(() => offlineHandler()) === false) break;

            // Ensure our payload includes fetchCalls and (optionally) request options

            const fetchCalls: FetchConfig[] = config.payload;
            const request: Request | undefined = undefined;
            if (!keycloak) break;

            const triggerCall = async () => {
              console.log('Trigger Fetch Call', fetchCalls);

              const result = await makeFetchCalls(
                fetchCalls,
                keycloak,
                request,
                envObject
              );

              setState((state: any) => ({ ...state, ...result }));

              console.log('Trigger Fetch Call', { result });
            };

            triggerCall();
            break;
          }

          case 'defaultModalAction': {
            if (typeof config.payload === 'function') {
              config.payload();
            }
            break;
          }
          case 'resetFields': {
            const fields = config.payload?.fields;
            if (fields) {
              for (const field of fields) {
                if (typeof field === 'string') {
                  formContext?.resetField(field);
                } else {
                  const store = useAppStore.getState();
                  const val: Record<string, any> = extractValues(
                    { [field.fieldName]: field.defaultValue },
                    store
                  );
                  formContext?.resetField(field.fieldName, {
                    defaultValue: val[field.fieldName],
                  });
                }
              }
            }
            break;
          }
          case 'conditional': {
            executeConditionalActions(
              config.payload.condition,
              config.payload.actions
            );
            break;
          }
          case 'clearStore': {
            if (config.payload.length === 0) {
              setState({}, true);
              break;
            }
            setState(
              config.payload.reduce(
                (acc, curr) => ({ ...acc, [curr]: undefined }),
                {}
              )
            );
            break;
          }
          case 'updateStore': {
            if (config.payload.length === 0) {
              break;
            }
            setState((state: any) => {
              return config.payload.reduce(
                (acc, curr) => ({
                  ...acc,
                  ...Object.keys(curr).reduce(
                    (acc2, key) => ({ ...acc2, [key]: curr[key] }),
                    {}
                  ),
                }),
                state
              );
            });
            break;
          }
          case 'clearErrors': {
            if (!config.payload) {
              clearAllErrors();
              break;
            }
            clearError(config.payload);
            break;
          }
          case 'timeout': {
            const timeout = config.payload[0];
            const actions = config.payload[1];
            const timeoutActions = async () => {
              for (const cf of actions || []) {
                await callClientAction(cf);
              }
            };
            setTimeout(timeoutActions, timeout);
            break;
          }
          case 'viewJobCardPDF': {
            const payload = config.payload;
            // getPdfUrl(keycloak?.token as string, payload?.jobId, payload?.apiBaseUrl, payload?.pdfUrl)
            // .then((url) => window.open(url, '_blank'));
            openPdfUrl(
              keycloak?.token as string,
              payload?.jobId,
              payload?.apiBaseUrl,
              payload?.pdfUrl
            );
            break;
          }
          default:
            console.log('no handler for the function you called');
            break;
        }

        // Call the function with the interpreted arguments
        //   clientActions[functionName](functionArgs);
      }
    },
    [
      useAsyncLoaderStore,
      keycloak,
      setModalState,
      navigate,
      fetcher,
      submit,
      formContext,
    ]
  );

  return { callClientAction };
};
