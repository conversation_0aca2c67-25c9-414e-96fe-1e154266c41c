import { Meta, Story } from '@storybook/react';
import { BOQModule } from '../BOQModule';
import { sampleBoqItems, samplePreselectedOptionalItem } from '../sample-data';

// Preparing all items as optional
interface Item {
  id: number;
  description: string | undefined;
  quantity: number;
  compulsoryItem: boolean;
  unitPrice: number | undefined;
}

// Treat all items as optional
// const optional: Item[] = sampleBoqItems.items;
let optional: Item[] = [];
// Function to handle item changes
const handleItemsChange = (updatedItems: Item[]) => {
  console.log('Updated items:', updatedItems);
};

// Meta for Storybook
const meta: Meta<typeof BOQModule> = {
  title: 'Fragments/BOQModule',
  component: BOQModule,
};

export default meta;

// Template for Storybook
const Template: Story<{
  lineItemsTableProps: any;
  invoiceSummaryNotesProps: any;
  invoiceSummaryProps: any;
}> = (args) => <BOQModule {...args} />;

// Story definition for State 47
export const State_47_Upload_New_Invoice = Template.bind({});
State_47_Upload_New_Invoice.args = {
  state: 47,
  boqItems: sampleBoqItems,
  travelDistance: 85,
  createCustomItemButton: true,
  preselectedOptionalItem: samplePreselectedOptionalItem,
};


export const State_26_BOQ_All_Compulsory = Template.bind({});
State_26_BOQ_All_Compulsory.args = {
  state: 26,
  boqItems: sampleBoqItems,
  travelDistance: 85,
  preselectedCompulsoryItem: samplePreselectedOptionalItem,
};