// external module imports
import Keycloak from 'keycloak-js';
import { useMemo } from 'react';
import styled from 'styled-components';
// internal module imports
import {
  DocumentCard,
  DocumentCardProps,
} from '../../Components/Cards/DocumentCard/DocumentCard';

type DocumentCardListProps = {
  documents: DocumentCardProps[];
  columns?: number;
  gap?: string;
  sp_id?: string;
  staff_id?: string;
  isStory?: boolean;
  base_url_env_name?: string;
  director_id?: string;
  _keycloak?: Keycloak;
  downloadable?: boolean;
  tokenPrefix?: 'Token' | 'Bearer';
  file_download_url?: string;
};

// Adjusting Container to explicitly show centering using margin auto
const Container = styled.div<{ gap?: string; columns?: number }>`
  position: relative;
  width: 596px; // Fixed width
  display: grid;
  grid-template-columns: repeat(${(props) => props.columns || 2}, 1fr);
  gap: ${(props) => props.gap || '8px'};
  margin-left: auto; // Automatically adjust left margin
  margin-right: auto; // Automatically adjust right margin
`;

/**
 * Renders a list of document cards.
 *
 * @param documents - An array of documents to display.
 * @returns JSX element representing the document card list.
 */

const documentViewModeDefaults: Partial<DocumentCardProps> = {
  enableUpload: false,
};

/**
 * Example use of the DocumentCardList component
 *
 * <DocumentCardList
 *    documents={documents}
 *    sp_id={sp_id}
 *    isStory={isStory}
 *    staff_id={staff_id}
 *    base_url={base_url}
 *    director_id={director_id}
 *    columns={columns}
 *    gap={gap}
 * />
 */
export function DocumentCardList({
  documents = [],
  sp_id,
  isStory,
  staff_id,
  base_url_env_name,
  director_id,
  columns,
  gap,
  _keycloak,
  downloadable,
  tokenPrefix,
  file_download_url,
}: DocumentCardListProps) {
  const memoizedDocs = useMemo(
    () => documents.map((doc) => ({ ...documentViewModeDefaults, ...doc })),
    [documents]
  );
  return (
    <Container {...{ columns, gap }}>
      {memoizedDocs.map((document: DocumentCardProps, idx: number) => (
        <DocumentCard
          key={idx}
          sp={sp_id}
          staff={staff_id}
          {...document}
          {...{
            isStory,
            base_url_env_name,
            director_id,
            _keycloak,
            downloadable,
            tokenPrefix,
            file_download: file_download_url,
          }}
        />
      ))}
    </Container>
  );
}
