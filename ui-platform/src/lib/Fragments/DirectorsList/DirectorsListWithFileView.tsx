// external module imports
import Keycloak from 'keycloak-js';
import R from 'ramda';
import { useEffect, useMemo, useRef, useState } from 'react';
import { UseFormGetFieldState } from 'react-hook-form';
import { SubmitFunction } from 'react-router-dom';
import styled from 'styled-components';
// internal module imports
import { useSpaKeycloak } from '../../Auth';
import {
  DocumentCard,
  Icon,
  IconTypes,
  PlainTextInput,
  Toaster,
} from '../../Components';
import {
  useAppStore,
  useErrorModal,
  useErrorStore,
  useFieldAccessPermissions,
} from '../../Engine';
import { TemplateLiteralLogger, normalizeUrl } from '../../Utilities';
import { FormConfig } from '../form-builder/types/form.config';

// instantiate logger for debugging generate control component
const log = TemplateLiteralLogger.createLog(
  {
    prefix: '🪵[Directors List With Preview log]:',
    enabled: false,
    options: { style: { backgroundColor: '#efefef', color: '#017812' } },
  },
  'log'
);

const inspect = TemplateLiteralLogger.createLog(
  {
    prefix: '🧐[Directors List With Preview inspect]:',
    enabled: true,
    options: { style: { color: '#003d8c' } },
  },
  'i'
);

const warn = TemplateLiteralLogger.createLog(
  {
    prefix: '[Directors List With Preview warning]:',
    enabled: true,
    options: { style: { color: '#a03f0b' } },
  },
  'warn'
);

type Props = {
  control: any;
  name: string;
  formConfig: FormConfig;
  directors: any[];
  getFieldState: UseFormGetFieldState<any>;
  _submit: SubmitFunction;
  filesUrl: string;
  disabledWhen?: boolean;
  tokenPrefix?: 'Bearer' | 'Token';
  _keycloak: Keycloak;
  removeDirectorUrl: string;
  removeDirectorRedirectUrl: string;
  nameField?: IDirectorTextField;
  identityField?: IDirectorTextField;
  identityFileField?: IDirectorFileCard;
  addBtn?: boolean;
  isStory?: boolean;
  _actionData?: any;
};

const Container = styled.div`
  position: relative;
  width: 50%;
  min-width: 712px;
  display: grid;
  grid-auto-flow: row;
  gap: ${(props) => props.theme.SpacingXxl};
  margin-bottom: 4rem;
`;

const FormContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 3rem;
  column-gap: ${(props) => props.theme.SpacingXxl};
`;

const Button = styled.button`
  all: unset;
  cursor: pointer;
  box-shadow: 0px 0px 3px ${(props) => props.theme.ColorsTypographyPrimary};
  border-radius: 100%;
  background-color: ${(props) => props.theme.ColorsTabsToolbarPrimary};
  width: 100%;
  height: 34px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  gap: 8px;
  padding: 0;
  width: 3rem;
  height: 3rem;
  text-align: center;
  font-size: 12px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: Inter;
  &:hover {
    background-color: ${(props) => props?.theme.ColorsTabsToolbarHover};
  }
  &:disabled {
    border: 1px solid ${(props) => props?.theme.ColorsStrokesGrey};
    background-color: transparent;
  }
  &:active {
    background-color: ${(props) => props?.theme.ColorsTabsToolbarActivated};
    box-shadow: 0px 0px 5.2px 0px #fbfeff;
  }
`;

const FieldsWrapper = styled.div`
  display: grid;
  grid-auto-flow: row;
  gap: ${(props) => props.theme.SpacingLg};
  width: 100%;
`;

// const PlainTextField = styled(PlainTextInput)``;

const DocumentCardField = styled(DocumentCard)`
  grid-column: span 2;
  width: 100%;
`;

const ButtonAdd = styled(Button)`
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) translateY(3rem);
`;

const ButtonRemove = styled(Button)`
  margin: auto 0;
`;

const NewDirectorWrapper = styled.div``;

interface IFieldAccess {
  key?: string;
  path?: { edit: string; view: string; isSpecial: string };
}

interface IDirectorField {
  name: string;
  placeholder?: string;
  disabledWhen?: string;
  fieldAccess?: IFieldAccess;
}

interface IDirectorTextField extends IDirectorField {
  state?: 'display-only' | 'default';
  icon?: {
    type: IconTypes;
    position: 'left' | 'right';
  };
  fieldState?: 'display-only' | 'default';
  label?: string;
}

interface IDirectorFileCard extends IDirectorField {
  purpose: string;
  purposeAsName?: boolean;
  isLandscape?: boolean;
  file_download?: string;
  base_url_name?: string;
}
/**
 * Director form component for displaying and editing director information.
 * @param numberOfDirectors The total number of directors.
 * @param dir The director object containing id, name, identity, files, and sp.
 * @param index The index of the director in the list.
 * @param remove Function to remove a director.
 * @param namePlaceholder Placeholder text for the name input field.
 * @param identityPlaceholder Placeholder text for the identity input field.
 * @param nameFieldName The field name for the director's name.
 * @param identityFieldName The field name for the director's identity.
 * @param fileFieldName The field name for the director's file.
 * @param key The unique key for the director form.
 * @param fileEmptyText Text to display when no file is uploaded.
 */

const DirectorForm = ({
  numberOfDirectors,
  dir,
  index,
  remove,
  namePlaceholder = '',
  identityPlaceholder = '',
  nameFieldName = 'director_name',
  identityFieldName = 'director_identity',
  fileFieldName = 'director_file',
  key,
  fileEmptyText = '',
  disabledWhen = false,
  _keycloak,
  nameField = {
    name: 'director_name',
    placeholder: '',
    label: 'Name',
  },
  identityField = {
    name: 'director_identity',
    placeholder: '',
    label: 'Identity',
  },
  identityFileField = {
    name: 'director_file',
    placeholder: '',
    purpose: 'Identity',
    purposeAsName: true,
    isLandscape: true,
    file_download: 'api/v1/file_actions/director_get_file',
    base_url_name: 'VITE_SP_SERVER',
  },
  fieldAccess,
}: {
  dir?: {
    id: string;
    name: string;
    identity: string;
    files: any[];
    sp: string;
  };
  fieldAccess?: any;
  index?: number;
  remove?: (dir?: {
    id: string;
    name: string;
    identity: string;
    files: any[];
  }) => void;
  nameField?: IDirectorTextField;
  identityField?: IDirectorTextField;
  identityFileField?: IDirectorFileCard;
  namePlaceholder?: string;
  identityPlaceholder?: string;
  nameFieldName?: string;
  identityFieldName?: string;
  fileFieldName?: string;
  key?: string;
  fileEmptyText?: string;
  numberOfDirectors: number;
  disabledWhen?: boolean;
  _keycloak?: Keycloak;
}) => {
  const {
    canView: canViewDirectorName,
    canEdit: canEditDirectorName,
    isSpecial: isSpecialDirectorName,
  } = useFieldAccessPermissions({
    fieldAccess,
    fieldAccessKey:
      nameField.fieldAccess?.key ?? nameFieldName ?? nameField.name,
    fieldAccessPath: nameField?.fieldAccess?.path ?? {
      edit: 'companydirector',
      view: 'companydirector',
      special: 'companydirector',
    },
  });
  const {
    canView: canViewDirectorIdentity,
    canEdit: canEditDirectorIdentity,
    isSpecial: isSpecialDirectorIdentity,
  } = useFieldAccessPermissions({
    fieldAccess,
    fieldAccessKey:
      identityField.fieldAccess?.key ?? identityFieldName ?? identityField.name,
    fieldAccessPath: identityField?.fieldAccess?.path ?? {
      edit: 'companydirector',
      view: 'companydirector',
      special: 'companydirector',
    },
  });
  const {
    canView: canViewDirectorIdentityFile,
    canEdit: canEditDirectorIdentityFile,
    isSpecial: isSpecialDirectorIdentityFile,
  } = useFieldAccessPermissions({
    fieldAccess,
    fieldAccessKey:
      identityField.fieldAccess?.key ?? identityFieldName ?? identityField.name,
    fieldAccessPath: identityField?.fieldAccess?.path ?? {
      edit: 'directordocument',
      view: 'directordocument',
      special: 'directordocument',
    },
  });
  const file = useMemo(() => dir?.files?.[dir?.files?.length - 1], [dir]);
  return (
    <FormContainer key={dir?.id ?? key}>
      <div>
        <FieldsWrapper>
          <PlainTextInput
            name={nameFieldName || nameField.name}
            label={nameField.label}
            value={dir?.name ?? ''}
            placeholder={dir?.name ?? namePlaceholder}
            icon={
              isSpecialDirectorName ? (
                <Icon type="alarm-clock" size={24} />
              ) : (
                identityField?.icon?.type && (
                  <Icon type={identityField?.icon?.type} size={24} />
                )
              )
            }
            position={
              isSpecialDirectorName
                ? 'right'
                : identityField?.icon?.position
                ? identityField?.icon?.position
                : 'none'
            }
            state={
              !canEditDirectorName
                ? 'display-only'
                : nameField.state
                ? nameField.state
                : 'default'
            }
            disabled={
              disabledWhen || !canEditDirectorName || !canViewDirectorName
            }
          />
          <PlainTextInput
            name={identityFieldName || identityField.name}
            label={identityField.label}
            value={dir?.identity ?? ''}
            placeholder={dir?.identity ?? identityPlaceholder}
            icon={
              isSpecialDirectorIdentity ? (
                <Icon type="alarm-clock" size={24} />
              ) : (
                identityField?.icon?.type && (
                  <Icon type={identityField?.icon?.type} size={24} />
                )
              )
            }
            position={
              isSpecialDirectorIdentity
                ? 'right'
                : identityField?.icon?.position
                ? identityField?.icon?.position
                : 'none'
            }
            state={
              !canEditDirectorIdentity
                ? 'display-only'
                : identityField.state
                ? identityField.state
                : 'default'
            }
            disabled={
              disabledWhen ||
              !canEditDirectorIdentity ||
              !canViewDirectorIdentity
            }
          />
          {/* <div>{JSON.stringify(dir?.files)}</div> */}
          <DocumentCardField
            purpose={identityFileField.purpose}
            purposeAsName={identityFileField.purposeAsName}
            isLandscape={identityFileField.isLandscape}
            name={fileFieldName || identityField.name}
            filename={file?.filename ?? 'No file uploaded'}
            thumbnail={file?.thumbnail ?? ''}
            created={file?.created ?? ''}
            sp={dir?.sp ?? ''}
            file_download={identityFileField.file_download}
            base_url_env_name={`${
              identityFileField.base_url_name ?? 'VITE_SP_SERVER'
            }`}
            fileData={file}
            director_id={dir?.id ?? ''}
            enableUpload={
              canEditDirectorIdentityFile && canViewDirectorIdentityFile
                ? canEditDirectorIdentityFile
                : !disabledWhen
            }
            _keycloak={_keycloak}
          />
        </FieldsWrapper>
      </div>
      {numberOfDirectors > 1 ? (
        <ButtonRemove onClick={() => remove && remove(dir)}>
          <Icon type={'trash-01'} size={24} />
        </ButtonRemove>
      ) : null}
    </FormContainer>
  );
};

export const DirectorsListWithFileView = ({
  directors = [],
  filesUrl = '/api/v1/file_actions/director_get_files',
  isStory,
  addBtn = true,
  disabledWhen = false,
  nameField = {
    name: 'director_name',
    placeholder: '',
    label: 'Name',
  },
  identityField = {
    name: 'director_identity',
    placeholder: '',
    label: 'Identity',
  },
  identityFileField = {
    name: 'director_file',
    placeholder: '',
    purpose: 'Identity',
    purposeAsName: true,
    isLandscape: true,
    file_download: '/api/v1/file_actions/director_get_file',
    base_url_name: 'VITE_SP_SERVER',
  },
  _submit,
  _actionData,
  tokenPrefix = 'Bearer',
  _keycloak,
  removeDirectorUrl = '/api/v1/spaas_actions/remove_director',
  removeDirectorRedirectUrl = '/field-ops/tasks/edit/directors',
}: Props) => {
  const [showAddBtn, setShowAddBtn] = useState(addBtn);
  const [directorsData, setDirectorsData] = useState<any[]>([]);
  const keycloak = isStory ? null : _keycloak;
  const { handleServerError } = useErrorModal();
  const { addError } = useErrorStore();

  const hasRun = useRef(false);
  const [isSoleProprietor, setIsSoleProprietor] = useState(false);
  const { sp_profile, fieldAccess }: any = useAppStore((state) => state);

  const fetchDirectorFiles = async (dirs: any[]) => {
    try {
      return await Promise.all(
        dirs.map(async (dir) => {
          const response = await fetch(
            `${
              import.meta.env[
                identityFileField?.base_url_name ?? 'VITE_SP_SERVER'
              ]
            }${normalizeUrl(filesUrl)}`,
            {
              method: 'POST',
              body: JSON.stringify({
                director_id: dir.id,
                with_thumbnails: true,
                purpose: 'Identity',
              }),
              headers: {
                'Content-Type': 'application/json',
                Authorization: `${tokenPrefix} ${keycloak?.token}`,
              },
            }
          );

          if (!response.ok) {
            throw new Error(
              `Failed to fetch files for director ${dir.id}: ${response.statusText}`
            );
          }

          const filesResponse = await response.json();
          const files = filesResponse.payload;
          return { ...dir, files };
        })
      );
    } catch (error) {
      addError({
        key: `directors-list-with-file-view-fetch-director-files-${Date.now()}`,
        message:
          error instanceof Error
            ? `${error.message}: Please try refreshing the page`
            : 'Unknown error: Try refreshing the website',
        source: 'server',
        stackTrace: error instanceof Error ? error.stack : undefined,
      });
      handleServerError(
        error instanceof Error
          ? `${error.message}: Please try refreshing the page`
          : 'Failed to fetch director files'
      );
      return dirs.map((dir) => ({ ...dir, files: [] }));
    }
  };

  useEffect(() => {
    if (directors && !!directors.length && keycloak) {
      const { company_type } = sp_profile.details;
      const isSoleProprietorType = company_type === 2; // magic number "2" is for sole proprietor
      fetchDirectorFiles(directors).then((dirs: any[]) => {
        setDirectorsData(dirs);
        setIsSoleProprietor(isSoleProprietorType);
        setShowAddBtn(!isSoleProprietorType);
      });
    }
  }, [directors, keycloak]);

  useEffect(() => {
    if (_actionData?.error) {
      handleServerError(
        typeof _actionData.error === 'string'
          ? _actionData.error
          : 'Failed to remove director'
      );
    }
  }, [_actionData, handleServerError]);

  const remove = (dir: any) => {
    try {
      _submit(
        {
          data: JSON.stringify({ director_id: dir.id }),
          url: `{${
            identityFileField.base_url_name ?? 'VITE_SP_SERVER'
          }}${normalizeUrl(removeDirectorUrl)}`,
          redirect: normalizeUrl(removeDirectorRedirectUrl),
          headers: {},
        },
        { method: 'POST' }
      );
      setShowAddBtn(true);
    } catch (error) {
      addError({
        key: `directors-list-with-file-view-fetch-director-files-${Date.now()}`,
        message:
          error instanceof Error
            ? `${error.message}: Please try refreshing the page`
            : 'Unknown error: Try refreshing the website',
        source: 'server',
        stackTrace: error instanceof Error ? error.stack : undefined,
      });
      handleServerError('Failed to remove director');
    }
  };

  return (
    <Container key={'directors-list'}>
      {isSoleProprietor && directorsData.length > 1 && (
        <Toaster
          content="The company is a sole proprietor which should only have one director."
          className="error"
        />
      )}

      {(directorsData || [])?.map((dir, index) => (
        <div key={dir.id}>
          <DirectorForm
            numberOfDirectors={directorsData.length} // There will be no delete button if length is 1
            dir={dir}
            index={index}
            remove={remove}
            nameFieldName={`${nameField.name}-${index}`}
            identityFieldName={`${identityField.name}-${index}`}
            fileFieldName={`${identityFileField.name}-${index}`}
            disabledWhen={disabledWhen}
            _keycloak={keycloak ? keycloak : undefined}
            nameField={nameField}
            identityField={identityField}
            identityFileField={identityFileField}
            fieldAccess={fieldAccess}
          />
        </div>
      ))}
      {!showAddBtn && !isSoleProprietor && (
        <DirectorForm
          numberOfDirectors={10} //  Just any number bigger than 1 so delete button always shows
          fileEmptyText="No file uploaded"
          remove={() => {
            setShowAddBtn(true);
          }}
        />
      )}
      <div>
        {showAddBtn && !disabledWhen && (
          <ButtonAdd onClick={() => setShowAddBtn(false)}>
            <Icon type={'plus'} size={24} />
          </ButtonAdd>
        )}
      </div>
    </Container>
  );
};
