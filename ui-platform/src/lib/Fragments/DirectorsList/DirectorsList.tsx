// import external modules
import Keycloak from 'keycloak-js';
import { useEffect, useRef, useState } from 'react';
import { UseFormGetFieldState, useFormContext } from 'react-hook-form';
import { SubmitFunction } from 'react-router-dom';
import styled from 'styled-components';
// import internal modules
import { useSpaKeycloak } from '../../Auth';
import { DragAndDrop, Icon, IconTypes, PlainTextInput } from '../../Components';
import {
  useAppStore,
  useErrorStore,
  useFieldAccessPermissions,
} from '../../Engine';
import { useErrorModal } from '../../Engine/hooks/useErrorModal';
import { normalizeUrl } from '../../Utilities';
import { FormConfig } from '../form-builder/types/form.config';

type Props = {
  control: any;
  name: string;
  formConfig: FormConfig;
  directors: any[];
  getFieldState: UseFormGetFieldState<any>;
  _submit: SubmitFunction;
  filesUrl: string;
  nameField?: IDirectorTextField;
  identityField?: IDirectorTextField;
  identityFileField?: IDirectorFileDragDrop;
  isStory?: boolean;
  addBtn?: boolean;
  disabledWhen?: boolean;
  _actionData?: any;
  _store?: any;
  tokenPrefix?: 'Bearer' | 'Token';
  removeDirectorUrl?: string;
  removeDirectorRedirectUrl?: string;
};

const Container = styled.div`
  position: relative;
  width: 50%;
  min-width: 712px;
  display: grid;
  grid-auto-flow: row;
  gap: ${(props) => props.theme.SpacingXxl};
  margin-bottom: 4rem;
`;

const FormContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 3rem;
  column-gap: ${(props) => props.theme.SpacingXxl};
`;

const Button = styled.button`
  all: unset;
  cursor: pointer;
  box-shadow: 0px 0px 3px ${(props) => props.theme.ColorsTypographyPrimary};
  border-radius: 100%;
  background-color: ${(props) => props.theme.ColorsTabsToolbarPrimary};
  width: 100%;
  height: 34px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  gap: 8px;
  padding: 0;
  width: 3rem;
  height: 3rem;
  text-align: center;
  font-size: 12px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: Inter;
  &:hover {
    background-color: ${(props) => props?.theme.ColorsTabsToolbarHover};
  }
  &:disabled {
    border: 1px solid ${(props) => props?.theme.ColorsStrokesGrey};
    background-color: transparent;
  }
  &:active {
    background-color: ${(props) => props?.theme.ColorsTabsToolbarActivated};
    box-shadow: 0px 0px 5.2px 0px #fbfeff;
  }
`;

const FieldsWrapper = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: ${(props) => props.theme.SpacingLg};
  width: 100%;
`;

// const PlainTextField = styled(PlainTextInput)``;

const DragAndDropField = styled(DragAndDrop)`
  grid-column: span 2;
  width: 100%;
`;

const ButtonAdd = styled(Button)`
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) translateY(3rem);
`;

const ButtonRemove = styled(Button)`
  margin: auto 0;
`;

const NewDirectorWrapper = styled.div``;

interface IFieldAccess {
  key?: string;
  path?: { edit: string; view: string; isSpecial: string };
}

interface IDirectorField {
  name: string;
  placeholder?: string;
  disabledWhen?: string;
  fieldAccess?: IFieldAccess;
  label?: string;
}

interface IDirectorTextField extends IDirectorField {
  state?: 'display-only' | 'default';
  icon?: {
    type: IconTypes;
    position: 'left' | 'right';
  };
  fieldState?: 'display-only' | 'default';
}

interface IDirectorFileDragDrop extends IDirectorField {
  iconRight?: IconTypes;
  toggleButton?: boolean;
  filesBaseUrlName?: string;
  filesEndpoint?: string;
  purpose?: string;
}

/**
 * Component for rendering a form section for a director with fields for name, identity, and file upload.
 *
 * @param dir - Object containing director information like id, name, identity, and files.
 * @param index - Index of the director in the list.
 * @param remove - Function to remove a director from the list.
 * @param namePlaceholder - Placeholder text for the name field.
 * @param identityPlaceholder - Placeholder text for the identity field.
 * @param nameFieldName - Field name for the director's name.
 * @param identityFieldName - Field name for the director's identity.
 * @param fileFieldName - Field name for the director's file upload.
 * @param key - Unique key for the director form.
 * @param fileEmptyText - Text to display when no file is uploaded.
 * @param numberOfDirectors - Total number of directors in the list.
 */

const DirectorForm = ({
  numberOfDirectors,
  dir,
  index,
  remove,
  namePlaceholder = '',
  identityFieldName = 'director_identity',
  identityPlaceholder = '',
  nameFieldName = 'director_name',
  fileFieldName = 'director_file',
  key,
  fileEmptyText = '',
  nameField = {
    name: 'director_name',
    placeholder: '',
    label: 'Name',
  },
  identityField = {
    name: 'director_identity',
    placeholder: '',
    label: 'Identity',
  },
  identityFileField = {
    name: 'director_file',
    placeholder: '',
    label: 'Copy of ID or passport document',
    iconRight: 'alarm-clock',
    toggleButton: true,
  },
  fieldAccess,
  disabledWhen,
}: {
  dir?: {
    id: string;
    name: string;
    identity: string;
    files: any[];
    nameFieldAccess?: IFieldAccess;
    identityFieldAccess?: IFieldAccess;
    identityDocumentFieldAccess?: IFieldAccess;
  };
  index?: number;
  remove?: (dir?: {
    id: string;
    name: string;
    identity: string;
    files: any[];
  }) => void;
  namePlaceholder?: string;
  identityPlaceholder?: string;
  nameFieldName?: string;
  identityFieldName?: string;
  fileFieldName?: string;
  key?: string;
  fileEmptyText?: string;
  nameField?: IDirectorTextField;
  identityField?: IDirectorTextField;
  identityFileField?: IDirectorFileDragDrop;
  numberOfDirectors: number;
  fieldAccess?: any;
  disabledWhen?: boolean;
}) => {
  const { setValue, getValues } = useFormContext();
  const {
    canView: canViewDirectorName,
    canEdit: canEditDirectorName,
    isSpecial: isSpecialDirectorName,
  } = useFieldAccessPermissions({
    fieldAccess,
    fieldAccessKey:
      nameField.fieldAccess?.key ?? nameFieldName ?? nameField.name,
    fieldAccessPath: nameField?.fieldAccess?.path ?? {
      edit: 'companydirector',
      view: 'companydirector',
      special: 'companydirector',
    },
  });
  const {
    canView: canViewDirectorIdentity,
    canEdit: canEditDirectorIdentity,
    isSpecial: isSpecialDirectorIdentity,
  } = useFieldAccessPermissions({
    fieldAccess,
    fieldAccessKey:
      identityField.fieldAccess?.key ?? identityFieldName ?? identityField.name,
    fieldAccessPath: identityField?.fieldAccess?.path ?? {
      edit: 'companydirector',
      view: 'companydirector',
      special: 'companydirector',
    },
  });
  const {
    canView: canViewDirectorIdentityFile,
    canEdit: canEditDirectorIdentityFile,
    isSpecial: isSpecialDirectorIdentityFile,
  } = useFieldAccessPermissions({
    fieldAccess,
    fieldAccessKey:
      identityField.fieldAccess?.key ?? identityFieldName ?? identityField.name,
    fieldAccessPath: identityField?.fieldAccess?.path ?? {
      edit: 'directordocument',
      view: 'directordocument',
      special: 'directordocument',
    },
  });
  return (
    <FormContainer key={dir?.id ?? key}>
      <div>
        <FieldsWrapper>
          <PlainTextInput
            name={nameFieldName ?? nameField.name}
            label={nameField?.label}
            value={dir?.name ?? ''}
            placeholder={dir?.name ?? namePlaceholder ?? nameField?.placeholder}
            icon={
              isSpecialDirectorName ? (
                <Icon type="alarm-clock" size={24} />
              ) : (
                identityField?.icon?.type && (
                  <Icon type={identityField?.icon?.type} size={24} />
                )
              )
            }
            position={
              isSpecialDirectorName
                ? 'right'
                : identityField?.icon?.position
                ? identityField?.icon?.position
                : 'none'
            }
            state={
              !canEditDirectorName
                ? 'display-only'
                : nameField.state
                ? nameField.state
                : 'default'
            }
            disabled={
              disabledWhen || !canEditDirectorName || !canViewDirectorName
            }
          />
          <PlainTextInput
            name={identityFieldName ?? identityField.name}
            label={nameField?.label}
            value={dir?.identity ?? ''}
            placeholder={
              dir?.identity ?? identityPlaceholder ?? identityField?.placeholder
            }
            icon={
              isSpecialDirectorIdentity ? (
                <Icon type="alarm-clock" size={24} />
              ) : (
                identityField?.icon?.type && (
                  <Icon type={identityField?.icon?.type} size={24} />
                )
              )
            }
            position={
              isSpecialDirectorIdentity
                ? 'right'
                : identityField?.icon?.position
                ? identityField?.icon?.position
                : 'none'
            }
            state={
              !canEditDirectorIdentity
                ? 'display-only'
                : nameField.state
                ? nameField.state
                : 'default'
            }
            disabled={
              disabledWhen ||
              !canEditDirectorIdentity ||
              !canViewDirectorIdentity
            }
          />
          {/* <div>{JSON.stringify(dir?.files)}</div> */}
          <DragAndDropField
            name={fileFieldName ?? identityFileField.name}
            label={identityFileField?.label}
            toggleButton={identityFileField?.toggleButton}
            iconRight={
              canEditDirectorIdentity && isSpecialDirectorIdentityFile
                ? 'alarm-clock'
                : identityFileField?.iconRight
                ? identityFileField?.iconRight
                : undefined
            }
            onDrop={(acceptedFiles, fileRejections, evt) => {
              setValue('director_file', acceptedFiles[0] as File);
            }}
            filename={
              dir?.files[dir?.files.length - 1]?.filename ??
              getValues(fileFieldName)?.name ??
              (identityFileField?.placeholder || 'No file uploaded')
            }
            disabled={
              disabledWhen ||
              !canEditDirectorIdentityFile ||
              !canViewDirectorIdentityFile
            }
          />
        </FieldsWrapper>
      </div>
      {numberOfDirectors > 1 ? (
        <ButtonRemove onClick={() => remove && remove(dir)}>
          <Icon type={'trash-01'} size={24} />
        </ButtonRemove>
      ) : null}
    </FormContainer>
  );
};

export const DirectorsList = ({
  directors = [],
  filesUrl = '/api/v1/file_actions/director_get_files',
  isStory,
  addBtn = true,
  _submit,
  _actionData,
  tokenPrefix = 'Bearer',
  _store,
  nameField = {
    name: 'director_name',
    placeholder: '',
    label: 'Name',
  },
  identityField = {
    name: 'director_identity',
    placeholder: '',
    label: 'Identity',
  },
  identityFileField = {
    name: 'director_file',
    placeholder: '',
    label: 'Copy of ID or passport document',
    iconRight: 'alarm-clock',
    toggleButton: true,
    purpose: 'Identity',
    filesBaseUrlName: 'VITE_SP_SERVER',
    filesEndpoint: '/api/v1/file_actions/director_get_files',
  },
  disabledWhen,
  removeDirectorUrl = '/api/v1/spaas_actions/remove_director',
  removeDirectorRedirectUrl = '/field-ops/tasks/edit/directors',
}: Props) => {
  const [showAddBtn, setShowAddBtn] = useState(addBtn);
  const [directorsData, setDirectorsData] = useState<any[]>([]);
  const { keycloak } = useSpaKeycloak();
  const { handleServerError } = useErrorModal();
  const { addError } = useErrorStore();
  const { fieldAccess }: any = useAppStore((state) => state);

  const hasRun = useRef(false);

  const fetchDirectorFiles = async (dirs: any[]) => {
    try {
      return await Promise.all(
        dirs.map(async (dir) => {
          const response = await fetch(
            `${
              import.meta.env[
                identityFileField?.filesBaseUrlName || 'VITE_SP_SERVER'
              ]
            }${normalizeUrl(filesUrl)}`,
            {
              method: 'POST',
              body: JSON.stringify({
                director_id: dir.id,
                with_thumbnails: false,
                purpose: identityFileField?.purpose || 'Identity',
              }),
              headers: {
                'Content-Type': 'application/json',
                Authorization: `${tokenPrefix} ${keycloak?.token}`,
              },
            }
          );

          if (!response.ok) {
            throw new Error(
              `Failed to fetch files for director ${dir.id}: ${response.statusText}`
            );
          }

          const filesResponse = await response.json();
          const files = filesResponse.payload;
          return { ...dir, files };
        })
      );
    } catch (error) {
      addError({
        key: `directors-list-fetch-director-files-${Date.now()}`,
        message:
          error instanceof Error
            ? `${error.message}: Please try refreshing the page`
            : 'Unknown error: Try refreshing the website',
        source: 'server',
        stackTrace: error instanceof Error ? error.stack : undefined,
      });
      handleServerError(
        error instanceof Error
          ? `${error.message}: Please try refreshing the page`
          : 'Failed to fetch director files'
      );
      return dirs.map((dir) => ({ ...dir, files: [] })); // Return directors with empty files array
    }
  };

  useEffect(() => {
    if (directors && !!directors.length && keycloak) {
      fetchDirectorFiles(directors).then((dirs: any[]) => {
        setDirectorsData(dirs);
        setShowAddBtn(true);
      });
    }
  }, [directors, keycloak]);

  useEffect(() => {
    if (_actionData?.error) {
      handleServerError(
        typeof _actionData.error === 'string'
          ? _actionData.error
          : 'Failed to remove director'
      );
    }
  }, [_actionData, handleServerError]);

  const remove = (dir: any) => {
    try {
      _submit(
        {
          data: JSON.stringify({ director_id: dir.id }),
          url: `{${identityFileField.filesBaseUrlName}}${normalizeUrl(
            removeDirectorUrl
          )}`,
          redirect: normalizeUrl(removeDirectorRedirectUrl),
          headers: {},
        },
        { method: 'POST' }
      );
      setShowAddBtn(true);
    } catch (error) {
      // Handle immediate errors (like network issues)
      addError({
        key: `directors-list-remove-director-${Date.now()}`,
        message:
          error instanceof Error
            ? `${error.message}: Please try refreshing the page`
            : 'Unknown error: Try refreshing the website',
        source: 'server',
        stackTrace: error instanceof Error ? error.stack : undefined,
      });
      handleServerError('Failed to remove director');
    }
  };

  return (
    <Container key={'directors-list'}>
      {(directorsData || [])?.map((dir, index) => (
        <div key={dir.id}>
          <DirectorForm
            numberOfDirectors={directorsData.length} // There will be no delete button if length is 1
            dir={dir}
            index={index}
            remove={remove}
            nameFieldName={`${nameField.name}-${index}`}
            identityFieldName={`${identityField.name}-${index}`}
            fileFieldName={`${identityFileField.name}-${index}`}
            fieldAccess={fieldAccess}
            nameField={nameField}
            identityField={identityField}
            identityFileField={identityFileField}
          />
        </div>
      ))}
      {!showAddBtn && (
        <DirectorForm
          numberOfDirectors={10} //  Just any number bigger than 1 so delete button always shows
          fileEmptyText="No file uploaded"
          remove={() => {
            setShowAddBtn(true);
          }}
        />
      )}
      <div>
        {showAddBtn && !disabledWhen && (
          <ButtonAdd onClick={() => setShowAddBtn(false)}>
            <Icon type={'plus'} size={24} />
          </ButtonAdd>
        )}
      </div>
    </Container>
  );
};
