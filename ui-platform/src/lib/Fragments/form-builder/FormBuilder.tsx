// external module imports
import Keycloak from 'keycloak-js';
import React, { lazy, Suspense, useCallback, useEffect, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { useLocation, useNavigate } from 'react-router-dom';
// internal module imports
import { ActionConfig, useAppStore, useErrorStore } from '../../Engine';
import { useErrorModal } from '../../Engine/hooks';
import { TemplateLiteralLogger } from '../../Utilities';
import { GenerateControl } from './FormComponents';
import { FormConfig } from './types/form.config';

// instantiate logger for debugging generate control component
const log = TemplateLiteralLogger.createLog(
  {
    prefix: '🧐[Form Builder debug]:',
    enabled: false,
    options: { style: { backgroundColor: '#efefef', color: '#017812' } },
  },
  'log'
);

type FormBuilderProps = {
  /** Form configuration object defining structure and behavior */
  config: FormConfig;
  defaultValues: { [key: string]: any };
  hiddenControlConditions?: { name: string; hiddenWhen: string }[];
  onSubmit?: (data: any) => Promise<void>;
  isStory?: boolean;
  /** Name for grouped forms (used in nested scenarios) */
  formGroupName?: string;
  /** Index for array-based form groups */
  index?: number;
  _fetcher?: any;
  _actionData?: any;
  _callClientAction?: (config: ActionConfig) => void;
  fieldAccessPermissions?: {
    view: { [key: string]: string[] };
    edit: { [key: string]: string[] };
    special: { [key: string]: string[] };
  };
  _keycloak?: Keycloak;
};

/**
 * FormBuilder is a component that accepts a FormConfig object and renders a form based on
 * the configuration. It also handles form submission and provides a way to set default values.
 * Additionally, it provides some basic error handling and allows for action hooks at the form
 * level.
 *
 * @param {FormBuilderProps} props - The props object should contain the FormConfig object, any
 * default values, and an optional onSubmit function.
 *
 * @returns {JSX.Element} A rendered form based on the configuration.
 */
export const FormBuilder: React.FC<FormBuilderProps> = (props) => {
  const {
    isStory,
    config,
    defaultValues,
    formGroupName,
    index,
    _fetcher,
    _actionData,
    _callClientAction,
    fieldAccessPermissions,
    _keycloak,
  } = props;

  const { formState, control, setValue, getValues, watch, getFieldState } =
    useFormContext();

  const { errors } = formState;
  const { addError } = useErrorStore();

  // Only watch necessary fields instead of entire form
  const watchedFields = useMemo(
    () => config.controls.map((ctrl) => ctrl.name),
    [config.controls]
  );
  const $form = watch(watchedFields);
  const $formState = formState;

  // Memoize client action callback
  const store: any = useAppStore((state) => state);
  const fieldAccess = fieldAccessPermissions || store.fieldAccess;
  log`Field access permissions loaded - ${!!fieldAccess} ${fieldAccess}`;
  log`Default values check - ${'Line 83'} ${defaultValues}`;

  // const [onFocusTrgger, setOnFocusTrigger] = useState(false);

  // useEffect(() => {
  //   if (!currentModalConfig?.display) {
  //     setOnFocusTrigger(true);
  //   } else {
  //     setOnFocusTrigger(true);
  //   }
  // }, [currentModalConfig?.display === false]);

  // const translateActionConfigToFunction = useCallback(async (ctrl: BaseControlConfig | undefined, focusSetter: Function) => {
  //   if (ctrl?.onFocus) {
  //     for (const cf of (ctrl?.onFocus || [])) {
  //       await callClientAction(cf);
  //       console.log('also focusing here')
  //     }

  //   } else {
  //   }
  // }, []);

  // Consolidated error handling
  const { handleServerError } = useErrorModal();
  const handleError = useCallback(
    (error: string | undefined) => {
      if (error) {
        handleServerError(error);
      }
    },
    [handleServerError]
  );

  useEffect(() => {
    if (_actionData?.errors?.length) {
      addError({
        key: `directors-list-fetch-director-files-${Date.now()}`,
        message:
          _actionData.errors[0] instanceof Error
            ? _actionData.errors[0].message
            : 'Unknown error: Try refreshing the website',
        source: 'server',
        stackTrace:
          _actionData.errors[0] instanceof Error
            ? _actionData.errors[0].stack
            : undefined,
      });
      handleError(
        _actionData.errors[0] ||
          'An error occurred while processing your request'
      );
    }
  }, [_actionData, handleError]);

  // Memoized default values handling
  const setDefaultValues = useCallback(() => {
    if (defaultValues && !$formState.isDirty) {
      Object.entries(defaultValues).forEach(([key, value]) => {
        const existingValue = getValues(key);
        log`Debug form builder ${'Line 143'} ${existingValue}`;
        if (!existingValue && key && value !== undefined) {
          setValue(key, value, { shouldDirty: false, shouldTouch: false });
        }
      });
    }
  }, [defaultValues, $formState.isDirty, getValues, setValue]);

  useEffect(() => {
    setDefaultValues();
  }, [setDefaultValues]);

  useEffect(() => {
    log`Deug form builder ${'Line 155'} ${{ $form, store, defaultValues }}`;
  }, [$form, store]);

  // Memoize control generation props
  const controlProps = useMemo(
    () => ({
      formBuilderProps: props,
      control,
      errors,
      $form,
      $formState,
      $store: store,
      _callClientAction,
      fetcher: _fetcher,
      setValue,
      getValues,
      defaultValues,
      getFieldState,
      fieldAccess,
      isStory,
      _keycloak,
    }),
    [
      props,
      control,
      errors,
      $form,
      $formState,
      store,
      _callClientAction,
      _fetcher,
      setValue,
      getValues,
      defaultValues,
      getFieldState,
      fieldAccess,
      isStory,
      _keycloak,
    ]
  );

  return (
    <div style={config.style}>
      {config.controls.map((ctrl, idx) => (
        <Suspense fallback={<div>Loading...</div>} key={`${ctrl.name}.${idx}`}>
          <GenerateControl
            {...controlProps}
            ctrl={ctrl}
            name={
              formGroupName
                ? `${formGroupName}.${index}.${ctrl.name}`
                : ctrl.name
            }
          />
        </Suspense>
      ))}
    </div>
  );
};

export default FormBuilder;
