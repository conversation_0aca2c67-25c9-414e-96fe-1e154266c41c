import React, { useState } from 'react';
import styled from 'styled-components';
import { TextButton } from '../../Components';
import { FormBuilder } from '../form-builder/FormBuilder';
import { Divider } from "../../Components/Dividers/Divider";

const Container = styled.div`
  display: grid;
  grid-auto-flow: column;
  align-items: flex-start;
  gap: 20px;
  max-width: 100%;
  overflow-x: auto;
  margin: 10px;
`;

const Label = styled.span`
  margin: 2rem;
  font-size: bold;
`;

const Section = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 150px;
`;

const RatingButtons = styled.div`
  display: flex;
  justify-content: center;
  gap: 5px;
`;

const RatingButton = styled.button<{ selected: boolean }>`
  width: 30px;
  height: 30px;
  border-radius: 10%;
  background-color: #121212;
  color: ${({ selected }) => (selected ? '#007bff' : '#f8f8f8')};
  border: 1px solid ${({ selected }) => (selected ? '#007bff' : '#f8f8f8')};
  cursor: pointer;
`;

const ButtonRow = styled.div`
  display: flex;
  gap: 5px;
`;

const Buttons = styled(TextButton)`
  width: 40px;
  height: 40px;
  border-radius: 6px;
`;

export const CustomerRating = () => {
    const [selectedRating, setSelectedRating] = useState<number | null>(null);

    return (
        <>
            <Container>
                <Section>
                    <Label>WHO</Label>
                    <span>BET Centre Rating</span>
                </Section>

                <Section>
                    <Label>RATING</Label>
                    <RatingButtons>
                        {Array.from({ length: 10 }, (_, i) => i + 1).map((num) => (
                            <RatingButton
                                key={num}
                                selected={selectedRating === num}
                                onClick={() => setSelectedRating(num)}
                            >
                                {num}
                            </RatingButton>
                        ))}
                    </RatingButtons>
                </Section>

                <Section>
                    <Label>WAS "WHAT MATTERS" MET</Label>
                    <ButtonRow>
                        <Buttons
                            btnValue="NO"
                            actiontype="alternative"
                            size="small"
                            onClick={() => console.log('no')}
                        />
                        <Buttons
                            btnValue="YES"
                            actiontype="proceed"
                            size="small"
                            onClick={() => console.log('yes')}
                        />
                    </ButtonRow>
                </Section>

                <Section>
                    <Label>REASON</Label>
                    <FormBuilder 
                        config={{ 
                            style: {
                                display: 'grid',
                                gridTemplateColumns: 'repeat(1, 1fr)',
                                rowGap: '1rem',
                                columnGap: '1rem',
                                width: '100px',
                            },
                            controls: [
                            {
                                type: 'textarea',
                                name: 'Reason',
                                placeholder: '',
                                css: { wrapper: { gridColumn: '1 / span 2', gridRow: '1' } },
                            },
                        ] }}
                        defaultValues={{}}
                    />
                </Section>
            </Container>
            <Divider 
                height="default"
                state="default"
                type="tabSmll"
                background="primary"
                size="fullWidth"
            />
        </>
    );
};
