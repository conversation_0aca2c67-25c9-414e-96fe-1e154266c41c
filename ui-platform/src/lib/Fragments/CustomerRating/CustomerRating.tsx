import React, { useState } from 'react';
import styled from 'styled-components';
import { TextButton } from '../../Components';
import { FormBuilder } from '../form-builder/FormBuilder';
import { Divider } from '../../Components/Dividers/Divider';

const Container = styled.div`
  display: grid;
  grid-template-columns: 1fr auto 1fr 1fr;
  align-items: flex-start;
  width: 100%;
  gap: 20px;
  margin: 10px;
  overflow-x: auto;
`;

const Label = styled.span`
  font-weight: bold;
  margin-bottom: 1rem;
  word-wrap: break-word;
`;

const Section = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 150px;
  gap: 10px;
`;

const Text = styled.div`
  white-space: normal;
  word-wrap: break-word;
  width: 100%;
  text-align: left;
`;

const RatingButtons = styled.div`
  display: flex;
  flex-wrap: nowrap;
  gap: 5px;
`;

const RatingButton = styled.button<{ selected: boolean }>`
  width: 30px;
  height: 30px;
  border-radius: 10%;
  background-color: #121212;
  color: ${({ selected }) => (selected ? '#007bff' : '#f8f8f8')};
  border: 1px solid ${({ selected }) => (selected ? '#007bff' : '#f8f8f8')};
  cursor: pointer;
`;

const ButtonRow = styled.div`
  display: flex;
  gap: 5px;
`;

const Buttons = styled(TextButton)`
  width: 40px;
  height: 40px;
  border-radius: 6px;
`;

type CustomerRatingProps = {
  data: string[];
};

export const CustomerRating = ({ data }: CustomerRatingProps) => {
  const [ratings, setRatings] = useState<Record<string, number | null>>({});

  const handleRatingChange = (item: string, value: number) => {
    setRatings((prev) => ({ ...prev, [item]: value }));
  };

  return (
    <>
      {data.map((item, index) => (
        <Container key={index}>
          <Section>
            {index === 0 && <Label>WHO</Label>}
            <Text>{item}</Text>
          </Section>

          <Section>
            {index === 0 && <Label>RATING</Label>}
            <RatingButtons>
              {Array.from({ length: 10 }, (_, i) => i + 1).map((num) => (
                <RatingButton
                  key={num}
                  selected={ratings[item] === num}
                  onClick={() => handleRatingChange(item, num)}
                >
                  {num}
                </RatingButton>
              ))}
            </RatingButtons>
          </Section>

          <Section>
            {index === 0 && <Label>WAS "WHAT MATTERS" MET</Label>}
            <ButtonRow>
              <Buttons
                btnValue="NO"
                actiontype="alternative"
                size="small"
                onClick={() => console.log(`${item} - no`)}
              />
              <Buttons
                btnValue="YES"
                actiontype="proceed"
                size="small"
                onClick={() => console.log(`${item} - yes`)}
              />
            </ButtonRow>
          </Section>

          <Section>
            {index === 0 && <Label>REASON</Label>}
            <FormBuilder
              config={{
                style: {
                  display: 'grid',
                  gridTemplateColumns: 'repeat(1, 1fr)',
                  width: '100px',
                },
                controls: [
                  {
                    type: 'textarea',
                    name: `Reason-${item}`,
                    placeholder: '',
                    //css: { wrapper: { gridColumn: '1 / span 2', gridRow: '1' } },
                  },
                ] }}
              defaultValues={{}}
            />
          </Section>
        </Container>
      ))}
      <Divider
        height="default"
        state="default"
        type="tabSmll"
        background="primary"
        size="fullWidth"
      />
    </>
  );
};
