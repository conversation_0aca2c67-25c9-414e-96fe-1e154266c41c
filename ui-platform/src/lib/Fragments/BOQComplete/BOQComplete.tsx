import React, { useState, useEffect } from 'react';
import styled, { CSSProperties } from 'styled-components';
import LineItemsTable, {
  LineItem,
  LineItemsTableProps,
} from '../../Components/BOQ/LineItemTable/LineItemTable';
import InvoiceSummaryNotes, {
  InvoiceSummaryNotesProps,
} from '../../Components/BOQ/InvoiceSummaryNotes/InvoiceSummaryNotes';
import InvoiceSummary, {
  InvoiceSummaryProps,
} from '../../Components/BOQ/InvoiceSummary/InvoiceSummary';
import DateComponent from '../../Components/BOQ/DateComponent/DateComponent';
import InvoiceInput from '../../Components/BOQ/InputBox/InputBox';
import { useAppStore } from '../../Engine';
import { DateRange } from 'react-day-picker';
import BOQTravelCostAlertModal from '../BOQModule/BOQTravelCostAlertModal';

export interface PreselectedOptionalItem {
  id: number;
  is_interim: boolean;
  category: string | null;
  token: string;
  created: string;
  modified: string;
  state: number;
  job: number;
  line_items: {
    id: number;
    item_id: number;
    item: {
      id: number;
      name: string;
      price: number;
      measurement: string;
      item_type: number;
      is_custom_items: boolean;
      is_after_hours: boolean;
      skill_categories: {
        id: number;
        name: string;
      }[];
      year: number;
    };
    job: number;
    unit_price: string;
    invoice: number | null;
    total: string;
    quantity: number;
    job_invoice: number;
  }[];
}

export interface PreselectedCompulsoryItem {
  id: number;
  is_interim: boolean;
  category: string | null;
  token: string;
  created: string;
  modified: string;
  state: number;
  job: number;
  line_items: {
    id: number;
    item_id: number;
    item: {
      id: number;
      name: string;
      price: number;
      measurement: string;
      item_type: number;
      is_custom_items: boolean;
      is_after_hours: boolean;
      skill_categories: {
        id: number;
        name: string;
      }[];
      year: number;
    };
    job: number;
    unit_price: string;
    invoice: number | null;
    total: string;
    quantity: number;
    job_invoice: number;
  }[];
}
// Type definition for component props
type Props = {
  lineItemsTableProps: LineItemsTableProps;
  invoiceSummaryNotesProps: InvoiceSummaryNotesProps;
  invoiceSummaryProps: InvoiceSummaryProps;
  layout?: CSSProperties;
  travelDistance?: number; // Optional prop for travel distance
  createCustomItemButton?: boolean;
  preselectedOptionalItem?: PreselectedOptionalItem;
  preselectedCompulsoryItem?:PreselectedCompulsoryItem;
  excessAmount?: number;
};

const Wrapper = styled.div`
  display: grid;
  justify-content: center;
  padding: 40px;
`;

const InputWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 20px;
`;

const BottomContainer = styled.div`
  display: flex;
  justify-content: space-between;
`;

export function BOQComplete({
  lineItemsTableProps,
  invoiceSummaryNotesProps,
  invoiceSummaryProps,
  layout,
  travelDistance,
  createCustomItemButton,
  excessAmount,
  preselectedOptionalItem,
  preselectedCompulsoryItem,
}: Props): JSX.Element {
  const [items, setItems] = useState<LineItem[]>(
    lineItemsTableProps?.compulsoryItems || []
  );
  const [subTotal, setSubTotal] = useState(0);
  const [selectedDate, setSelectedDate] = useState<Date | DateRange | null>(
    null
  );
  const [showTravelModal, setShowTravelModal] = useState(false);

  useEffect(() => {
    if (travelDistance && travelDistance > 0) {
      setShowTravelModal(true);
    }
  }, [travelDistance]);

  const setBOQState = useAppStore((state: any) => state?.setState);

  // useEffect(() => {
  //   setBOQState({ selectedBOQItems: lineItemsTableProps?.compulsoryItems });
  // }, [setBOQState, lineItemsTableProps?.compulsoryItems]);

  // Convert API response into a LineItem object and add to the state

  useEffect(() => {
    if (preselectedOptionalItem?.line_items?.length) {
      const mappedOptionalItems = preselectedOptionalItem.line_items.map(
        (lineItem) => ({
          id: lineItem.item.id,
          description: lineItem.item.name,
          unitPrice: lineItem.item.price,
          quantity: lineItem.quantity,
          optionalItem: true,
        })
      );

      setItems((prevItems) => {
        const existingIds = new Set(prevItems.map((item) => item.id));
        const newItems = mappedOptionalItems.filter(
          (item) => !existingIds.has(item.id)
        );
        const updatedItems = [...prevItems, ...newItems];
        setBOQState({ selectedBOQItems: updatedItems });
        return updatedItems;
      });
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [preselectedOptionalItem]);

  useEffect(() => {
    if (preselectedCompulsoryItem?.line_items?.length) {
      const mappedCompulsoryItems = preselectedCompulsoryItem.line_items.map(
        (lineItem) => ({
          id: lineItem.item.id,
          description: lineItem.item.name,
          unitPrice: lineItem.item.price,
          quantity: lineItem.quantity,
          optionalItem: false,
        })
      );

      setItems((prevItems) => {
        const existingIds = new Set(prevItems.map((item) => item.id));
        const newItems = mappedCompulsoryItems.filter(
          (item) => !existingIds.has(item.id)
        );
        const updatedItems = [...prevItems, ...newItems];
        setBOQState({ selectedBOQItems: updatedItems });
        return updatedItems;
      });
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [preselectedCompulsoryItem]);


  // Calculate subtotal based on the current items
  useEffect(() => {
    // const calculateSubTotal = [...items].reduce(
    //   (acc, item) => acc + (item.unitPrice || 0) * (item.quantity || 0),
    //   0
    // );
    // setItems((prevItems) => [boqLineItems, ...prevItems]);

    let calculateSubTotal = 0;

    items.forEach((item) => {
      calculateSubTotal += (item.unitPrice || 0) * (item.quantity || 0);
    });

    setSubTotal(calculateSubTotal);
    setBOQState({ boqSubTotal: calculateSubTotal });
  }, [items, setBOQState]);

  const handleItemChange = (updatedItems: LineItem[]) => {
    console.log('the item is changed', updatedItems);
    setItems(updatedItems);
    setBOQState({ selectedBOQItems: updatedItems });
  };

  const handleInvoiceNumberChange = (value: string) => {
    setBOQState({ boqInvoiceNumber: value });
  };

  const handleDateChange = (date: Date | DateRange) => {
    setSelectedDate(date);
    setBOQState({ boqDate: date });
  };

  const handleInvoiceNotesChange = (value: string) => {
    setBOQState({ boqInvoiceNotes: value });
  };

  const handleAddTravelCostYes = (boqDetails: {
    description: string;
    quantity: number;
    unitPrice: number;
    optionalItem: boolean;
  }) => {
    const newItem: LineItem = { id: 625, ...boqDetails };
    // setItems((prevItems) => [newItem, ...prevItems]);
    setBOQState({ selectedBOQItems: [newItem, ...items] });

    // Add the new item to the compulsoryItems for display
    setItems((prevItems) => [newItem, ...prevItems]);

    setShowTravelModal(false);
  };

  return (
    <Wrapper style={layout}>
      {showTravelModal && (
        <BOQTravelCostAlertModal
          travelDistance={travelDistance || 0}
          closeModal={() => setShowTravelModal(false)}
          submitBOQ={handleAddTravelCostYes}
        />
      )}
      <InputWrapper>
        <DateComponent onSelect={handleDateChange} />
        <InvoiceInput onInputChange={handleInvoiceNumberChange} />
      </InputWrapper>
      <LineItemsTable
        columnNames={lineItemsTableProps.columnNames}
        // compulsoryItems={lineItemsTableProps.compulsoryItems}
        optionalItems={lineItemsTableProps.optionalItems}
        items={items}
        onItemsChange={handleItemChange}
        createCustomItemButton={createCustomItemButton}
      />
      <BottomContainer>
        <InvoiceSummaryNotes
          {...invoiceSummaryNotesProps}
          onNotesChange={handleInvoiceNotesChange}
        />
        <InvoiceSummary
          {...invoiceSummaryProps}
          subTotal={subTotal}
          excessAmount={excessAmount}
        />
      </BottomContainer>
    </Wrapper>
  );
}
