import React, { useEffect } from "react";
import styled from "styled-components";
import { Divider } from "../../Components/Dividers/Divider";
import { useAppStore } from "../../Engine";
import { useNotesStore } from "../../Engine/hooks/useNotesStore";
import { useNotes } from "../../Hooks/useNotes";
import { TextButton } from "../../Components/Buttons/TextButton/TextButton";



export type AuthorRole = {
  id: number;
  description: string;
};

export type NotesDisplayProps = {
  id?: number;
  author_name: string;
  author_contact: string;
  author_company: string;
  author_roles: AuthorRole[];
  timestamp: string;
  message: string;
};

const Container = styled.div`
  width: 100%;
  max-width: 300px;
  height: auto;
  display: grid;
  justify-content: center;
  justify-items: start;
  align-items: center;
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  font-size: ${(props) => props.theme.FontSize2}px;
  border-radius: 5px;
  margin: 10px;
  gap: 8px;
`;

const AuthorName = styled.div`
  color: ${(props) => props.theme.ColorsUtilityColorWarning};
  font-size: ${(props) => props.theme.FontSize3}px;
`;

const Line = styled.div`
  width: 100%;
  height: 0.1px;
  background-color: ${(props) => props.theme.ColorsTypographyDisabled};
`;

// export const NotesDisplay = ({ notes }: { notes: NotesDisplayProps[] }) => {
export const NotesDisplay = ({ activeJobId, token, getNotesUrl, addNoteUrl }: { activeJobId: number, token: string, getNotesUrl: string, addNoteUrl: string } ) => {
  const { notes, isLoading, error } = useNotesStore();
  const setNotes = useNotesStore(state => state.setNotes);
  // const { env } = useNotes();

  useEffect(() => {
    const fetchNotes = async () => {
      if(!activeJobId || !token || !getNotesUrl) {
        useNotesStore.setState({ isLoading: false });
        return;
      }
      
      useNotesStore.setState({ isLoading: true, error: null });

      try {
        const response = await fetch(getNotesUrl, {
          method: 'POST',
          headers: {
            'Authorization': `Token ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ job_id: activeJobId })
        });

        if(!response.ok) throw new Error('Failed to fetch notes');

        const data = await response.json();
        console.log('RAW RESPONSE', response);
        console.log('NOTES DATA', data);
        setNotes(data);
      
      } catch (error) {
        console.log('ERROR FETCHING NOTES', error);
        useNotesStore.setState({
          error: error instanceof Error ? error.message : 'Failed to fetch notes',
          isLoading: false
        })
      }
    }
    fetchNotes();
    // if(activeJobId && token && getNotesUrl) {
    //   fetch(`${getNotesUrl}`, {
    //     method: 'POST',
    //     headers: {
    //       'Authorization': `Token ${token}`,
    //     },
    //     body: JSON.stringify({ job_id: activeJobId }),
    //   }).then(response => response.json()).then(data => {
    //     console.log('NOTES DATA', data);
    //     useNotesStore.getState().setNotes(data);
    //   });
    // }
  }, [activeJobId, token, getNotesUrl, setNotes])

  // useEffect(() => {
  //   if (env) {
  //     console.log('fetching notes in NotesDisplay', env);
  //     useNotesStore.getState().fetchNotes(activeJobId, env?.VITE_API_BASE_URL);
  //   }
  // },[activeJobId, env])   

  // const addNote = (message: string) => {
  //   useNotesStore.getState().addNote(message, activeJobId,  token, addNoteUrl);
  // }
  if (isLoading) return <div>Loading notes...</div>;
  if (error) return <div>Error {error}</div>;
  if (!notes.length) return <div>No notes available. Add a note to get started.</div>;
  return (
    <>
      <Divider 
        height="default"
        state="default"
        type="tabSmll"
        background="primary"
        size="medium"
      />
      {notes.map((note, index) => (
        <Container key={index}>
          {/* <div>{note.id}</div> */}
          <AuthorName>{note.author_name}</AuthorName>
          <div>{note.author_contact}</div>
          <div>from {note.author_company}</div>
          <div>({note.author_roles.map((role) => role.description).join(", ")})</div>
          <div>{new Date(note.timestamp).toLocaleString()}</div>
          <Line />
          <div>{note.message}</div>
        </Container>	
      ))}
      {/* <TextButton btnValue="Add Note" size="small" onClick={addNote}></TextButton> */}
      <Divider 
        height="default"
        state="default"
        type="tabSmll"
        background="primary"
        size="medium"
      />
    </>
  );
};