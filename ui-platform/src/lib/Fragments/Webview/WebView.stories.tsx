import type { Meta, StoryObj } from '@storybook/react';
import { WebView } from './WebView';

const meta: Meta<typeof WebView> = {
  component: WebView,
  title: 'Fragments/WebView',
  argTypes: {
    url: { control: 'text' },
    title: { control: 'text' },
    width: { control: 'text' },
    height: { control: 'text' },
    allowFullScreen: { control: 'boolean' },
    sandbox: { control: 'text' },
    loading: { control: { type: 'select', options: ['eager', 'lazy'] } },
  },
};

export default meta;
type Story = StoryObj<typeof WebView>;

export const Default: Story = {
  args: {
    url: 'https://www.example.com',
    title: 'Example Website',
    height: '400px',
  },
};

export const GoogleMaps: Story = {
  args: {
    url: 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d12094.57348593182!2d-74.0065734241332!3d40.71274683866249!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c25a197c06b7cb%3A0x40a06c78f79e5de6!2sFinancial%20District%2C%20New%20York%2C%20NY%2C%20USA!5e0!3m2!1sen!2suk!4v1652117526302!5m2!1sen!2suk',
    title: 'Google Maps Example',
    height: '500px',
    allowFullScreen: true,
  },
};

export const YouTube: Story = {
  args: {
    url: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    title: 'YouTube Example',
    height: '400px',
    allowFullScreen: true,
  },
};

export const MissingURL: Story = {
  args: {
    title: 'Missing URL Example',
  },
};