import React from 'react';
import styled from 'styled-components';

interface WebViewProps {
  url: string;
  title?: string;
  width?: string | number;
  height?: string | number;
  allowFullScreen?: boolean;
  sandbox?: string;
  loading?: 'eager' | 'lazy';
  className?: string;
}

const StyledIframe = styled.iframe<{ width?: string | number; height?: string | number }>`
  width: ${props => props.width || '100%'};
  height: ${props => props.height || '500px'};
  border: none;
  border-radius: 4px;
  background-color: #f7f7f7;
`;

const WebViewContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

const WebViewTitle = styled.h3`
  font-size: 16px;
  margin-bottom: 8px;
  color: #e5e5e5;
`;

const ErrorMessage = styled.div`
  padding: 20px;
  background-color: #333;
  color: #ff5252;
  border: 1px solid #ff5252;
  border-radius: 4px;
  text-align: center;
`;

/**
 * A component that renders a URL in an iframe.
 * 
 * @param {WebViewProps} props - The props for the WebView component
 * @param {string} props.url - The URL to display in the WebView
 * @param {string} [props.title] - Optional title to display above the WebView
 * @param {string|number} [props.width] - Width of the WebView (default: 100%)
 * @param {string|number} [props.height] - Height of the WebView (default: 500px)
 * @param {boolean} [props.allowFullScreen] - Whether to allow fullscreen mode (default: false)
 * @param {string} [props.sandbox] - Optional sandbox restrictions for the iframe
 * @param {'eager'|'lazy'} [props.loading] - Loading strategy for the iframe (default: 'lazy')
 * @param {string} [props.className] - Additional CSS class for styling
 * @returns {JSX.Element} The WebView component
 */
export const WebView: React.FC<WebViewProps> = ({
  url,
  title,
  width,
  height,
  allowFullScreen = false,
  sandbox = 'allow-same-origin allow-scripts allow-popups allow-forms',
  loading = 'lazy',
  className,
}) => {
  if (!url) {
    return <ErrorMessage>Error: URL is required</ErrorMessage>;
  }

  return (
    <WebViewContainer className={className}>
      {title && <WebViewTitle>{title}</WebViewTitle>}
      <StyledIframe
        src={url}
        title={title || 'Web View'}
        width={width}
        height={height}
        allowFullScreen={allowFullScreen}
        sandbox={sandbox}
        loading={loading}
      />
    </WebViewContainer>
  );
};