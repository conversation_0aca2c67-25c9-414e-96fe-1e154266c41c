import { useEffect, useState } from "react";

interface NotesFragmentProps {
  activeJobId: number;
//   baseUrl: string;
}
interface EnvData {
    VITE_API_BASE_URL: string;
    [key: string]: any;
}

export const useNotes = () => {
    const [env, setEnv] = useState<EnvData | null>(null);
    useEffect(() => {
        try {
            const stringState = localStorage.getItem('4sure-ui-platform-store');
            const state = stringState ? JSON.parse(stringState) : null;
            const apiBaseUrl = state.state.env.VITE_API_BASE_URL
            console.log('T H E S T A T E ==>  URL', apiBaseUrl);
            const envData = {
                VITE_API_BASE_URL: apiBaseUrl
            }
            setEnv(envData);
        } catch (error) {
            setEnv(null);
        }

    },[])

    return { env };
}