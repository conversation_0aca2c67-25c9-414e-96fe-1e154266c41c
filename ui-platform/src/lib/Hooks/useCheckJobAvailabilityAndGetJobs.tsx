/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect, useState } from "react";
import { useAvailableJobsStore } from "../Engine/hooks/useAvailableJobsStore";
import { useLiveQuery } from 'dexie-react-hooks';
// import { db } from '../db/index'; 
import { db } from '../services/IndexedDbService';
import { JobResponse } from '../db/index';
import { StaffMember } from "../Auth";
import { jwtDecode } from 'jwt-decode'; 

interface CheckJobAvailabilityProps {
  baseUrl?: string;
  staffMember?: StaffMember;
}

const POLL_INTERVAL = 5 * 60 * 1000;
// export const useCheckJobAvailabilityAndGetJobs = (_keycloak: Keycloak) => {
export const useCheckJobAvailabilityAndGetJobs = ({ baseUrl, staffMember }: CheckJobAvailabilityProps) => {
  
  // useEffect(() => {
  //   console.log('useCheckJobAvailabilityAndGetJobs ui package update success tracker version 0.2.3')
  // }, []); // Empty dependency array means this runs once on mount
 

  // const [staffDetails, setStaffDetails] = useState<any>(null);
  const [localToken, setLocalToken] = useState<string | null>(() => {
    try{
      const token = localStorage.getItem('4sure.web.jwtToken');
      console.log('token', token)
      return token ? JSON.parse(token) : null;
    } catch (error) {
      return null
    } 
  });

  // Add decoded token state
const [decodedToken, setDecodedToken] = useState<any>(null);

useEffect(() => {
  if (localToken) {
    try {
      const decoded = jwtDecode(localToken);
      console.log('D E C O D E D TOKEN', decoded)
      setDecodedToken(decoded);
    } catch (error) {
      console.error('Error decoding token:', error);
    }
  }
}, [localToken]);


  const [isLoading, setIsLoading] = useState(true);  

  const jobResponses = useLiveQuery(() => db.job_responses.toArray());
  

  
  const { 
    serverJobCount,
    timestampCheck,
    jobsMeetingCriteria,
    availableJobs, 
    setJobAvailability,
    setAvailableJobs,
    setFilteredJobs,
    setJobResponses,
    setError,
    toggleModal,
   } = useAvailableJobsStore();

   //function to make the call to the server to register job interest
   const registerJobInterest = async (jobId: number | string, interest: number, spm: string | number) => {
    
    const numericJobId = typeof jobId === 'string' ? parseInt(jobId) : jobId;
    try {
        const response = await fetch(`${baseUrl}/v1/job_action/state_job_interest`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Token ${localToken}`
        },
        body: JSON.stringify({
          job_id: numericJobId,
          interested: interest,
          spm: decodedToken?.id
        })
      });
  
      if (!response.ok) {
        throw new Error('Failed to register job interest');
      }
  
      const apiResponse = await response.json();

      // Store response in indexedDB
      const newJobResponse: JobResponse = {
        job_id: numericJobId,
        interested: interest,
        spm: decodedToken?.id,
        time_of_response: new Date().toISOString()
      }
      await db.job_responses.add(newJobResponse); 

      // Update the job_responses array in Zustand
      const allJobResponses = await db.job_responses.toArray();
      setJobResponses(allJobResponses);
      
      // Update filteredJobs in Zustand
      if(availableJobs && availableJobs.length > 0) {
        const updatedFilteredJobs = availableJobs.filter((job: any) => {
         
          const hasResponse = allJobResponses.some(
            (response: JobResponse) => Number(response.job_id) === Number(job.id)
          )
          return !hasResponse;
        })

        console.log('updatedFilteredJobs', updatedFilteredJobs)
        console.log('Filtered jobs:', {
          total: availableJobs.length,
          filtered: updatedFilteredJobs.length,
          removed: availableJobs.length - updatedFilteredJobs.length
        });
        // Update filteredJobs in Zustand
        setFilteredJobs(updatedFilteredJobs);
      }

      

      return apiResponse;
    } catch (error) {
      console.error('Error registering job interest:', error);
      throw error;
    }
  };
  // Function to inspect localStorage
  const inspectLocalStorage = useCallback(() => {
    // console.log('inspectLocalStorage')
    const allKeys = Object.keys(localStorage);
    const storageContents: Record<string, any> = {};
    allKeys.forEach(key => {
      try {
        const value = localStorage.getItem(key);
        storageContents[key] = value ? JSON.parse(value) : value;
      } catch (e) {
        storageContents[key] = localStorage.getItem(key);
      }
    });
    return storageContents;
  }, [])

  useEffect(() => { 
    const localStore = inspectLocalStorage()
   
    setLocalToken(localStore['4sure.web.jwtToken'])
  }, [inspectLocalStorage])
  

  const checkAvailableAndGetJobs = useCallback(async () => {
   
     try {
      console.log('S T A F F M E M B E R', staffMember)
      //   console.log("checkAvailableAndGetJobs in try");
      // const response = await fetch(`${baseUrl}/v1/staff_action/get_staffmember/`, {
      //   method: "POST",
      //   body: JSON.stringify({ staff_id: staff_id }),
      //   headers: {
      //     "Content-Type": "application/json",
      //     Authorization: `Token ${localToken}`,
      //   },
      // });

      // if (!response.ok) throw new Error("Failed to fetch staff details");
      // const staffdata = await response.json();
      // setStaffDetails(staffdata?.payload);
      // setIsLoading(false);

       // Add check_available logic here
       const availabilityResponse = await fetch(`${baseUrl}/v1/job_action/check_available/`, {
        method: "POST",
        body: JSON.stringify({
          sp_id: staffMember?.sp?.id,
          skills: staffMember?.sp.skills,
          areas: staffMember?.sp.regions,
        }),
        headers: {
          "Content-Type": "application/json",
          Authorization: `Token ${localToken}`,
        },
      });
      if (!availabilityResponse.ok) throw new Error("Failed to check job availability");

      
      // const [jobsMeetingCriteria, newServerJobCount, newTimestampCheck] = (await availabilityResponse.text()).split("|").map(Number);

      const responseValues = (await availabilityResponse.text()).split("|")
      const [newJobsMeetingCriteria, newServerJobCount, newTimestampCheck] = [
        Number(responseValues[0]) || 0,
        Number(responseValues[1]) || 0,
        responseValues[2] ? Number(responseValues[2]) : undefined
      ]

      const forceUpdate = true;
      console.log('...potential logic ERRORS...')
      // Initialise forceUpdate based on whether there are more jobs on the server than the current count
      // let forceUpdate = newServerJobCount !== null && newServerJobCount > 0;

      // only proceed with addtional checks if we have jobs
      // if (forceUpdate) {
      //   // check if any of the other conditions are met
      //   forceUpdate = newJobsMeetingCriteria >= 1 && // must have jobs meeting criteria
      //   (
      //     newServerJobCount !== serverJobCount || // Jobs count has changed
      //     Number(timestampCheck) !== newTimestampCheck || // Timestamp has changed 
      //     Number(jobsMeetingCriteria) !== Number(newJobsMeetingCriteria) // Jobs meeting criteria has changed 
      //   )
      // }
      

      setJobAvailability({ 
        serverJobCount: newServerJobCount, 
        jobsMeetingCriteria: newJobsMeetingCriteria, 
        timestampCheck: newTimestampCheck?.toString() ?? 0?.toString()
      });  

      if (forceUpdate) {
        console.log('F O R C E U P D A T E staffMember', staffMember)
        try {
          const jobsResponse = await fetch(`${baseUrl}/v1/job_action/get_available_summary/`, {
            method: "POST",
            body: JSON.stringify({
              sp_id: staffMember?.sp?.id,
              skills: staffMember?.sp?.skills.toString(),
              areas: staffMember?.sp?.regions.toString(),
            }),
            headers: {
              "Content-Type": "application/json",
              Authorization: `Token ${localToken}`,
            },
          });

          if (!jobsResponse.ok) throw new Error("Failed to fetch available jobs");

          const jobsData = await jobsResponse.json();
          const availableJobs = jobsData.payload

          console.log('Modal state check:', {
            availableJobs: availableJobs,
            jobCount: availableJobs?.length,
            forceUpdate: forceUpdate
          });

          console.log('Debug jobsData:', {
            jobsData,
            payload: jobsData.payload,
            payloadType: typeof jobsData.payload,
            isArray: Array.isArray(jobsData.payload)
          });
          if (!Array.isArray(jobsData.payload)) {
            console.error('API returned non-array payload:', jobsData.payload);
            setAvailableJobs([]);  // Set empty array as fallback
            return;
          }

          if(availableJobs && availableJobs.length > 0) {
            console.log('Attempting to open modal and play sound');
            toggleModal(true);
            playPingSound();
          } else {
            console.log('No available jobs');
          }
         
          setAvailableJobs(availableJobs);
          // setIsLoading(false); // don't think this is needed
        } catch (error: any) {
          console.error('Error in main try block:', error);
          setError(error.message);
          setIsLoading(false);
        } 
      }
      setIsLoading(false);
      
      // return staffdata;
    } catch (error: any) {
      
      setError(error.message);
      setIsLoading(false);
      throw error;
    }
  }, [
    localToken, 
    setError, 
    serverJobCount, 
    timestampCheck, 
    setJobAvailability, 
    toggleModal, 
    setAvailableJobs,
    jobsMeetingCriteria,
    staffMember,
    baseUrl
  ])

  useEffect(() => {
  
    
    let retryCount = 0;
    const maxRetries = 3;
    let pollInterval: NodeJS.Timeout;
    
    const attemptFetch = () => {
      if (localToken && staffMember) {
        console.log('Staffmember with token', staffMember)
        checkAvailableAndGetJobs();
        // set up polling interval after initial fetch
        pollInterval = setInterval(() => {
          console.log('Polling for available jobs')
          checkAvailableAndGetJobs();
        }, POLL_INTERVAL);
       
      } else if (retryCount < maxRetries) {
        retryCount++;
        const timeoutId = setTimeout(attemptFetch, 3000);
        return () => clearTimeout(timeoutId);
      }
    };

    attemptFetch();
    // cleanup function to clear interval when component unmounts
    return () => {
      if (pollInterval) {
        clearInterval(pollInterval);
      }
    }
  }, [localToken, staffMember, checkAvailableAndGetJobs]);


  useEffect(() => {
    if (availableJobs && jobResponses) {

      // Debug what availableJobs actually is
      console.log('Debug availableJobs:', {
        value: availableJobs,
        type: typeof availableJobs,
        isArray: Array.isArray(availableJobs),
        constructor: availableJobs?.constructor?.name
      });

       // Guard against non-array values
       if (!Array.isArray(availableJobs)) {
        console.error('availableJobs is not an array:', availableJobs);
        return;
      }

      const updatedFilteredJobs = availableJobs.filter((job: any) => {
        // Check if this job has any matching response in IndexedDB
        const hasResponse = jobResponses.some(
          (response: JobResponse) => Number(response.job_id) === Number(job.id)
        );
        // Keep only jobs that don't have responses
        return !hasResponse;
      });

      console.log('Filtered jobs:', {
        total: availableJobs.length,
        filtered: updatedFilteredJobs.length,
        removed: availableJobs.length - updatedFilteredJobs.length,
        responses: jobResponses?.length || 0
      });

      setFilteredJobs(updatedFilteredJobs);
    }
  }, [availableJobs, jobResponses, setFilteredJobs]);

  const playPingSound = () => {
    const sound = new Audio("../Assets/ping_audio.wav");
    console.log('sound', sound)
    sound.play();
  };

  return { inspectLocalStorage, isLoading, availableJobs, localToken, registerJobInterest };
};
