import React, { useState } from 'react';
import { sampleAppConfig } from '../../../../../sample-app-config';
import {
  StateShell,
} from '../../../../Shells';
import { Pagination } from '../../../../Fragments/Pagination/Pagination';
import styled from 'styled-components';
import { ScrollableContent } from '../../../../Components/Scrollbar/Scrollbar';
import { useListPagination2 } from '../../../../Components';
import {  ClaimCardList, DefaultWorkflowFilters, JobCardList } from '../../../../Fragments';
import { WorkFlowFilterData } from '../../../../Components/Filter/WorkFlowFilter/WorkFlowFilter';
import { FilterSelectorItem } from '../../../../Components/Filter/Filter';
import { StaffMember } from '../../../../Auth';
import { AllInfo } from '../../../../Engine';


const ModuleContent = styled.div`
  background: ${(props) => props.theme.ColorsBackgroundModule};
  padding-top: 2rem;
  padding-left: 1rem;
  padding-right: 1rem;
  box-sizing: border-box;
  border-radius: 0 7px 7px 7px;
  grid-area: Module-Content;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
`;

const FiltersWrapper = styled.div`
  position: relative;
  z-index: 2;
`;

const JobListWrapper = styled.div`
  position: relative;
  z-index: 1;
  margin-top: 1rem;
`;

const ViewShellPagination = styled(Pagination)`
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
`;

const Content = styled(ScrollableContent)<{
  items?: any[];
  scrollable?: boolean;
}>`
  max-height: 100% !important;
  padding-right: ${(props) => props.theme.SpacingXl};
  height: calc(100vh - 2rem - 72px);

  ${(props) =>
    props?.scrollable || (props?.items && props?.items.length > 0)
      ? 'padding-bottom: 4rem; box-sizing: border-box'
      : ''};
  ${(props) => (!props?.scrollable ? 'overflow: hidden;' : '')};
`;



interface Props {
  filtersData: (WorkFlowFilterData)[];
  filterMenuData: FilterSelectorItem[];
  claims: any[];
  staffMember: StaffMember;
  allInfo: Partial<AllInfo>;
  itemsPerPage?: number;
  jobCardNumberPrefix: string;
  searchUrl?: string;
  ClaimLinkRouter: any;
  JobLinkRouter: any;
  tokenPrefix?: string;
  token?: string;
  getClaimMenuItems: (claim: any) => {icon: string; label: string; path: string}[];
  getJobMenuItems: (job: any) => {icon: string; label: string; path: string}[];
}

export function ClaimsDetailedView({ ClaimLinkRouter, JobLinkRouter, searchUrl, filtersData, filterMenuData, claims, staffMember, allInfo, itemsPerPage, token, tokenPrefix, getClaimMenuItems, getJobMenuItems}: Props) {
  const [filteredClaims, setFilteredClaims] = useState<any[]>([]);
  const { pages, currentPage, pageItems, ...rest } = useListPagination2({
    items: filteredClaims || [],
    itemsPerPage: itemsPerPage || 10,
  });
  
  return (
      <StateShell
        callClientAction={() => {}}
        stateConfig={sampleAppConfig.states['98']}
        clientDataObject={{}}
        fakeUseNavigation={{ state: 'idle' }}
      >
        <ModuleContent data-testid="workflow-view-shell-module-content">
          <Content
            data-testid="workflow-view-shell-content"
            items={pageItems}
            scrollable={true}
          >
            <FiltersWrapper>
              <DefaultWorkflowFilters 
                filtersData={filtersData} 
                filterMenuData={filterMenuData} 
                searchUrl={searchUrl || ''}
                items={claims} 
                setFilteredItems={setFilteredClaims} 
                token={token}
                tokenPrefix={tokenPrefix} />
            </FiltersWrapper>
            <JobListWrapper>
              <ClaimCardList displayLogo={false} claims={pageItems} staffMember={staffMember} allInfo={allInfo} getClaimMenuItems={getClaimMenuItems} getJobMenuItems={getJobMenuItems} ClaimLinkRouter={ClaimLinkRouter} JobLinkRouter={JobLinkRouter} />
            </JobListWrapper>
          </Content>
          <ViewShellPagination
            pages={pages}
            currentPage={currentPage}
            {...rest}
          />
        </ModuleContent>
      </StateShell>
  );
}
