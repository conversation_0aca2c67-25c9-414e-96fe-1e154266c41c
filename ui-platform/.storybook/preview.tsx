import {
  desktopDark,
  desktopLight,
  GlobalStyles,
  mobileDark,
  mobileLight,
} from '../src/lib/themes';
import { withThemeFromJSXProvider } from '@storybook/addon-themes';
import { Preview } from '@storybook/react';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { MemoryRouter } from 'react-router-dom';
import styled, { css, ThemeProvider } from 'styled-components';

const themeMap: any = {
  desktopDark,
  desktopLight,
  mobileDark,
  mobileLight,
};

const ThemeBlock = styled.div<{ fill?: boolean }>(
  ({ fill, theme }) =>
    css`
      position: absolute;
      top: 0;
      bottom: 0;
      font-family: 'Inter';
      width: 100%;
      height: 100%;
      background: ${theme.ColorsBackgroundShell};
      color: ${theme.ColorsTypographyPrimary};
      overflow: auto;
    `
);

const preview: Preview = {
  tags: ['autodocs'],
  decorators: [
    (Story, context) => {
      const theme: typeof desktopDark =
        themeMap[context.globals.theme || 'desktopDark'];
        const methods = useForm({
          mode: 'onChange',
        });

      return (
        <MemoryRouter>
          <ThemeProvider theme={theme}>
            <ThemeBlock>
              {/* <div
                style={{
                  paddingLeft: '1rem',
                  paddingRight: '2rem',
                  paddingTop: '1rem',
                }}
              > */}
                <FormProvider {...methods}>
                  <Story />
                </FormProvider>
              {/* </div> */}
            </ThemeBlock>
          </ThemeProvider>
        </MemoryRouter>
      );
    },
  ],

  parameters: {
    // layout: 'centered',
    controls: { hideNoControlsWarning: true },
    // actions: { argTypesRegex: '^on.*' },
    docs: {
      // theme
    },
    options: {
      storySort: {
        order: [
          'Docs',
          [
            'Intro',
            'Getting Started',
            ['Installing'],
            'Hooks',
            'Advanced',
            'Contributing'
          ],
          '*'
        ]
      }
    }
  },

  globalTypes: {
    theme: {
      description: 'Global theme for components',
      defaultValue: 'desktopDark',
      toolbar: {
        title: 'Theme',
        icon: 'paintbrush',
        items: [
          { value: 'desktopLight', title: 'Desktop Light' },
          { value: 'desktopDark', title: 'Desktop Dark' },
          { value: 'mobileLight', title: 'Mobile Light' },
          { value: 'mobileDark', title: 'Mobile Dark' },
        ],
        showName: true,
      },
    },
  },


  // tags: ['autodocs']
};

export default preview;
