import type { StorybookConfig } from '@storybook/react-vite';

const config: StorybookConfig = {
  stories: [
    '../docs/*.mdx',
    '../docs/**/*.mdx',
    '../src/lib/**/*.@(mdx|stories.@(js|jsx|ts|tsx))',
  ],

  addons: [
    { name: '@storybook/addon-essentials', options: { lazy: true } },
    { name: '@storybook/addon-interactions', options: { lazy: true } },
    { name: '@storybook/addon-themes', options: { lazy: true } },
    { name: '@chromatic-com/storybook', options: { lazy: true } }
  ],

  framework: {
    name: '@storybook/react-vite',
    options: {
      builder: {
        viteConfigPath: 'ui-platform/vite.config.ts',
      },
    },
  },

  staticDirs: ['./fonts', './assets'],

  docs: {},
  typescript: {
    reactDocgen: 'react-docgen-typescript'
  },
   viteFinal: (config) => {
    config.cacheDir = './.storybook/cache';
    if (config.mode === 'production') {
    config.build = config.build || {};
    config.build.sourcemap = false;
    }
    return config;
  }
};

export default config;

// To customize your Vite configuration you can use the viteFinal field.
// Check https://storybook.js.org/docs/react/builders/vite#configuration
// and https://nx.dev/recipes/storybook/custom-builder-configs
