# This file was auto-generated by the Firebase CLI
# https://github.com/firebase/firebase-tools

name: FieldOps Prod Deploy On Merge
on:
  push:
    branches:
      - release/fieldops-prod
jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'
          registry-url:  https://npm.pkg.github.com
          scope: "@4-sure"
      - name: 'Generate firebase.json file'
        run: 'cp ./.hosting/biab-fieldops-4sure-prod.firebase.json ./firebase.json'
      - name: 'NPM Install'
        run: npm install -f
      - name: 'Build FieldOps App For Prod'
        run: npx nx build field-ops --configuration=production
      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: ${{ secrets.GITHUB_TOKEN }}
          firebaseServiceAccount: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_BIAB_FIELDOPS_4SURE_PROD }}
          channelId: live
          projectId: biab-fieldops-4sure-prod
