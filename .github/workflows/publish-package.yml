name: Publish NPM Package To Github Registry
on:
  push:
    branches:
      - deploy
jobs:
  publish_gpr:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'
          registry-url:  https://npm.pkg.github.com
          scope: "@4-sure"
      - name: 'Generate firebase.json file'
        run: 'cp ./.hosting/ui-platform.firebase.json ./firebase.json'
      - name: 'NPM Install'
        run: npm install -f
      - name: 'Build UI Platform'
        run: npx nx build ui-platform
      - name: 'Publish Package'
        run: cd dist/ui-platform && npm publish
        env:
          NODE_AUTH_TOKEN: ${{secrets.GITHUB_TOKEN}}
      