# This file was auto-generated by the Firebase CLI
# https://github.com/firebase/firebase-tools

name: SP Mgt Dev Deploy On Merge
on:
  push:
    branches:
      - release/sp-mgt-dev
jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'
          registry-url:  https://npm.pkg.github.com
          scope: "@4-sure"
      - name: 'Generate firebase.json file'
        run: 'cp ./.hosting/sp-mgt-dev.firebase.json ./firebase.json'
      - name: 'NPM Install'
        run: npm install -f
      - name: 'Build SP Mgt For Dev'
        run: npx nx build spa-test --configuration=development
      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: ${{ secrets.GITHUB_TOKEN }}
          firebaseServiceAccount: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_SP_MGT_4SURE_DEV }}
          channelId: live
          projectId: sp-mgt-4sure-dev
