# This file was auto-generated by the Firebase CLI
# https://github.com/firebase/firebase-tools

name: Storybook PR
on: pull_request
permissions:
  checks: write
  contents: read
  pull-requests: write
jobs:
  build_and_preview:
    if: ${{ github.event.pull_request.head.repo.full_name == github.repository }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url:  https://npm.pkg.github.com
          scope: "@4-sure"
      - name: 'Generate firebase.json file'
        run: 'cp ./.hosting/ui-platform.firebase.json ./firebase.json'
      - name: 'NPM Install'
        run: npm install
      - name: 'Build UI Platform'
        run: npx nx build ui-platform
      - name: 'Build Storybook'
        run: NODE_OPTIONS="--max-old-space-size=8192" npx nx build-storybook ui-platform
      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: ${{ secrets.GITHUB_TOKEN }}
          firebaseServiceAccount: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_UI_PLATFORM_4SURE }}
          projectId: ui-platform-4sure
