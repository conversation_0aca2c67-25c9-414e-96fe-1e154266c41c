{"name": "landing-e2e", "$schema": "../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "landing-e2e/src", "implicitDependencies": ["landing"], "targets": {"e2e": {"executor": "@nx/playwright:playwright", "outputs": ["{workspaceRoot}/dist/.playwright/landing-e2e"], "options": {"config": "landing-e2e/playwright.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}