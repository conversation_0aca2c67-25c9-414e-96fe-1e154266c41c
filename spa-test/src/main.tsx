import { StrictMode } from 'react';
import * as ReactDOM from 'react-dom/client';

import {
  AuthorisationProvider,
  SpaKeycloakAuthProvider,
} from '@4-sure/ui-platform';
import App from './app/app';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <StrictMode>
    <SpaKeycloakAuthProvider
      fieldAccessUrl={`${
        import.meta.env.VITE_SP_SERVER
      }/api/v1/spaas_actions/sp_field_access`}
      config={{
        clientId: import.meta.env.VITE_KEYCLOAK_CLIENT_ID,
        realm: import.meta.env.VITE_KEYCLOAK_REALM,
        url: import.meta.env.VITE_KEYCLOAK_URL,
      }}
    >
      <AuthorisationProvider
        userProfileUrl={`${
          import.meta.env.VITE_STAFF_SERVER
        }/api/v1/profile_actions/get_profile`}
        userProfilePicUrl={`${
          import.meta.env.VITE_STAFF_SERVER
        }/api/v1/file_actions/get_file`}
      >
        <App />
      </AuthorisationProvider>
    </SpaKeycloakAuthProvider>
  </StrictMode>
);
