import { useEffect, useMemo } from 'react';
import {
  createBrowserRouter,
  Navigate,
  RouterProvider,
} from 'react-router-dom';
import { TeamsRoutes } from './pages/teams/TeamsRoutes';

import {
  desktopDark,
  SplashScreenLoader,
  useAuthorisation,
  useGetOpenTasks,
  useSpaKeycloak,
} from '@4-sure/ui-platform';
import 'styled-components';
import { AppWrapper } from './app-wrapper';
import { SettingsRoutes } from './pages/settings/SettingsRoutes';
import { TasksRoutes } from './pages/tasks/TasksRoutes';

type CustomTheme = typeof desktopDark;

declare module 'styled-components' {
  export interface DefaultTheme extends CustomTheme {}
}

export function App() {
  const { keycloak } = useSpaKeycloak();
  const { isAdmin, validUser, currentUserProfile } = useAuthorisation();
  // const { admin_tasks, staff_members } = useFetchTeamMembers({
  //   _keycloak: keycloak,
  // });

  useGetOpenTasks({
    _keycloak: keycloak,
    isAdmin,
    isAuthorised: currentUserProfile?.onboarding_state === 3 && currentUserProfile?.authorised,
  });

  const StateRoutes = useMemo(() => {
    return [
      SettingsRoutes(keycloak, isAdmin),
      TeamsRoutes(keycloak, isAdmin),
      TasksRoutes(keycloak, isAdmin),
    ];
  }, [isAdmin]);

  const landingScreen = useMemo(
    () => (isAdmin ? '/teams' : '/settings'),
    [isAdmin]
  );

  useEffect(() => {
    if (typeof validUser === 'boolean') {
      if (!validUser) {
        window.location.replace(
          `${(import.meta as any).env.VITE_LANDING_PAGE_URL}`
        );
      }
    }
  }, [validUser]);

  const router = useMemo(
    () =>
      createBrowserRouter([
        {
          path: '',
          element: <AppWrapper />,
          children: [
            {
              path: '/',
              element: <Navigate to={landingScreen} />,
            },
            ...StateRoutes,
          ],
        },
      ]),
    [StateRoutes, landingScreen]
  );
  if (isAdmin === undefined) return <SplashScreenLoader />;
  return <RouterProvider router={router} />;
}

export default App;
