import { StateConfig, validationRegex } from '@4-sure/ui-platform';

export const MANAGE_TEAM_ADD_TEAM_MEMBER_STATE = {
  title: { template: '' },
  fetchCalls: [],
  defaultScreen: 'add-member',
  screens: {
    'add-member': {
      layout: {},
      fetchCalls: [],
      onEnter: [],
      fragments: [
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Add team member',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
              {
                text: 'Please be aware that this image will be visible to customers',
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                },
              },
            ],
          },
          layout: { justifyItems: 'center', display: 'grid' },
        },
        {
          component: 'FormBuilder',
          props: {
            config: {
              style: {
                rowGap: '1rem',
                columnGap: '1rem',
                paddingTop: '2rem',
                paddingBottom: '1rem',
                width: '336px',
                justifyItems: 'center',
                alignContent: 'space-around',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'username',
                  label: 'User Name',
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                    pattern: {
                      value: validationRegex.username.pattern,
                      message: validationRegex.username.message,
                    },
                  },
                  instructions: 'Used to login to the platform', //TODO: the instructions does not work(comment under form)
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
        {
          component: 'FormBuilder',
          layout: {
            display: 'grid',
            justifyItems: 'center',
          },
          props: {
            config: {
              style: {
                display: 'grid',
                gridTemplateColumns: 'repeat(2, 1fr)',
                rowGap: '1rem',
                columnGap: '1rem',
                width: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'full_name',
                  label: 'Full Name',
                  validation: {
                    pattern: {
                      value: validationRegex.name.pattern,
                      message: validationRegex.name.message,
                    },
                    required: {
                      value: true,
                      message: 'Name is required',
                    },
                    minLength: {
                      value: 2,
                      message: 'Name must be at least 2 characters',
                    },
                    maxLength: {
                      value: 30,
                      message: 'You cant go past 30 characters',
                    },
                  },
                  css: {
                    wrapper: { gridColumn: '1', gridRow: '1' },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'id_passport',
                  label: 'Identification',
                  validation: {
                    required: {
                      value: true,
                      message: 'Field is required',
                    },
                    pattern: {
                      value: validationRegex.id_passport.pattern,
                      message: validationRegex.id_passport.message,
                    },
                  },
                  css: {
                    wrapper: { gridColumn: '2', gridRow: '1' },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'contact_number',
                  label: 'Contact number',
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                    pattern: {
                      value: validationRegex.phone_number.pattern,
                      message: validationRegex.phone_number.message,
                    },
                  },
                  css: { wrapper: { gridColumn: '1', gridRow: '2' } },
                },
                {
                  type: 'plain-text',
                  name: 'email_address',
                  label: 'Email Address',
                  validation: {
                    pattern: {
                      value: validationRegex.email.pattern,
                      message: validationRegex.email.message,
                    },
                  },
                  css: { wrapper: { gridColumn: '2', gridRow: '2' } },
                },
                {
                  type: 'multi-select',
                  name: 'roles',
                  label: 'Roles',
                  labelProp: 'description',
                  valueProp: 'id',
                  options: {
                    source: 'store',
                    storeDataPath: 'staff_enums.roles_2',
                  },
                  validation: {
                    required: true,
                    message: 'This field is required',
                  },
                  placeholder: 'Select roles...',
                  css: { wrapper: { gridColumn: '1', gridRow: '3' } },
                },
                {
                  type: 'single-select',
                  name: 'id_type',
                  label: 'ID Types',
                  placeholder: 'Select type',
                  labelProp: 'name',
                  valueProp: 'id',
                  validation: {
                    required: true,
                    message: 'This field is required',
                  },
                  options: {
                    source: 'store',
                    storeDataPath: 'staff_enums.id_types',
                  },
                  dropdownScroll: true,
                  css: {
                    wrapper: {
                      gridColumn: 2,
                      gridRow: 3,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
        },
      ],
      navs: [
        {
          label: 'Back To Teams',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'navigate',
              payload: ['/teams/manage-team/list-view'],
            },
          ],
        },
        {
          label: 'Add Team Member',
          position: 'right',
          disabledWhen: `!$formState.isDirty ||
           !$formState.isValid ||
           !(Object.keys($formState.touchedFields).length > 0) ||
           !$form.username ||
           !$form.full_name ||
           !$form.contact_number ||
           !$form.id_type ||
           !$form.roles ||
           !$form.id_passport`,
          onClick: [
            {
              type: 'clientAction',
              action: 'submitAndNavigate',
              payload: [
                {
                  username: '$formDataRaw.username',
                  full_name: '$formDataRaw.full_name',
                  contact_number: '$formDataRaw.contact_number',
                  email_address: '$formDataRaw.email_address',
                  id_type: '$formDataRaw.id_type', // SA id or drivers license = 1, passport = 2
                  id_passport: '$formDataRaw.id_passport',
                  qualification_number: null,
                  available: true,
                  staff_type: 2,
                  roles: '$formDataRaw.roles',
                  callback_uri: `${
                    import.meta.env.VITE_REGISTRATION_APP_URL
                  }set-preregistration-password`,
                },
                {
                  url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/register_new_profile',
                  headers: {},
                  redirect: '/teams/manage-team/add/successful-request',
                },
                {
                  method: 'post',
                  action: '/teams/manage-team/add/add-member',
                },
              ],
            },
          ],
        },
      ],
    },

    'successful-request': {
      layout: {},
      fetchCalls: [
        {
          key: 'new_staff_profile',
          method: 'POST',
          body: {},
          url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/get_profile',
        },
      ],
      fragments: [
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Join request successfully sent',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
            ],
            layout: {
              display: 'grid',
              justifyItems: 'center',
              paddingTop: '2rem',
              paddingBottom: '2rem',
            },
          },
          layout: { justifyItems: 'center', display: 'grid' },
        },
        {
          component: 'UploadPhoto',
          props: {
            image: '',
            action: 'upload',
            url: '',
            _fetcher: '$fetcher',
            buttonActive: false,
          },
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                // TODO: to amend this so as to make it a pure json object
                text: `#{formDataRaw.full_name}`,
                options: {
                  format: 'heading',
                  type: 'control-view-subheading',
                },
              },
              {
                // TODO: to amend this so as to make it a pure json object
                text: `#{formDataRaw.email_address}`,
                options: {
                  format: 'heading',
                  type: 'control-view-subheading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '1rem',
          },
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                // TODO: to amend this so as to make it a pure json object
                text: `#An email has been sent to {formDataRaw.full_name} to join your organization`,
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '1rem',
          },
        },
      ],
      navs: [
        {
          label: 'Back To Teams',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'navigate',
              payload: ['/teams/manage-team/list-view'],
            },
          ],
        },
      ],
    },
  },
  actionPanels: [
    // Scratch Pad
    {
      icon: 'clipboard',
      title: 'Scratch Pad', //?actionPanel=Messages--bell-02
      // fetchCalls: [],
      layout: {},
      onEnter: [],
      onLeave: [],
      fragments: [
        {
          component: 'ScratchPadView',
          layout: { marginLeft: '10px', marginRight: '10px' },
          props: {
            titlePlaceholder: 'Heading',
            icon: 'trash-01',
            iconHandler: (data: { heading: string; body: string }) =>
              console.log(
                'got data: Heading - ' + data.heading + ' Body - ' + data.body
              ),
            placeHolder: 'Text here...',
          },
        },
      ],
      actionLevel: 'bottomControls',
    },
  ],
} satisfies StateConfig;
