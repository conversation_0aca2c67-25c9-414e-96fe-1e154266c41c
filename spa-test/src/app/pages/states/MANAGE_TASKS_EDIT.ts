import { StateConfig } from '@4-sure/ui-platform';

export const MANAGE_TASKS_EDIT_STATE = {
  title: { template: '' },
  fetchCalls: [
    {
      key: 'task_details',
      method: 'POST',
      url: '{VITE_SP_SERVER}/api/v1/task_actions/get_task_details',
      slicePath: 'payload',
      successFetchCalls: [
        {
          key: 'staff_profile',
          method: 'POST',
          url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/get_profile',
          slicePath: 'payload',
          body: { staff_id: '$object_id' },
          successFetchCalls: [
            {
              key: 'profile_picture',
              method: 'POST',
              url: '{VITE_STAFF_SERVER}/api/v1/file_actions/get_file',
              body: { file_id: '$profile_pic', staff_id: '$sso_id' },
              slicePath: 'payload',
            },
            {
              key: 'staff_member_documentation',
              method: 'POST',
              url: '{VITE_STAFF_SERVER}/api/v1/file_actions/get_files',
              body: {
                with_thumbnails: true,
                staff_id: '$sso_id',
                purpose: 'Identity',
                order: '-',
              },
              slicePath: 'payload',
            },
          ],
        },
      ],
    },
  ],
  defaultScreen: 'details',
  onLeave: [],
  screens: {
    // screen
    details: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Approve Team Member',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
              {
                text: 'Please be aware that this image will be visible to customers',
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            // width: 'calc(100% - 226px - 56px)',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'UploadPhoto',
          props: {
            image: '$profile_picture.file',
            action: 'upload',
            url: '{VITE_STAFF_SERVER}/api/v1/file_actions/upload_file',
            _fetcher: '$fetcher',
            buttonActive: false,
          },
          layout: {},
        },
        {
          component: 'FormBuilder',
          props: {
            proof_of_id:
              "$staff_member_documentation?find:item.purpose === 'Identity'",
            defaultValues: {
              user_name: '$staff_profile.username',
              full_name: '$staff_profile.full_name',
              contact_number: '$staff_profile.contact_number',
              email_address: '$staff_profile.email_address',
              id_passport: '$staff_profile.id_passport',
              roles: '$staff_profile.roles',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'user_name',
                  label: 'User name',
                  icon: 'lock-04',
                  position: 'right',
                  state: 'display-only',
                  disabledWhen: 'true',
                  css: { wrapper: { width: '100%' } },
                },
                {
                  type: 'plain-text',
                  name: 'full_name',
                  label: 'Full name',
                  icon: 'lock-04',
                  position: 'right',
                  state: 'display-only',
                  disabledWhen: 'true',
                  css: { wrapper: { width: '100%' } },
                },
                {
                  type: 'plain-text',
                  name: 'contact_number',
                  label: 'Cell number',
                  icon: 'lock-04',
                  position: 'right',
                  state: 'display-only',
                  css: { wrapper: { width: '100%' } },
                },
                {
                  type: 'plain-text',
                  name: 'email_address',
                  label: 'Email',
                  icon: 'lock-04',
                  position: 'right',
                  state: 'display-only',
                  disabledWhen: 'true',
                  css: { wrapper: { width: '100%' } },
                },
                {
                  type: 'plain-text',
                  name: 'id_passport',
                  label: 'ID Number',
                  icon: 'lock-04',
                  position: 'right',
                  state: 'display-only',
                  disabledWhen: 'true',
                  css: { wrapper: { width: '100%' } },
                },
                {
                  type: 'document-card',
                  name: 'proof_of_id',
                  documents: '$staff_profile.documents',
                  purpose: 'Proof of ID',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  fieldAccessPath: { view: 'details', edit: 'details' },
                  url: '{VITE_STAFF_SERVER}/api/v1/file_actions/upload_file',
                  action: '/tasks/manage-tasks/edit/details',
                  list: 'False',
                  isOtherStaff: true,
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'multiselect-checklist',
                  name: 'roles',
                  label: 'Roles',
                  state: 'display-only',
                  fieldAccessPath: {
                    view: 'roles',
                    edit: 'roles',
                    special: 'roles',
                  },
                  labelProp: 'description',
                  valueProp: 'id',
                  options: {
                    source: 'store',
                    storeDataPath: 'staff_enums.roles_2',
                  },
                  maxColumns: 3,
                  checkedItems: 'staff_profile.roles',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
          },
        },
      ],
      navs: [
        {
          label: 'Back to tasks',
          position: 'left',
          toScreen: '/tasks/manage-tasks/list-view',
          clearParams: true,
        },
        {
          label: 'Reject member',
          position: 'right',
          colorVariant: 'alternative',
          onClick: [
            {
              type: 'clientAction',
              action: 'triggerModal',
              payload: [
                {
                  display: true,
                  type: 'warning',
                  layout: {},
                  onEnter: [],
                  onLeave: [],
                  fragments: [
                    {
                      component: 'Text',
                      props: {
                        textItems: [
                          {
                            text: 'Warning',
                            options: {
                              format: 'heading',
                              type: 'page-heading',
                              style: {
                                marginBottom: '1rem',
                              },
                            },
                          },
                          {
                            text: 'You are about to decline this request',
                            options: {
                              format: 'heading',
                              type: 'sub-heading',
                            },
                          },
                          {
                            text: 'Are you sure you would like to reject this member?',
                            options: {
                              format: 'heading',
                              type: 'sub-heading',
                            },
                          },
                        ],
                      },
                      layout: {
                        display: 'grid',
                        justifyItems: 'center',
                      },
                    },
                    {
                      component: 'ButtonRow',
                      layout: {
                        width: 'fit-content',
                        margin: '0 auto',
                      },
                      props: {
                        buttons: [
                          {
                            btnValue: 'No, go back to approval',
                            onClick: [
                              {
                                type: 'clientAction',
                                action: 'closeModal',
                              },
                            ],
                          },
                          {
                            btnValue: 'Yes, reject member',
                            onClick: [
                              {
                                type: 'clientAction',
                                action: 'submitAndNavigate',
                                payload: [
                                  {
                                    staff_id: '$staff_profile.sso_id',
                                  },
                                  {
                                    url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/remove_staff_from_sp',
                                    headers: {},
                                    redirect: '/tasks/manage-tasks/list-view',
                                  },
                                  {
                                    method: 'post',
                                    action: '/tasks/manage-tasks/edit/details',
                                  },
                                ],
                              },
                            ],
                          },
                        ],
                      },
                    },
                  ],
                  navs: [],
                },
              ],
            },
          ],
        },
        {
          label: 'Approve member',
          position: 'right',
          colorVariant: 'preferred',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store?.postData && (Object.keys($store.postData).length > 0) && (Object.keys($formState.errors).length === 0)',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'submitAndFetch',
                      payload: [
                        {
                          postData: {
                            roles: '$formDataRaw.roles',
                            contact_number: '$formDataRaw.contact_number',
                            staff_id: '$staff_profile.sso_id',
                          },
                        },
                        {
                          url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_staff_profile',
                          headers: {},
                          bodySlicePath: 'postData',
                        },
                        {
                          method: 'post',
                          action: '/tasks/manage-tasks/edit/details',
                        },
                      ],
                    },
                  ],
                },
              },
            },
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  'Array.isArray($store.staff_profile.roles) && ($store.staff_profile.roles).includes(43)',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: 'true',
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning: Admin Access',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                      style: {
                                        marginBottom: '1rem',
                                      },
                                    },
                                  },
                                  {
                                    text: `This person will have access to edit and view company details and other company related information.`,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                    },
                                  },
                                  {
                                    text: `#Are you sure you want to approve <b>{staff_profile.full_name}</b> with the <b>SP Admin</b> role?`,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Yes, approve member',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndNavigate',
                                        payload: [
                                          {
                                            staff_id: '$staff_profile.sso_id',
                                          },
                                          {
                                            url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/approve_staffmember',
                                            headers: {},
                                            redirect:
                                              '/tasks/manage-tasks/list-view',
                                          },
                                          {
                                            method: 'post',
                                            action:
                                              '/tasks/manage-tasks/edit/details',
                                          },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndNavigate',
                                        payload: [
                                          {
                                            task_id: '$task_details.id',
                                          },
                                          {
                                            url: '{VITE_SP_SERVER}/api/v1/task_actions/complete_task',
                                            headers: {},
                                            redirect:
                                              '/tasks/manage-tasks/list-view',
                                          },
                                          {
                                            method: 'post',
                                            action:
                                              '/tasks/manage-tasks/edit/details',
                                          },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'No, go back',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'submitAndNavigate',
                      payload: [
                        {
                          staff_id: '$staff_profile.sso_id',
                        },
                        {
                          url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/approve_staffmember',
                          headers: {},
                          redirect: '/tasks/manage-tasks/list-view',
                        },
                        {
                          method: 'post',
                          action: '/tasks/manage-tasks/edit/details',
                        },
                      ],
                    },
                    {
                      type: 'clientAction',
                      action: 'submitAndNavigate',
                      payload: [
                        {
                          task_id: '$task_details.id',
                        },
                        {
                          url: '{VITE_SP_SERVER}/api/v1/task_actions/complete_task',
                          headers: {},
                          redirect: '/tasks/manage-tasks/list-view',
                        },
                        {
                          method: 'post',
                          action: '/tasks/manage-tasks/edit/details',
                        },
                      ],
                    },
                  ],
                },
              },
            },
          ],
        },
      ],
      onLeave: [
        {
          type: 'clientAction',
          action: 'clearStore',
          payload: [
            'staff_member_documentation',
            'profile_picture',
            'staff_profile',
            'originalValues',
            'postData',
            'formDataRaw',
            'task_details',
          ],
        },
      ],
    },
    // end screen
  },
  formOriginalValues: {
    'staff_profile.username': 'user_name',
    'staff_profile.full_name': 'full_name',
    'staff_profile.contact_number': 'contact_number',
    'staff_profile.roles': 'roles',
    'staff_profile.email_address': 'email_address',
    'staff_profile.id_passport': 'id_passport',
  },
  formTransformMapper: {
    roles: 'roles',
    user_name: 'user_name',
    contact_number: 'contact_number',
    full_name: 'full_name',
    email_address: 'email_address',
    id_passport: 'id_passport',
  },
  actionPanels: [
    // Scratch Pad
    {
      icon: 'clipboard',
      title: 'Scratch Pad', //?actionPanel=Messages--bell-02
      // fetchCalls: [],
      layout: {},
      onEnter: [],
      onLeave: [],
      fragments: [
        {
          component: 'ScratchPadView',
          layout: { marginLeft: '10px', marginRight: '10px' },
          props: {
            titlePlaceholder: 'Heading',
            icon: 'trash-01',
            iconHandler: (data: { heading: string; body: string }) =>
              console.log(
                'got data: Heading - ' + data.heading + ' Body - ' + data.body
              ),
            placeHolder: 'Text here...',
          },
        },
      ],
      actionLevel: 'bottomControls',
    },
  ],
} satisfies StateConfig;
