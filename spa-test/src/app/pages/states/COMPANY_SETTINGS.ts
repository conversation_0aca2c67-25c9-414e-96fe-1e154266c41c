import { bbbeee, StateConfig, validationRegex } from '@4-sure/ui-platform';

export const COMPANY_SETTINGS_STATE = {
  title: { template: '' },
  fetchCalls: [
    {
      key: 'sp_enums',
      method: 'POST',
      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_enum',
      body: { enum: 'all' },
      slicePath: 'payload',
    },
    {
      key: 'sp_profile',
      method: 'POST',
      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_sp',
      slicePath: 'payload',
    },
    {
      key: 'company_files',
      method: 'POST',
      url: '{VITE_SP_SERVER}/api/v1/file_actions/get_files',
      body: { with_thumbnails: true, order: '-' },
      slicePath: 'payload',
    },
  ],
  defaultScreen: 'company',
  screens: {
    company: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Company',
                path: '../company',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documents',
                path: '../documents',
              },
              {
                name: 'Subscription',
                path: '../subscription',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'ProfileHero',
          layout: {
            display: 'grid',
            justifyContent: 'center',
          },
          props: {
            fullname: '$sp_profile.details.name',
            subText: 'Registration number: ',
            username: '$sp_profile.details.co_reg',
            active: false,
            image: '$sp_profile.company_profile_picture',
            url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
            action: '/settings/company/company',
            profileType: 'company',
            state: '$sp_profile.details.onboarding_state',
            showImgUpload: false,
          },
        },
        {
          component: 'FormBuilder',
          props: {
            bbbeee_certificate:
              "$company_files?find:item.purpose === 'Reg BBEEE certificate'",
            defaultValues: {
              trading_as: '$sp_profile.details.trading_as',
              bbeee: '$sp_profile.details.bbeee',
              vat_no: '$sp_profile.financial.vat_no',
            },
            config: {
              style: {
                display: 'grid',
                gridTemplateColumns: 'repeat(2, 1fr)',
                gridTemplateRows: 'repeat(3, 1fr)',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'trading_as',
                  fieldAccessPath: {
                    view: 'details',
                    edit: 'details',
                    special: 'details',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  label: 'Trading as',
                  css: {
                    wrapper: {
                      gridColumn: 1,
                      gridRow: 1,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'single-select',
                  dropdownScroll: true,
                  name: 'bbeee',
                  fieldAccessPath: {
                    view: 'details',
                    edit: 'details',
                    special: 'details',
                  },
                  label: 'BBBEEE Level',
                  position: 'right',
                  labelProp: 'label',
                  valueProp: 'value',
                  options: {
                    data: bbbeee,
                    source: 'literal',
                  },
                  css: {
                    wrapper: {
                      gridColumn: 2,
                      gridRow: 1,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'drag-and-drop',
                  name: 'bbbeee_certificate',
                  label: 'BBBEEE Certificate',
                  fieldAccessKey: 'file',
                  fieldAccessPath: {
                    view: 'spdocument',
                    edit: 'spdocument',
                    special: 'spdocument',
                  },
                  purpose: 'Reg BBEEE certificate',
                  list: 'False',
                  action: '/settings/company/company',
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  css: {
                    wrapper: {
                      gridColumn: '1 / span 2',
                      gridRow: 2,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
      ],
      navs: [
        {
          label: 'Back To Teams',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store?.postData && (Object.keys($store.postData).length > 0) && $formState.isValid',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: 'You have unsaved changes. What would you like to do?',
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    actiontype: 'warning',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: [
                                          '/teams/manage-team/list-view',
                                          { clearParams: true },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    actiontype: 'proceed',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndFetch',
                                        payload: [
                                          {
                                            postData: '$postData',
                                          },
                                          {
                                            url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                            headers: {},
                                            bodySlicePath: 'postData',
                                            redirect: '/teams',
                                          },
                                          {
                                            method: 'post',
                                            action: '/settings/company/company',
                                          },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: [
                        '/teams/manage-team/list-view',
                        { clearParams: true },
                      ],
                    },
                  ],
                },
              },
            },
          ],
        },
        {
          label: 'Cancel Changes',
          position: 'center',
          disabledWhen:
            '$store?.postData && !(Object.keys($store.postData).length > 0)',
          onClick: [
            {
              type: 'clientAction',
              action: 'resetFields',
              payload: {
                fields: [
                  {
                    fieldName: 'trading_as',
                    defaultValue: '$sp_profile.details.trading_as',
                  },
                  {
                    fieldName: 'bbeee',
                    defaultValue: '$sp_profile.details.bbeee',
                  },
                  {
                    fieldName: 'b_acc_holder',
                    defaultValue: '$sp_profile.financial.b_acc_holder',
                  },
                  {
                    fieldName: 'b_branch_name',
                    defaultValue: '$sp_profile.financial.b_branch_name',
                  },
                  {
                    fieldName: 'b_acc_type',
                    defaultValue: '$sp_profile.financial.b_acc_type',
                  },
                  {
                    fieldName: 'b_branch_code',
                    defaultValue: '$sp_profile.financial.b_branch_code',
                  },
                  {
                    fieldName: 'b_bank_name',
                    defaultValue: '$sp_profile.financial.b_bank_name',
                  },
                  {
                    fieldName: 'b_acc_no',
                    defaultValue: '$sp_profile.financial.b_acc_no',
                  },
                  {
                    fieldName: 'vat_no',
                    defaultValue: '$sp_profile.financial.vat_no',
                  },
                  {
                    fieldName: 'tax_no',
                    defaultValue: '$sp_profile.financial.tax_no',
                  },
                  {
                    fieldName: 'contact_person',
                    defaultValue: '$sp_profile.address.contact_person',
                  },
                  {
                    fieldName: 'contact_primary',
                    defaultValue: '$sp_profile.address.contact_primary',
                  },
                  {
                    fieldName: 'contact_secondary',
                    defaultValue: '$sp_profile.address.contact_secondary',
                  },
                  {
                    fieldName: 'email_receiving',
                    defaultValue: '$sp_profile.address.email_receiving',
                  },
                  {
                    fieldName: 'physical_addr',
                    defaultValue: '$sp_profile.address.physical_addr',
                  },
                  {
                    fieldName: 'physical_city',
                    defaultValue: '$sp_profile.address.physical_city',
                  },
                  {
                    fieldName: 'physical_code',
                    defaultValue: '$sp_profile.address.physical_code',
                  },
                  {
                    fieldName: 'physical_suburb',
                    defaultValue: '$sp_profile.address.physical_suburb',
                  },
                  {
                    fieldName: 'province',
                    defaultValue: '$sp_profile.address.province',
                  },
                  {
                    fieldName: 'postal_box',
                    defaultValue: '$sp_profile.address.postal_box',
                  },
                  {
                    fieldName: 'postal_city',
                    defaultValue: '$sp_profile.address.postal_city',
                  },
                  {
                    fieldName: 'postal_code',
                    defaultValue: '$sp_profile.address.postal_code',
                  },
                  {
                    fieldName: 'skills',
                    defaultValue: '$sp_profile.skills',
                  },
                  {
                    fieldName: 'after_hours',
                    defaultValue: '$sp_profile.after_hours',
                  },
                  {
                    fieldName: 'companies',
                    defaultValue: '$derivedCompanies',
                  },
                  {
                    fieldName: 'accredition',
                    defaultValue:
                      '$sp_profile.additional_identities.accredition',
                  },
                  {
                    fieldName: 'mid',
                    defaultValue: '$sp_profile.additional_identities.mid',
                  },
                  {
                    fieldName: 'operational_area',
                    defaultValue: '$derivedOperationalArea',
                  },
                  {
                    fieldName: 'radius',
                    defaultValue: '$derivedOperationalArea.0.operating_range',
                  },
                  {
                    fieldName: 'jobLocation',
                    defaultValue: '$derivedOperationalArea.0.location',
                  },
                ],
              },
            },
          ],
        },
        {
          label: 'Save Changes',
          position: 'center',
          disabledWhen:
            '!$store?.postData || !(Object.keys($store.postData).length > 0) || !$formState.isValid || !$formState.isDirty',
          onClick: [
            {
              type: 'clientAction',
              action: 'submitAndFetch',
              payload: [
                {
                  postData: '$postData',
                },
                {
                  url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                  headers: {},
                  bodySlicePath: 'postData',
                  // redirect: '/settings/company/banking',
                },
                { method: 'post', action: '/settings/company/company' },
              ],
            },
          ],
        },
        {
          label: 'Next',
          position: 'right',
          toScreen: '../banking',
        },
      ],
    },

    banking: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Company',
                path: '../company',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documents',
                path: '../documents',
              },
              {
                name: 'Subscription',
                path: '../subscription',
              },
            ],
          },
          layout: {},
          isStickyHeader: true,
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Company banking details',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
              {
                text: 'Changing bank details requires approval',
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                },
                icon: {
                  type: 'alert-diamond',
                  size: 24,
                  strokeWidth: '1px',
                  color: '#FF9800',
                  style: { margin: '0 0 0 4px' },
                },
                iconPosition: 'right',
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            // proof_of_bank_account:
            //   "$company_files?find:item.purpose === 'Reg Proof of bank account'",
            defaultValues: {
              b_acc_holder: '$sp_profile.financial.b_acc_holder',
              b_branch_name: '$sp_profile.financial.b_branch_name',
              b_acc_type: '$sp_profile.financial.b_acc_type',
              b_branch_code: '$sp_profile.financial.b_branch_code',
              b_bank_name: '$sp_profile.financial.b_bank_name',
              b_acc_no: '$sp_profile.financial.b_acc_no',
              // vat_registered: '$sp_profile.financial.vat_registered',
              tax_no: '#{sp_profile.financial.tax_no}',
              vat_no: '#(sp_profile.financial.vat_no)',
            },
            config: {
              style: {
                display: 'grid',
                gridTemplateColumns: 'repeat(2, 1fr)',
                gridTemplateRows: 'repeat(4, 1fr)',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'b_acc_holder',
                  label: 'Bank account holder name',
                  position: 'right',
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  fieldAccessPath: {
                    view: 'financial',
                    edit: 'financial',
                    special: 'financial',
                  },
                  css: {
                    wrapper: {
                      gridColumn: 1,
                      gridRow: 1,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  // icon: 'alarm-clock',
                  position: 'right',
                  type: 'single-select',
                  name: 'b_bank_name',
                  label: 'Bank name',
                  labelProp: 'name',
                  fieldAccessPath: {
                    view: 'financial',
                    edit: 'financial',
                    special: 'financial',
                  },
                  valueProp: 'id',
                  dropdownScroll: true,
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.banks',
                    labelProp: 'name',
                    valueProp: 'id',
                  },
                  css: {
                    wrapper: {
                      gridColumn: 2,
                      gridRow: 1,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'b_acc_no',
                  label: 'Bank account number',
                  position: 'right',
                  fieldAccessPath: {
                    view: 'financial',
                    edit: 'financial',
                    special: 'financial',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'Bank account number is required',
                    },
                    pattern: {
                      value: validationRegex.bank_account_number.pattern,
                      message: validationRegex.bank_account_number.message,
                    },
                    minLength: {
                      value: 9,
                      message:
                        'Account number is invalid, please confirm banking details',
                    },
                    maxLength: {
                      value: 16,
                      message:
                        'Account number is invalid, please confirm banking details',
                    },
                  },
                  css: {
                    wrapper: {
                      gridColumn: 1,
                      gridRow: 2,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'single-select',
                  name: 'b_acc_type',
                  label: 'Account type',
                  labelProp: 'name',
                  valueProp: 'id',
                  // icon: 'alarm-clock',
                  fieldAccessPath: {
                    view: 'financial',
                    edit: 'financial',
                    special: 'financial',
                  },
                  position: 'right',
                  dropdownScroll: true,
                  validation: {
                    required: {
                      value: true,
                      message: 'Bank account type is required',
                    },
                  },
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.account_types',
                    labelProp: 'name',
                    valueProp: 'id',
                  },
                  css: {
                    wrapper: {
                      gridColumn: 2,
                      gridRow: 2,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'b_branch_name',
                  label: 'Branch name',
                  position: 'right',
                  validation: {
                    required: {
                      value: true,
                      message: 'Branch name is required',
                    },
                  },
                  fieldAccessPath: {
                    view: 'financial',
                    edit: 'financial',
                    special: 'financial',
                  },
                  css: {
                    wrapper: {
                      gridColumn: 1,
                      gridRow: 3,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'b_branch_code',
                  label: 'Branch code',
                  // icon: 'alarm-clock',
                  fieldAccessPath: {
                    view: 'financial',
                    edit: 'financial',
                    special: 'financial',
                  },
                  position: 'right',
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                    pattern: {
                      value: validationRegex.branch_code.pattern,
                      message: validationRegex.branch_code.message,
                    },
                  },
                  css: {
                    wrapper: {
                      gridColumn: 2,
                      gridRow: 3,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'drag-and-drop',
                  name: 'proof_of_bank_account',
                  label: 'Proof of Bank Account',
                  fileTypesAllowed: ['pdf', 'image'],
                  emptyText: 'No files uploaded',
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/settings/company/banking',
                  purpose: 'Reg Proof of bank account', // expected by file sent to server
                  list: 'False', //expected by file sent to server
                  iconLeft: 'notes',
                  fieldAccessKey: 'file',
                  fieldAccessPath: {
                    view: 'spdocument',
                    edit: 'spdocument',
                    special: 'spdocument',
                  },
                  css: {
                    wrapper: {
                      gridColumn: '1 / span 2',
                      gridRow: 4,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'vat_no',
                  label: 'VAT registration number',
                  fieldAccessPath: {
                    view: 'financial',
                    edit: 'financial',
                    special: 'financial',
                  },
                  position: 'right',
                  validation: {
                    pattern: {
                      value: validationRegex.vat_number.pattern,
                      message: validationRegex.vat_number.message,
                    },
                  },
                  css: {
                    wrapper: {
                      gridColumn: 1,
                      gridRow: 5,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'tax_no',
                  label: 'Tax registration number',
                  fieldAccessPath: {
                    view: 'financial',
                    edit: 'financial',
                    special: 'financial',
                  },
                  position: 'right',
                  validation: {
                    pattern: {
                      value: validationRegex.tax_number.pattern,
                      message: validationRegex.tax_number.message,
                    },
                  },
                  css: {
                    wrapper: {
                      gridColumn: 2,
                      gridRow: 5,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
      ],
      navs: [
        {
          label: 'Back To Teams',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store?.postData && (Object.keys($store.postData).length > 0) && $formState.isValid',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: 'You have unsaved changes. What would you like to do?',
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    actiontype: 'warning',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: [
                                          '/teams/manage-team/list-view',
                                          { clearParams: true },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    actiontype: 'proceed',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndFetch',
                                        payload: [
                                          {
                                            postData: '$postData',
                                          },
                                          {
                                            url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                            headers: {},
                                            bodySlicePath: 'postData',
                                          },
                                          {
                                            method: 'post',
                                            action: '/settings/company/banking',
                                          },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: [
                        '/teams/manage-team/list-view',
                        { clearParams: true },
                      ],
                    },
                  ],
                },
              },
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../company',
        },
        {
          label: 'Cancel Changes',
          position: 'center',
          disabledWhen:
            '$store?.postData && !(Object.keys($store.postData).length > 0)',
          onClick: [
            {
              type: 'clientAction',
              action: 'resetFields',
              payload: {
                fields: [
                  {
                    fieldName: 'trading_as',
                    defaultValue: '$sp_profile.details.trading_as',
                  },
                  {
                    fieldName: 'bbeee',
                    defaultValue: '$sp_profile.details.bbeee',
                  },
                  {
                    fieldName: 'b_acc_holder',
                    defaultValue: '$sp_profile.financial.b_acc_holder',
                  },
                  {
                    fieldName: 'b_branch_name',
                    defaultValue: '$sp_profile.financial.b_branch_name',
                  },
                  {
                    fieldName: 'b_acc_type',
                    defaultValue: '$sp_profile.financial.b_acc_type',
                  },
                  {
                    fieldName: 'b_branch_code',
                    defaultValue: '$sp_profile.financial.b_branch_code',
                  },
                  {
                    fieldName: 'b_bank_name',
                    defaultValue: '$sp_profile.financial.b_bank_name',
                  },
                  {
                    fieldName: 'b_acc_no',
                    defaultValue: '$sp_profile.financial.b_acc_no',
                  },
                  {
                    fieldName: 'vat_no',
                    defaultValue: '$sp_profile.financial.vat_no',
                  },
                  {
                    fieldName: 'tax_no',
                    defaultValue: '$sp_profile.financial.tax_no',
                  },
                  {
                    fieldName: 'contact_person',
                    defaultValue: '$sp_profile.address.contact_person',
                  },
                  {
                    fieldName: 'contact_primary',
                    defaultValue: '$sp_profile.address.contact_primary',
                  },
                  {
                    fieldName: 'contact_secondary',
                    defaultValue: '$sp_profile.address.contact_secondary',
                  },
                  {
                    fieldName: 'email_receiving',
                    defaultValue: '$sp_profile.address.email_receiving',
                  },
                  {
                    fieldName: 'physical_addr',
                    defaultValue: '$sp_profile.address.physical_addr',
                  },
                  {
                    fieldName: 'physical_city',
                    defaultValue: '$sp_profile.address.physical_city',
                  },
                  {
                    fieldName: 'physical_code',
                    defaultValue: '$sp_profile.address.physical_code',
                  },
                  {
                    fieldName: 'physical_suburb',
                    defaultValue: '$sp_profile.address.physical_suburb',
                  },
                  {
                    fieldName: 'province',
                    defaultValue: '$sp_profile.address.province',
                  },
                  {
                    fieldName: 'postal_box',
                    defaultValue: '$sp_profile.address.postal_box',
                  },
                  {
                    fieldName: 'postal_city',
                    defaultValue: '$sp_profile.address.postal_city',
                  },
                  {
                    fieldName: 'postal_code',
                    defaultValue: '$sp_profile.address.postal_code',
                  },
                  {
                    fieldName: 'skills',
                    defaultValue: '$sp_profile.skills',
                  },
                  {
                    fieldName: 'after_hours',
                    defaultValue: '$sp_profile.after_hours',
                  },
                  {
                    fieldName: 'companies',
                    defaultValue: '$derivedCompanies',
                  },
                  {
                    fieldName: 'accredition',
                    defaultValue:
                      '$sp_profile.additional_identities.accredition',
                  },
                  {
                    fieldName: 'mid',
                    defaultValue: '$sp_profile.additional_identities.mid',
                  },
                  {
                    fieldName: 'operational_area',
                    defaultValue: '$derivedOperationalArea',
                  },
                  {
                    fieldName: 'radius',
                    defaultValue: '$derivedOperationalArea.0.operating_range',
                  },
                  {
                    fieldName: 'jobLocation',
                    defaultValue: '$derivedOperationalArea.0.location',
                  },
                ],
              },
            },
          ],
        },
        {
          label: 'Save Changes',
          position: 'center',
          disabledWhen:
            '!$store?.postData || !(Object.keys($store.postData).length > 0) || !$formState.isValid || !$formState.isDirty',
          onClick: [
            {
              type: 'clientAction',
              action: 'submitAndFetch',
              payload: [
                {
                  postData: '$postData',
                },
                {
                  url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                  headers: {},
                  bodySlicePath: 'postData',
                  // redirect: '/settings/company/directors',
                },
                { method: 'post', action: '/settings/company/banking' },
              ],
            },
          ],
        },
        { label: 'Next', position: 'right', toScreen: '../directors' },
      ],
    },

    directors: {
      layout: {},
      fetchCalls: [
        {
          key: 'directors',
          method: 'POST',
          url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_directors',
          body: {},
          slicePath: 'payload',
        },
      ],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Company',
                path: '../company',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documents',
                path: '../documents',
              },
              {
                name: 'Subscription',
                path: '../subscription',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Enter all director names',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
              {
                text: 'Changing director names requires approval',
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'DirectorsList',
          props: {
            directors: '$directors',
            nameField: {
              name: 'director_name',
              placeholder: '',
              label: 'Name',
              fieldAccess: {
                key: 'name',
                path: {
                  edit: 'companydirector',
                  view: 'companydirector',
                  isSpecial: 'companydirector',
                },
              },
            },
            identityField: {
              name: 'director_identity',
              placeholder: '',
              label: 'Identity',
              fieldAccess: {
                key: 'identity',
                path: {
                  edit: 'companydirector',
                  view: 'companydirector',
                  isSpecial: 'companydirector',
                },
              },
            },
            identityFileField: {
              name: 'director_file',
              placeholder: 'No file uploaded',
              purpose: 'Identity',
              filesEndpoint: 'api/v1/file_actions/director_get_files',
              filesBaseUrlName: 'VITE_SP_SERVER',
              toggleButton: true,
              label: 'Copy of ID or passport document',
              fieldAccess: {
                key: 'file',
                path: {
                  edit: 'directordocument',
                  view: 'directordocument',
                  isSpecial: 'directordocument',
                },
              },
            },
            disabledWhen: true,
            filesUrl: '/api/v1/file_actions/director_get_files',
            removeDirectorUrl: '/api/v1/spaas_actions/remove_director',
            removeDirectorRedirectUrl: '/settings/company/directors',
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
      ],
      navs: [
        {
          label: 'Back To Teams',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$form.director_name && $form.director_identity && $form.director_file',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: 'You have unsaved changes. What would you like to do?',
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    actiontype: 'warning',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: [
                                          '/teams/manage-team/list-view',
                                          { clearParams: true },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    actiontype: 'proceed',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAsync',
                                        payload: {
                                          calls: [
                                            {
                                              key: 'add_director',
                                              url: '{VITE_SP_SERVER}/api/v1/spaas_actions/add_director',
                                              data: {
                                                name: '$director_name',
                                                identity: '$director_identity',
                                              },
                                            },
                                            {
                                              key: 'upload_director_file',
                                              url: '{VITE_SP_SERVER}/api/v1/file_actions/director_upload_file',
                                              data: {
                                                file: '$director_file',
                                                purpose: 'Identity',
                                                list: 'True',
                                                director_id:
                                                  '{add_director.payload.id}',
                                              },
                                            },
                                          ],
                                          onFinish: {
                                            type: 'clientAction',
                                            action: 'resetFields',
                                            payload: {
                                              fields: [
                                                'director_name',
                                                'director_identity',
                                                'director_file',
                                              ],
                                            },
                                          },
                                          redirect:
                                            '/settings/company/directors',
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: ['/teams'],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: [
                        '/teams/manage-team/list-view',
                        { clearParams: true },
                      ],
                    },
                  ],
                },
              },
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../banking',
        },
        {
          label: 'Cancel Changes',
          position: 'center',
          disabledWhen:
            '!$form.director_name || !$form.director_identity || !$form.director_file',
          onClick: [
            {
              type: 'clientAction',
              action: 'resetFields',
              payload: {
                fields: ['director_name', 'director_identity', 'director_file'],
              },
            },
          ],
        },
        {
          label: 'Save Changes',
          disabledWhen:
            '!$form.director_name || !$form.director_identity || !$form.director_file',
          position: 'center',
          onClick: [
            {
              type: 'clientAction',
              action: 'submitAsync',
              payload: {
                calls: [
                  {
                    key: 'add_director',
                    url: '{VITE_SP_SERVER}/api/v1/spaas_actions/add_director',
                    data: {
                      name: '$director_name',
                      identity: '$director_identity',
                    },
                  },
                  {
                    key: 'upload_director_file',
                    url: '{VITE_SP_SERVER}/api/v1/file_actions/director_upload_file',
                    data: {
                      file: '$director_file',
                      purpose: 'Identity',
                      list: 'True',
                      director_id: '{add_director.payload.id}',
                    },
                  },
                ],
                onFinish: {
                  type: 'clientAction',
                  action: 'resetFields',
                  payload: {
                    fields: [
                      'director_name',
                      'director_identity',
                      'director_file',
                    ],
                  },
                },
                redirect: '/settings/company/directors',
              },
            },
          ],
        },
        { label: 'Next', position: 'right', toScreen: '../contact' },
      ],
    },

    contact: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Company',
                path: '../company',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documents',
                path: '../documents',
              },
              {
                name: 'Subscription',
                path: '../subscription',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Company contact information',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            defaultValues: {
              contact_primary: '$sp_profile.address.contact_primary',
              contact_secondary: '$sp_profile.address.contact_secondary',
              contact_person: '$sp_profile.address.contact_person',
              email_receiving: '$sp_profile.address.email_receiving',
            },
            config: {
              style: {
                display: 'grid',
                gridTemplateColumns: 'repeat(2, 1fr)',
                gridTemplateRows: 'repeat(2, 1fr)',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'contact_person',
                  label: 'Primary Contact Person Name',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      gridColumn: 1,
                      gridRow: 1,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'contact_primary',
                  label: 'Primary Contact Number',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                    pattern: {
                      value: validationRegex.phone_number.pattern,
                      message: validationRegex.phone_number.message,
                    },
                  },
                  css: {
                    wrapper: {
                      gridColumn: 2,
                      gridRow: 1,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'contact_secondary',
                  label: 'Secondary Contact Number',
                  validation: {
                    pattern: {
                      value: validationRegex.phone_number.pattern,
                      message: validationRegex.phone_number.message,
                    },
                  },
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  css: {
                    wrapper: {
                      gridColumn: 1,
                      gridRow: 2,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'email_receiving',
                  label: 'Email Address',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                    pattern: {
                      value: validationRegex.email.pattern,
                      message: validationRegex.email.message,
                    },
                  },
                  css: {
                    wrapper: {
                      gridColumn: 2,
                      gridRow: 2,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
        {
          component: 'Separator',
          layout: { width: '50%', minWidth: '712px', margin: 'auto' },
          props: { height: 'thin' },
        },
        {
          component: 'FormBuilder',
          props: {
            defaultValues: {
              physical_addr: '$sp_profile.address.physical_addr',
              physical_city: '$sp_profile.address.physical_city',
              physical_code: '$sp_profile.address.physical_code',
              physical_suburb: '$sp_profile.address.physical_suburb',
              province: '$sp_profile.address.province',
            },
            config: {
              style: {
                display: 'grid',
                gridTemplateColumns: 'repeat(2, 1fr)',
                gridTemplateRows: 'repeat(3, 1fr)',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'physical_addr',
                  label: 'Company street address',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      gridColumn: 1,
                      gridRow: 1,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'physical_suburb',
                  label: 'Company suburb',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      gridColumn: 2,
                      gridRow: 1,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'physical_city',
                  label: 'Company city',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This is required',
                    },
                  },
                  css: {
                    wrapper: {
                      gridColumn: 1,
                      gridRow: 2,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'physical_code',
                  label: 'Company postal code',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This is required',
                    },
                  },
                  css: {
                    wrapper: {
                      gridColumn: 2,
                      gridRow: 2,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'single-select',
                  name: 'province',
                  label: 'Company province',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  dropdownScroll: true,
                  labelProp: 'name',
                  valueProp: 'name',
                  validation: {
                    required: {
                      value: true,
                      message: 'Province is required',
                    },
                  },
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.provinces',
                  },
                  css: {
                    wrapper: {
                      gridColumn: 1,
                      gridRow: 3,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
        {
          component: 'Separator',
          layout: { width: '50%', minWidth: '712px', margin: 'auto' },
          props: { height: 'thin' },
        },
        {
          component: 'FormBuilder',
          props: {
            defaultValues: {
              postal_box: '$sp_profile.address.postal_box',
              postal_city: '$sp_profile.address.postal_city',
              postal_code: '$sp_profile.address.postal_code',
            },
            config: {
              style: {
                display: 'grid',
                gridTemplateColumns: 'repeat(2, 1fr)',
                gridTemplateRows: 'repeat(2, 1fr)',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'postal_box',
                  label: 'Postal address',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  css: {
                    wrapper: {
                      gridColumn: 1,
                      gridRow: 1,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'postal_city',
                  label: 'Postal address suburb/town',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  css: {
                    wrapper: {
                      gridColumn: 2,
                      gridRow: 1,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'postal_code',
                  label: 'Postal address postal code',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      gridColumn: 1,
                      gridRow: 2,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
      ],
      navs: [
        {
          label: 'Back To Teams',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store?.postData && (Object.keys($store.postData).length > 0) && $formState.isValid',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: 'You have unsaved changes. What would you like to do?',
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    actiontype: 'warning',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: [
                                          '/teams/manage-team/list-view',
                                          { clearParams: true },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    actiontype: 'proceed',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndFetch',
                                        payload: [
                                          {
                                            postData: '$postData',
                                          },
                                          {
                                            url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                            headers: {},
                                            bodySlicePath: 'postData',
                                            redirect: '/teams',
                                          },
                                          {
                                            method: 'post',
                                            action: '/settings/company/contact',
                                          },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: [
                        '/teams/manage-team/list-view',
                        { clearParams: true },
                      ],
                    },
                  ],
                },
              },
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../directors',
        },
        {
          label: 'Cancel Changes',
          position: 'center',
          disabledWhen:
            '$store?.postData && !(Object.keys($store.postData).length > 0)',
          onClick: [
            {
              type: 'clientAction',
              action: 'resetFields',
              payload: {
                fields: [
                  {
                    fieldName: 'trading_as',
                    defaultValue: '$sp_profile.details.trading_as',
                  },
                  {
                    fieldName: 'bbeee',
                    defaultValue: '$sp_profile.details.bbeee',
                  },
                  {
                    fieldName: 'b_acc_holder',
                    defaultValue: '$sp_profile.financial.b_acc_holder',
                  },
                  {
                    fieldName: 'b_branch_name',
                    defaultValue: '$sp_profile.financial.b_branch_name',
                  },
                  {
                    fieldName: 'b_acc_type',
                    defaultValue: '$sp_profile.financial.b_acc_type',
                  },
                  {
                    fieldName: 'b_branch_code',
                    defaultValue: '$sp_profile.financial.b_branch_code',
                  },
                  {
                    fieldName: 'b_bank_name',
                    defaultValue: '$sp_profile.financial.b_bank_name',
                  },
                  {
                    fieldName: 'b_acc_no',
                    defaultValue: '$sp_profile.financial.b_acc_no',
                  },
                  {
                    fieldName: 'vat_no',
                    defaultValue: '$sp_profile.financial.vat_no',
                  },
                  {
                    fieldName: 'tax_no',
                    defaultValue: '$sp_profile.financial.tax_no',
                  },
                  {
                    fieldName: 'contact_person',
                    defaultValue: '$sp_profile.address.contact_person',
                  },
                  {
                    fieldName: 'contact_primary',
                    defaultValue: '$sp_profile.address.contact_primary',
                  },
                  {
                    fieldName: 'contact_secondary',
                    defaultValue: '$sp_profile.address.contact_secondary',
                  },
                  {
                    fieldName: 'email_receiving',
                    defaultValue: '$sp_profile.address.email_receiving',
                  },
                  {
                    fieldName: 'physical_addr',
                    defaultValue: '$sp_profile.address.physical_addr',
                  },
                  {
                    fieldName: 'physical_city',
                    defaultValue: '$sp_profile.address.physical_city',
                  },
                  {
                    fieldName: 'physical_code',
                    defaultValue: '$sp_profile.address.physical_code',
                  },
                  {
                    fieldName: 'physical_suburb',
                    defaultValue: '$sp_profile.address.physical_suburb',
                  },
                  {
                    fieldName: 'province',
                    defaultValue: '$sp_profile.address.province',
                  },
                  {
                    fieldName: 'postal_box',
                    defaultValue: '$sp_profile.address.postal_box',
                  },
                  {
                    fieldName: 'postal_city',
                    defaultValue: '$sp_profile.address.postal_city',
                  },
                  {
                    fieldName: 'postal_code',
                    defaultValue: '$sp_profile.address.postal_code',
                  },
                  {
                    fieldName: 'skills',
                    defaultValue: '$sp_profile.skills',
                  },
                  {
                    fieldName: 'after_hours',
                    defaultValue: '$sp_profile.after_hours',
                  },
                  {
                    fieldName: 'companies',
                    defaultValue: '$derivedCompanies',
                  },
                  {
                    fieldName: 'accredition',
                    defaultValue:
                      '$sp_profile.additional_identities.accredition',
                  },
                  {
                    fieldName: 'mid',
                    defaultValue: '$sp_profile.additional_identities.mid',
                  },
                  {
                    fieldName: 'operational_area',
                    defaultValue: '$derivedOperationalArea',
                  },
                  {
                    fieldName: 'radius',
                    defaultValue: '$derivedOperationalArea.0.operating_range',
                  },
                  {
                    fieldName: 'jobLocation',
                    defaultValue: '$derivedOperationalArea.0.location',
                  },
                ],
              },
            },
          ],
        },
        {
          label: 'Save Changes',
          position: 'center',
          disabledWhen:
            '!$store?.postData || !(Object.keys($store.postData).length > 0) || !$formState.isValid || !$formState.isDirty',
          onClick: [
            {
              type: 'clientAction',
              action: 'submitAndFetch',
              payload: [
                {
                  postData: '$postData',
                },
                {
                  url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                  headers: {},
                  bodySlicePath: 'postData',
                  // redirect: '/settings/company/work',
                },
                { method: 'post', action: '/settings/company/contact' },
              ],
            },
          ],
        },
        { label: 'Next', position: 'right', toScreen: '../work' },
      ],
    },

    work: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Company',
                path: '../company',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documents',
                path: '../documents',
              },
              {
                name: 'Subscription',
                path: '../subscription',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Scope of work you accept',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            defaultValues: {
              skills: '$sp_profile.skills',
              companies: '$sp_profile.companies',
              after_hours: '$sp_profile.after_hours',
            },
            config: {
              style: {
                display: 'grid',
                gridTemplateColumns: 'repeat(2, 1fr)',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'multi-select',
                  name: 'skills',
                  label: 'Company skills',
                  fieldAccessPath: {
                    view: 'skills',
                    edit: 'skills',
                    special: 'skills',
                  },
                  labelProp: 'name',
                  valueProp: 'id',
                  position: 'right',
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.skills',
                  },
                  multiSelect: true,
                  dropdownScroll: true,
                  css: {
                    wrapper: {
                      gridColumn: 1,
                      gridRow: 1,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'radio-group',
                  name: 'after_hours',
                  label: 'Do you work after hours?',
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  fieldAccessPath: {
                    view: 'afterhours',
                    edit: 'afterhours',
                    special: 'afterhours',
                  },
                  options: {
                    source: 'literal',
                    data: [
                      { label: 'Yes', value: true },
                      { label: 'No', value: false },
                    ],
                  },
                  returnBoolean: true,
                  size: 'small',
                  columns: 6,
                  css: {
                    wrapper: {
                      gridColumn: 2,
                      gridRow: 1,
                      justifySelf: 'start',
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'multi-select',
                  name: 'companies',
                  label: 'Companies you would like to recieve work from',
                  labelProp: 'name',
                  valueProp: 'id',
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.companies',
                  },
                  position: 'right',
                  multiSelect: true,
                  dropdownScroll: true,
                  dataTransformMapField: 'client_id',
                  fieldAccessPath: {
                    view: 'activeregionsbyclient',
                    edit: 'serviceproviderclient',
                    special: 'serviceproviderclient',
                  },
                  fieldAccessKey: 'client',
                  css: {
                    wrapper: {
                      gridColumn: 1,
                      gridRow: 2,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
      ],
      navs: [
        {
          label: 'Back To Teams',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store?.postData && (Object.keys($store.postData).length > 0) && $formState.isValid',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: 'You have unsaved changes. What would you like to do?',
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    actiontype: 'warning',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: [
                                          '/teams/manage-team/list-view',
                                          { clearParams: true },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    actiontype: 'proceed',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndFetch',
                                        payload: [
                                          {
                                            postData: '$postData',
                                            sp_id: '$sp_profile.id',
                                          },
                                          {
                                            url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                            headers: {},
                                            bodySlicePath: 'postData',
                                          },
                                          {
                                            method: 'post',
                                            action: '/settings/company/areas',
                                          },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: [
                                          '/teams/manage-team/list-view',
                                          { clearParams: true },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: [
                        '/teams/manage-team/list-view',
                        { clearParams: true },
                      ],
                    },
                  ],
                },
              },
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../contact',
        },
        {
          label: 'Cancel Changes',
          position: 'center',
          disabledWhen: `$store?.postData && !(Object.keys($store.postData).length > 0)`,
          onClick: [
            {
              type: 'clientAction',
              action: 'resetFields',
              payload: {
                fields: [
                  {
                    fieldName: 'trading_as',
                    defaultValue: '$sp_profile.details.trading_as',
                  },
                  {
                    fieldName: 'bbeee',
                    defaultValue: '$sp_profile.details.bbeee',
                  },
                  {
                    fieldName: 'b_acc_holder',
                    defaultValue: '$sp_profile.financial.b_acc_holder',
                  },
                  {
                    fieldName: 'b_branch_name',
                    defaultValue: '$sp_profile.financial.b_branch_name',
                  },
                  {
                    fieldName: 'b_acc_type',
                    defaultValue: '$sp_profile.financial.b_acc_type',
                  },
                  {
                    fieldName: 'b_branch_code',
                    defaultValue: '$sp_profile.financial.b_branch_code',
                  },
                  {
                    fieldName: 'b_bank_name',
                    defaultValue: '$sp_profile.financial.b_bank_name',
                  },
                  {
                    fieldName: 'b_acc_no',
                    defaultValue: '$sp_profile.financial.b_acc_no',
                  },
                  {
                    fieldName: 'vat_no',
                    defaultValue: '$sp_profile.financial.vat_no',
                  },
                  {
                    fieldName: 'tax_no',
                    defaultValue: '$sp_profile.financial.tax_no',
                  },
                  {
                    fieldName: 'contact_person',
                    defaultValue: '$sp_profile.address.contact_person',
                  },
                  {
                    fieldName: 'contact_primary',
                    defaultValue: '$sp_profile.address.contact_primary',
                  },
                  {
                    fieldName: 'contact_secondary',
                    defaultValue: '$sp_profile.address.contact_secondary',
                  },
                  {
                    fieldName: 'email_receiving',
                    defaultValue: '$sp_profile.address.email_receiving',
                  },
                  {
                    fieldName: 'physical_addr',
                    defaultValue: '$sp_profile.address.physical_addr',
                  },
                  {
                    fieldName: 'physical_city',
                    defaultValue: '$sp_profile.address.physical_city',
                  },
                  {
                    fieldName: 'physical_code',
                    defaultValue: '$sp_profile.address.physical_code',
                  },
                  {
                    fieldName: 'physical_suburb',
                    defaultValue: '$sp_profile.address.physical_suburb',
                  },
                  {
                    fieldName: 'province',
                    defaultValue: '$sp_profile.address.province',
                  },
                  {
                    fieldName: 'postal_box',
                    defaultValue: '$sp_profile.address.postal_box',
                  },
                  {
                    fieldName: 'postal_city',
                    defaultValue: '$sp_profile.address.postal_city',
                  },
                  {
                    fieldName: 'postal_code',
                    defaultValue: '$sp_profile.address.postal_code',
                  },
                  {
                    fieldName: 'skills',
                    defaultValue: '$sp_profile.skills',
                  },
                  {
                    fieldName: 'after_hours',
                    defaultValue: '$sp_profile.after_hours',
                  },
                  {
                    fieldName: 'companies',
                    defaultValue: '$derivedCompanies',
                  },
                  {
                    fieldName: 'accredition',
                    defaultValue:
                      '$sp_profile.additional_identities.accredition',
                  },
                  {
                    fieldName: 'mid',
                    defaultValue: '$sp_profile.additional_identities.mid',
                  },
                  {
                    fieldName: 'operational_area',
                    defaultValue: '$derivedOperationalArea',
                  },
                  {
                    fieldName: 'radius',
                    defaultValue: '$derivedOperationalArea.0.operating_range',
                  },
                  {
                    fieldName: 'jobLocation',
                    defaultValue: '$derivedOperationalArea.0.location',
                  },
                ],
              },
            },
          ],
        },
        {
          label: 'Save Changes',
          position: 'center',
          disabledWhen:
            '!$store?.postData || !(Object.keys($store.postData).length > 0)',
          onClick: [
            {
              type: 'clientAction',
              action: 'submitAndFetch',
              payload: [
                {
                  postData: '$postData',
                },
                {
                  url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                  headers: {},
                  bodySlicePath: 'postData',
                },
                { method: 'post', action: '/settings/company/work' },
              ],
            },
          ],
        },
        { label: 'Next', position: 'right', toScreen: '../areas' },
      ],
    },

    areas: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Company',
                path: '../company',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documents',
                path: '../documents',
              },
              {
                name: 'Subscription',
                path: '../subscription',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'FormBuilder',
          layout: {
            paddingTop: '2rem',
          },
          props: {
            operational_area: '$sp_profile.operational_area',
            defaultValues: {
              // operational_area: '$sp_profile.operational_area',
              radius: '$sp_profile.operational_area.0.operating_range',
              jobLocation: '$sp_profile.operational_area.0.location',
            },
            config: {
              style: {
                display: 'grid',
                gridTemplateColumns: '1fr',
                rowGap: '15px',
                justifyItems: 'center',
              },
              controls: [
                {
                  type: 'radius', // Custom control type for OperationAreas
                  name: 'operational_area',
                  label: 'Operational Area',
                  instructions: 'Drag the pin to adjust your work area radius',
                  fieldAccessPath: {
                    view: 'operational_area',
                    edit: 'operational_area',
                    special: 'operational_area',
                  },
                  fieldAccessKey: 'location',
                  marks: [
                    { value: 25, label: '25km' },
                    { value: 50, label: '50km' },
                    { value: 75, label: '75km' },
                    { value: 100, label: '100km' },
                    { value: 150, label: '150km' },
                    { value: 200, label: '200km' },
                    { value: 250, label: '250km' },
                  ],
                  css: { wrapper: { gridColumn: '1', gridRow: '1' } },
                },
              ],
            },
          },
        },
      ],
      navs: [
        {
          label: 'Back To Teams',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '!$formState.isDirty || !$formState.isValid || !(Object.keys($formState.touchedFields).length > 0)',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: 'You have unsaved changes. What would you like to do?',
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    actiontype: 'warning',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: [
                                          '/teams/manage-team/list-view',
                                          { clearParams: true },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    actiontype: 'proceed',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndNavigate',
                                        payload: [
                                          {
                                            operational_area:
                                              '$formDataRaw.operational_area',
                                          },
                                          {
                                            url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                            headers: {},
                                            redirect:
                                              '/teams/manage-team/list-view',
                                          },
                                          {
                                            method: 'post',
                                            action: '/settings/company/areas',
                                          },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: [
                        '/teams/manage-team/list-view',
                        { clearParams: true },
                      ],
                    },
                  ],
                },
              },
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'resetFields',
              payload: {
                fields: [
                  {
                    fieldName: 'operational_area',
                    defaultValue: '$derivedOperationalArea',
                  },
                  {
                    fieldName: 'radius',
                    defaultValue: '$derivedOperationalArea.0.operating_range',
                  },
                  {
                    fieldName: 'jobLocation',
                    defaultValue: '$derivedOperationalArea.0.location',
                  },
                ],
              },
            },
            {
              type: 'clientAction',
              action: 'navigate',
              payload: ['/settings/company/work'],
            },
          ],
        },
        {
          label: 'Cancel Changes',
          position: 'center',
          disabledWhen: 'true',
          onClick: [
            {
              type: 'clientAction',
              action: 'resetFields',
              payload: {
                fields: [
                  {
                    fieldName: 'trading_as',
                    defaultValue: '$sp_profile.details.trading_as',
                  },
                  {
                    fieldName: 'bbeee',
                    defaultValue: '$sp_profile.details.bbeee',
                  },
                  {
                    fieldName: 'b_acc_holder',
                    defaultValue: '$sp_profile.financial.b_acc_holder',
                  },
                  {
                    fieldName: 'b_branch_name',
                    defaultValue: '$sp_profile.financial.b_branch_name',
                  },
                  {
                    fieldName: 'b_acc_type',
                    defaultValue: '$sp_profile.financial.b_acc_type',
                  },
                  {
                    fieldName: 'b_branch_code',
                    defaultValue: '$sp_profile.financial.b_branch_code',
                  },
                  {
                    fieldName: 'b_bank_name',
                    defaultValue: '$sp_profile.financial.b_bank_name',
                  },
                  {
                    fieldName: 'b_acc_no',
                    defaultValue: '$sp_profile.financial.b_acc_no',
                  },
                  {
                    fieldName: 'vat_no',
                    defaultValue: '$sp_profile.financial.vat_no',
                  },
                  {
                    fieldName: 'tax_no',
                    defaultValue: '$sp_profile.financial.tax_no',
                  },
                  {
                    fieldName: 'contact_person',
                    defaultValue: '$sp_profile.address.contact_person',
                  },
                  {
                    fieldName: 'contact_primary',
                    defaultValue: '$sp_profile.address.contact_primary',
                  },
                  {
                    fieldName: 'contact_secondary',
                    defaultValue: '$sp_profile.address.contact_secondary',
                  },
                  {
                    fieldName: 'email_receiving',
                    defaultValue: '$sp_profile.address.email_receiving',
                  },
                  {
                    fieldName: 'physical_addr',
                    defaultValue: '$sp_profile.address.physical_addr',
                  },
                  {
                    fieldName: 'physical_city',
                    defaultValue: '$sp_profile.address.physical_city',
                  },
                  {
                    fieldName: 'physical_code',
                    defaultValue: '$sp_profile.address.physical_code',
                  },
                  {
                    fieldName: 'physical_suburb',
                    defaultValue: '$sp_profile.address.physical_suburb',
                  },
                  {
                    fieldName: 'province',
                    defaultValue: '$sp_profile.address.province',
                  },
                  {
                    fieldName: 'postal_box',
                    defaultValue: '$sp_profile.address.postal_box',
                  },
                  {
                    fieldName: 'postal_city',
                    defaultValue: '$sp_profile.address.postal_city',
                  },
                  {
                    fieldName: 'postal_code',
                    defaultValue: '$sp_profile.address.postal_code',
                  },
                  {
                    fieldName: 'skills',
                    defaultValue: '$sp_profile.skills',
                  },
                  {
                    fieldName: 'after_hours',
                    defaultValue: '$sp_profile.after_hours',
                  },
                  {
                    fieldName: 'companies',
                    defaultValue: '$derivedCompanies',
                  },
                  {
                    fieldName: 'accredition',
                    defaultValue:
                      '$sp_profile.additional_identities.accredition',
                  },
                  {
                    fieldName: 'mid',
                    defaultValue: '$sp_profile.additional_identities.mid',
                  },
                  {
                    fieldName: 'operational_area',
                    defaultValue: '$derivedOperationalArea',
                  },
                  {
                    fieldName: 'radius',
                    defaultValue: '$derivedOperationalArea.0.operating_range',
                  },
                  {
                    fieldName: 'jobLocation',
                    defaultValue: '$derivedOperationalArea.0.location',
                  },
                ],
              },
            },
            {
              type: 'clientAction',
              action: 'clearStore',
              payload: ['updatedOperationalArea'],
            },
          ],
        },
        {
          label: 'Save Changes',
          position: 'center',
          disabledWhen:
            '$store?.postData || !(Object.keys($store.postData).length > 0)',
          onClick: [
            {
              type: 'clientAction',
              action: 'submitAndFetch',
              payload: [
                {
                  postData: '$postData',
                },
                {
                  url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                  headers: {},
                  bodySlicePath: 'postData',
                },
                { method: 'post', action: '/settings/company/areas' },
              ],
            },
          ],
        },
        {
          label: 'Next',
          position: 'right',
          onClick: [
            {
              type: 'clientAction',
              action: 'resetFields',
              payload: {
                fields: [
                  {
                    fieldName: 'operational_area',
                    defaultValue: '$derivedOperationalArea',
                  },
                  {
                    fieldName: 'radius',
                    defaultValue: '$derivedOperationalArea.0.operating_range',
                  },
                  {
                    fieldName: 'jobLocation',
                    defaultValue: '$derivedOperationalArea.0.location',
                  },
                ],
              },
            },
            {
              type: 'clientAction',
              action: 'navigate',
              payload: ['/settings/company/documents'],
            },
          ],
        },
      ],
    },

    documents: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Company',
                path: '../company',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documents',
                path: '../documents',
              },
              {
                name: 'Subscription',
                path: '../subscription',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Uploaded documents',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
              {
                text: 'PDF only. Maximum 5Mb per document',
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            co_reg_document:
              "$company_files?findLast:item.purpose === 'Reg Company registration'",
            pub_profile_document:
              "$company_files?findLast:item.purpose === 'Reg Public profile'",
            pub_liability_document:
              "$company_files?findLast:item.purpose === 'Reg Public liability insurance'",
            bbeee_cert_document:
              "$company_files?findLast:item.purpose === 'Reg BBEEE certificate'",
            sars_tax_document:
              "$company_files?findLast:item.purpose === 'Reg SARS Tax certificate'",
            proof_of_bank_account_document:
              "$company_files?findLast:item.purpose === 'Reg Proof of bank account'",
            vehicle_document:
              "$company_files?findLast:item.purpose === 'Reg Vehicle picture'",
            office_document:
              "$company_files?findLast:item.purpose === 'Reg Office picture'",
            staff_uniform_document:
              "$company_files?findLast:item.purpose === 'Reg Staff uniform picture'",
            config: {
              style: {
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'document-card',
                  name: 'co_reg_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Company registration',
                  purposeAsName: true,
                  isLandscape: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  base_url: 'VITE_SP_SERVER',
                  action: '/settings/company/documents',
                  list: 'False',
                  fieldAccessKey: 'file',
                  fieldAccessPath: {
                    view: 'spdocument',
                    edit: 'spdocument',
                    special: 'spdocument',
                  },
                  css: {
                    wrapper: {},
                  },
                },
                {
                  type: 'document-card',
                  name: 'pub_profile_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Public profile',
                  purposeAsName: true,
                  isLandscape: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  base_url: 'VITE_SP_SERVER',
                  action: '/settings/company/documents',
                  list: 'False',
                  fieldAccessKey: 'file',
                  fieldAccessPath: {
                    view: 'spdocument',
                    edit: 'spdocument',
                    special: 'spdocument',
                  },
                  css: {
                    wrapper: {},
                  },
                },
                {
                  type: 'document-card',
                  name: 'pub_liability_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Public liability insurance',
                  purposeAsName: true,
                  isLandscape: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  base_url: 'VITE_SP_SERVER',
                  action: '/settings/company/documents',
                  list: 'False',
                  fieldAccessKey: 'file',
                  fieldAccessPath: {
                    view: 'spdocument',
                    edit: 'spdocument',
                    special: 'spdocument',
                  },
                  css: {
                    wrapper: {},
                  },
                },
                {
                  type: 'document-card',
                  name: 'bbeee_cert_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg BBEEE certificate',
                  purposeAsName: true,
                  isLandscape: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  base_url: 'VITE_SP_SERVER',
                  action: '/settings/company/documents',
                  list: 'False',
                  fieldAccessKey: 'file',
                  fieldAccessPath: {
                    view: 'spdocument',
                    edit: 'spdocument',
                    special: 'spdocument',
                  },
                  css: {
                    wrapper: {},
                  },
                },
                {
                  type: 'document-card',
                  name: 'sars_tax_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg SARS Tax certificate',
                  purposeAsName: true,
                  isLandscape: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  base_url: 'VITE_SP_SERVER',
                  action: '/settings/company/documents',
                  list: 'False',
                  fieldAccessKey: 'file',
                  fieldAccessPath: {
                    view: 'spdocument',
                    edit: 'spdocument',
                    special: 'spdocument',
                  },
                  css: {
                    wrapper: {},
                  },
                },
                {
                  type: 'document-card',
                  name: 'proof_of_bank_account_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Proof of bank account',
                  purposeAsName: true,
                  isLandscape: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  base_url: 'VITE_SP_SERVER',
                  action: '/settings/company/documents',
                  list: 'False',
                  fieldAccessKey: 'file',
                  fieldAccessPath: {
                    view: 'spdocument',
                    edit: 'spdocument',
                    special: 'spdocument',
                  },
                  css: {
                    wrapper: {},
                  },
                },
                {
                  type: 'document-card',
                  name: 'vehicle_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Vehicle picture',
                  purposeAsName: true,
                  isLandscape: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  base_url: 'VITE_SP_SERVER',
                  action: '/settings/company/documents',
                  list: 'False',
                  fieldAccessKey: 'file',
                  fieldAccessPath: {
                    view: 'spdocument',
                    edit: 'spdocument',
                    special: 'spdocument',
                  },
                  css: {
                    wrapper: {},
                  },
                },
                {
                  type: 'document-card',
                  name: 'office_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Office picture',
                  purposeAsName: true,
                  isLandscape: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  base_url: 'VITE_SP_SERVER',
                  action: '/settings/company/documents',
                  list: 'False',
                  fieldAccessKey: 'file',
                  fieldAccessPath: {
                    view: 'spdocument',
                    edit: 'spdocument',
                    special: 'spdocument',
                  },
                  css: {
                    wrapper: {},
                  },
                },
                {
                  type: 'document-card',
                  name: 'staff_uniform_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Staff uniform picture',
                  purposeAsName: true,
                  isLandscape: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  base_url: 'VITE_SP_SERVER',
                  action: '/settings/company/documents',
                  list: 'False',
                  fieldAccessKey: 'file',
                  fieldAccessPath: {
                    view: 'spdocument',
                    edit: 'spdocument',
                    special: 'spdocument',
                  },
                  css: {
                    wrapper: {},
                  },
                },
              ],
            },
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
          },
        },
      ],
      navs: [
        {
          label: 'Back To Teams',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '!$formState.isDirty || !$formState.isValid || !(Object.keys($formState.touchedFields).length > 0)',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: 'You have unsaved changes. What would you like to do?',
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    actiontype: 'warning',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: [
                                          '/teams/manage-team/list-view',
                                          { clearParams: true },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    actiontype: 'proceed',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndNavigate',
                                        payload: [
                                          {
                                            operational_area:
                                              '$formDataRaw.operational_area',
                                          },
                                          {
                                            url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                            headers: {},
                                            redirect:
                                              '/teams/manage-team/list-view',
                                          },
                                          {
                                            method: 'post',
                                            action: '/settings/company/areas',
                                          },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: [
                        '/teams/manage-team/list-view',
                        { clearParams: true },
                      ],
                    },
                  ],
                },
              },
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../areas',
        },
        {
          label: 'Cancel Changes',
          position: 'center',
          disabledWhen:
            '($store?.postData && !(Object.keys($store.postData).length > 0)) && !$store.updatedOperationalArea',
          onClick: [
            {
              type: 'clientAction',
              action: 'resetFields',
              payload: {
                fields: [
                  {
                    fieldName: 'trading_as',
                    defaultValue: '$sp_profile.details.trading_as',
                  },
                  {
                    fieldName: 'bbeee',
                    defaultValue: '$sp_profile.details.bbeee',
                  },
                  {
                    fieldName: 'b_acc_holder',
                    defaultValue: '$sp_profile.financial.b_acc_holder',
                  },
                  {
                    fieldName: 'b_branch_name',
                    defaultValue: '$sp_profile.financial.b_branch_name',
                  },
                  {
                    fieldName: 'b_acc_type',
                    defaultValue: '$sp_profile.financial.b_acc_type',
                  },
                  {
                    fieldName: 'b_branch_code',
                    defaultValue: '$sp_profile.financial.b_branch_code',
                  },
                  {
                    fieldName: 'b_bank_name',
                    defaultValue: '$sp_profile.financial.b_bank_name',
                  },
                  {
                    fieldName: 'b_acc_no',
                    defaultValue: '$sp_profile.financial.b_acc_no',
                  },
                  {
                    fieldName: 'vat_no',
                    defaultValue: '$sp_profile.financial.vat_no',
                  },
                  {
                    fieldName: 'tax_no',
                    defaultValue: '$sp_profile.financial.tax_no',
                  },
                  {
                    fieldName: 'contact_person',
                    defaultValue: '$sp_profile.address.contact_person',
                  },
                  {
                    fieldName: 'contact_primary',
                    defaultValue: '$sp_profile.address.contact_primary',
                  },
                  {
                    fieldName: 'contact_secondary',
                    defaultValue: '$sp_profile.address.contact_secondary',
                  },
                  {
                    fieldName: 'email_receiving',
                    defaultValue: '$sp_profile.address.email_receiving',
                  },
                  {
                    fieldName: 'physical_addr',
                    defaultValue: '$sp_profile.address.physical_addr',
                  },
                  {
                    fieldName: 'physical_city',
                    defaultValue: '$sp_profile.address.physical_city',
                  },
                  {
                    fieldName: 'physical_code',
                    defaultValue: '$sp_profile.address.physical_code',
                  },
                  {
                    fieldName: 'physical_suburb',
                    defaultValue: '$sp_profile.address.physical_suburb',
                  },
                  {
                    fieldName: 'province',
                    defaultValue: '$sp_profile.address.province',
                  },
                  {
                    fieldName: 'postal_box',
                    defaultValue: '$sp_profile.address.postal_box',
                  },
                  {
                    fieldName: 'postal_city',
                    defaultValue: '$sp_profile.address.postal_city',
                  },
                  {
                    fieldName: 'postal_code',
                    defaultValue: '$sp_profile.address.postal_code',
                  },
                  {
                    fieldName: 'skills',
                    defaultValue: '$sp_profile.skills',
                  },
                  {
                    fieldName: 'after_hours',
                    defaultValue: '$sp_profile.after_hours',
                  },
                  {
                    fieldName: 'companies',
                    defaultValue: '$derivedCompanies',
                  },
                  {
                    fieldName: 'accredition',
                    defaultValue:
                      '$sp_profile.additional_identities.accredition',
                  },
                  {
                    fieldName: 'mid',
                    defaultValue: '$sp_profile.additional_identities.mid',
                  },
                  {
                    fieldName: 'operational_area',
                    defaultValue: '$derivedOperationalArea',
                  },
                  {
                    fieldName: 'radius',
                    defaultValue: '$derivedOperationalArea.0.operating_range',
                  },
                  {
                    fieldName: 'jobLocation',
                    defaultValue: '$derivedOperationalArea.0.location',
                  },
                ],
              },
            },
            {
              type: 'clientAction',
              action: 'clearStore',
              payload: ['updatedOperationalArea'],
            },
          ],
        },
        { label: 'Next', position: 'right', toScreen: '../subscription' },
      ],
    },

    subscription: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Company',
                path: '../company',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documents',
                path: '../documents',
              },
              {
                name: 'Subscription',
                path: '../subscription',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'FormBuilder',
          layout: {
            paddingTop: '2rem',
          },
          props: {
            defaultValues: {
              subscriptionConfirmation:
                '$sp_profile.details.additional.subscriptionData.subscriptionConfirmation',
            },
            config: {
              style: {
                display: 'grid',
                gridTemplateColumns: '1fr',
                rowGap: '15px',
                justifyItems: 'center',
              },
              controls: [
                {
                  type: 'subscription-agreement', // Custom control type for OperationAreas
                  name: 'subscriptionConfirmation',
                  checked:
                    '$sp_profile.details.additional.subscriptionData.subscriptionConfirmation',
                  canChange: false,
                  css: { wrapper: { gridColumn: '1', gridRow: '1' } },
                },
              ],
            },
          },
        },
      ],
      navs: [
        {
          label: 'Back To Teams',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '!$formState.isDirty || !$formState.isValid || !(Object.keys($formState.touchedFields).length > 0)',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: 'You have unsaved changes. What would you like to do?',
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    actiontype: 'warning',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: [
                                          '/teams/manage-team/list-view',
                                          { clearParams: true },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    actiontype: 'proceed',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndNavigate',
                                        payload: [
                                          {
                                            operational_area:
                                              '$formDataRaw.operational_area',
                                          },
                                          {
                                            url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                            headers: {},
                                            redirect:
                                              '/teams/manage-team/list-view',
                                          },
                                          {
                                            method: 'post',
                                            action: '/settings/company/areas',
                                          },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: [
                        '/teams/manage-team/list-view',
                        { clearParams: true },
                      ],
                    },
                  ],
                },
              },
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'resetFields',
              payload: {
                fields: [
                  {
                    fieldName: 'operational_area',
                    defaultValue: '$derivedOperationalArea',
                  },
                  {
                    fieldName: 'radius',
                    defaultValue: '$derivedOperationalArea.0.operating_range',
                  },
                  {
                    fieldName: 'jobLocation',
                    defaultValue: '$derivedOperationalArea.0.location',
                  },
                ],
              },
            },
            {
              type: 'clientAction',
              action: 'navigate',
              payload: ['/settings/company/documents'],
            },
          ],
        },
      ],
    },
  },
  onLeave: [
    {
      type: 'clientAction',
      action: 'clearStore',
      payload: ['formDataRaw', 'postData'],
    },
  ],
  formOriginalValues: {
    // Orignals for diffing to find changed fields (from clientDataObject to store)
    'sp_profile.details.trading_as': 'trading_as',
    'sp_profile.details.bbeee': 'bbeee',
    'sp_profile.details.additional.subscriptionData.subscriptionConfirmation':
      'subscriptionConfirmation',

    'sp_profile.financial.vat_registered': 'vat_registered',
    'sp_profile.financial.vat_no': 'vat_no',
    'sp_profile.financial.tax_no': 'tax_no',
    'sp_profile.financial.b_acc_holder': 'b_acc_holder',
    'sp_profile.financial.b_acc_type': 'b_acc_type',
    'sp_profile.financial.b_acc_no': 'b_acc_no',
    'sp_profile.financial.b_bank_name': 'b_bank_name',
    'sp_profile.financial.b_branch_name': 'b_branch_name',
    'sp_profile.financial.b_branch_code': 'b_branch_code',

    'sp_profile.address.contact_primary': 'contact_primary',
    'sp_profile.address.contact_secondary': 'contact_secondary',
    'sp_profile.address.email_receiving': 'email_receiving',
    'sp_profile.address.physical_addr': 'physical_addr',
    'sp_profile.address.physical_city': 'physical_city',
    'sp_profile.address.physical_suburb': 'physical_suburb',
    'sp_profile.address.physical_code': 'physical_code',
    'sp_profile.address.postal_box': 'postal_box',
    'sp_profile.address.postal_city': 'postal_city',
    'sp_profile.address.postal_code': 'postal_code',
    'sp_profile.address.province': 'province',
    'sp_profile.address.contact_person': 'contact_person',

    'sp_profile.skills': 'skills',
    derivedCompanies: 'companies',
    'sp_profile.after_hours': 'after_hours',
    'sp_profile.mid': 'mid',
    'sp_profile.accredition': 'accredition',

    derivedOperationalArea: 'operational_area',
  },
  formTransformMapper: {
    // Actual mapper to get final object shaped for server
    trading_as: 'details.trading_as',
    bbeee: 'details.bbeee',

    vat_registered: 'financial.vat_registered',
    vat_no: 'financial.vat_no',
    tax_no: 'financial.tax_no',
    b_acc_holder: 'financial.b_acc_holder',
    b_acc_type: 'financial.b_acc_type',
    b_acc_no: 'financial.b_acc_no',
    b_bank_name: 'financial.b_bank_name',
    b_branch_name: 'financial.b_branch_name',
    b_branch_code: 'financial.b_branch_code',

    contact_primary: 'address.contact_primary',
    contact_secondary: 'address.contact_secondary',
    email_receiving: 'address.email_receiving',
    physical_addr: 'address.physical_addr',
    physical_city: 'address.physical_city',
    physical_suburb: 'address.physical_suburb',
    physical_code: 'address.physical_code',
    postal_box: 'address.postal_box',
    postal_city: 'address.postal_city',
    postal_code: 'address.postal_code',
    province: 'address.province',
    contact_person: 'address.contact_person',

    skills: 'skills',
    companies: 'companies',
    after_hours: 'after_hours',
    mid: 'mid',
    accredition: 'accredition',

    operational_area: 'operational_area',
    subscriptionConfirmation:
      'details.additional.subscriptionData.subscriptionConfirmation',
  },
  actionPanels: [
    // Scratch Pad
    {
      icon: 'clipboard',
      title: 'Scratch Pad', //?actionPanel=Messages--bell-02
      layout: {},
      onEnter: [],
      onLeave: [],
      fragments: [
        {
          component: 'ScratchPadView',
          layout: { marginLeft: '10px', marginRight: '10px' },
          props: {
            titlePlaceholder: 'Heading',
            icon: 'trash-01',
            iconHandler: (data: { heading: string; body: string }) =>
              console.log(
                'got data: Heading - ' + data.heading + ' Body - ' + data.body
              ),
            placeHolder: 'Text here...',
          },
        },
      ],
      actionLevel: 'bottomControls',
    },
  ],
} satisfies StateConfig;
