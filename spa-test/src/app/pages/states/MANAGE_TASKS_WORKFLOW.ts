import { StateConfig } from '@4-sure/ui-platform';

export const MANAGE_TASKS_WORKFLOW_STATE = {
  title: { template: '' },
  fetchCalls: [
    {
      key: 'admin_tasks',
      method: 'POST',
      body: {},
      url: '{VITE_SP_SERVER}/api/v1/task_actions/get_tasks',
      slicePath: 'payload',
    },
  ],
  defaultScreen: 'list-view',
  screens: {
    // screen
    'list-view': {
      layout: {},
      fragments: [
        {
          component: 'TeamMemberCardList',
          props: {
            data: '$admin_tasks',
            config: [
              {
                type: 'linkColumn',
                showAvatar: true,
                showTitleText: true,
                showStatusIndicator: false,
                showRating: false,
                titleKey: 'content_object',
              },
              {
                type: 'textColumn',
                titleKey: 'task_type',
              },
              {
                type: 'textColumn',
                titleKey: 'created',
              },
            ],
            linkUrl: '/tasks/manage-tasks/edit/details',
            linkUrlParams: [{ key: 'task_id', value: 'id' }],
            isBorder: true,
          },
          layout: {},
        },
      ],
      navs: [],
    },
    // end screen
  },
  actionPanels: [
    {
      icon: 'clipboard',
      title: 'Scratch Pad', //?actionPanel=Messages--bell-02
      // fetchCalls: [],
      layout: {},
      onEnter: [],
      onLeave: [],
      fragments: [
        {
          component: 'ScratchPadView',
          layout: { marginLeft: '10px', marginRight: '10px' },
          props: {
            titlePlaceholder: 'Heading',
            icon: 'trash-01',
            iconHandler: (data: { heading: string; body: string }) =>
              console.log(
                'got data: Heading - ' + data.heading + ' Body - ' + data.body
              ),
            placeHolder: 'Text here...',
          },
        },
      ],
      actionLevel: 'bottomControls',
    },
  ],
} satisfies StateConfig;
