import { AppShell, desktopDark, useAppStore, useClientAction, useSpaKeycloak } from "@4-sure/ui-platform";
import { appConfig, getAppConfig } from "../../app-confg";
import { Outlet, useLoaderData, useLocation, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";



const moduleTabs = [
   { name: 'Claims & Jobs', path: './claims-and-jobs' },
  //  { name: 'Calendar', path: '/workflow/calendar-view' },
  //  { name: 'Map', path: '/workflow/map-view' },
  //  {
  //    name: 'Copilot Work',
  //    path: '/workflow/copilot-view',
  //  },
 ];

 const rightsideTabs: any[] = [
  // { name: 'Personal settings', path: '/settings/personal' },
  // { name: 'Company settings', path: '/settings/company' },
];

export function WorkflowModule () {
    
    const data: any = useLoaderData();
    const auth = useAppStore((state: any) => state.auth);
    const { keycloak } = useSpaKeycloak();
    const navigate = useNavigate();
    const location = useLocation();
    const { callClientAction } = useClientAction({
      keycloak,
      navigate,
      location,
    });

    return (
        <AppShell callClientAction={callClientAction} activeModuleName="Workflow" buttonText="" keycloak={keycloak} username={auth?.preferred_username} email={auth?.email} moduleTabs={moduleTabs} rightsideTabs={rightsideTabs} theme={desktopDark} appConfig={data?.appConfig} clientDataObject={{ ...data?.fetchResultsObject}}>
          <Outlet />      
        </AppShell>
      );
}