import { claimWithJobs2, makeItemArray, WorkflowConfig } from "@4-sure/ui-platform";


export  const workflowConfig: WorkflowConfig = {
    defaultWorkflowType: 'claims-and-jobs',
    workflowTypes: {
        'claims-and-jobs': {
          fetchCalls: [
              {key: 'claims', method: 'GET', url: 'https://jsonplaceholder.typicode.com/todos'},
              // {key: 'claims', method: 'GET', url: 'https://jsonplaceholder.typicode.com/todos?userId=9999'},
              // {key: 'test-profile', method: 'POST', body: {"enum": "all"}, url: 'https://staffnp.4-sure.net/api/v1/profile_actions/get_profile/'},
          ],
          onEnter: [],
          onLeave: [],
          defaultView: 'details',
          views: {
              details: {
                  layout: {},
                  onEnter: [],
                  onLeave: [],
                  fetchCalls: [],
                  conditionalFragment: {
                    condition: {item: '$claims', comparator: 'isEmpty'},
                    fragments: [
                      {
                        component: 'EmptyWorkflowView',
                        props: {
                          textItems: [
                            {
                              text: 'You currently have no jobs assigned to you',
                              options: {
                                format: 'heading',
                                type: 'page-heading',
                              },
                            },
                            {
                              text: 'Jobs will fill up in your workflow as soon as your registration is complete',
                              options: {
                                format: 'heading',
                                type: 'sub-heading',
                              },
                            },
                          ],
                        },
                        layout: {},
                      },
                    ],
                  },
                  fragments: [
                    { 
                      component: 'DefaultWorkflowFilters', 
                      props: { 
                        filtersData: [
                          {
                            buttonText: 'Skills',
                            items: [
                              {
                                text: 'plumber',
                                filterCondition: {
                                  name: 'plumberByAgea',
                                  key: 'id',
                                  value: 5,
                                  comparator: 'lesserThan',
                                },
                              },
                              {
                                text: 'electrician',
                                filterCondition: {
                                  name: 'electicitan testa',
                                  key: 'title',
                                  value: 'del',
                                  comparator: 'contains',
                                },
                              },
                            ],
                          },
                          {
                            buttonText: 'Skills',
                            items: [
                              {
                                text: 'plumber',
                                filterCondition: {
                                  name: 'plumberByAgea',
                                  key: 'id',
                                  value: 5,
                                  comparator: 'lesserThan',
                                },
                              },
                              {
                                text: 'electrician',
                                filterCondition: {
                                  name: 'electicitan testa',
                                  key: 'title',
                                  value: 'del',
                                  comparator: 'contains',
                                },
                              },
                            ],
                          },
                          {
                            buttonText: 'Skills',
                            items: [
                              {
                                text: 'plumber',
                                filterCondition: {
                                  name: 'plumberByAgea',
                                  key: 'id',
                                  value: 5,
                                  comparator: 'lesserThan',
                                },
                              },
                              {
                                text: 'electrician',
                                filterCondition: {
                                  name: 'electicitan testa',
                                  key: 'title',
                                  value: 'del',
                                  comparator: 'contains',
                                },
                              },
                            ],
                          },
                          {
                            buttonText: 'Skills',
                            items: [
                              {
                                text: 'plumber',
                                filterCondition: {
                                  name: 'plumberByAgea',
                                  key: 'id',
                                  value: 5,
                                  comparator: 'lesserThan',
                                },
                              },
                              {
                                text: 'electrician',
                                filterCondition: {
                                  name: 'electicitan testa',
                                  key: 'title',
                                  value: 'del',
                                  comparator: 'contains',
                                },
                              },
                            ],
                          },
                          {
                            buttonText: 'Skills',
                            items: [
                              {
                                text: 'plumber',
                                filterCondition: {
                                  name: 'plumberByAgea',
                                  key: 'id',
                                  value: 5,
                                  comparator: 'lesserThan',
                                },
                              },
                              {
                                text: 'electrician',
                                filterCondition: {
                                  name: 'electicitan testa',
                                  key: 'title',
                                  value: 'del',
                                  comparator: 'contains',
                                },
                              },
                            ],
                          },
                        ],
                        
                      }, 
                      layout: {} 
                    },
                    {
                      component: 'ClaimCardList',
                      // props: { items: '$pageItems' },
                      props: {
                        scrollable: false,
                        items: makeItemArray({
                          item: claimWithJobs2,
                          length: 5,
                        }),
                      },
                      layout: {},
                    },
                      // Filter Fragment
                      // Claim card detail list fragment
                      // {component: 'TestFilteringAndPagination', props: {items: '$pageItems'}, layout: {}}
                  ],
                  filterConfig: {
                      inputCollection: 'claims',
                      outputCollection: 'filteredClaims',
                      filterConditionsSource: 'workflowFilters',
                      sortingCriteriaSource: 'workflowSorts'
                  },
                  paginationConfig: {
                      dataSource: 'filteredClaims',
                      itemsPerPage: 15,
                  }
              },
          },
          actionPanels:  [
            {
              icon: 'bell-02',
              title: 'Messages', //?actionPanel=Messages--bell-02
        
              // fetchCalls: [],
              layout: {},
              onEnter: [],
              onLeave: [],
              fragments: [
                {
                  component: 'NoteCardList',
                  layout: {
                    marginTop: '20px',
                    marginLeft: '10px',
                    marginRight: '10px',
                  },
                  props: {
                    notes: [
                      {
                        title: 'Find Invoice',
                        date: '05/10/23',
                        time: '11:42',
                        content:
                          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                      },
                      {
                        title: 'Find Invoice',
                        date: '05/10/23',
                        time: '11:42',
                        content:
                          'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                      },
                      {
                        title: 'Find Invoice',
                        date: '05/10/23',
                        time: '11:42',
                        content:
                          'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                      },
                    ],
                  },
                },
              ],
              actionLevel: 'bottomControls',
            },
            {
              icon: 'trash-01',
              title: 'Scratch Pad', //?actionPanel=Messages--bell-02
        
              // fetchCalls: [],
              layout: {},
              onEnter: [],
              onLeave: [],
              fragments: [
                {
                  component: 'NoteCardList',
                  layout: {
                    marginTop: '20px',
                    marginBottom: '20px',
                    marginLeft: '10px',
                    marginRight: '10px',
                  },
                  props: {
                    notes: [
                      {
                        title: 'Find Invoice',
                        date: '05/10/23',
                        time: '11:42',
                        content:
                          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                      },
                      {
                        title: 'Find Invoice',
                        date: '05/10/23',
                        time: '11:42',
                        content:
                          'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                      },
                      {
                        title: 'Find Invoice',
                        date: '05/10/23',
                        time: '11:42',
                        content:
                          'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                      },
                    ],
                  },
                },
              ],
              actionLevel: 'bottomControls',
            },
          ],
       },
      }
} 