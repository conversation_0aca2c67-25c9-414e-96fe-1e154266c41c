import {
  ClientWorkflowFiltersProcessor,
  componentMap,
  useClientActionAsync,
  useSpaKeycloak,
  ViewRenderer,
  ViewShell,
} from '@4-sure/ui-platform';
import { useLoaderData, useLocation, useNavigate } from 'react-router-dom';

export function WorkflowView() {
  const data: any = useLoaderData();
  const { keycloak } = useSpaKeycloak();
  const navigate = useNavigate();
  const location = useLocation();
  const { callClientAction } = useClientActionAsync({
    keycloak,
    navigate,
    location,
  });

  return (
    <ViewShell
      callClientAction={callClientAction}
      viewConfig={data?.viewConfig || {}}
      clientDataObject={{ ...data?.fetchResultsObject }}
    >
      <ClientWorkflowFiltersProcessor view={data?.viewConfig || {}} />
      <ViewRenderer
        callClientAction={callClientAction}
        viewConfig={data?.viewConfig || {}}
        clientDataObject={{ ...data?.fetchResultsObject }}
        componentMap={componentMap}
        submit={{}}
        fetcher={{}}
      />
    </ViewShell>
  );
}
