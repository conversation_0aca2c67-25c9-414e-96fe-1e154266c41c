import {
  componentMap,
  useClientActionAsync,
  useSpaKeycloak,
  WorkflowTypeShell,
} from '@4-sure/ui-platform';
import {
  Outlet,
  useLoaderData,
  useLocation,
  useNavigate,
  useParams,
} from 'react-router-dom';

// State
export function WorkflowType() {
  const data: any = useLoaderData();
  const { keycloak } = useSpaKeycloak();
  const navigate = useNavigate();
  const location = useLocation();
  const { callClientAction } = useClientActionAsync({
    keycloak,
    navigate,
    location,
  });

  return (
    <WorkflowTypeShell
      callClientAction={callClientAction}
      componentMap={componentMap}
      workflowTypeConfig={data?.workflowTypeConfig || {}}
      clientDataObject={{ ...data?.fetchResultsObject }}
    >
      <Outlet />
    </WorkflowTypeShell>
  );
}
