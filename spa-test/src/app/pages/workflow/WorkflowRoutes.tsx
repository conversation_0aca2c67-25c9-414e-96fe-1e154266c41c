import { Navigate, RouteObject } from "react-router-dom";
import { WorkflowModule } from "./WorkflowModule";
import { getAppConfig } from "../../app-confg";
import { WorkflowType } from "./WorkflowType";
import { WorkflowView } from "./WorkflowView";
import { workflowConfig } from "./WorkflowConfig";
import { actionFunctionGenerator, makeFetchCalls, SpaPrivateRoute } from "@4-sure/ui-platform";
import Keycloak from 'keycloak-js';
import { envObject } from "../../../env";

export const WorkflowRoutes: (keycloak: Keycloak) => RouteObject = (keycloak: Keycloak) => ({
  path: '/workflow',
  element: <SpaPrivateRoute component={WorkflowModule} />,
  loader: async ({ request }) => {
    const appConfig  = await getAppConfig();
    const fetchCalls = appConfig.fetchCalls || [];
    const fetchResultsObject = await makeFetchCalls(fetchCalls, keycloak, request, envObject);
      return {appConfig, fetchResultsObject};
  },
  action: actionFunctionGenerator(keycloak, envObject),
  children: Object.entries(workflowConfig.workflowTypes).reduce((statesAcc, [statePath, workflowTypeConfig]) => {
  
    return [
      ...statesAcc,
      {
        path: statePath,
        element: <WorkflowType />,
        loader: async ({ params, request }) => {
          const fetchCalls = workflowTypeConfig.fetchCalls || [];
          const fetchResultsObject = await makeFetchCalls(fetchCalls, keycloak, request, envObject);
            return {workflowTypeConfig, fetchResultsObject};
        },
        action: actionFunctionGenerator(keycloak, envObject),
        children: Object.entries(workflowTypeConfig.views).reduce((viewAcc, [viewPath, viewConfig]) => {

          return [
            ...viewAcc,
            {
              path: viewPath,
              element: <WorkflowView />,
            loader: async ({ params, request }) => {
              // const type = params.type || '';
              // const viewId = params.view || '';
              // const viewConfig  = await getViewConfig(type, viewId);
              // console.log({type, viewId, viewConfig})
              const fetchCalls = viewConfig.fetchCalls || [];
              const fetchResultsObject = await makeFetchCalls(fetchCalls, keycloak, request, envObject);
                return {viewConfig, fetchResultsObject};
            },
            action: actionFunctionGenerator(keycloak, envObject),
            } as RouteObject
          ];
        }, [{path: '', element: <Navigate to={workflowTypeConfig.defaultView} />}])
      } as RouteObject
    ];
  }, [{path: '', element: <Navigate to={workflowConfig.defaultWorkflowType} />}]),
});