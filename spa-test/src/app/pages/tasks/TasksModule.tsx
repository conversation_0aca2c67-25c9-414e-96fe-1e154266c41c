import {
  AppShell,
  desktopDark,
  useAppStore,
  useClientAction,
  useSpaKeycloak,
} from '@4-sure/ui-platform';
import { useEffect, useMemo, useState } from 'react';
import {
  Outlet,
  useLoaderData,
  useLocation,
  useNavigate,
} from 'react-router-dom';
import { getAppConfig } from '../../app-confg';

export function TasksModule() {
  const data: any = useLoaderData();
  const auth = useAppStore((state: any) => state.auth);
  const profile = useAppStore((state: any) => state.my_profile_picture);
  const isAdmin = useAppStore((state: any) => state.isAdmin);
  const pending_admin_tasks = useAppStore(
    (state: any) => state.pending_admin_tasks || 0
  );
  const { keycloak } = useSpaKeycloak();
  const navigate = useNavigate();
  const location = useLocation();
  const { callClientAction } = useClientAction({
    keycloak,
    navigate,
    location,
  });

  const [newAppConfig, setNewAppConfig] = useState<any>(null);

  useEffect(() => {
    const fetchAppConfig = async () => {
      const config = await getAppConfig(isAdmin, !!pending_admin_tasks);
      setNewAppConfig(config);
    };

    fetchAppConfig();
  }, [isAdmin, pending_admin_tasks]);
  const updatedAppConfig = useMemo(
    () => ({
      ...data?.appConfig,
      ...newAppConfig,
    }),
    [data?.appConfig, newAppConfig]
  );

  const moduleTabs = useMemo(
    () => [
      { name: 'Manage Team', path: '../teams/manage-team' },
      { name: 'Tasks', path: './manage-tasks', alert: !!pending_admin_tasks },
    ],
    [pending_admin_tasks]
  );

  return (
    <AppShell
      activeModuleName="Team"
      callClientAction={callClientAction || (() => undefined)}
      keycloak={keycloak}
      username={auth?.preferred_username}
      email={auth?.email}
      moduleTabs={moduleTabs}
      theme={desktopDark}
      appConfig={updatedAppConfig}
      clientDataObject={{ ...data?.fetchResultsObject }}
      image={profile?.file}
    >
      <Outlet />
    </AppShell>
  );
}
