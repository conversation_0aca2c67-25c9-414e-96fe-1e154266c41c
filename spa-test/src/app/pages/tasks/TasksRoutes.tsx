import {
  actionFunctionGenerator,
  makeFetchCalls,
  SpaPrivateRoute,
} from '@4-sure/ui-platform';
import Keycloak from 'keycloak-js';
import { Navigate, RouteObject } from 'react-router-dom';
import { envObject } from '../../../env';
import { getAppConfig } from '../../app-confg';
import { tasksConfig } from './TasksConfig';
import { TasksModule } from './TasksModule';
import { TasksScreen } from './TasksScreen';
import { TasksState } from './TasksState';

export const TasksRoutes: (
  keycloak: Keycloak,
  isAdmin?: boolean,
  tasksAlert?: boolean
) => RouteObject = (
  keycloak: Keycloak,
  isAdmin?: boolean,
  tasksAlert?: boolean
) => ({
  path: '/tasks',
  element: <SpaPrivateRoute component={TasksModule} />,
  loader: async ({ request }) => {
    const appConfig = await getAppConfig(isAdmin, tasksAlert);
    const fetchCalls = appConfig.fetchCalls || [];
    const fetchResultsObject = await makeFetchCalls(
      fetchCalls,
      keycloak,
      request,
      envObject
    );
    return { appConfig, fetchResultsObject };
  },
  action: actionFunctionGenerator(keycloak, envObject),
  children: Object.entries(tasksConfig.taskStates).reduce(
    (statesAcc, [statePath, stateConfig]) => {
      return [
        ...statesAcc,
        {
          path: statePath,
          element: <TasksState />,
          loader: async ({ params, request }) => {
            const fetchCalls = stateConfig.fetchCalls || [];
            const fetchResultsObject = await makeFetchCalls(
              fetchCalls,
              keycloak,
              request,
              envObject
            );
            return { stateConfig, fetchResultsObject };
          },
          action: actionFunctionGenerator(keycloak, envObject),
          children: Object.entries(stateConfig.screens).reduce(
            (screenAcc, [screenPath, screenConfig]) => {
              return [
                ...screenAcc,
                {
                  path: screenPath,
                  element: <TasksScreen />,
                  loader: async ({ params, request }) => {
                    const fetchCalls = screenConfig.fetchCalls || [];
                    const fetchResultsObject = await makeFetchCalls(
                      fetchCalls,
                      keycloak,
                      request,
                      envObject
                    );
                    return {
                      screenConfig: { ...screenConfig, id: screenPath },
                      fetchResultsObject,
                    };
                  },
                  action: actionFunctionGenerator(keycloak, envObject),
                } as RouteObject,
              ];
            },
            [{ path: '', element: <Navigate to={stateConfig.defaultScreen} /> }]
          ),
        } as RouteObject,
      ];
    },
    [{ path: '', element: <Navigate to={tasksConfig.defaultState} /> }]
  ),
});
