import {
  actionFunctionGenerator,
  makeFetchCalls,
  SpaPrivateRoute,
} from '@4-sure/ui-platform';
import Keycloak from 'keycloak-js';
import { Navigate, redirect, RouteObject } from 'react-router-dom';
import { envObject } from '../../../env';
import { getAppConfig } from '../../app-confg';
import { settingsConfig } from './SettingsConfig';
import { SettingsModule } from './SettingsModule';
import { SettingsScreen } from './SettingsScreen';
import { SettingsState } from './SettingsState';

export const SettingsRoutes: (
  keycloak: Keycloak,
  isAdmin?: boolean,
  tasksAlert?: boolean
) => RouteObject = (
  keycloak: Keycloak,
  isAdmin?: boolean,
  tasksAlert?: boolean
) => ({
  path: '/settings',
  element: <SpaPrivateRoute component={SettingsModule} />,
  loader: async ({ request }) => {
    const appConfig = await getAppConfig(isAdmin, tasksAlert);
    const fetchCalls = appConfig.fetchCalls || [];
    const fetchResultsObject = await makeFetchCalls(
      fetchCalls,
      keycloak,
      request,
      envObject
    );
    return { appConfig, fetchResultsObject };
  },

  action: actionFunctionGenerator(keycloak, envObject),
  children: Object.entries(settingsConfig.settingsStates).reduce(
    (statesAcc, [statePath, stateConfig]) => {
      return [
        ...statesAcc,
        {
          path: statePath,
          element: <SettingsState />,
          loader: async ({ params, request }) => {
            const fetchCalls = stateConfig.fetchCalls || [];
            const fetchResultsObject = await makeFetchCalls(
              fetchCalls,
              keycloak,
              request,
              envObject
            );
            return { stateConfig, fetchResultsObject };
          },
          action: actionFunctionGenerator(keycloak, envObject),
          children: Object.entries(stateConfig.screens).reduce(
            (screenAcc, [screenPath, screenConfig]) => {
              return [
                ...screenAcc,
                {
                  path: screenPath,
                  element: <SettingsScreen />,
                  loader: async ({ params, request }) => {
                    const fetchCalls = screenConfig.fetchCalls || [];
                    const fetchResultsObject = await makeFetchCalls(
                      fetchCalls,
                      keycloak,
                      request,
                      envObject
                    );
                    return {
                      screenConfig: { ...screenConfig, id: screenPath },
                      fetchResultsObject,
                    };
                  },
                  action: actionFunctionGenerator(keycloak, envObject),
                } as RouteObject,
              ];
            },
            [{ path: '', element: <Navigate to={stateConfig.defaultScreen} /> }]
          ),
        } as RouteObject,
      ];
    },
    [
      {
        path: '',
        element: <Navigate to={settingsConfig.defaultSettingsState} />,
      },
    ]
  ),
});
