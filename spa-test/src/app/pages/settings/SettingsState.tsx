import {
  StateShell,
  useClientActionAsync,
  useSpaKeycloak,
} from '@4-sure/ui-platform';
import React from 'react';
import {
  Outlet,
  useLoaderData,
  useLocation,
  useNavigate,
} from 'react-router-dom';

// State
export const SettingsState = React.memo(() => {
  const data: any = useLoaderData();
  const { keycloak } = useSpaKeycloak();
  const navigate = useNavigate();
  const location = useLocation();
  const { callClientAction } = useClientActionAsync({
    keycloak,
    navigate,
    location,
  });

  return (
    <StateShell
      callClientAction={callClientAction}
      stateConfig={data?.stateConfig || {}}
      clientDataObject={{ ...data?.fetchResultsObject }}
    >
      <Outlet />
    </StateShell>
  );
});
