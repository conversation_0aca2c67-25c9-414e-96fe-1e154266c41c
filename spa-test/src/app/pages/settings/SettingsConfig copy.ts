import { bbbeee, SettingsConfig, validationRegex } from '@4-sure/ui-platform';

export const settingsConfig: SettingsConfig = {
  defaultSettingsState: 'personal',
  settingsStates: {
    personal: {
      title: { template: '' },
      fetchCalls: [
        {
          key: 'my_profile',
          method: 'POST',
          url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/get_profile',
          body: {},
          slicePath: 'payload',
          successFetchCalls: [
            {
              key: 'my_profile_picture',
              method: 'POST',
              url: '{VITE_STAFF_SERVER}/api/v1/file_actions/get_file',
              body: { file_id: '$profile_pic' },
              slicePath: 'payload',
            },
          ],
        },
      ],
      defaultScreen: 'my-profile',
      screens: {
        // screen
        'my-profile': {
          layout: {},
          fetchCalls: [
            {
              key: 'my_files',
              method: 'POST',
              url: '{VITE_STAFF_SERVER}/api/v1/file_actions/get_files',
              body: {},
              slicePath: 'payload',
            },
            {
              key: 'staffFieldAccess',
              method: 'POST',
              url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/staff_field_access',
              body: {},
              slicePath: 'payload',
            },
          ],
          fragments: [
            // {
            //   component: 'TabsAndOptions',
            //   props: {
            //     tabs: [
            //       {
            //         name: 'My Profile',
            //         path: '../my-profile',
            //       },
            //     ],
            //   },
            //   layout: {},
            // },
            {
              component: 'ProfileHero',
              layout: {
                width: '50%',
                minWidth: '712px',
                margin: '0 auto 2rem',
              },
              props: {
                fullname: '$my_profile.full_name',
                username: '$my_profile.username',
                active: '$my_profile.active.active_state',
                activeState: '$my_profile.active.active_state_name',
                activeStateReason: '$my_profile.active.active_state_name',
                image: '$my_profile_picture.file',
                url: '{VITE_STAFF_SERVER}/api/v1/file_actions/upload_file',
                action: '/settings/personal/my-profile',
                // state: '$my_profile.onboarding_state',
              },
            },
            {
              component: 'ButtonRow',
              layout: {
                display: 'grid',
                gridAutoFlow: 'column',
                gap: '2rem',
                gridTemplateColumns: '1/3fr',
                justifyItems: 'center',
                alignContent: 'space-around',
              },
              props: {
                buttons: [
                  {
                    btnValue: 'change password',
                    onClick: [
                      {
                        type: 'clientAction',
                        action: 'triggerModal',
                        payload: [
                          {
                            display: true,
                            type: 'warning',
                            heading: 'Change Password',
                            headingType: 'page-heading',
                            layout: {},
                            onEnter: [],
                            onLeave: [],
                            fragments: [
                              {
                                component: 'FormBuilder',
                                layout: {
                                  width: '55%',
                                  margin: '0 auto',
                                },
                                props: {
                                  config: {
                                    style: {
                                      display: 'grid',
                                      gridAutoFlow: 'row',
                                      gap: '2rem',
                                    },
                                    controls: [
                                      {
                                        type: 'plain-text',
                                        name: 'old_password',
                                        label: 'Old Password',
                                        inputType: 'password',
                                        validation: {
                                          required: {
                                            value: true,
                                            message: 'Old password is required',
                                          },
                                        },
                                        css: { wrapper: { width: '100%' } },
                                      },
                                      {
                                        type: 'plain-text',
                                        name: 'new_password',
                                        label: 'New Password',
                                        inputType: 'password',
                                        validation: {
                                          pattern: {
                                            value:
                                              validationRegex.password.pattern,
                                            message:
                                              validationRegex.password.message,
                                          },
                                          required: {
                                            value: true,
                                            message: 'New password is required',
                                          },
                                          minLength: {
                                            value: 8,
                                            message:
                                              'New password must be at least 8 characters long',
                                          },
                                          // maxLength: {
                                          //   value: 30,
                                          //   message:
                                          //     'New password must not exceed 30 characters',
                                          // },
                                        },
                                        css: { wrapper: { width: '100%' } },
                                        instructions:
                                          'Password minimum characters 8, one uppercase, one lowercase, one special character, one number',
                                      },
                                      {
                                        type: 'plain-text',
                                        name: 'confirm_password',
                                        inputType: 'password',
                                        validation: {
                                          match: {
                                            value: 'new_password',
                                            message: 'Passwords do not match',
                                          },
                                        },
                                        label: 'Confirm New Password',
                                        css: { wrapper: { width: '100%' } },
                                      },
                                    ],
                                  },
                                },
                              },
                              {
                                component: 'ButtonRow',
                                layout: {
                                  width: 'fit-content',
                                  margin: '0 auto',
                                },
                                props: {
                                  buttons: [
                                    {
                                      btnValue: 'Cancel Change',
                                      disabledWhen: `
                                          !$formState.dirtyFields.old_password &&
                                          !$formState.dirtyFields.new_password &&
                                          !$formState.dirtyFields.confirm_password ||
                                          !(Object.keys($formState.touchedFields).length > 0)
                                        `,
                                      onClick: [
                                        {
                                          type: 'clientAction',
                                          action: 'resetFields',
                                          payload: {
                                            fields: [
                                              'old_password',
                                              'new_password',
                                              'confirm_password',
                                            ],
                                          },
                                        },
                                        {
                                          type: 'clientAction',
                                          action: 'closeModal',
                                        },
                                      ],
                                    },
                                    {
                                      btnValue: 'Change Password',
                                      disabledWhen: `
                                          !$formState.dirtyFields.old_password ||
                                          !$formState.dirtyFields.new_password ||
                                          !$formState.dirtyFields.confirm_password ||
                                          !!$formState.errors.confirm_password ||
                                          !!$formState.errors.new_password |
                                          !!$formState.errors.old_password ||
                                          !(Object.keys($formState.touchedFields).length > 0)
                                        `,
                                      onClick: [
                                        {
                                          type: 'clientAction',
                                          action: 'log',
                                          payload: ['Change Password clicked'],
                                        },
                                        {
                                          type: 'clientAction',
                                          action: 'submitAndNavigate',
                                          payload: [
                                            {
                                              old_password:
                                                '$formDataRaw.old_password',
                                              new_password:
                                                '$formDataRaw.new_password',
                                            },
                                            {
                                              url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/change_password',
                                              headers: {},
                                              redirect:
                                                '/settings/personal/my-profile',
                                            },
                                            {
                                              method: 'post',
                                              action:
                                                '/settings/personal/my-profile',
                                            },
                                          ],
                                        },
                                        {
                                          type: 'clientAction',
                                          action: 'closeModal',
                                        },
                                      ],
                                    },
                                  ],
                                },
                              },
                            ],
                            navs: [],
                          },
                        ],
                      },
                    ],
                    actiontype: 'alternative',
                  },
                ],
              },
            },
            {
              component: 'FormBuilder',
              props: {
                // defaultValues: {
                //   email: '<EMAIL>',
                //   cellphone: '*********'
                // },
                proof_of_id: "$my_files?find:item.purpose === 'Identity'",
                defaultValues: {
                  email_address: '$my_profile.email_address',
                  contact_number: '$my_profile.contact_number',
                  full_name: '$my_profile.full_name',
                  roles: '$my_profile.roles',
                  id_type: '$my_profile.id_type',
                  id_passport: '$my_profile.id_passport',
                  proof_of_id: undefined,
                },
                fieldAccessPermissions: '#{staffFieldAccess}',
                config: {
                  style: {
                    display: 'grid',
                    gridTemplateColumns: 'repeat(2, 1fr)',
                    gridTemplateRows: 'repeat(3, 1fr)',
                    rowGap: '2rem',
                    columnGap: '1rem',
                    justifyItems: 'center',
                    alignContent: 'space-around',
                    height: 'auto',
                    paddingTop: '2rem',
                    paddingBottom: '2rem',
                    width: '50%',
                    minWidth: '712px',
                  },
                  controls: [
                    {
                      type: 'plain-text',
                      name: 'full_name',
                      label: 'Full Name',
                      fieldAccessPath: {
                        view: 'staffmember',
                        edit: 'staffmember',
                        special: 'staffmember',
                      },
                      validation: {
                        pattern: {
                          value: validationRegex.name.pattern,
                          message: validationRegex.name.message,
                        },
                        required: {
                          value: true,
                          message: 'Name is required',
                        },
                        minLength: {
                          value: 2,
                          message: 'Name must be at least 2 characters',
                        },
                        maxLength: {
                          value: 30,
                          message: 'You cant go past 30 characters',
                        },
                      },
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 1,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'drag-and-drop-in-memory',
                      name: 'proof_of_id',
                      label: 'Proof of ID',
                      url: '{VITE_STAFF_SERVER}/api/v1/file_actions/upload_file',
                      action: '/settings/personal/my-profile',
                      purpose: 'Identity', // expected by file sent to server
                      list: 'False', //expected by file sent to server
                      emptyText: 'No files uploaded',
                      fileTypesAllowed: ['pdf', 'image'],
                      fileSizeLimit: 5242880,
                      pathToRefId: 'my_profile.sso_id',
                      fieldAccessPath: {
                        view: 'staffmember',
                        edit: 'staffmember',
                        special: 'staffmember',
                      },
                      css: {
                        wrapper: {
                          gridColumn: '1 / span 2',
                          gridRow: 3,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                      disabledWhen: 'true',
                    },
                    {
                      type: 'plain-text',
                      name: 'contact_number',
                      label: 'Contact Number',
                      validation: {
                        pattern: {
                          value: validationRegex.phone_number.pattern,
                          message: validationRegex.phone_number.message,
                        },
                      },
                      fieldAccessPath: {
                        view: 'staffmember',
                        edit: 'staffmember',
                        special: 'staffmember',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 4,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'email_address',
                      label: 'Email Address',
                      validation: {
                        pattern: {
                          value: validationRegex.email.pattern,
                          message: validationRegex.email.message,
                        },
                      },
                      fieldAccessPath: {
                        view: 'staffmember',
                        edit: 'staffmember',
                        special: 'staffmember',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 2,
                          gridRow: 4,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'multi-select',
                      name: 'roles',
                      label: 'Role(s)',
                      placeholder: 'Select roles',
                      labelProp: 'description',
                      valueProp: 'id',
                      options: {
                        source: 'store',
                        storeDataPath: 'staff_enums.roles_2',
                      },
                      multiSelect: true,
                      dropdownScroll: true,
                      // manually disabling staffmember's ability to edit their ouw roles
                      fieldAccessPath: {
                        view: 'staffmember',
                        edit: 'staffmember',
                        special: 'staffmember',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 2,
                          gridRow: 1,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'single-select',
                      name: 'id_type',
                      label: 'ID Types',
                      placeholder: 'Select type',
                      labelProp: 'name',
                      valueProp: 'id',
                      options: {
                        source: 'store',
                        storeDataPath: 'staff_enums.id_types',
                      },
                      dropdownScroll: true,
                      fieldAccessPath: {
                        view: 'staffmember',
                        edit: 'staffmember',
                        special: 'staffmember',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 2,
                          gridRow: 2,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'id_passport',
                      label: 'Identity Number',
                      fieldAccessPath: {
                        view: 'staffmember',
                        edit: 'staffmember',
                        special: 'staffmember',
                      },
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                        pattern: {
                          value: validationRegex.id_passport.pattern,
                          message: validationRegex.id_passport.message,
                        },
                      },
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 2,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                  ],
                },
              },
              layout: { display: 'grid', justifyItems: 'center' },
            },
          ],
          navs: [
            {
              label: 'Back To Teams',
              position: 'left',
              hideWhen: `!$store?.isAdmin`,
              onClick: [
                {
                  type: 'clientAction',
                  action: 'conditional',
                  payload: {
                    condition:
                      '$store?.postData && (Object.keys($store.postData).length > 0) && (Object.keys($formState.errors).length === 0)',
                    actions: {
                      whenTrue: [
                        {
                          type: 'clientAction',
                          action: 'triggerModal',
                          payload: [
                            {
                              display: true,
                              type: 'warning',
                              layout: {},
                              onEnter: [],
                              onLeave: [],
                              fragments: [
                                {
                                  component: 'Text',
                                  props: {
                                    textItems: [
                                      {
                                        text: 'Warning',
                                        options: {
                                          format: 'heading',
                                          type: 'page-heading',
                                        },
                                      },
                                      {
                                        text: 'You have unsaved changes. What would you like to do?',
                                        options: {
                                          format: 'heading',
                                          type: 'sub-heading',
                                          style: {
                                            paddingTop: '2rem',
                                          },
                                        },
                                      },
                                    ],
                                  },
                                  layout: {
                                    display: 'grid',
                                    gridAutoFlow: 'row',
                                    justifyItems: 'center',
                                  },
                                },
                                {
                                  component: 'ButtonRow',
                                  layout: {
                                    width: 'fit-content',
                                    margin: '0 auto',
                                  },
                                  props: {
                                    buttons: [
                                      {
                                        btnValue: 'Clear Changes',
                                        actiontype: 'warning',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'navigate',
                                            payload: [
                                              '/teams/manage-team/list-view',
                                            ],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                      {
                                        btnValue: 'Save Changes',
                                        actiontype: 'proceed',
                                        onClick: [
                                          // {
                                          //   type: 'clientAction',
                                          //   action: 'submitAndNavigate',
                                          //   payload: [
                                          //     {
                                          //       postData: '$postData',
                                          //     },
                                          //     {
                                          //       url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_profile',
                                          //       headers: {},
                                          //       bodySlicePath: 'postData',
                                          //       redirect:
                                          //         '/teams/manage-team/list-view',
                                          //     },
                                          //     {
                                          //       method: 'post',
                                          //       action:
                                          //         '/teams/manage-team/profile/member-profile',
                                          //     },
                                          //   ],
                                          // },
                                          {
                                            type: 'clientAction',
                                            action: 'conditional',
                                            payload: {
                                              condition:
                                                '$store.filePostData && (Object.keys($store.filePostData) < 1)',
                                              actions: {
                                                whenTrue: [
                                                  {
                                                    type: 'clientAction',
                                                    action: 'submitAndNavigate',
                                                    payload: [
                                                      {
                                                        full_name:
                                                          '$formDataRaw.full_name',
                                                        contact_number:
                                                          '$formDataRaw.contact_number',
                                                        email_address:
                                                          '$formDataRaw.email_address',
                                                        id_type:
                                                          '$formDataRaw.id_type', // SA id or drivers license = 1, passport = 2
                                                        id_passport:
                                                          '$formDataRaw.id_passport',
                                                        // qualification_number: null,
                                                        // available: true,
                                                        // staff_type: 2,
                                                        roles:
                                                          '$formDataRaw.roles',
                                                        staff_id:
                                                          '$my_profile.sso_id',
                                                      },
                                                      {
                                                        url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_profile',
                                                        headers: {},
                                                        redirect:
                                                          '/teams/manage-team/list-view',
                                                      },
                                                      {
                                                        method: 'post',
                                                        action:
                                                          '/settings/personal/my-profile',
                                                      },
                                                    ],
                                                  },
                                                ],
                                                whenFalse: [
                                                  {
                                                    type: 'clientAction',
                                                    action: 'conditional',
                                                    payload: {
                                                      condition:
                                                        '$store.formPostData && (Object.keys($store.formPostData) < 1)',
                                                      actions: {
                                                        whenTrue: [
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'submitAsync',
                                                            payload: {
                                                              calls: [
                                                                {
                                                                  key: 'update_proof_of_id',
                                                                  url: `${
                                                                    import.meta
                                                                      .env
                                                                      .VITE_STAFF_SERVER
                                                                  }/api/v1/file_actions/upload_file`,
                                                                  data: {
                                                                    file: '$proof_of_id.file',
                                                                    purpose:
                                                                      '$proof_of_id.purpose',
                                                                    list: '$proof_of_id.list',
                                                                    staff_id:
                                                                      '$proof_of_id.ref_id',
                                                                  },
                                                                },
                                                              ],
                                                              onFinish: {
                                                                type: 'clientAction',
                                                                action:
                                                                  'navigate',
                                                                payload: [
                                                                  '/teams/manage-team/list-view',
                                                                ],
                                                              },
                                                              redirect:
                                                                '/settings/personal/my-profile',
                                                            },
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'resetFields',
                                                            payload: {
                                                              fields: [
                                                                {
                                                                  fieldName:
                                                                    'proof_of_id',
                                                                  defaultValue:
                                                                    undefined,
                                                                },
                                                              ],
                                                            },
                                                          },
                                                        ],
                                                        whenFalse: [
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'submitAsync',
                                                            payload: {
                                                              calls: [
                                                                {
                                                                  key: 'update_proof_of_id',
                                                                  url: `${
                                                                    import.meta
                                                                      .env
                                                                      .VITE_STAFF_SERVER
                                                                  }/api/v1/file_actions/upload_file`,
                                                                  data: {
                                                                    file: '$proof_of_id.file',
                                                                    purpose:
                                                                      '$proof_of_id.purpose',
                                                                    list: '$proof_of_id.list',
                                                                    staff_id:
                                                                      '$proof_of_id.ref_id',
                                                                  },
                                                                },
                                                              ],
                                                              onFinish: {
                                                                type: 'clientAction',
                                                                action:
                                                                  'clearStore',
                                                                payload: [
                                                                  'postData',
                                                                  'filePostData',
                                                                ],
                                                              },
                                                              redirect:
                                                                '/settings/profile/my-profile',
                                                            },
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'submitAndNavigate',
                                                            payload: [
                                                              {
                                                                type: 'clientAction',
                                                                action:
                                                                  'submitAndNavigate',
                                                                payload: [
                                                                  {
                                                                    full_name:
                                                                      '$formDataRaw.full_name',
                                                                    contact_number:
                                                                      '$formDataRaw.contact_number',
                                                                    email_address:
                                                                      '$formDataRaw.email_address',
                                                                    id_type:
                                                                      '$formDataRaw.id_type', // SA id or drivers license = 1, passport = 2
                                                                    id_passport:
                                                                      '$formDataRaw.id_passport',
                                                                    // qualification_number: null,
                                                                    // available: true,
                                                                    // staff_type: 2,
                                                                    roles:
                                                                      '$formDataRaw.roles',
                                                                    staff_id:
                                                                      '$my_profile.sso_id',
                                                                  },
                                                                  {
                                                                    url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_profile',
                                                                    headers: {},
                                                                    redirect:
                                                                      '/teams/manage-team/list-view',
                                                                  },
                                                                  {
                                                                    method:
                                                                      'post',
                                                                    action:
                                                                      '/settings/personal/my-profile',
                                                                  },
                                                                ],
                                                              },
                                                              {
                                                                url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_profile',
                                                                headers: {},
                                                                redirect:
                                                                  '/teams/manage-team/list-view',
                                                              },
                                                              {
                                                                method: 'post',
                                                                action:
                                                                  '/settings/personal/my-profile',
                                                              },
                                                            ],
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'resetFields',
                                                            payload: {
                                                              fields: [
                                                                {
                                                                  fieldName:
                                                                    'proof_of_id',
                                                                  defaultValue:
                                                                    undefined,
                                                                },
                                                              ],
                                                            },
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'clearStore',
                                                            payload: [
                                                              'postData',
                                                            ],
                                                          },
                                                        ],
                                                      },
                                                    },
                                                  },
                                                ],
                                              },
                                            },
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                },
                              ],
                              navs: [],
                            },
                          ],
                        },
                      ],
                      whenFalse: [
                        {
                          type: 'clientAction',
                          action: 'navigate',
                          payload: ['/teams/manage-team/list-view'],
                        },
                      ],
                    },
                  },
                },
              ],
            },
            {
              label: 'Cancel Changes',
              position: 'center',
              disabledWhen: `$store?.postData && !(Object.keys($store.postData).length > 0) || !$store?.postData`,
              onClick: [
                {
                  type: 'clientAction',
                  action: 'resetFields',
                  payload: {
                    fields: [
                      {
                        fieldName: 'full_name',
                        defaultValue: '$my_profile.full_name',
                      },
                      {
                        fieldName: 'contact_number',
                        defaultValue: '$my_profile.contact_number',
                      },
                      {
                        fieldName: 'email_address',
                        defaultValue: '$my_profile.email_address',
                      },
                      {
                        fieldName: 'roles',
                        defaultValue: '$my_profile.roles',
                      },
                      {
                        fieldName: 'id_type',
                        defaultValue: '$my_profile.id_type',
                      },
                      {
                        fieldName: 'id_passport',
                        defaultValue: '$my_profile.id_passport',
                      },
                      {
                        fieldName: 'proof_of_id',
                        defaultValue: undefined,
                      },
                    ],
                  },
                },
              ],
            },
            {
              label: 'Save Changes',
              position: 'center',
              // disabledWhen:
              //   '!$store?.postData || !(Object.keys($store.postData).length > 0) || !$formState.isValid || !$formState.isDirty',
              disabledWhen:
                '!$store?.postData || !(Object.keys($store.postData).length > 0) || (Object.keys($formState.errors).length > 0) || !$formState.isDirty',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'conditional',
                  payload: {
                    condition:
                      '$store.filePostData && (Object.keys($store.filePostData) < 1)',
                    actions: {
                      whenTrue: [
                        {
                          type: 'clientAction',
                          action: 'submitAndNavigate',
                          payload: [
                            {
                              full_name: '$formDataRaw.full_name',
                              contact_number: '$formDataRaw.contact_number',
                              email_address: '$formDataRaw.email_address',
                              id_type: '$formDataRaw.id_type', // SA id or drivers license = 1, passport = 2
                              id_passport: '$formDataRaw.id_passport',
                              // qualification_number: null,
                              // available: true,
                              // staff_type: 2,
                              roles: '$formDataRaw.roles',
                              staff_id: '$my_profile.sso_id',
                            },
                            {
                              url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_profile',
                              headers: {},
                              redirect: '/teams/manage-team/list-view',
                            },
                            {
                              method: 'post',
                              action: '/settings/personal/my-profile',
                            },
                          ],
                        },
                      ],
                      whenFalse: [
                        {
                          type: 'clientAction',
                          action: 'conditional',
                          payload: {
                            condition:
                              '$store.formPostData && (Object.keys($store.formPostData) < 1)',
                            actions: {
                              whenTrue: [
                                {
                                  type: 'clientAction',
                                  action: 'submitAsync',
                                  payload: {
                                    calls: [
                                      {
                                        key: 'update_proof_of_id',
                                        url: '{VITE_STAFF_SERVER}/api/v1/file_actions/upload_file',
                                        data: {
                                          file: '$proof_of_id.file',
                                          purpose: '$proof_of_id.purpose',
                                          list: '$proof_of_id.list',
                                          staff_id: '$proof_of_id.ref_id',
                                        },
                                      },
                                    ],
                                    onFinish: {
                                      type: 'clientAction',
                                      action: 'navigate',
                                      payload: [
                                        '/teams/manage-team/list-view',
                                        { clearParams: true },
                                      ],
                                    },
                                    redirect: '/settings/personal/my-profile',
                                  },
                                },
                                {
                                  type: 'clientAction',
                                  action: 'resetFields',
                                  payload: {
                                    fields: [
                                      {
                                        fieldName: 'proof_of_id',
                                        defaultValue: undefined,
                                      },
                                    ],
                                  },
                                },
                              ],
                              whenFalse: [
                                {
                                  type: 'clientAction',
                                  action: 'submitAsync',
                                  payload: {
                                    calls: [
                                      {
                                        key: 'update_proof_of_id',
                                        url: '{VITE_STAFF_SERVER}/api/v1/file_actions/upload_file',
                                        data: {
                                          file: '$proof_of_id.file',
                                          purpose: '$proof_of_id.purpose',
                                          list: '$proof_of_id.list',
                                          staff_id: '$proof_of_id.ref_id',
                                        },
                                      },
                                    ],
                                    onFinish: {
                                      type: 'clientAction',
                                      action: 'clearStore',
                                      payload: ['postData', 'filePostData'],
                                    },
                                    redirect: '/settings/profile/my-profile',
                                  },
                                },
                                {
                                  type: 'clientAction',
                                  action: 'submitAndNavigate',
                                  payload: [
                                    {
                                      type: 'clientAction',
                                      action: 'submitAndNavigate',
                                      payload: [
                                        {
                                          full_name: '$formDataRaw.full_name',
                                          contact_number:
                                            '$formDataRaw.contact_number',
                                          email_address:
                                            '$formDataRaw.email_address',
                                          id_type: '$formDataRaw.id_type', // SA id or drivers license = 1, passport = 2
                                          id_passport:
                                            '$formDataRaw.id_passport',
                                          // qualification_number: null,
                                          // available: true,
                                          // staff_type: 2,
                                          roles: '$formDataRaw.roles',
                                          staff_id: '$my_profile.sso_id',
                                        },
                                        {
                                          url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_profile',
                                          headers: {},
                                          redirect:
                                            '/teams/manage-team/list-view',
                                        },
                                        {
                                          method: 'post',
                                          action:
                                            '/settings/personal/my-profile',
                                        },
                                      ],
                                    },
                                    {
                                      url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_profile',
                                      headers: {},
                                      redirect: '/teams/manage-team/list-view',
                                    },
                                    {
                                      method: 'post',
                                      action: '/settings/personal/my-profile',
                                    },
                                  ],
                                },
                                {
                                  type: 'clientAction',
                                  action: 'resetFields',
                                  payload: {
                                    fields: [
                                      {
                                        fieldName: 'proof_of_id',
                                        defaultValue: undefined,
                                      },
                                    ],
                                  },
                                },
                                {
                                  type: 'clientAction',
                                  action: 'clearStore',
                                  payload: ['postData'],
                                },
                              ],
                            },
                          },
                        },
                      ],
                    },
                  },
                },
              ],
            },
          ],
          onLeave: [
            {
              type: 'clientAction',
              action: 'clearStore',
              payload: ['derivedproof_of_id', 'postData', 'formDataRaw'],
            },
          ],
        },
        // end screen
      },
      formOriginalValues: {
        // Orignals for diffing to find changed fields (from clientDataObject to store)
        'my_profile.email_address': 'email_address',
        'my_profile.contact_number': 'contact_number',
        'my_profile.full_name': 'full_name',
        'my_profile.id_type': 'id_type',
        'my_profile.id_passport': 'id_passport',
        'my_profile.roles': 'roles',
        derivedproof_of_id: 'proof_of_id',
      },
      formTransformMapper: {
        // Actual mapper to get final object shaped for server
        email_address: 'email_address',
        contact_number: 'contact_number',
        full_name: 'full_name',
        id_type: 'id_type',
        id_passport: 'id_passport',
        roles: 'roles',
        proof_of_id: 'proof_of_id',
      },
      actionPanels: [
        // Reminders
        // {
        //   icon: 'bell-02',
        //   title: 'Reminders', //?actionPanel=Messages--bell-02

        //   // fetchCalls: [],
        //   layout: {},
        //   onEnter: [],
        //   onLeave: [],
        //   fragments: [
        //     {
        //       component: 'ReminderView',
        //       layout: {
        //         marginTop: '10px',
        //         marginLeft: '10px',
        //         marginRight: '10px',
        //       },
        //       props: {
        //         reminders: [
        //           {
        //             background: 'primary',
        //             timeOfReminder: '01:00',
        //             reminderDate: '2024-07-01',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '09:30',
        //             reminderDate: '2024-07-03',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '00:01',
        //             reminderDate: '2024-07-09',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '01:00',
        //             reminderDate: '2024-07-01',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '09:30',
        //             reminderDate: '2024-07-03',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '00:01',
        //             reminderDate: '2024-07-09',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '01:00',
        //             reminderDate: '2024-07-01',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '09:30',
        //             reminderDate: '2024-07-03',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '00:01',
        //             reminderDate: '2024-07-09',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //         ],
        //         toolbar: {
        //           buttonText: 'CREATE REMINDER',
        //           onClick: [
        //             {
        //               type: 'clientAction',
        //               action: 'triggerModal',
        //               payload: [
        //                 {
        //                   display: true,
        //                   type: 'warning',
        //                   heading: 'Add New Reminder',
        //                   headingType: 'page-heading',
        //                   layout: {},
        //                   onEnter: [],
        //                   onLeave: [],
        //                   fragments: [
        //                     {
        //                       component: 'FormBuilder',
        //                       layout: {
        //                         display: 'grid',
        //                         justifyItems: 'center',
        //                         margin: '0 auto',
        //                       },
        //                       props: {
        //                         defaultValues: {
        //                           channel: 0,
        //                           reminder_label: '',
        //                         },
        //                         config: {
        //                           style: {
        //                             display: 'grid',
        //                             gridTemplateColumns: 'repeat(2, 1fr)',
        //                             rowGap: '1rem',
        //                             columnGap: '1rem',
        //                             width: 'calc(100vw * 0.5)',
        //                             maxWidth: '819px',
        //                           },
        //                           controls: [
        //                             {
        //                               type: 'plain-text',
        //                               name: 'reminder_label',
        //                               label: 'Reminder Label',
        //                               validation: {
        //                                 required: {
        //                                   value: true,
        //                                   message: 'This field is required',
        //                                 },
        //                               },
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1',
        //                                   gridRow: '1',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'single-select',
        //                               dropdownScroll: true,
        //                               name: 'channel',
        //                               label: 'Channel',
        //                               labelProp: 'label',
        //                               valueProp: 'value',
        //                               validation: {
        //                                 required: {
        //                                   value: true,
        //                                   message: 'This field is required',
        //                                 },
        //                               },
        //                               options: {
        //                                 source: 'literal',
        //                                 data: [
        //                                   {
        //                                     value: 1,
        //                                     label: 'General',
        //                                   },
        //                                   {
        //                                     value: 2,
        //                                     label: 'Phone',
        //                                   },
        //                                   {
        //                                     value: 3,
        //                                     label: 'Email',
        //                                   },
        //                                 ],
        //                               },
        //                               css: {
        //                                 wrapper: {
        //                                   wrapper: {
        //                                     gridColumn: '2',
        //                                     gridRow: '1',
        //                                     width: '100%',
        //                                   },
        //                                 },
        //                               },
        //                               instructions:
        //                                 'Password minimum characters 12, one uppercase, one lowercase, one special character, one number',
        //                             },
        //                             {
        //                               type: 'datepicker',
        //                               name: 'reminder_date',
        //                               label: 'Date',
        //                               placeholder: 'Select date for reminder',
        //                               instructions: 'Select date for reminder',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1',
        //                                   gridRow: '2',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'timepicker',
        //                               name: 'reminder_time',
        //                               label: 'Time',
        //                               validation: {
        //                                 required: {
        //                                   value: true,
        //                                   message: 'This field is required',
        //                                 },
        //                                 pattern: {
        //                                   value: '^(0[6-9]|1[0-3]):[0-5][0-9]$',
        //                                   message:
        //                                     'Time must be set between 06:00 and 14:00',
        //                                 },
        //                               },
        //                               instructions: 'Select time for reminder',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '2',
        //                                   gridRow: '2',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'textarea',
        //                               name: 'what_matters',
        //                               label: 'Reminder Message',
        //                               placeholder: 'Reminder Message',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1 / span 2',
        //                                   gridRow: '3',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'plain-text',
        //                               name: 'link_to_claim',
        //                               label: 'Link to claim',
        //                               icon: 'search-sm',
        //                               position: 'right',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1 / span 2',
        //                                   gridRow: '4',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                           ],
        //                         },
        //                       },
        //                     },
        //                     {
        //                       component: 'ButtonRow',
        //                       layout: {
        //                         width: 'fit-content',
        //                         margin: 'auto',
        //                       },
        //                       props: {
        //                         buttons: [
        //                           {
        //                             btnValue: 'Cancel',
        //                             onClick: [
        //                               {
        //                                 type: 'clientAction',
        //                                 action: 'log',
        //                                 payload: ['Clicked cancel'],
        //                               },
        //                             ],
        //                           },
        //                           {
        //                             btnValue: 'Add Reminder',
        //                             onClick: [
        //                               {
        //                                 type: 'clientAction',
        //                                 action: 'log',
        //                                 payload: ['Clicked Add Reminder'],
        //                               },
        //                             ],
        //                           },
        //                         ],
        //                       },
        //                     },
        //                   ],
        //                   navs: [],
        //                 },
        //               ],
        //             },
        //           ],
        //         },
        //       },
        //     },
        //   ],
        //   actionLevel: 'bottomControls',
        // },
        // Scratch Pad
        {
          icon: 'clipboard',
          title: 'Scratch Pad', //?actionPanel=Messages--bell-02
          // fetchCalls: [],
          layout: {},
          onEnter: [],
          onLeave: [],
          fragments: [
            {
              component: 'ScratchPadView',
              layout: { marginLeft: '10px', marginRight: '10px' },
              props: {
                titlePlaceholder: 'Heading',
                icon: 'trash-01',
                iconHandler: (data: { heading: string; body: string }) =>
                  console.log(
                    'got data: Heading - ' +
                      data.heading +
                      ' Body - ' +
                      data.body
                  ),
                placeHolder: 'Text here...',
              },
            },
          ],
          actionLevel: 'bottomControls',
        },
      ],
      // excludeFromPostData: ['proof_of_id'],
    },

    company: {
      title: { template: '' },
      fetchCalls: [
        {
          key: 'sp_enums',
          method: 'POST',
          url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_enum',
          body: { enum: 'all' },
          slicePath: 'payload',
        },
        // {
        //   key: 'my_profile',
        //   method: 'POST',
        //   url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/get_profile',
        //   body: {},
        //   slicePath: 'payload',
        //   successFetchCalls: [
        //     {
        //       key: 'my_profile_picture',
        //       method: 'POST',
        //       url: '{VITE_STAFF_SERVER}/api/v1/file_actions/get_file',
        //       body: { file_id: '$profile_pic' },
        //       slicePath: 'payload',
        //     },
        //   ],
        // },
        {
          key: 'sp_profile',
          method: 'POST',
          url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_sp',
          slicePath: 'payload',
          /* Get SP response data does not have profile picture url*/
          // successFetchCalls: [
          //   {
          //     key: 'company_profile_picture',
          //     method: 'POST',
          //     url: '{VITE_SP_SERVER}/api/v1/file_actions/get_file',
          //     body: { file_id: '$details.id' },
          //     slicePath: 'payload',
          //   },
          // ],
        },
        {
          key: 'company_files',
          method: 'POST',
          url: '{VITE_SP_SERVER}/api/v1/file_actions/get_files',
          body: { with_thumbnails: true, order: '-' },
          slicePath: 'payload',
        },
      ],
      defaultScreen: 'company',
      screens: {
        // screen
        company: {
          layout: {},
          fetchCalls: [],
          fragments: [
            {
              component: 'TabsAndOptions',
              props: {
                tabs: [
                  {
                    name: 'Company',
                    path: '../company',
                  },
                  {
                    name: 'Banking',
                    path: '../banking',
                  },
                  {
                    name: 'Directors',
                    path: '../directors',
                  },
                  {
                    name: 'Contact',
                    path: '../contact',
                  },
                  {
                    name: 'Work',
                    path: '../work',
                  },
                  {
                    name: 'Areas',
                    path: '../areas',
                  },
                  {
                    name: 'Documents',
                    path: '../documents',
                  },
                  {
                    name: 'Subscription',
                    path: '../subscription',
                  },
                ],
              },
              isStickyHeader: true,
              layout: {},
            },
            {
              component: 'ProfileHero',
              layout: {
                display: 'grid',
                justifyContent: 'center',
              },
              props: {
                fullname: '$sp_profile.details.name',
                subText: 'Registration number: ',
                username: '$sp_profile.details.co_reg',
                active: false,
                image: '$sp_profile.company_profile_picture',
                url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                action: '/settings/company/company',
                profileType: 'company',
                state: '$sp_profile.details.onboarding_state',
                showImgUpload: false,
              },
            },
            {
              component: 'FormBuilder',
              props: {
                bbbeee_certificate:
                  "$company_files?find:item.purpose === 'Reg BBEEE certificate'",
                defaultValues: {
                  trading_as: '$sp_profile.details.trading_as',
                  bbeee: '$sp_profile.details.bbeee',
                  vat_no: '$sp_profile.financial.vat_no',
                },
                config: {
                  style: {
                    display: 'grid',
                    gridTemplateColumns: 'repeat(2, 1fr)',
                    gridTemplateRows: 'repeat(3, 1fr)',
                    rowGap: '2rem',
                    columnGap: '1rem',
                    justifyItems: 'center',
                    alignContent: 'space-around',
                    height: 'auto',
                    paddingTop: '2rem',
                    paddingBottom: '2rem',
                    width: '50%',
                    minWidth: '712px',
                  },
                  controls: [
                    {
                      type: 'plain-text',
                      name: 'trading_as',
                      fieldAccessPath: {
                        view: 'details',
                        edit: 'details',
                        special: 'details',
                      },
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                      },
                      label: 'Trading as',
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 1,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'single-select',
                      dropdownScroll: true,
                      name: 'bbeee',
                      fieldAccessPath: {
                        view: 'details',
                        edit: 'details',
                        special: 'details',
                      },
                      label: 'BBBEEE Level',
                      position: 'right',
                      labelProp: 'label',
                      valueProp: 'value',
                      options: {
                        data: bbbeee,
                        source: 'literal',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 2,
                          gridRow: 1,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'drag-and-drop',
                      name: 'bbbeee_certificate',
                      label: 'BBBEEE Certificate',
                      fieldAccessPath: {
                        view: 'details',
                        edit: 'details',
                        special: 'details',
                      },
                      // emptyText: '',
                      purpose: 'Reg BBEEE certificate',
                      list: 'False',
                      action: '/settings/company/company',
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      css: {
                        wrapper: {
                          gridColumn: '1 / span 2',
                          gridRow: 2,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                  ],
                },
              },
              layout: { display: 'grid', justifyItems: 'center' },
            },
          ],
          navs: [
            {
              label: 'Back To Teams',
              position: 'left',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'conditional',
                  payload: {
                    condition:
                      '$store?.postData && (Object.keys($store.postData).length > 0) && $formState.isValid',
                    actions: {
                      whenTrue: [
                        {
                          type: 'clientAction',
                          action: 'triggerModal',
                          payload: [
                            {
                              display: true,
                              type: 'warning',
                              layout: {},
                              onEnter: [],
                              onLeave: [],
                              fragments: [
                                {
                                  component: 'Text',
                                  props: {
                                    textItems: [
                                      {
                                        text: 'Warning',
                                        options: {
                                          format: 'heading',
                                          type: 'page-heading',
                                        },
                                      },
                                      {
                                        text: 'You have unsaved changes. What would you like to do?',
                                        options: {
                                          format: 'heading',
                                          type: 'sub-heading',
                                          style: {
                                            paddingTop: '2rem',
                                          },
                                        },
                                      },
                                    ],
                                  },
                                  layout: {
                                    display: 'grid',
                                    gridAutoFlow: 'row',
                                    justifyItems: 'center',
                                  },
                                },
                                {
                                  component: 'ButtonRow',
                                  layout: {
                                    width: 'fit-content',
                                    margin: '0 auto',
                                  },
                                  props: {
                                    buttons: [
                                      {
                                        btnValue: 'Clear Changes',
                                        actiontype: 'warning',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'navigate',
                                            payload: [
                                              '/teams/manage-team/list-view',
                                              { clearParams: true },
                                            ],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                      {
                                        btnValue: 'Save Changes',
                                        actiontype: 'proceed',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'submitAndFetch',
                                            payload: [
                                              {
                                                postData: '$postData',
                                              },
                                              {
                                                url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                                headers: {},
                                                bodySlicePath: 'postData',
                                                redirect: '/teams',
                                              },
                                              {
                                                method: 'post',
                                                action:
                                                  '/settings/company/company',
                                              },
                                            ],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                },
                              ],
                              navs: [],
                            },
                          ],
                        },
                      ],
                      whenFalse: [
                        {
                          type: 'clientAction',
                          action: 'navigate',
                          payload: [
                            '/teams/manage-team/list-view',
                            { clearParams: true },
                          ],
                        },
                      ],
                    },
                  },
                },
              ],
            },
            {
              label: 'Cancel Changes',
              position: 'center',
              disabledWhen:
                '$store?.postData && !(Object.keys($store.postData).length > 0)',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'resetFields',
                  payload: {
                    fields: [
                      {
                        fieldName: 'trading_as',
                        defaultValue: '$sp_profile.details.trading_as',
                      },
                      {
                        fieldName: 'bbeee',
                        defaultValue: '$sp_profile.details.bbeee',
                      },
                      {
                        fieldName: 'b_acc_holder',
                        defaultValue: '$sp_profile.financial.b_acc_holder',
                      },
                      {
                        fieldName: 'b_branch_name',
                        defaultValue: '$sp_profile.financial.b_branch_name',
                      },
                      {
                        fieldName: 'b_acc_type',
                        defaultValue: '$sp_profile.financial.b_acc_type',
                      },
                      {
                        fieldName: 'b_branch_code',
                        defaultValue: '$sp_profile.financial.b_branch_code',
                      },
                      {
                        fieldName: 'b_bank_name',
                        defaultValue: '$sp_profile.financial.b_bank_name',
                      },
                      {
                        fieldName: 'b_acc_no',
                        defaultValue: '$sp_profile.financial.b_acc_no',
                      },
                      {
                        fieldName: 'vat_no',
                        defaultValue: '$sp_profile.financial.vat_no',
                      },
                      {
                        fieldName: 'tax_no',
                        defaultValue: '$sp_profile.financial.tax_no',
                      },
                      {
                        fieldName: 'contact_person',
                        defaultValue: '$sp_profile.address.contact_person',
                      },
                      {
                        fieldName: 'contact_primary',
                        defaultValue: '$sp_profile.address.contact_primary',
                      },
                      {
                        fieldName: 'contact_secondary',
                        defaultValue: '$sp_profile.address.contact_secondary',
                      },
                      {
                        fieldName: 'email_receiving',
                        defaultValue: '$sp_profile.address.email_receiving',
                      },
                      {
                        fieldName: 'physical_addr',
                        defaultValue: '$sp_profile.address.physical_addr',
                      },
                      {
                        fieldName: 'physical_city',
                        defaultValue: '$sp_profile.address.physical_city',
                      },
                      {
                        fieldName: 'physical_code',
                        defaultValue: '$sp_profile.address.physical_code',
                      },
                      {
                        fieldName: 'physical_suburb',
                        defaultValue: '$sp_profile.address.physical_suburb',
                      },
                      {
                        fieldName: 'province',
                        defaultValue: '$sp_profile.address.province',
                      },
                      {
                        fieldName: 'postal_box',
                        defaultValue: '$sp_profile.address.postal_box',
                      },
                      {
                        fieldName: 'postal_city',
                        defaultValue: '$sp_profile.address.postal_city',
                      },
                      {
                        fieldName: 'postal_code',
                        defaultValue: '$sp_profile.address.postal_code',
                      },
                      {
                        fieldName: 'skills',
                        defaultValue: '$sp_profile.skills',
                      },
                      {
                        fieldName: 'after_hours',
                        defaultValue: '$sp_profile.after_hours',
                      },
                      {
                        fieldName: 'companies',
                        defaultValue: '$derivedCompanies',
                      },
                      {
                        fieldName: 'accredition',
                        defaultValue:
                          '$sp_profile.additional_identities.accredition',
                      },
                      {
                        fieldName: 'mid',
                        defaultValue: '$sp_profile.additional_identities.mid',
                      },
                      {
                        fieldName: 'operational_area',
                        defaultValue: '$derivedOperationalArea',
                      },
                      {
                        fieldName: 'radius',
                        defaultValue:
                          '$derivedOperationalArea.0.operating_range',
                      },
                      {
                        fieldName: 'jobLocation',
                        defaultValue: '$derivedOperationalArea.0.location',
                      },
                    ],
                  },
                },
              ],
            },
            {
              label: 'Save Changes',
              position: 'center',
              disabledWhen:
                '!$store?.postData || !(Object.keys($store.postData).length > 0) || !$formState.isValid || !$formState.isDirty',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'submitAndFetch',
                  payload: [
                    {
                      postData: '$postData',
                    },
                    {
                      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                      headers: {},
                      bodySlicePath: 'postData',
                      // redirect: '/settings/company/banking',
                    },
                    { method: 'post', action: '/settings/company/company' },
                  ],
                },
              ],
            },
            {
              label: 'Next',
              // disabledWhen: "$form.bbbeee_level === 'hello'",
              position: 'right',
              toScreen: '../banking',
            },
          ],
        },

        banking: {
          layout: {},
          fetchCalls: [],
          fragments: [
            {
              component: 'TabsAndOptions',
              props: {
                tabs: [
                  {
                    name: 'Company',
                    path: '../company',
                  },
                  {
                    name: 'Banking',
                    path: '../banking',
                  },
                  {
                    name: 'Directors',
                    path: '../directors',
                  },
                  {
                    name: 'Contact',
                    path: '../contact',
                  },
                  {
                    name: 'Work',
                    path: '../work',
                  },
                  {
                    name: 'Areas',
                    path: '../areas',
                  },
                  {
                    name: 'Documents',
                    path: '../documents',
                  },
                  {
                    name: 'Subscription',
                    path: '../subscription',
                  },
                ],
              },
              layout: {},
              isStickyHeader: true,
            },
            {
              component: 'Text',
              props: {
                textItems: [
                  {
                    text: 'Company banking details',
                    options: {
                      format: 'heading',
                      type: 'page-heading',
                    },
                  },
                  {
                    text: 'Changing bank details requires approval',
                    options: {
                      format: 'heading',
                      type: 'sub-heading',
                    },
                    icon: {
                      type: 'alert-diamond',
                      size: 24,
                      strokeWidth: '1px',
                      color: '#FF9800',
                      style: { margin: '0 0 0 4px' },
                    },
                    iconPosition: 'right',
                  },
                ],
              },
              layout: {
                display: 'grid',
                justifyItems: 'center',
                paddingTop: '2rem',
                paddingBottom: '2rem',
              },
            },
            {
              component: 'FormBuilder',
              props: {
                proof_of_bank_account:
                  "$company_files?find:item.purpose === 'Reg Proof of bank account'",
                defaultValues: {
                  b_acc_holder: '$sp_profile.financial.b_acc_holder',
                  b_branch_name: '$sp_profile.financial.b_branch_name',
                  b_acc_type: '$sp_profile.financial.b_acc_type',
                  b_branch_code: '$sp_profile.financial.b_branch_code',
                  b_bank_name: '$sp_profile.financial.b_bank_name',
                  b_acc_no: '$sp_profile.financial.b_acc_no',
                  vat_registered: '$sp_profile.financial.vat_registered',
                  tax_no: '$sp_profile.financial.tax_no',
                  vat_no: '$sp_profile.financial.vat_no',
                },
                config: {
                  style: {
                    display: 'grid',
                    gridTemplateColumns: 'repeat(2, 1fr)',
                    gridTemplateRows: 'repeat(4, 1fr)',
                    rowGap: '2rem',
                    columnGap: '1rem',
                    justifyItems: 'center',
                    alignContent: 'space-around',
                    height: 'auto',
                    paddingTop: '2rem',
                    paddingBottom: '2rem',
                    width: '50%',
                    minWidth: '712px',
                  },
                  controls: [
                    {
                      type: 'plain-text',
                      name: 'b_acc_holder',
                      label: 'Bank account holder name',
                      position: 'right',
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                      },
                      fieldAccessPath: {
                        view: 'financial',
                        edit: 'financial',
                        special: 'financial',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 1,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      // icon: 'alarm-clock',
                      position: 'right',
                      type: 'single-select',
                      name: 'b_bank_name',
                      label: 'Bank name',
                      labelProp: 'name',
                      fieldAccessPath: {
                        view: 'financial',
                        edit: 'financial',
                        special: 'financial',
                      },
                      valueProp: 'id',
                      dropdownScroll: true,
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                      },
                      options: {
                        source: 'store',
                        storeDataPath: 'sp_enums.banks',
                        labelProp: 'name',
                        valueProp: 'id',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 2,
                          gridRow: 1,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'b_acc_no',
                      label: 'Bank account number',
                      position: 'right',
                      fieldAccessPath: {
                        view: 'financial',
                        edit: 'financial',
                        special: 'financial',
                      },
                      validation: {
                        required: {
                          value: true,
                          message: 'Bank account number is required',
                        },
                        pattern: {
                          value: validationRegex.bank_account_number.pattern,
                          message: validationRegex.bank_account_number.message,
                        },
                        minLength: {
                          value: 9,
                          message:
                            'Account number is invalid, please confirm banking details',
                        },
                        maxLength: {
                          value: 16,
                          message:
                            'Account number is invalid, please confirm banking details',
                        },
                      },
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 2,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'single-select',
                      name: 'b_acc_type',
                      label: 'Account type',
                      labelProp: 'name',
                      valueProp: 'id',
                      // icon: 'alarm-clock',
                      fieldAccessPath: {
                        view: 'financial',
                        edit: 'financial',
                        special: 'financial',
                      },
                      position: 'right',
                      dropdownScroll: true,
                      validation: {
                        required: {
                          value: true,
                          message: 'Bank account type is required',
                        },
                      },
                      options: {
                        source: 'store',
                        storeDataPath: 'sp_enums.account_types',
                        labelProp: 'name',
                        valueProp: 'id',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 2,
                          gridRow: 2,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'b_branch_name',
                      label: 'Branch name',
                      position: 'right',
                      validation: {
                        required: {
                          value: true,
                          message: 'Branch name is required',
                        },
                      },
                      fieldAccessPath: {
                        view: 'financial',
                        edit: 'financial',
                        special: 'financial',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 3,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'b_branch_code',
                      label: 'Branch code',
                      // icon: 'alarm-clock',
                      fieldAccessPath: {
                        view: 'financial',
                        edit: 'financial',
                        special: 'financial',
                      },
                      position: 'right',
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                        pattern: {
                          value: validationRegex.branch_code.pattern,
                          message: validationRegex.branch_code.message,
                        },
                      },
                      css: {
                        wrapper: {
                          gridColumn: 2,
                          gridRow: 3,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'drag-and-drop',
                      name: 'proof_of_bank_account',
                      label: 'Proof of Bank Account',
                      fieldAccessPath: {
                        view: 'financial',
                        edit: 'financial',
                        special: 'financial',
                      },
                      fileTypesAllowed: ['pdf', 'image'],
                      emptyText: 'No files uploaded',
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      action: '/settings/company/banking',
                      purpose: 'Reg Proof of bank account', // expected by file sent to server
                      list: 'False', //expected by file sent to server
                      iconLeft: 'notes',
                      iconRight: 'alarm-clock',
                      css: {
                        wrapper: {
                          gridColumn: '1 / span 2',
                          gridRow: 4,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'vat_no',
                      label: 'VAT registration number',
                      fieldAccessPath: {
                        view: 'financial',
                        edit: 'financial',
                        special: 'financial',
                      },
                      position: 'right',
                      validation: {
                        pattern: {
                          value: validationRegex.vat_number.pattern,
                          message: validationRegex.vat_number.message,
                        },
                      },
                      // TODO: How to close modal and refocus on control?
                      onFocus: [
                        {
                          type: 'clientAction',
                          action: 'triggerModal',
                          payload: [
                            {
                              display: true,
                              type: 'warning',
                              layout: {},
                              onEnter: [],
                              onLeave: [],
                              fragments: [
                                {
                                  component: 'Text',
                                  props: {
                                    textItems: [
                                      {
                                        text: 'Warning',
                                        options: {
                                          format: 'heading',
                                          type: 'page-heading',
                                        },
                                        icon: {
                                          type: 'alarm-clock',
                                          size: 60,
                                          strokeWidth: '1',
                                          color: '#e5e5e5',
                                          style: { paddingLeft: '1rem' },
                                        },
                                        iconPosition: 'right',
                                      },
                                      {
                                        text: 'This change is not instant.',
                                        options: {
                                          format: 'heading',
                                          type: 'sub-heading',
                                          style: {
                                            paddingTop: '2rem',
                                          },
                                        },
                                      },
                                      {
                                        text: 'Any field with a clock icon will require an approval process',
                                        options: {
                                          format: 'heading',
                                          type: 'sub-heading',
                                        },
                                      },
                                    ],
                                  },
                                  layout: {
                                    display: 'grid',
                                    gridAutoFlow: 'row',
                                    justifyItems: 'center',
                                  },
                                },
                                {
                                  component: 'ButtonRow',
                                  layout: {
                                    display: 'grid',
                                    marginTop: '80px',
                                    justifyItems: 'center',
                                    width: '75%',
                                    marginLeft: 'auto',
                                    marginRight: 'auto',
                                  },
                                  props: {
                                    buttons: [
                                      {
                                        btnValue: 'Cancel edit',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'log',
                                            payload: ['Cancel Edit clicked'],
                                          },
                                        ],
                                      },
                                      {
                                        btnValue: 'Continue with edit',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'log',
                                            payload: [
                                              'Continue With Edit clicked',
                                            ],
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                },
                              ],
                            },
                          ],
                        },
                      ],
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 5,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'tax_no',
                      label: 'Tax registration number',
                      fieldAccessPath: {
                        view: 'financial',
                        edit: 'financial',
                        special: 'financial',
                      },
                      position: 'right',
                      validation: {
                        pattern: {
                          value: validationRegex.tax_number.pattern,
                          message: validationRegex.tax_number.message,
                        },
                      },
                      // TODO: How to close modal and refocus on control?
                      onFocus: [
                        {
                          type: 'clientAction',
                          action: 'triggerModal',
                          payload: [
                            {
                              display: true,
                              type: 'warning',
                              layout: {},
                              onEnter: [],
                              onLeave: [],
                              fragments: [
                                {
                                  component: 'Text',
                                  props: {
                                    textItems: [
                                      {
                                        text: 'Warning',
                                        options: {
                                          format: 'heading',
                                          type: 'page-heading',
                                        },
                                        icon: {
                                          type: 'alarm-clock',
                                          size: 60,
                                          strokeWidth: '1',
                                          color: '#e5e5e5',
                                          style: { paddingLeft: '1rem' },
                                        },
                                        iconPosition: 'right',
                                      },
                                      {
                                        text: 'This change is not instant.',
                                        options: {
                                          format: 'heading',
                                          type: 'sub-heading',
                                          style: {
                                            paddingTop: '2rem',
                                          },
                                        },
                                      },
                                      {
                                        text: 'Any field with a clock icon will require an approval process',
                                        options: {
                                          format: 'heading',
                                          type: 'sub-heading',
                                        },
                                      },
                                    ],
                                  },
                                  layout: {
                                    display: 'grid',
                                    gridAutoFlow: 'row',
                                    justifyItems: 'center',
                                  },
                                },
                                {
                                  component: 'ButtonRow',
                                  layout: {
                                    display: 'grid',
                                    marginTop: '80px',
                                    justifyItems: 'center',
                                    width: '75%',
                                    marginLeft: 'auto',
                                    marginRight: 'auto',
                                  },
                                  props: {
                                    buttons: [
                                      {
                                        btnValue: 'Cancel edit',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'log',
                                            payload: ['Cancel Edit clicked'],
                                          },
                                        ],
                                      },
                                      {
                                        btnValue: 'Continue with edit',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'log',
                                            payload: [
                                              'Continue With Edit clicked',
                                            ],
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                },
                              ],
                            },
                          ],
                        },
                      ],
                      css: {
                        wrapper: {
                          gridColumn: 2,
                          gridRow: 5,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                  ],
                },
              },
              layout: { display: 'grid', justifyItems: 'center' },
            },
          ],
          navs: [
            {
              label: 'Back To Teams',
              position: 'left',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'conditional',
                  payload: {
                    condition:
                      '$store?.postData && (Object.keys($store.postData).length > 0) && $formState.isValid',
                    actions: {
                      whenTrue: [
                        {
                          type: 'clientAction',
                          action: 'triggerModal',
                          payload: [
                            {
                              display: true,
                              type: 'warning',
                              layout: {},
                              onEnter: [],
                              onLeave: [],
                              fragments: [
                                {
                                  component: 'Text',
                                  props: {
                                    textItems: [
                                      {
                                        text: 'Warning',
                                        options: {
                                          format: 'heading',
                                          type: 'page-heading',
                                        },
                                      },
                                      {
                                        text: 'You have unsaved changes. What would you like to do?',
                                        options: {
                                          format: 'heading',
                                          type: 'sub-heading',
                                          style: {
                                            paddingTop: '2rem',
                                          },
                                        },
                                      },
                                    ],
                                  },
                                  layout: {
                                    display: 'grid',
                                    gridAutoFlow: 'row',
                                    justifyItems: 'center',
                                  },
                                },
                                {
                                  component: 'ButtonRow',
                                  layout: {
                                    width: 'fit-content',
                                    margin: '0 auto',
                                  },
                                  props: {
                                    buttons: [
                                      {
                                        btnValue: 'Clear Changes',
                                        actiontype: 'warning',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'navigate',
                                            payload: [
                                              '/teams/manage-team/list-view',
                                              { clearParams: true },
                                            ],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                      {
                                        btnValue: 'Save Changes',
                                        actiontype: 'proceed',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'submitAndFetch',
                                            payload: [
                                              {
                                                postData: '$postData',
                                              },
                                              {
                                                url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                                headers: {},
                                                bodySlicePath: 'postData',
                                              },
                                              {
                                                method: 'post',
                                                action:
                                                  '/settings/company/banking',
                                              },
                                            ],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                },
                              ],
                              navs: [],
                            },
                          ],
                        },
                      ],
                      whenFalse: [
                        {
                          type: 'clientAction',
                          action: 'navigate',
                          payload: [
                            '/teams/manage-team/list-view',
                            { clearParams: true },
                          ],
                        },
                      ],
                    },
                  },
                },
              ],
            },
            {
              label: 'Previous',
              position: 'left',
              toScreen: '../company',
            },
            {
              label: 'Cancel Changes',
              position: 'center',
              disabledWhen:
                '$store?.postData && !(Object.keys($store.postData).length > 0)',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'resetFields',
                  payload: {
                    fields: [
                      {
                        fieldName: 'trading_as',
                        defaultValue: '$sp_profile.details.trading_as',
                      },
                      {
                        fieldName: 'bbeee',
                        defaultValue: '$sp_profile.details.bbeee',
                      },
                      {
                        fieldName: 'b_acc_holder',
                        defaultValue: '$sp_profile.financial.b_acc_holder',
                      },
                      {
                        fieldName: 'b_branch_name',
                        defaultValue: '$sp_profile.financial.b_branch_name',
                      },
                      {
                        fieldName: 'b_acc_type',
                        defaultValue: '$sp_profile.financial.b_acc_type',
                      },
                      {
                        fieldName: 'b_branch_code',
                        defaultValue: '$sp_profile.financial.b_branch_code',
                      },
                      {
                        fieldName: 'b_bank_name',
                        defaultValue: '$sp_profile.financial.b_bank_name',
                      },
                      {
                        fieldName: 'b_acc_no',
                        defaultValue: '$sp_profile.financial.b_acc_no',
                      },
                      {
                        fieldName: 'vat_no',
                        defaultValue: '$sp_profile.financial.vat_no',
                      },
                      {
                        fieldName: 'tax_no',
                        defaultValue: '$sp_profile.financial.tax_no',
                      },
                      {
                        fieldName: 'contact_person',
                        defaultValue: '$sp_profile.address.contact_person',
                      },
                      {
                        fieldName: 'contact_primary',
                        defaultValue: '$sp_profile.address.contact_primary',
                      },
                      {
                        fieldName: 'contact_secondary',
                        defaultValue: '$sp_profile.address.contact_secondary',
                      },
                      {
                        fieldName: 'email_receiving',
                        defaultValue: '$sp_profile.address.email_receiving',
                      },
                      {
                        fieldName: 'physical_addr',
                        defaultValue: '$sp_profile.address.physical_addr',
                      },
                      {
                        fieldName: 'physical_city',
                        defaultValue: '$sp_profile.address.physical_city',
                      },
                      {
                        fieldName: 'physical_code',
                        defaultValue: '$sp_profile.address.physical_code',
                      },
                      {
                        fieldName: 'physical_suburb',
                        defaultValue: '$sp_profile.address.physical_suburb',
                      },
                      {
                        fieldName: 'province',
                        defaultValue: '$sp_profile.address.province',
                      },
                      {
                        fieldName: 'postal_box',
                        defaultValue: '$sp_profile.address.postal_box',
                      },
                      {
                        fieldName: 'postal_city',
                        defaultValue: '$sp_profile.address.postal_city',
                      },
                      {
                        fieldName: 'postal_code',
                        defaultValue: '$sp_profile.address.postal_code',
                      },
                      {
                        fieldName: 'skills',
                        defaultValue: '$sp_profile.skills',
                      },
                      {
                        fieldName: 'after_hours',
                        defaultValue: '$sp_profile.after_hours',
                      },
                      {
                        fieldName: 'companies',
                        defaultValue: '$derivedCompanies',
                      },
                      {
                        fieldName: 'accredition',
                        defaultValue:
                          '$sp_profile.additional_identities.accredition',
                      },
                      {
                        fieldName: 'mid',
                        defaultValue: '$sp_profile.additional_identities.mid',
                      },
                      {
                        fieldName: 'operational_area',
                        defaultValue: '$derivedOperationalArea',
                      },
                      {
                        fieldName: 'radius',
                        defaultValue:
                          '$derivedOperationalArea.0.operating_range',
                      },
                      {
                        fieldName: 'jobLocation',
                        defaultValue: '$derivedOperationalArea.0.location',
                      },
                    ],
                  },
                },
              ],
            },
            {
              label: 'Save Changes',
              position: 'center',
              disabledWhen:
                '!$store?.postData || !(Object.keys($store.postData).length > 0) || !$formState.isValid || !$formState.isDirty',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'submitAndFetch',
                  payload: [
                    {
                      postData: '$postData',
                    },
                    {
                      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                      headers: {},
                      bodySlicePath: 'postData',
                      // redirect: '/settings/company/directors',
                    },
                    { method: 'post', action: '/settings/company/banking' },
                  ],
                },
              ],
            },
            { label: 'Next', position: 'right', toScreen: '../directors' },
          ],
        },

        directors: {
          layout: {},
          fetchCalls: [
            {
              key: 'directors',
              method: 'POST',
              url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_directors',
              body: {},
              slicePath: 'payload',
            },
          ],
          fragments: [
            {
              component: 'TabsAndOptions',
              props: {
                tabs: [
                  {
                    name: 'Company',
                    path: '../company',
                  },
                  {
                    name: 'Banking',
                    path: '../banking',
                  },
                  {
                    name: 'Directors',
                    path: '../directors',
                  },
                  {
                    name: 'Contact',
                    path: '../contact',
                  },
                  {
                    name: 'Work',
                    path: '../work',
                  },
                  {
                    name: 'Areas',
                    path: '../areas',
                  },
                  {
                    name: 'Documents',
                    path: '../documents',
                  },
                  {
                    name: 'Subscription',
                    path: '../subscription',
                  },
                ],
              },
              isStickyHeader: true,
              layout: {},
            },
            {
              component: 'Text',
              props: {
                textItems: [
                  {
                    text: 'Enter all director names',
                    options: {
                      format: 'heading',
                      type: 'page-heading',
                    },
                  },
                  {
                    text: 'Changing director names requires approval',
                    options: {
                      format: 'heading',
                      type: 'sub-heading',
                    },
                  },
                ],
              },
              layout: {
                display: 'grid',
                justifyItems: 'center',
                paddingTop: '2rem',
                paddingBottom: '2rem',
              },
            },
            {
              component: 'DirectorsList',
              props: {
                directors: '$directors',
              },
              layout: { display: 'grid', justifyItems: 'center' },
            },
            // {
            //   component: 'FormBuilder',
            //   props: {
            //     config: {
            //       style: {
            //         display: 'grid',
            //         gridTemplateColumns: 'repeat(2, 1fr)',
            //         gridTemplateRows: 'repeat(2, 1fr)',
            //         rowGap: '2rem',
            //         columnGap: '1rem',
            //         justifyItems: 'center',
            //         alignContent: 'space-around',
            //         height: 'auto',
            //         paddingTop: '2rem',
            //         paddingBottom: '2rem',
            //         width: '50%',
            //         minWidth: '712px',
            //       },
            //       controls: [
            //         {
            //           type: 'plain-text',
            //           name: 'director_name',
            //           label: 'Name',
            //           //
            //           css: {
            //             wrapper: {
            //               gridColumn: 1,
            //               gridRow: 1,
            //               height: '4rem',
            //               width: '100%',
            //             },
            //           },
            //         },
            //         {
            //           type: 'plain-text',
            //           name: 'director_identity',
            //           label: 'ID number',
            //           //
            //           css: {
            //             wrapper: {
            //               gridColumn: 2,
            //               gridRow: 1,
            //               height: '4rem',
            //               width: '100%',
            //             },
            //           },
            //         },
            //         {
            //           type: 'drag-and-drop-in-memory',
            //           name: 'director_file',
            //           label: 'Copy of ID or passport document',
            //           url: '{VITE_SP_SERVER}/api/v1/file_actions/director_upload_file',
            //           action: '/settings/company/directors',
            //           purpose: 'Identity', // expected by file sent to server
            //           list: 'True', //expected by file sent to server
            //           emptyText: 'No files uploaded',
            //           css: {
            //             wrapper: {
            //               gridColumn: '1 / span 2',
            //               gridRow: 2,
            //               height: '4rem',
            //               width: '100%',
            //             },
            //           },
            //         },
            //         // {
            //         //   type: 'plain-text',
            //         //   name: 'identification',
            //         //   label: 'Copy of ID or passport document',
            //         //   //
            //         //   css: {
            //         //     wrapper: {
            //         //       gridColumn: '1 / span 2',
            //         //       gridRow: 3,
            //         //       height: '4rem',
            //         //       width: '100%',
            //         //     },
            //         //   },
            //         // },
            //       ],
            //     },
            //   },
            //   layout: { display: 'grid', justifyItems: 'center' },
            // },
            // {
            //   component: 'ButtonRow',
            //   layout: {
            //     display: 'grid',
            //     justifyItems: 'center',
            //     alignContent: 'center',
            //   },
            //   props: {
            //     buttons: [
            //       {
            //         icon: 'clipboard',
            //         btnValue: 'Add director',
            //         actionType: 'alternative',
            //         useCase: 'iconButton',
            //         onClick: [
            //           {
            //             type: 'clientAction',
            //             action: 'log',
            //             payload: ['Clicked cancel'],
            //           },
            //         ],
            //         style: {},
            //       },
            //     ],
            //   },
            // },
          ],
          navs: [
            {
              label: 'Back To Teams',
              position: 'left',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'conditional',
                  payload: {
                    condition:
                      '$form.director_name && $form.director_identity && $form.director_file',
                    actions: {
                      whenTrue: [
                        {
                          type: 'clientAction',
                          action: 'triggerModal',
                          payload: [
                            {
                              display: true,
                              type: 'warning',
                              layout: {},
                              onEnter: [],
                              onLeave: [],
                              fragments: [
                                {
                                  component: 'Text',
                                  props: {
                                    textItems: [
                                      {
                                        text: 'Warning',
                                        options: {
                                          format: 'heading',
                                          type: 'page-heading',
                                        },
                                      },
                                      {
                                        text: 'You have unsaved changes. What would you like to do?',
                                        options: {
                                          format: 'heading',
                                          type: 'sub-heading',
                                          style: {
                                            paddingTop: '2rem',
                                          },
                                        },
                                      },
                                    ],
                                  },
                                  layout: {
                                    display: 'grid',
                                    gridAutoFlow: 'row',
                                    justifyItems: 'center',
                                  },
                                },
                                {
                                  component: 'ButtonRow',
                                  layout: {
                                    width: 'fit-content',
                                    margin: '0 auto',
                                  },
                                  props: {
                                    buttons: [
                                      {
                                        btnValue: 'Clear Changes',
                                        actiontype: 'warning',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'navigate',
                                            payload: [
                                              '/teams/manage-team/list-view',
                                              { clearParams: true },
                                            ],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                      {
                                        btnValue: 'Save Changes',
                                        actiontype: 'proceed',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'submitAsync',
                                            payload: {
                                              calls: [
                                                {
                                                  key: 'add_director',
                                                  url: '{VITE_SP_SERVER}/api/v1/spaas_actions/add_director',
                                                  data: {
                                                    name: '$director_name',
                                                    identity:
                                                      '$director_identity',
                                                  },
                                                },
                                                {
                                                  key: 'upload_director_file',
                                                  url: '{VITE_SP_SERVER}/api/v1/file_actions/director_upload_file',
                                                  data: {
                                                    file: '$director_file',
                                                    purpose: 'Identity',
                                                    list: 'True',
                                                    director_id:
                                                      '{add_director.payload.id}',
                                                  },
                                                },
                                              ],
                                              onFinish: {
                                                type: 'clientAction',
                                                action: 'resetFields',
                                                payload: {
                                                  fields: [
                                                    'director_name',
                                                    'director_identity',
                                                    'director_file',
                                                  ],
                                                },
                                              },
                                              redirect:
                                                '/settings/company/directors',
                                            },
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'navigate',
                                            payload: ['/teams'],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                },
                              ],
                              navs: [],
                            },
                          ],
                        },
                      ],
                      whenFalse: [
                        {
                          type: 'clientAction',
                          action: 'navigate',
                          payload: [
                            '/teams/manage-team/list-view',
                            { clearParams: true },
                          ],
                        },
                      ],
                    },
                  },
                },
              ],
            },
            {
              label: 'Previous',
              position: 'left',
              toScreen: '../banking',
            },
            {
              label: 'Cancel Changes',
              position: 'center',
              disabledWhen:
                '!$form.director_name || !$form.director_identity || !$form.director_file',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'resetFields',
                  payload: {
                    fields: [
                      'director_name',
                      'director_identity',
                      'director_file',
                    ],
                  },
                },
              ],
            },
            {
              label: 'Save Changes',
              disabledWhen:
                '!$form.director_name || !$form.director_identity || !$form.director_file',
              position: 'center',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'submitAsync',
                  payload: {
                    calls: [
                      {
                        key: 'add_director',
                        url: '{VITE_SP_SERVER}/api/v1/spaas_actions/add_director',
                        data: {
                          name: '$director_name',
                          identity: '$director_identity',
                        },
                      },
                      {
                        key: 'upload_director_file',
                        url: '{VITE_SP_SERVER}/api/v1/file_actions/director_upload_file',
                        data: {
                          file: '$director_file',
                          purpose: 'Identity',
                          list: 'True',
                          director_id: '{add_director.payload.id}',
                        },
                      },
                    ],
                    onFinish: {
                      type: 'clientAction',
                      action: 'resetFields',
                      payload: {
                        fields: [
                          'director_name',
                          'director_identity',
                          'director_file',
                        ],
                      },
                    },
                    redirect: '/settings/company/directors',
                  },
                  // payload: [
                  //   {
                  //     postData: '$postData',
                  //   },
                  //   {
                  //     url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                  //     headers: {},
                  //     redirect: '/settings/company/banking',
                  //     bodySlicePath: 'postData',
                  //     // redirect: '/settings/company/directors',
                  //   },
                  //   { method: 'post', action: '/settings/company/banking' },
                  // ],
                },
              ],
            },
            { label: 'Next', position: 'right', toScreen: '../contact' },
          ],
        },

        contact: {
          layout: {},
          fetchCalls: [],
          fragments: [
            {
              component: 'TabsAndOptions',
              props: {
                tabs: [
                  {
                    name: 'Company',
                    path: '../company',
                  },
                  {
                    name: 'Banking',
                    path: '../banking',
                  },
                  {
                    name: 'Directors',
                    path: '../directors',
                  },
                  {
                    name: 'Contact',
                    path: '../contact',
                  },
                  {
                    name: 'Work',
                    path: '../work',
                  },
                  {
                    name: 'Areas',
                    path: '../areas',
                  },
                  {
                    name: 'Documents',
                    path: '../documents',
                  },
                  {
                    name: 'Subscription',
                    path: '../subscription',
                  },
                ],
              },
              isStickyHeader: true,
              layout: {},
            },
            {
              component: 'Text',
              props: {
                textItems: [
                  {
                    text: 'Company contact information',
                    options: {
                      format: 'heading',
                      type: 'page-heading',
                    },
                  },
                ],
              },
              layout: {
                display: 'grid',
                justifyItems: 'center',
                paddingTop: '2rem',
                paddingBottom: '2rem',
              },
            },
            {
              component: 'FormBuilder',
              props: {
                defaultValues: {
                  contact_primary: '$sp_profile.address.contact_primary',
                  contact_secondary: '$sp_profile.address.contact_secondary',
                  contact_person: '$sp_profile.address.contact_person',
                  email_receiving: '$sp_profile.address.email_receiving',
                },
                config: {
                  style: {
                    display: 'grid',
                    gridTemplateColumns: 'repeat(2, 1fr)',
                    gridTemplateRows: 'repeat(2, 1fr)',
                    rowGap: '2rem',
                    columnGap: '1rem',
                    justifyItems: 'center',
                    alignContent: 'space-around',
                    height: 'auto',
                    paddingTop: '2rem',
                    paddingBottom: '2rem',
                    width: '50%',
                    minWidth: '712px',
                  },
                  controls: [
                    {
                      type: 'plain-text',
                      name: 'contact_person',
                      label: 'Primary Contact Person Name',
                      fieldAccessPath: {
                        view: 'address',
                        edit: 'address',
                        special: 'address',
                      },
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                      },
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 1,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'contact_primary',
                      label: 'Primary Contact Number',
                      fieldAccessPath: {
                        view: 'address',
                        edit: 'address',
                        special: 'address',
                      },
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                        pattern: {
                          value: validationRegex.phone_number.pattern,
                          message: validationRegex.phone_number.message,
                        },
                      },
                      css: {
                        wrapper: {
                          gridColumn: 2,
                          gridRow: 1,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'contact_secondary',
                      label: 'Secondary Contact Number',
                      validation: {
                        pattern: {
                          value: validationRegex.phone_number.pattern,
                          message: validationRegex.phone_number.message,
                        },
                      },
                      fieldAccessPath: {
                        view: 'address',
                        edit: 'address',
                        special: 'address',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 2,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'email_receiving',
                      label: 'Email Address',
                      fieldAccessPath: {
                        view: 'address',
                        edit: 'address',
                        special: 'address',
                      },
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                        pattern: {
                          value: validationRegex.email.pattern,
                          message: validationRegex.email.message,
                        },
                      },
                      css: {
                        wrapper: {
                          gridColumn: 2,
                          gridRow: 2,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                  ],
                },
              },
              layout: { display: 'grid', justifyItems: 'center' },
            },
            {
              component: 'Separator',
              layout: { width: '50%', minWidth: '712px', margin: 'auto' },
              props: { height: 'thin' },
            },
            {
              component: 'FormBuilder',
              props: {
                defaultValues: {
                  physical_addr: '$sp_profile.address.physical_addr',
                  physical_city: '$sp_profile.address.physical_city',
                  physical_code: '$sp_profile.address.physical_code',
                  physical_suburb: '$sp_profile.address.physical_suburb',
                  province: '$sp_profile.address.province',
                },
                config: {
                  style: {
                    display: 'grid',
                    gridTemplateColumns: 'repeat(2, 1fr)',
                    gridTemplateRows: 'repeat(3, 1fr)',
                    rowGap: '2rem',
                    columnGap: '1rem',
                    justifyItems: 'center',
                    alignContent: 'space-around',
                    height: 'auto',
                    paddingTop: '2rem',
                    paddingBottom: '2rem',
                    width: '50%',
                    minWidth: '712px',
                  },
                  controls: [
                    {
                      type: 'plain-text',
                      name: 'physical_addr',
                      label: 'Company street address',
                      fieldAccessPath: {
                        view: 'address',
                        edit: 'address',
                        special: 'address',
                      },
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                      },
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 1,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'physical_suburb',
                      label: 'Company suburb',
                      fieldAccessPath: {
                        view: 'address',
                        edit: 'address',
                        special: 'address',
                      },
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                      },
                      css: {
                        wrapper: {
                          gridColumn: 2,
                          gridRow: 1,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'physical_city',
                      label: 'Company city',
                      fieldAccessPath: {
                        view: 'address',
                        edit: 'address',
                        special: 'address',
                      },
                      validation: {
                        required: {
                          value: true,
                          message: 'This is required',
                        },
                      },
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 2,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'physical_code',
                      label: 'Company postal code',
                      fieldAccessPath: {
                        view: 'address',
                        edit: 'address',
                        special: 'address',
                      },
                      validation: {
                        required: {
                          value: true,
                          message: 'This is required',
                        },
                      },
                      css: {
                        wrapper: {
                          gridColumn: 2,
                          gridRow: 2,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'single-select',
                      name: 'province',
                      label: 'Company province',
                      fieldAccessPath: {
                        view: 'address',
                        edit: 'address',
                        special: 'address',
                      },
                      dropdownScroll: true,
                      labelProp: 'name',
                      valueProp: 'name',
                      validation: {
                        required: {
                          value: true,
                          message: 'Province is required',
                        },
                      },
                      options: {
                        source: 'store',
                        storeDataPath: 'sp_enums.provinces',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 3,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                  ],
                },
              },
              layout: { display: 'grid', justifyItems: 'center' },
            },
            {
              component: 'Separator',
              layout: { width: '50%', minWidth: '712px', margin: 'auto' },
              props: { height: 'thin' },
            },
            {
              component: 'FormBuilder',
              props: {
                defaultValues: {
                  postal_box: '$sp_profile.address.postal_box',
                  postal_city: '$sp_profile.address.postal_city',
                  postal_code: '$sp_profile.address.postal_code',
                },
                config: {
                  style: {
                    display: 'grid',
                    gridTemplateColumns: 'repeat(2, 1fr)',
                    gridTemplateRows: 'repeat(2, 1fr)',
                    rowGap: '2rem',
                    columnGap: '1rem',
                    justifyItems: 'center',
                    alignContent: 'space-around',
                    height: 'auto',
                    paddingTop: '2rem',
                    paddingBottom: '2rem',
                    width: '50%',
                    minWidth: '712px',
                  },
                  controls: [
                    {
                      type: 'plain-text',
                      name: 'postal_box',
                      label: 'Postal address',
                      fieldAccessPath: {
                        view: 'address',
                        edit: 'address',
                        special: 'address',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 1,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'postal_city',
                      label: 'Postal address suburb/town',
                      fieldAccessPath: {
                        view: 'address',
                        edit: 'address',
                        special: 'address',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 2,
                          gridRow: 1,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'postal_code',
                      label: 'Postal address postal code',
                      fieldAccessPath: {
                        view: 'address',
                        edit: 'address',
                        special: 'address',
                      },
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                      },
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 2,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                  ],
                },
              },
              layout: { display: 'grid', justifyItems: 'center' },
            },
          ],
          navs: [
            {
              label: 'Back To Teams',
              position: 'left',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'conditional',
                  payload: {
                    condition:
                      '$store?.postData && (Object.keys($store.postData).length > 0) && $formState.isValid',
                    actions: {
                      whenTrue: [
                        {
                          type: 'clientAction',
                          action: 'triggerModal',
                          payload: [
                            {
                              display: true,
                              type: 'warning',
                              layout: {},
                              onEnter: [],
                              onLeave: [],
                              fragments: [
                                {
                                  component: 'Text',
                                  props: {
                                    textItems: [
                                      {
                                        text: 'Warning',
                                        options: {
                                          format: 'heading',
                                          type: 'page-heading',
                                        },
                                      },
                                      {
                                        text: 'You have unsaved changes. What would you like to do?',
                                        options: {
                                          format: 'heading',
                                          type: 'sub-heading',
                                          style: {
                                            paddingTop: '2rem',
                                          },
                                        },
                                      },
                                    ],
                                  },
                                  layout: {
                                    display: 'grid',
                                    gridAutoFlow: 'row',
                                    justifyItems: 'center',
                                  },
                                },
                                {
                                  component: 'ButtonRow',
                                  layout: {
                                    width: 'fit-content',
                                    margin: '0 auto',
                                  },
                                  props: {
                                    buttons: [
                                      {
                                        btnValue: 'Clear Changes',
                                        actiontype: 'warning',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'navigate',
                                            payload: [
                                              '/teams/manage-team/list-view',
                                              { clearParams: true },
                                            ],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                      {
                                        btnValue: 'Save Changes',
                                        actiontype: 'proceed',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'submitAndFetch',
                                            payload: [
                                              {
                                                postData: '$postData',
                                              },
                                              {
                                                url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                                headers: {},
                                                bodySlicePath: 'postData',
                                                redirect: '/teams',
                                              },
                                              {
                                                method: 'post',
                                                action:
                                                  '/settings/company/contact',
                                              },
                                            ],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                },
                              ],
                              navs: [],
                            },
                          ],
                        },
                      ],
                      whenFalse: [
                        {
                          type: 'clientAction',
                          action: 'navigate',
                          payload: [
                            '/teams/manage-team/list-view',
                            { clearParams: true },
                          ],
                        },
                      ],
                    },
                  },
                },
              ],
            },
            {
              label: 'Previous',
              position: 'left',
              toScreen: '../directors',
            },
            {
              label: 'Cancel Changes',
              position: 'center',
              disabledWhen:
                '$store?.postData && !(Object.keys($store.postData).length > 0)',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'resetFields',
                  payload: {
                    fields: [
                      {
                        fieldName: 'trading_as',
                        defaultValue: '$sp_profile.details.trading_as',
                      },
                      {
                        fieldName: 'bbeee',
                        defaultValue: '$sp_profile.details.bbeee',
                      },
                      {
                        fieldName: 'b_acc_holder',
                        defaultValue: '$sp_profile.financial.b_acc_holder',
                      },
                      {
                        fieldName: 'b_branch_name',
                        defaultValue: '$sp_profile.financial.b_branch_name',
                      },
                      {
                        fieldName: 'b_acc_type',
                        defaultValue: '$sp_profile.financial.b_acc_type',
                      },
                      {
                        fieldName: 'b_branch_code',
                        defaultValue: '$sp_profile.financial.b_branch_code',
                      },
                      {
                        fieldName: 'b_bank_name',
                        defaultValue: '$sp_profile.financial.b_bank_name',
                      },
                      {
                        fieldName: 'b_acc_no',
                        defaultValue: '$sp_profile.financial.b_acc_no',
                      },
                      {
                        fieldName: 'vat_no',
                        defaultValue: '$sp_profile.financial.vat_no',
                      },
                      {
                        fieldName: 'tax_no',
                        defaultValue: '$sp_profile.financial.tax_no',
                      },
                      {
                        fieldName: 'contact_person',
                        defaultValue: '$sp_profile.address.contact_person',
                      },
                      {
                        fieldName: 'contact_primary',
                        defaultValue: '$sp_profile.address.contact_primary',
                      },
                      {
                        fieldName: 'contact_secondary',
                        defaultValue: '$sp_profile.address.contact_secondary',
                      },
                      {
                        fieldName: 'email_receiving',
                        defaultValue: '$sp_profile.address.email_receiving',
                      },
                      {
                        fieldName: 'physical_addr',
                        defaultValue: '$sp_profile.address.physical_addr',
                      },
                      {
                        fieldName: 'physical_city',
                        defaultValue: '$sp_profile.address.physical_city',
                      },
                      {
                        fieldName: 'physical_code',
                        defaultValue: '$sp_profile.address.physical_code',
                      },
                      {
                        fieldName: 'physical_suburb',
                        defaultValue: '$sp_profile.address.physical_suburb',
                      },
                      {
                        fieldName: 'province',
                        defaultValue: '$sp_profile.address.province',
                      },
                      {
                        fieldName: 'postal_box',
                        defaultValue: '$sp_profile.address.postal_box',
                      },
                      {
                        fieldName: 'postal_city',
                        defaultValue: '$sp_profile.address.postal_city',
                      },
                      {
                        fieldName: 'postal_code',
                        defaultValue: '$sp_profile.address.postal_code',
                      },
                      {
                        fieldName: 'skills',
                        defaultValue: '$sp_profile.skills',
                      },
                      {
                        fieldName: 'after_hours',
                        defaultValue: '$sp_profile.after_hours',
                      },
                      {
                        fieldName: 'companies',
                        defaultValue: '$derivedCompanies',
                      },
                      {
                        fieldName: 'accredition',
                        defaultValue:
                          '$sp_profile.additional_identities.accredition',
                      },
                      {
                        fieldName: 'mid',
                        defaultValue: '$sp_profile.additional_identities.mid',
                      },
                      {
                        fieldName: 'operational_area',
                        defaultValue: '$derivedOperationalArea',
                      },
                      {
                        fieldName: 'radius',
                        defaultValue:
                          '$derivedOperationalArea.0.operating_range',
                      },
                      {
                        fieldName: 'jobLocation',
                        defaultValue: '$derivedOperationalArea.0.location',
                      },
                    ],
                  },
                },
              ],
            },
            {
              label: 'Save Changes',
              position: 'center',
              disabledWhen:
                '!$store?.postData || !(Object.keys($store.postData).length > 0) || !$formState.isValid || !$formState.isDirty',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'submitAndFetch',
                  payload: [
                    {
                      postData: '$postData',
                    },
                    {
                      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                      headers: {},
                      bodySlicePath: 'postData',
                      // redirect: '/settings/company/work',
                    },
                    { method: 'post', action: '/settings/company/contact' },
                  ],
                },
              ],
            },
            { label: 'Next', position: 'right', toScreen: '../work' },
          ],
        },

        work: {
          layout: {},
          fetchCalls: [],
          fragments: [
            {
              component: 'TabsAndOptions',
              props: {
                tabs: [
                  {
                    name: 'Company',
                    path: '../company',
                  },
                  {
                    name: 'Banking',
                    path: '../banking',
                  },
                  {
                    name: 'Directors',
                    path: '../directors',
                  },
                  {
                    name: 'Contact',
                    path: '../contact',
                  },
                  {
                    name: 'Work',
                    path: '../work',
                  },
                  {
                    name: 'Areas',
                    path: '../areas',
                  },
                  {
                    name: 'Documents',
                    path: '../documents',
                  },
                  {
                    name: 'Subscription',
                    path: '../subscription',
                  },
                ],
              },
              isStickyHeader: true,
              layout: {},
            },
            {
              component: 'Text',
              props: {
                textItems: [
                  {
                    text: 'Scope of work you accept',
                    options: {
                      format: 'heading',
                      type: 'page-heading',
                    },
                  },
                ],
              },
              layout: {
                display: 'grid',
                justifyItems: 'center',
                paddingTop: '2rem',
                paddingBottom: '2rem',
              },
            },
            {
              component: 'FormBuilder',
              props: {
                defaultValues: {
                  skills: '$sp_profile.skills',
                  companies: '$sp_profile.companies',
                  after_hours: '$sp_profile.after_hours',
                  // mid: '$sp_profile.mid',
                  // accredition: '$sp_profile.accredition',
                },
                config: {
                  style: {
                    display: 'grid',
                    gridTemplateColumns: 'repeat(2, 1fr)',
                    // gridTemplateRows: 'repeat(3, 1fr)',
                    gridAutoFlow: 'row',
                    rowGap: '2rem',
                    columnGap: '1rem',
                    justifyItems: 'center',
                    alignContent: 'space-around',
                    height: 'auto',
                    paddingTop: '2rem',
                    paddingBottom: '2rem',
                    width: '50%',
                    minWidth: '712px',
                  },
                  controls: [
                    {
                      type: 'multi-select',
                      name: 'skills',
                      label: 'Company skills',
                      fieldAccessPath: {
                        view: 'skills',
                        edit: 'skills',
                        special: 'skills',
                      },
                      labelProp: 'name',
                      valueProp: 'id',
                      position: 'right',
                      options: {
                        source: 'store',
                        storeDataPath: 'sp_enums.skills',
                      },
                      multiSelect: true,
                      dropdownScroll: true,
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 1,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'radio-group',
                      name: 'after_hours',
                      label: 'Do you work after hours?',
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                      },
                      fieldAccessPath: {
                        view: 'afterhours',
                        edit: 'afterhours',
                        special: 'afterhours',
                      },
                      options: {
                        source: 'literal',
                        data: [
                          { label: 'Yes', value: true },
                          { label: 'No', value: false },
                        ],
                      },
                      returnBoolean: true,
                      size: 'small',
                      columns: 6,
                      css: {
                        wrapper: {
                          gridColumn: 2,
                          gridRow: 1,
                          justifySelf: 'start',
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'multi-select',
                      name: 'companies',
                      label: 'Companies you would like to recieve work from',
                      labelProp: 'name',
                      valueProp: 'id',
                      options: {
                        source: 'store',
                        storeDataPath: 'sp_enums.companies',
                      },
                      position: 'right',
                      multiSelect: true,
                      dropdownScroll: true,
                      dataTransformMapField: 'client_id',
                      fieldAccessPath: {
                        view: 'activeregionsbyclient',
                        edit: 'serviceproviderclient',
                        special: 'serviceproviderclient',
                      },
                      fieldAccessKey: 'client',
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 2,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    // {
                    //   type: 'plain-text',
                    //   name: 'mid',
                    //   label: 'Standard Insurance (SIL) MID',
                    //   icon: 'alarm-clock',
                    //   //
                    //   position: 'right',
                    //   css: {
                    //     wrapper: {
                    //       gridColumn: 1,
                    //       gridRow: 3,
                    //       height: '4rem',
                    //       width: '100%',
                    //     },
                    //   },
                    // },
                    // {
                    //   type: 'plain-text',
                    //   name: 'accredition',
                    //   label: 'Multichoice accredition number',
                    //   icon: 'alarm-clock',
                    //   //
                    //   position: 'right',
                    //   css: {
                    //     wrapper: {
                    //       gridColumn: 2,
                    //       gridRow: 3,
                    //       height: '4rem',
                    //       width: '100%',
                    //     },
                    //   },
                    // },
                  ],
                },
              },
              layout: { display: 'grid', justifyItems: 'center' },
            },
          ],
          navs: [
            {
              label: 'Back To Teams',
              position: 'left',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'conditional',
                  payload: {
                    condition:
                      '$store?.postData && (Object.keys($store.postData).length > 0) && $formState.isValid',
                    actions: {
                      whenTrue: [
                        {
                          type: 'clientAction',
                          action: 'triggerModal',
                          payload: [
                            {
                              display: true,
                              type: 'warning',
                              layout: {},
                              onEnter: [],
                              onLeave: [],
                              fragments: [
                                {
                                  component: 'Text',
                                  props: {
                                    textItems: [
                                      {
                                        text: 'Warning',
                                        options: {
                                          format: 'heading',
                                          type: 'page-heading',
                                        },
                                      },
                                      {
                                        text: 'You have unsaved changes. What would you like to do?',
                                        options: {
                                          format: 'heading',
                                          type: 'sub-heading',
                                          style: {
                                            paddingTop: '2rem',
                                          },
                                        },
                                      },
                                    ],
                                  },
                                  layout: {
                                    display: 'grid',
                                    gridAutoFlow: 'row',
                                    justifyItems: 'center',
                                  },
                                },
                                {
                                  component: 'ButtonRow',
                                  layout: {
                                    width: 'fit-content',
                                    margin: '0 auto',
                                  },
                                  props: {
                                    buttons: [
                                      {
                                        btnValue: 'Clear Changes',
                                        actiontype: 'warning',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'navigate',
                                            payload: [
                                              '/teams/manage-team/list-view',
                                              { clearParams: true },
                                            ],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                      {
                                        btnValue: 'Save Changes',
                                        actiontype: 'proceed',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'submitAndFetch',
                                            payload: [
                                              {
                                                postData: '$postData',
                                                sp_id: '$sp_profile.id',
                                              },
                                              {
                                                url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                                headers: {},
                                                bodySlicePath: 'postData',
                                              },
                                              {
                                                method: 'post',
                                                action:
                                                  '/settings/company/areas',
                                              },
                                            ],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'navigate',
                                            payload: [
                                              '/teams/manage-team/list-view',
                                              { clearParams: true },
                                            ],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                },
                              ],
                              navs: [],
                            },
                          ],
                        },
                      ],
                      whenFalse: [
                        {
                          type: 'clientAction',
                          action: 'navigate',
                          payload: [
                            '/teams/manage-team/list-view',
                            { clearParams: true },
                          ],
                        },
                      ],
                    },
                  },
                },
              ],
            },
            {
              label: 'Previous',
              position: 'left',
              toScreen: '../contact',
            },
            {
              label: 'Cancel Changes',
              position: 'center',
              disabledWhen: `$store?.postData && !(Object.keys($store.postData).length > 0)`,
              onClick: [
                {
                  type: 'clientAction',
                  action: 'resetFields',
                  payload: {
                    fields: [
                      {
                        fieldName: 'trading_as',
                        defaultValue: '$sp_profile.details.trading_as',
                      },
                      {
                        fieldName: 'bbeee',
                        defaultValue: '$sp_profile.details.bbeee',
                      },
                      {
                        fieldName: 'b_acc_holder',
                        defaultValue: '$sp_profile.financial.b_acc_holder',
                      },
                      {
                        fieldName: 'b_branch_name',
                        defaultValue: '$sp_profile.financial.b_branch_name',
                      },
                      {
                        fieldName: 'b_acc_type',
                        defaultValue: '$sp_profile.financial.b_acc_type',
                      },
                      {
                        fieldName: 'b_branch_code',
                        defaultValue: '$sp_profile.financial.b_branch_code',
                      },
                      {
                        fieldName: 'b_bank_name',
                        defaultValue: '$sp_profile.financial.b_bank_name',
                      },
                      {
                        fieldName: 'b_acc_no',
                        defaultValue: '$sp_profile.financial.b_acc_no',
                      },
                      {
                        fieldName: 'vat_no',
                        defaultValue: '$sp_profile.financial.vat_no',
                      },
                      {
                        fieldName: 'tax_no',
                        defaultValue: '$sp_profile.financial.tax_no',
                      },
                      {
                        fieldName: 'contact_person',
                        defaultValue: '$sp_profile.address.contact_person',
                      },
                      {
                        fieldName: 'contact_primary',
                        defaultValue: '$sp_profile.address.contact_primary',
                      },
                      {
                        fieldName: 'contact_secondary',
                        defaultValue: '$sp_profile.address.contact_secondary',
                      },
                      {
                        fieldName: 'email_receiving',
                        defaultValue: '$sp_profile.address.email_receiving',
                      },
                      {
                        fieldName: 'physical_addr',
                        defaultValue: '$sp_profile.address.physical_addr',
                      },
                      {
                        fieldName: 'physical_city',
                        defaultValue: '$sp_profile.address.physical_city',
                      },
                      {
                        fieldName: 'physical_code',
                        defaultValue: '$sp_profile.address.physical_code',
                      },
                      {
                        fieldName: 'physical_suburb',
                        defaultValue: '$sp_profile.address.physical_suburb',
                      },
                      {
                        fieldName: 'province',
                        defaultValue: '$sp_profile.address.province',
                      },
                      {
                        fieldName: 'postal_box',
                        defaultValue: '$sp_profile.address.postal_box',
                      },
                      {
                        fieldName: 'postal_city',
                        defaultValue: '$sp_profile.address.postal_city',
                      },
                      {
                        fieldName: 'postal_code',
                        defaultValue: '$sp_profile.address.postal_code',
                      },
                      {
                        fieldName: 'skills',
                        defaultValue: '$sp_profile.skills',
                      },
                      {
                        fieldName: 'after_hours',
                        defaultValue: '$sp_profile.after_hours',
                      },
                      {
                        fieldName: 'companies',
                        defaultValue: '$derivedCompanies',
                      },
                      {
                        fieldName: 'accredition',
                        defaultValue:
                          '$sp_profile.additional_identities.accredition',
                      },
                      {
                        fieldName: 'mid',
                        defaultValue: '$sp_profile.additional_identities.mid',
                      },
                      {
                        fieldName: 'operational_area',
                        defaultValue: '$derivedOperationalArea',
                      },
                      {
                        fieldName: 'radius',
                        defaultValue:
                          '$derivedOperationalArea.0.operating_range',
                      },
                      {
                        fieldName: 'jobLocation',
                        defaultValue: '$derivedOperationalArea.0.location',
                      },
                    ],
                  },
                },
              ],
            },
            {
              label: 'Save Changes',
              position: 'center',
              // disabledWhen:
              //   '!$store?.postData || !(Object.keys($store.postData).length > 0) || $formState.isValid || $formState.isDirty',
              disabledWhen:
                '!$store?.postData || !(Object.keys($store.postData).length > 0)',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'submitAndFetch',
                  payload: [
                    {
                      postData: '$postData',
                    },
                    {
                      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                      headers: {},
                      bodySlicePath: 'postData',
                    },
                    { method: 'post', action: '/settings/company/work' },
                  ],
                },
              ],
            },
            { label: 'Next', position: 'right', toScreen: '../areas' },
          ],
        },

        areas: {
          layout: {},
          fetchCalls: [],
          fragments: [
            {
              component: 'TabsAndOptions',
              props: {
                tabs: [
                  {
                    name: 'Company',
                    path: '../company',
                  },
                  {
                    name: 'Banking',
                    path: '../banking',
                  },
                  {
                    name: 'Directors',
                    path: '../directors',
                  },
                  {
                    name: 'Contact',
                    path: '../contact',
                  },
                  {
                    name: 'Work',
                    path: '../work',
                  },
                  {
                    name: 'Areas',
                    path: '../areas',
                  },
                  {
                    name: 'Documents',
                    path: '../documents',
                  },
                  {
                    name: 'Subscription',
                    path: '../subscription',
                  },
                ],
              },
              isStickyHeader: true,
              layout: {},
            },
            {
              component: 'FormBuilder',
              layout: {
                paddingTop: '2rem',
              },
              props: {
                operational_area: '$sp_profile.operational_area',
                defaultValues: {
                  // operational_area: '$sp_profile.operational_area',
                  radius: '$sp_profile.operational_area.0.operating_range',
                  jobLocation: '$sp_profile.operational_area.0.location',
                },
                config: {
                  style: {
                    display: 'grid',
                    gridTemplateColumns: '1fr',
                    rowGap: '15px',
                    justifyItems: 'center',
                  },
                  controls: [
                    {
                      type: 'radius', // Custom control type for OperationAreas
                      name: 'operational_area',
                      label: 'Operational Area',
                      instructions:
                        'Drag the pin to adjust your work area radius',
                      fieldAccessPath: {
                        view: 'operational_area',
                        edit: 'operational_area',
                        special: 'operational_area',
                      },
                      fieldAccessKey: 'location',
                      marks: [
                        { value: 25, label: '25km' },
                        { value: 50, label: '50km' },
                        { value: 75, label: '75km' },
                        { value: 100, label: '100km' },
                        { value: 150, label: '150km' },
                        { value: 200, label: '200km' },
                        { value: 250, label: '250km' },
                      ],
                      // operations: {
                      //   source: 'literal',
                      //   data: [
                      //     { label: '10km', value: 10000 },
                      //     { label: '20km', value: 20000 },
                      //   ],
                      // },
                      css: { wrapper: { gridColumn: '1', gridRow: '1' } },
                    },
                  ],
                },
              },
            },
          ],
          navs: [
            {
              label: 'Back To Teams',
              position: 'left',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'conditional',
                  payload: {
                    condition:
                      '!$formState.isDirty || !$formState.isValid || !(Object.keys($formState.touchedFields).length > 0)',
                    actions: {
                      whenTrue: [
                        {
                          type: 'clientAction',
                          action: 'triggerModal',
                          payload: [
                            {
                              display: true,
                              type: 'warning',
                              layout: {},
                              onEnter: [],
                              onLeave: [],
                              fragments: [
                                {
                                  component: 'Text',
                                  props: {
                                    textItems: [
                                      {
                                        text: 'Warning',
                                        options: {
                                          format: 'heading',
                                          type: 'page-heading',
                                        },
                                      },
                                      {
                                        text: 'You have unsaved changes. What would you like to do?',
                                        options: {
                                          format: 'heading',
                                          type: 'sub-heading',
                                          style: {
                                            paddingTop: '2rem',
                                          },
                                        },
                                      },
                                    ],
                                  },
                                  layout: {
                                    display: 'grid',
                                    gridAutoFlow: 'row',
                                    justifyItems: 'center',
                                  },
                                },
                                {
                                  component: 'ButtonRow',
                                  layout: {
                                    width: 'fit-content',
                                    margin: '0 auto',
                                  },
                                  props: {
                                    buttons: [
                                      {
                                        btnValue: 'Clear Changes',
                                        actiontype: 'warning',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'navigate',
                                            payload: [
                                              '/teams/manage-team/list-view',
                                              { clearParams: true },
                                            ],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                      {
                                        btnValue: 'Save Changes',
                                        actiontype: 'proceed',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'submitAndNavigate',
                                            payload: [
                                              {
                                                operational_area:
                                                  '$formDataRaw.operational_area',
                                              },
                                              {
                                                url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                                headers: {},
                                                redirect:
                                                  '/teams/manage-team/list-view',
                                              },
                                              {
                                                method: 'post',
                                                action:
                                                  '/settings/company/areas',
                                              },
                                            ],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                },
                              ],
                              navs: [],
                            },
                          ],
                        },
                      ],
                      whenFalse: [
                        {
                          type: 'clientAction',
                          action: 'navigate',
                          payload: [
                            '/teams/manage-team/list-view',
                            { clearParams: true },
                          ],
                        },
                      ],
                    },
                  },
                },
              ],
            },
            {
              label: 'Previous',
              position: 'left',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'resetFields',
                  payload: {
                    fields: [
                      {
                        fieldName: 'operational_area',
                        defaultValue: '$derivedOperationalArea',
                      },
                      {
                        fieldName: 'radius',
                        defaultValue:
                          '$derivedOperationalArea.0.operating_range',
                      },
                      {
                        fieldName: 'jobLocation',
                        defaultValue: '$derivedOperationalArea.0.location',
                      },
                    ],
                  },
                },
                {
                  type: 'clientAction',
                  action: 'navigate',
                  payload: ['/settings/company/work'],
                },
              ],
            },
            {
              label: 'Cancel Changes',
              position: 'center',
              disabledWhen: 'true',
              // '($store?.postData && !(Object.keys($store.postData).length > 0)) && !$store.updatedOperationalArea',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'resetFields',
                  payload: {
                    fields: [
                      {
                        fieldName: 'trading_as',
                        defaultValue: '$sp_profile.details.trading_as',
                      },
                      {
                        fieldName: 'bbeee',
                        defaultValue: '$sp_profile.details.bbeee',
                      },
                      {
                        fieldName: 'b_acc_holder',
                        defaultValue: '$sp_profile.financial.b_acc_holder',
                      },
                      {
                        fieldName: 'b_branch_name',
                        defaultValue: '$sp_profile.financial.b_branch_name',
                      },
                      {
                        fieldName: 'b_acc_type',
                        defaultValue: '$sp_profile.financial.b_acc_type',
                      },
                      {
                        fieldName: 'b_branch_code',
                        defaultValue: '$sp_profile.financial.b_branch_code',
                      },
                      {
                        fieldName: 'b_bank_name',
                        defaultValue: '$sp_profile.financial.b_bank_name',
                      },
                      {
                        fieldName: 'b_acc_no',
                        defaultValue: '$sp_profile.financial.b_acc_no',
                      },
                      {
                        fieldName: 'vat_no',
                        defaultValue: '$sp_profile.financial.vat_no',
                      },
                      {
                        fieldName: 'tax_no',
                        defaultValue: '$sp_profile.financial.tax_no',
                      },
                      {
                        fieldName: 'contact_person',
                        defaultValue: '$sp_profile.address.contact_person',
                      },
                      {
                        fieldName: 'contact_primary',
                        defaultValue: '$sp_profile.address.contact_primary',
                      },
                      {
                        fieldName: 'contact_secondary',
                        defaultValue: '$sp_profile.address.contact_secondary',
                      },
                      {
                        fieldName: 'email_receiving',
                        defaultValue: '$sp_profile.address.email_receiving',
                      },
                      {
                        fieldName: 'physical_addr',
                        defaultValue: '$sp_profile.address.physical_addr',
                      },
                      {
                        fieldName: 'physical_city',
                        defaultValue: '$sp_profile.address.physical_city',
                      },
                      {
                        fieldName: 'physical_code',
                        defaultValue: '$sp_profile.address.physical_code',
                      },
                      {
                        fieldName: 'physical_suburb',
                        defaultValue: '$sp_profile.address.physical_suburb',
                      },
                      {
                        fieldName: 'province',
                        defaultValue: '$sp_profile.address.province',
                      },
                      {
                        fieldName: 'postal_box',
                        defaultValue: '$sp_profile.address.postal_box',
                      },
                      {
                        fieldName: 'postal_city',
                        defaultValue: '$sp_profile.address.postal_city',
                      },
                      {
                        fieldName: 'postal_code',
                        defaultValue: '$sp_profile.address.postal_code',
                      },
                      {
                        fieldName: 'skills',
                        defaultValue: '$sp_profile.skills',
                      },
                      {
                        fieldName: 'after_hours',
                        defaultValue: '$sp_profile.after_hours',
                      },
                      {
                        fieldName: 'companies',
                        defaultValue: '$derivedCompanies',
                      },
                      {
                        fieldName: 'accredition',
                        defaultValue:
                          '$sp_profile.additional_identities.accredition',
                      },
                      {
                        fieldName: 'mid',
                        defaultValue: '$sp_profile.additional_identities.mid',
                      },
                      {
                        fieldName: 'operational_area',
                        defaultValue: '$derivedOperationalArea',
                      },
                      {
                        fieldName: 'radius',
                        defaultValue:
                          '$derivedOperationalArea.0.operating_range',
                      },
                      {
                        fieldName: 'jobLocation',
                        defaultValue: '$derivedOperationalArea.0.location',
                      },
                    ],
                    // fields: ['operational_area'],
                  },
                },
                {
                  type: 'clientAction',
                  action: 'clearStore',
                  payload: ['updatedOperationalArea'],
                },
              ],
            },
            {
              label: 'Save Changes',
              position: 'center',
              disabledWhen:
                // '!$store?.postData || !(Object.keys($store.postData).length > 0)',
                '$store?.postData || !(Object.keys($store.postData).length > 0)',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'submitAndFetch',
                  payload: [
                    {
                      postData: '$postData',
                    },
                    {
                      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                      headers: {},
                      bodySlicePath: 'postData',
                      // redirect: '/settings/company/work',
                    },
                    { method: 'post', action: '/settings/company/areas' },
                  ],
                },
              ],
            },
            // { label: 'Next', position: 'right', toScreen: '../documents' },
            {
              label: 'Next',
              position: 'right',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'resetFields',
                  payload: {
                    fields: [
                      {
                        fieldName: 'operational_area',
                        defaultValue: '$derivedOperationalArea',
                      },
                      {
                        fieldName: 'radius',
                        defaultValue:
                          '$derivedOperationalArea.0.operating_range',
                      },
                      {
                        fieldName: 'jobLocation',
                        defaultValue: '$derivedOperationalArea.0.location',
                      },
                    ],
                  },
                },
                {
                  type: 'clientAction',
                  action: 'navigate',
                  payload: ['/settings/company/documents'],
                },
              ],
            },
          ],
        },

        documents: {
          layout: {},
          fetchCalls: [],
          fragments: [
            {
              component: 'TabsAndOptions',
              props: {
                tabs: [
                  {
                    name: 'Company',
                    path: '../company',
                  },
                  {
                    name: 'Banking',
                    path: '../banking',
                  },
                  {
                    name: 'Directors',
                    path: '../directors',
                  },
                  {
                    name: 'Contact',
                    path: '../contact',
                  },
                  {
                    name: 'Work',
                    path: '../work',
                  },
                  {
                    name: 'Areas',
                    path: '../areas',
                  },
                  {
                    name: 'Documents',
                    path: '../documents',
                  },
                  {
                    name: 'Subscription',
                    path: '../subscription',
                  },
                ],
              },
              isStickyHeader: true,
              layout: {},
            },
            {
              component: 'Text',
              props: {
                textItems: [
                  {
                    text: 'Uploaded documents',
                    options: {
                      format: 'heading',
                      type: 'page-heading',
                    },
                  },
                  {
                    text: 'PDF only. Maximum 5Mb per document',
                    options: {
                      format: 'heading',
                      type: 'sub-heading',
                    },
                  },
                ],
              },
              layout: {
                display: 'grid',
                justifyItems: 'center',
                paddingTop: '2rem',
                paddingBottom: '2rem',
              },
            },
            {
              component: 'FormBuilder',
              props: {
                co_reg_document:
                  "$company_files?findLast:item.purpose === 'Reg Company registration'",
                pub_profile_document:
                  "$company_files?findLast:item.purpose === 'Reg Public profile'",
                pub_liability_document:
                  "$company_files?findLast:item.purpose === 'Reg Public liability insurance'",
                bbeee_cert_document:
                  "$company_files?findLast:item.purpose === 'Reg BBEEE certificate'",
                sars_tax_document:
                  "$company_files?findLast:item.purpose === 'Reg SARS Tax certificate'",
                proof_of_bank_account_document:
                  "$company_files?findLast:item.purpose === 'Reg Proof of bank account'",
                vehicle_document:
                  "$company_files?findLast:item.purpose === 'Reg Vehicle picture'",
                office_document:
                  "$company_files?findLast:item.purpose === 'Reg Office picture'",
                staff_uniform_document:
                  "$company_files?findLast:item.purpose === 'Reg Staff uniform picture'",
                config: {
                  style: {
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr',
                    gridAutoFlow: 'row',
                    rowGap: '2rem',
                    columnGap: '1rem',
                    justifyItems: 'center',
                    alignContent: 'space-around',
                    height: 'auto',
                    paddingTop: '2rem',
                    paddingBottom: '2rem',
                    width: '50%',
                    minWidth: '712px',
                  },
                  controls: [
                    {
                      type: 'document-card',
                      name: 'co_reg_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg Company registration',
                      purposeAsName: true,
                      isLandscape: false,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      base_url: 'VITE_SP_SERVER',
                      action: '/settings/company/documents',
                      list: 'False',
                      css: {
                        wrapper: {
                          // width: '100%',
                        },
                      },
                    },
                    {
                      type: 'document-card',
                      name: 'pub_profile_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg Public profile',
                      purposeAsName: true,
                      isLandscape: false,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      base_url: 'VITE_SP_SERVER',
                      action: '/settings/company/documents',
                      list: 'False',
                      css: {
                        wrapper: {
                          // width: '100%',
                        },
                      },
                    },
                    {
                      type: 'document-card',
                      name: 'pub_liability_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg Public liability insurance',
                      purposeAsName: true,
                      isLandscape: false,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      base_url: 'VITE_SP_SERVER',
                      action: '/settings/company/documents',
                      list: 'False',
                      css: {
                        wrapper: {
                          // width: '100%',
                        },
                      },
                    },
                    {
                      type: 'document-card',
                      name: 'bbeee_cert_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg BBEEE certificate',
                      purposeAsName: true,
                      isLandscape: false,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      base_url: 'VITE_SP_SERVER',
                      action: '/settings/company/documents',
                      list: 'False',
                      css: {
                        wrapper: {
                          // width: '100%',
                        },
                      },
                    },
                    {
                      type: 'document-card',
                      name: 'sars_tax_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg SARS Tax certificate',
                      purposeAsName: true,
                      isLandscape: false,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      base_url: 'VITE_SP_SERVER',
                      action: '/settings/company/documents',
                      list: 'False',
                      css: {
                        wrapper: {
                          // width: '100%',
                        },
                      },
                    },
                    {
                      type: 'document-card',
                      name: 'proof_of_bank_account_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg Proof of bank account',
                      purposeAsName: true,
                      isLandscape: false,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      base_url: 'VITE_SP_SERVER',
                      action: '/settings/company/documents',
                      list: 'False',
                      css: {
                        wrapper: {
                          // width: '100%',
                        },
                      },
                    },
                    {
                      type: 'document-card',
                      name: 'vehicle_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg Vehicle picture',
                      purposeAsName: true,
                      isLandscape: false,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      base_url: 'VITE_SP_SERVER',
                      action: '/settings/company/documents',
                      list: 'False',
                      css: {
                        wrapper: {
                          // width: '100%',
                        },
                      },
                    },
                    {
                      type: 'document-card',
                      name: 'office_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg Office picture',
                      purposeAsName: true,
                      isLandscape: false,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      base_url: 'VITE_SP_SERVER',
                      action: '/settings/company/documents',
                      list: 'False',
                      css: {
                        wrapper: {
                          // width: '100%',
                        },
                      },
                    },
                    {
                      type: 'document-card',
                      name: 'staff_uniform_document',
                      documents: '$sp_profile.documents',
                      purpose: 'Reg Staff uniform picture',
                      purposeAsName: true,
                      isLandscape: false,
                      url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                      base_url: 'VITE_SP_SERVER',
                      action: '/settings/company/documents',
                      list: 'False',
                      css: {
                        wrapper: {
                          // width: '100%',
                        },
                      },
                    },
                  ],
                },
              },
              layout: {
                display: 'grid',
                justifyItems: 'center',
                // width: 'calc(100% - 226px - 56px)',
              },
              disableComponentConfig: {
                disableCondition: '$isAdmin',
              },
            },
          ],
          navs: [
            {
              label: 'Back To Teams',
              position: 'left',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'conditional',
                  payload: {
                    condition:
                      '!$formState.isDirty || !$formState.isValid || !(Object.keys($formState.touchedFields).length > 0)',
                    actions: {
                      whenTrue: [
                        {
                          type: 'clientAction',
                          action: 'triggerModal',
                          payload: [
                            {
                              display: true,
                              type: 'warning',
                              layout: {},
                              onEnter: [],
                              onLeave: [],
                              fragments: [
                                {
                                  component: 'Text',
                                  props: {
                                    textItems: [
                                      {
                                        text: 'Warning',
                                        options: {
                                          format: 'heading',
                                          type: 'page-heading',
                                        },
                                      },
                                      {
                                        text: 'You have unsaved changes. What would you like to do?',
                                        options: {
                                          format: 'heading',
                                          type: 'sub-heading',
                                          style: {
                                            paddingTop: '2rem',
                                          },
                                        },
                                      },
                                    ],
                                  },
                                  layout: {
                                    display: 'grid',
                                    gridAutoFlow: 'row',
                                    justifyItems: 'center',
                                  },
                                },
                                {
                                  component: 'ButtonRow',
                                  layout: {
                                    width: 'fit-content',
                                    margin: '0 auto',
                                  },
                                  props: {
                                    buttons: [
                                      {
                                        btnValue: 'Clear Changes',
                                        actiontype: 'warning',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'navigate',
                                            payload: [
                                              '/teams/manage-team/list-view',
                                              { clearParams: true },
                                            ],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                      {
                                        btnValue: 'Save Changes',
                                        actiontype: 'proceed',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'submitAndNavigate',
                                            payload: [
                                              {
                                                operational_area:
                                                  '$formDataRaw.operational_area',
                                              },
                                              {
                                                url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                                headers: {},
                                                redirect:
                                                  '/teams/manage-team/list-view',
                                              },
                                              {
                                                method: 'post',
                                                action:
                                                  '/settings/company/areas',
                                              },
                                            ],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                },
                              ],
                              navs: [],
                            },
                          ],
                        },
                      ],
                      whenFalse: [
                        {
                          type: 'clientAction',
                          action: 'navigate',
                          payload: [
                            '/teams/manage-team/list-view',
                            { clearParams: true },
                          ],
                        },
                      ],
                    },
                  },
                },
              ],
            },
            {
              label: 'Previous',
              position: 'left',
              toScreen: '../areas',
            },
            {
              label: 'Cancel Changes',
              position: 'center',
              disabledWhen:
                // '$store?.postData && !(Object.keys($store.postData).length > 0) && (JSON.stringify($store.postData.operational_area) === JSON.stringify($store.originalValues.operational_area))',
                '($store?.postData && !(Object.keys($store.postData).length > 0)) && !$store.updatedOperationalArea',
              // '!$store.updatedOperationalArea',
              // '!$formState.isDirty || !$formState.isValid || !(Object.keys($formState.touchedFields).length > 0)',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'resetFields',
                  payload: {
                    fields: [
                      {
                        fieldName: 'trading_as',
                        defaultValue: '$sp_profile.details.trading_as',
                      },
                      {
                        fieldName: 'bbeee',
                        defaultValue: '$sp_profile.details.bbeee',
                      },
                      {
                        fieldName: 'b_acc_holder',
                        defaultValue: '$sp_profile.financial.b_acc_holder',
                      },
                      {
                        fieldName: 'b_branch_name',
                        defaultValue: '$sp_profile.financial.b_branch_name',
                      },
                      {
                        fieldName: 'b_acc_type',
                        defaultValue: '$sp_profile.financial.b_acc_type',
                      },
                      {
                        fieldName: 'b_branch_code',
                        defaultValue: '$sp_profile.financial.b_branch_code',
                      },
                      {
                        fieldName: 'b_bank_name',
                        defaultValue: '$sp_profile.financial.b_bank_name',
                      },
                      {
                        fieldName: 'b_acc_no',
                        defaultValue: '$sp_profile.financial.b_acc_no',
                      },
                      {
                        fieldName: 'vat_no',
                        defaultValue: '$sp_profile.financial.vat_no',
                      },
                      {
                        fieldName: 'tax_no',
                        defaultValue: '$sp_profile.financial.tax_no',
                      },
                      {
                        fieldName: 'contact_person',
                        defaultValue: '$sp_profile.address.contact_person',
                      },
                      {
                        fieldName: 'contact_primary',
                        defaultValue: '$sp_profile.address.contact_primary',
                      },
                      {
                        fieldName: 'contact_secondary',
                        defaultValue: '$sp_profile.address.contact_secondary',
                      },
                      {
                        fieldName: 'email_receiving',
                        defaultValue: '$sp_profile.address.email_receiving',
                      },
                      {
                        fieldName: 'physical_addr',
                        defaultValue: '$sp_profile.address.physical_addr',
                      },
                      {
                        fieldName: 'physical_city',
                        defaultValue: '$sp_profile.address.physical_city',
                      },
                      {
                        fieldName: 'physical_code',
                        defaultValue: '$sp_profile.address.physical_code',
                      },
                      {
                        fieldName: 'physical_suburb',
                        defaultValue: '$sp_profile.address.physical_suburb',
                      },
                      {
                        fieldName: 'province',
                        defaultValue: '$sp_profile.address.province',
                      },
                      {
                        fieldName: 'postal_box',
                        defaultValue: '$sp_profile.address.postal_box',
                      },
                      {
                        fieldName: 'postal_city',
                        defaultValue: '$sp_profile.address.postal_city',
                      },
                      {
                        fieldName: 'postal_code',
                        defaultValue: '$sp_profile.address.postal_code',
                      },
                      {
                        fieldName: 'skills',
                        defaultValue: '$sp_profile.skills',
                      },
                      {
                        fieldName: 'after_hours',
                        defaultValue: '$sp_profile.after_hours',
                      },
                      {
                        fieldName: 'companies',
                        defaultValue: '$derivedCompanies',
                      },
                      {
                        fieldName: 'accredition',
                        defaultValue:
                          '$sp_profile.additional_identities.accredition',
                      },
                      {
                        fieldName: 'mid',
                        defaultValue: '$sp_profile.additional_identities.mid',
                      },
                      {
                        fieldName: 'operational_area',
                        defaultValue: '$derivedOperationalArea',
                      },
                      {
                        fieldName: 'radius',
                        defaultValue:
                          '$derivedOperationalArea.0.operating_range',
                      },
                      {
                        fieldName: 'jobLocation',
                        defaultValue: '$derivedOperationalArea.0.location',
                      },
                      // {
                      //   fieldName: 'jobLocation',
                      //   defaultValue: '$derivedOperationalArea.0.location',
                      // },
                    ],
                    // fields: ['operational_area'],
                  },
                },
                {
                  type: 'clientAction',
                  action: 'clearStore',
                  payload: ['updatedOperationalArea'],
                },
              ],
            },
            // {
            //   label: 'Save Changes',
            //   position: 'center',
            //   disabledWhen:
            //     '!$store?.postData || !(Object.keys($store.postData).length > 0)',
            //   onClick: [
            //     {
            //       type: 'clientAction',
            //       action: 'submitAndFetch',
            //       payload: [
            //         {
            //           postData: '$postData',
            //         },
            //         {
            //           url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
            //           headers: {},
            //           bodySlicePath: 'postData',
            //           // redirect: '/settings/company/work',
            //         },
            //         { method: 'post', action: '/settings/company/areas' },
            //       ],
            //     },
            //   ],
            // },
            { label: 'Next', position: 'right', toScreen: '../subscription' },
          ],
          // navs: [
          //   {
          //     label: 'Back To Teams',
          //     position: 'left',
          //     toScreen: '/teams',
          //   },
          //   {
          //     label: 'Previous',
          //     position: 'left',
          //     toScreen: '../areas',
          //   },
          // ],
        },

        subscription: {
          layout: {},
          fetchCalls: [],
          fragments: [
            {
              component: 'TabsAndOptions',
              props: {
                tabs: [
                  {
                    name: 'Company',
                    path: '../company',
                  },
                  {
                    name: 'Banking',
                    path: '../banking',
                  },
                  {
                    name: 'Directors',
                    path: '../directors',
                  },
                  {
                    name: 'Contact',
                    path: '../contact',
                  },
                  {
                    name: 'Work',
                    path: '../work',
                  },
                  {
                    name: 'Areas',
                    path: '../areas',
                  },
                  {
                    name: 'Documents',
                    path: '../documents',
                  },
                  {
                    name: 'Subscription',
                    path: '../subscription',
                  },
                ],
              },
              isStickyHeader: true,
              layout: {},
            },
            {
              component: 'FormBuilder',
              layout: {
                paddingTop: '2rem',
              },
              props: {
                defaultValues: {
                  subscriptionConfirmation:
                    '$sp_profile.details.additional.subscriptionData.subscriptionConfirmation',
                },
                config: {
                  style: {
                    display: 'grid',
                    gridTemplateColumns: '1fr',
                    rowGap: '15px',
                    justifyItems: 'center',
                  },
                  controls: [
                    {
                      type: 'subscription-agreement', // Custom control type for OperationAreas
                      name: 'subscriptionConfirmation',
                      checked:
                        '$sp_profile.details.additional.subscriptionData.subscriptionConfirmation',
                      canChange: false,
                      css: { wrapper: { gridColumn: '1', gridRow: '1' } },
                    },
                  ],
                },
              },
            },
            // {
            //   component: 'Subscription',
            //   layout: { paddingTop: '2rem' },
            //   props: {
            //     name: 'subscriptionConfirmation',
            //     checked: true,
            //     canChange: true,
            //     onChange: (val: any) =>
            //       console.log('set new subscription value', val),
            //   },
            // },
          ],
          navs: [
            {
              label: 'Back To Teams',
              position: 'left',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'conditional',
                  payload: {
                    condition:
                      '!$formState.isDirty || !$formState.isValid || !(Object.keys($formState.touchedFields).length > 0)',
                    actions: {
                      whenTrue: [
                        {
                          type: 'clientAction',
                          action: 'triggerModal',
                          payload: [
                            {
                              display: true,
                              type: 'warning',
                              layout: {},
                              onEnter: [],
                              onLeave: [],
                              fragments: [
                                {
                                  component: 'Text',
                                  props: {
                                    textItems: [
                                      {
                                        text: 'Warning',
                                        options: {
                                          format: 'heading',
                                          type: 'page-heading',
                                        },
                                      },
                                      {
                                        text: 'You have unsaved changes. What would you like to do?',
                                        options: {
                                          format: 'heading',
                                          type: 'sub-heading',
                                          style: {
                                            paddingTop: '2rem',
                                          },
                                        },
                                      },
                                    ],
                                  },
                                  layout: {
                                    display: 'grid',
                                    gridAutoFlow: 'row',
                                    justifyItems: 'center',
                                  },
                                },
                                {
                                  component: 'ButtonRow',
                                  layout: {
                                    width: 'fit-content',
                                    margin: '0 auto',
                                  },
                                  props: {
                                    buttons: [
                                      {
                                        btnValue: 'Clear Changes',
                                        actiontype: 'warning',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'navigate',
                                            payload: [
                                              '/teams/manage-team/list-view',
                                              { clearParams: true },
                                            ],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                      {
                                        btnValue: 'Save Changes',
                                        actiontype: 'proceed',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'submitAndNavigate',
                                            payload: [
                                              {
                                                operational_area:
                                                  '$formDataRaw.operational_area',
                                              },
                                              {
                                                url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                                headers: {},
                                                redirect:
                                                  '/teams/manage-team/list-view',
                                              },
                                              {
                                                method: 'post',
                                                action:
                                                  '/settings/company/areas',
                                              },
                                            ],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                },
                              ],
                              navs: [],
                            },
                          ],
                        },
                      ],
                      whenFalse: [
                        {
                          type: 'clientAction',
                          action: 'navigate',
                          payload: [
                            '/teams/manage-team/list-view',
                            { clearParams: true },
                          ],
                        },
                      ],
                    },
                  },
                },
              ],
            },
            {
              label: 'Previous',
              position: 'left',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'resetFields',
                  payload: {
                    fields: [
                      {
                        fieldName: 'operational_area',
                        defaultValue: '$derivedOperationalArea',
                      },
                      {
                        fieldName: 'radius',
                        defaultValue:
                          '$derivedOperationalArea.0.operating_range',
                      },
                      {
                        fieldName: 'jobLocation',
                        defaultValue: '$derivedOperationalArea.0.location',
                      },
                      // {
                      //   fieldName: 'jobLocation',
                      //   defaultValue: '$derivedOperationalArea.0.location',
                      // },
                    ],
                    // fields: ['operational_area'],
                  },
                },
                {
                  type: 'clientAction',
                  action: 'navigate',
                  payload: ['/settings/company/documents'],
                },
              ],
            },
          ],
        },
      },
      onLeave: [
        {
          type: 'clientAction',
          action: 'clearStore',
          payload: ['formDataRaw', 'postData'],
        },
      ],
      formOriginalValues: {
        // Orignals for diffing to find changed fields (from clientDataObject to store)
        'sp_profile.details.trading_as': 'trading_as',
        'sp_profile.details.bbeee': 'bbeee',
        'sp_profile.details.additional.subscriptionData.subscriptionConfirmation':
          'subscriptionConfirmation',

        'sp_profile.financial.vat_registered': 'vat_registered',
        'sp_profile.financial.vat_no': 'vat_no',
        'sp_profile.financial.tax_no': 'tax_no',
        'sp_profile.financial.b_acc_holder': 'b_acc_holder',
        'sp_profile.financial.b_acc_type': 'b_acc_type',
        'sp_profile.financial.b_acc_no': 'b_acc_no',
        'sp_profile.financial.b_bank_name': 'b_bank_name',
        'sp_profile.financial.b_branch_name': 'b_branch_name',
        'sp_profile.financial.b_branch_code': 'b_branch_code',

        'sp_profile.address.contact_primary': 'contact_primary',
        'sp_profile.address.contact_secondary': 'contact_secondary',
        'sp_profile.address.email_receiving': 'email_receiving',
        'sp_profile.address.physical_addr': 'physical_addr',
        'sp_profile.address.physical_city': 'physical_city',
        'sp_profile.address.physical_suburb': 'physical_suburb',
        'sp_profile.address.physical_code': 'physical_code',
        'sp_profile.address.postal_box': 'postal_box',
        'sp_profile.address.postal_city': 'postal_city',
        'sp_profile.address.postal_code': 'postal_code',
        'sp_profile.address.province': 'province',
        'sp_profile.address.contact_person': 'contact_person',

        'sp_profile.skills': 'skills',
        // 'sp_profile.companies': 'companies',
        derivedCompanies: 'companies',
        'sp_profile.after_hours': 'after_hours',
        'sp_profile.mid': 'mid',
        'sp_profile.accredition': 'accredition',

        // 'sp_profile.operational_area': 'operational_area',
        derivedOperationalArea: 'operational_area',
      },
      formTransformMapper: {
        // Actual mapper to get final object shaped for server
        trading_as: 'details.trading_as',
        bbeee: 'details.bbeee',

        vat_registered: 'financial.vat_registered',
        vat_no: 'financial.vat_no',
        tax_no: 'financial.tax_no',
        b_acc_holder: 'financial.b_acc_holder',
        b_acc_type: 'financial.b_acc_type',
        b_acc_no: 'financial.b_acc_no',
        b_bank_name: 'financial.b_bank_name',
        b_branch_name: 'financial.b_branch_name',
        b_branch_code: 'financial.b_branch_code',

        contact_primary: 'address.contact_primary',
        contact_secondary: 'address.contact_secondary',
        email_receiving: 'address.email_receiving',
        physical_addr: 'address.physical_addr',
        physical_city: 'address.physical_city',
        physical_suburb: 'address.physical_suburb',
        physical_code: 'address.physical_code',
        postal_box: 'address.postal_box',
        postal_city: 'address.postal_city',
        postal_code: 'address.postal_code',
        province: 'address.province',
        contact_person: 'address.contact_person',

        skills: 'skills',
        companies: 'companies',
        after_hours: 'after_hours',
        mid: 'mid',
        accredition: 'accredition',

        operational_area: 'operational_area',
        subscriptionConfirmation:
          'details.additional.subscriptionData.subscriptionConfirmation',
      },
      actionPanels: [
        // Reminders
        // {
        //   icon: 'bell-02',
        //   title: 'Reminders', //?actionPanel=Messages--bell-02

        //   // fetchCalls: [],
        //   layout: {},
        //   onEnter: [],
        //   onLeave: [],
        //   fragments: [
        //     {
        //       component: 'ReminderView',
        //       layout: {
        //         marginTop: '10px',
        //         marginLeft: '10px',
        //         marginRight: '10px',
        //       },
        //       props: {
        //         reminders: [
        //           {
        //             background: 'primary',
        //             timeOfReminder: '01:00',
        //             reminderDate: '2024-07-01',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '09:30',
        //             reminderDate: '2024-07-03',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '00:01',
        //             reminderDate: '2024-07-09',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '01:00',
        //             reminderDate: '2024-07-01',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '09:30',
        //             reminderDate: '2024-07-03',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '00:01',
        //             reminderDate: '2024-07-09',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '01:00',
        //             reminderDate: '2024-07-01',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '09:30',
        //             reminderDate: '2024-07-03',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '00:01',
        //             reminderDate: '2024-07-09',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //         ],
        //         toolbar: {
        //           buttonText: 'CREATE REMINDER',
        //           onClick: [
        //             {
        //               type: 'clientAction',
        //               action: 'triggerModal',
        //               payload: [
        //                 {
        //                   display: true,
        //                   type: 'warning',
        //                   heading: 'Add New Reminder',
        //                   headingType: 'page-heading',
        //                   layout: {},
        //                   onEnter: [],
        //                   onLeave: [],
        //                   fragments: [
        //                     {
        //                       component: 'FormBuilder',
        //                       layout: {
        //                         display: 'grid',
        //                         justifyItems: 'center',
        //                         margin: '0 auto',
        //                       },
        //                       props: {
        //                         config: {
        //                           style: {
        //                             display: 'grid',
        //                             gridTemplateColumns: 'repeat(2, 1fr)',
        //                             rowGap: '1rem',
        //                             columnGap: '1rem',
        //                             width: 'calc(100vw * 0.5)',
        //                             maxWidth: '819px',
        //                           },
        //                           controls: [
        //                             {
        //                               type: 'plain-text',
        //                               name: 'reminder_label',
        //                               label: 'Reminder Label',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1',
        //                                   gridRow: '1',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'single-select',
        //                               name: 'channel',
        //                               label: 'Channel',
        //                               labelProp: 'label',
        //                               valueProp: 'value',
        //                               dropdownScroll: true,
        //                               options: {
        //                                 source: 'literal',
        //                                 data: [
        //                                   {
        //                                     value: 1,
        //                                     label: 'General',
        //                                   },
        //                                   {
        //                                     value: 2,
        //                                     label: 'Phone',
        //                                   },
        //                                   {
        //                                     value: 3,
        //                                     label: 'Email',
        //                                   },
        //                                 ],
        //                               },
        //                               css: {
        //                                 wrapper: {
        //                                   wrapper: {
        //                                     gridColumn: '2',
        //                                     gridRow: '1',
        //                                     width: '100%',
        //                                   },
        //                                 },
        //                               },
        //                               instructions:
        //                                 'Password minimum characters 12, one uppercase, one lowercase, one special character, one number',
        //                             },
        //                             {
        //                               type: 'datepicker',
        //                               name: 'reminder_date',
        //                               label: 'Date',
        //                               placeholder: 'Select date for reminder',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1',
        //                                   gridRow: '2',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'timepicker',
        //                               name: 'reminder_time',
        //                               label: 'Time',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '2',
        //                                   gridRow: '2',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'textarea',
        //                               name: 'what_matters',
        //                               label: 'Reminder Message',
        //                               placeholder: 'Reminder Message',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1 / span 2',
        //                                   gridRow: '3',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'plain-text',
        //                               name: 'link_to_claim',
        //                               label: 'Link to claim',
        //                               icon: 'search-sm',
        //                               position: 'right',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1 / span 2',
        //                                   gridRow: '4',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                           ],
        //                         },
        //                       },
        //                     },
        //                     {
        //                       component: 'ButtonRow',
        //                       layout: {
        //                         width: 'fit-content',
        //                         margin: 'auto',
        //                       },
        //                       props: {
        //                         buttons: [
        //                           {
        //                             btnValue: 'Cancel',
        //                             onClick: [
        //                               {
        //                                 type: 'clientAction',
        //                                 action: 'log',
        //                                 payload: ['Clicked cancel'],
        //                               },
        //                             ],
        //                           },
        //                           {
        //                             btnValue: 'Add Reminder',
        //                             onClick: [
        //                               {
        //                                 type: 'clientAction',
        //                                 action: 'log',
        //                                 payload: ['Clicked Add Reminder'],
        //                               },
        //                             ],
        //                           },
        //                         ],
        //                       },
        //                     },
        //                   ],
        //                   navs: [],
        //                 },
        //               ],
        //             },
        //           ],
        //         },
        //       },
        //     },
        //   ],
        //   actionLevel: 'bottomControls',
        // },
        // Scratch Pad
        {
          icon: 'clipboard',
          title: 'Scratch Pad', //?actionPanel=Messages--bell-02
          // fetchCalls: [],
          layout: {},
          onEnter: [],
          onLeave: [],
          fragments: [
            {
              component: 'ScratchPadView',
              layout: { marginLeft: '10px', marginRight: '10px' },
              props: {
                titlePlaceholder: 'Heading',
                icon: 'trash-01',
                iconHandler: (data: { heading: string; body: string }) =>
                  console.log(
                    'got data: Heading - ' +
                      data.heading +
                      ' Body - ' +
                      data.body
                  ),
                placeHolder: 'Text here...',
              },
            },
          ],
          actionLevel: 'bottomControls',
        },
      ],
    },
  },
};
