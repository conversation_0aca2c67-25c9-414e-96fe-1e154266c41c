import {
  actionFunctionGenerator,
  makeFetchCalls,
  SpaPrivateRoute,
} from '@4-sure/ui-platform';
import Keycloak from 'keycloak-js';
import { Navigate, RouteObject } from 'react-router-dom';
import { envObject } from '../../../env';
import { getAppConfig } from '../../app-confg';
import { teamsConfig } from './TeamsConfig';
import { TeamsModule } from './TeamsModule';
import { TeamsScreen } from './TeamsScreen';
import { TeamsState } from './TeamsState';

export const TeamsRoutes: (
  keycloak: Keycloak,
  isAdmin?: boolean,
  tasksAlert?: boolean
) => RouteObject = (
  keycloak: Keycloak,
  isAdmin?: boolean,
  tasksAlert?: boolean
) => ({
  path: '/teams',
  element: <SpaPrivateRoute component={TeamsModule} />,
  loader: async ({ request }) => {
    const appConfig = await getAppConfig(isAdmin, tasksAlert);
    const fetchCalls = appConfig.fetchCalls || [];
    const fetchResultsObject = await makeFetchCalls(
      fetchCalls,
      keycloak,
      request,
      envObject
    );
    return { appConfig, fetchResultsObject };
  },
  action: actionFunctionGenerator(keycloak, envObject),
  children: Object.entries(teamsConfig.teamStates).reduce(
    (statesAcc, [statePath, stateConfig]) => {
      return [
        ...statesAcc,
        {
          path: statePath,
          element: <TeamsState />,
          loader: async ({ params, request }) => {
            const fetchCalls = stateConfig.fetchCalls || [];
            const fetchResultsObject = await makeFetchCalls(
              fetchCalls,
              keycloak,
              request,
              envObject
            );
            return { stateConfig, fetchResultsObject };
          },
          action: actionFunctionGenerator(keycloak, envObject),
          children: Object.entries(stateConfig.screens).reduce(
            (screenAcc, [screenPath, screenConfig]) => {
              return [
                ...screenAcc,
                {
                  path: screenPath,
                  element: <TeamsScreen />,
                  loader: async ({ params, request }) => {
                    const fetchCalls = screenConfig.fetchCalls || [];
                    const fetchResultsObject = await makeFetchCalls(
                      fetchCalls,
                      keycloak,
                      request,
                      envObject
                    );
                    return {
                      screenConfig: { ...screenConfig, id: screenPath },
                      fetchResultsObject,
                    };
                  },
                  action: actionFunctionGenerator(keycloak, envObject),
                } as RouteObject,
              ];
            },
            [{ path: '', element: <Navigate to={stateConfig.defaultScreen} /> }]
          ),
        } as RouteObject,
      ];
    },
    [{ path: '', element: <Navigate to={teamsConfig.defaultTeamState} /> }]
  ),
});
