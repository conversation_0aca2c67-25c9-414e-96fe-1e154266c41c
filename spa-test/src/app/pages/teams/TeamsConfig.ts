import { TeamsConfig } from '@4-sure/ui-platform';
import {
  MANAGE_TEAM_ADD_TEAM_MEMBER_STATE,
  MANAGE_TEAM_EDIT_TEAM_MEMBER_STATE,
  MANAGE_TEAM_WORKFLOW_STATE,
} from '../states';

export const teamsConfig: TeamsConfig = {
  defaultTeamState: 'manage-team',
  teamStates: {
    'manage-team': MANAGE_TEAM_WORKFLOW_STATE,
    'manage-team/add': MANAGE_TEAM_ADD_TEAM_MEMBER_STATE,
    'manage-team/profile': MANAGE_TEAM_EDIT_TEAM_MEMBER_STATE,
  },
};
