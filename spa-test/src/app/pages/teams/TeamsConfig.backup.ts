import { TeamsConfig, validationRegex } from '@4-sure/ui-platform';

export const teamsConfig: TeamsConfig = {
  defaultTeamState: 'manage-team',
  teamStates: {
    'manage-team': {
      title: { template: '' },
      fetchCalls: [
        {
          key: 'staff_members',
          method: 'POST',
          body: {},
          url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/get_staff_members',
          slicePath: 'payload',
        },
        {
          key: 'sp_profile_onboarding_state',
          method: 'POST',
          url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_sp',
          body: {},
          slicePath: 'payload.onboarding_state',
        },
      ],
      defaultScreen: 'list-view',
      // onEnter: [
      //   {
      //     type: 'clientAction',
      //     action: 'conditional',
      //     payload: {
      //       condition:
      //         '$store?.my_profile && (Object.keys($store.my_profile).length > 0) && !$store.my_profile.roles.includes(43)',
      //       actions: {
      //         whenTrue: [
      //           {
      //             type: 'clientAction',
      //             action: 'log',
      //             payload: ['User has no admin role'],
      //           },
      //         ],
      //         whenFalse: [
      //           {
      //             type: 'clientAction',
      //             action: 'log',
      //             payload: ['User has admin role'],
      //           },
      //         ],
      //       },
      //     },
      //   },
      // ],
      screens: {
        // screen
        'list-view': {
          layout: {},
          // fetchCalls: [
          //   {
          //     key: 'my_profile',
          //     method: 'POST',
          //     url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/get_profile',
          //     body: {},
          //     slicePath: 'payload',
          //     successFetchCalls: [
          //       {
          //         key: 'my_profile_picture',
          //         method: 'POST',
          //         url: '{VITE_STAFF_SERVER}/api/v1/file_actions/get_file',
          //         body: { file_id: '$profile_pic' },
          //         slicePath: 'payload',
          //       },
          //     ],
          //   },
          // ],
          fragments: [
            // {
            //   component: 'DefaultWorkflowFilters',
            //   props: {
            //     addButtonConfig: {
            //       buttonText: 'ADD MEMBER',
            //       onClick: [
            //         {
            //           type: 'clientAction',
            //           action: 'navigate',
            //           payload: ['/teams/manage-team/add/add-member'],
            //         },
            //       ],
            //     },
            //   },
            //   layout: {},
            // },
            {
              component: 'ButtonRow',
              layout: {
                display: 'grid',
                gridAutoFlow: 'column',
                justifyContent: 'right',
                marginBottom: '1rem',
              },
              props: {
                buttons: [
                  {
                    btnValue: 'ADD MEMBER',
                    onClick: [
                      {
                        type: 'clientAction',
                        action: 'navigate',
                        payload: ['/teams/manage-team/add/add-member'],
                      },
                    ],
                    iconRight: 'plus',
                  },
                ],
              },
            },
            {
              component: 'TeamMemberCardList',
              props: {
                data: '$staff_members',
                config: [
                  {
                    type: 'linkColumn',
                    showAvatar: true,
                    showTitleText: true,
                    showStatusIndicator: false,
                    showRating: true,
                    titleKey: 'full_name',
                  },
                  {
                    type: 'textColumn',
                    titleKey: 'roles',
                  },
                  {
                    type: 'textColumn',
                    titleKey: 'contact_number',
                  },
                  {
                    type: 'textColumn',
                    titleKey: 'email_address',
                  },
                ],
                linkUrl: '/teams/manage-team/profile/member-profile',
                linkUrlParams: [{ key: 'staff_id', value: 'staff_id' }],
              },
              layout: {},
            },
          ],
          navs: [],
        },
        // end screen
      },
      actionPanels: [
        // Reminders
        // {
        //   icon: 'bell-02',
        //   title: 'Reminders', //?actionPanel=Messages--bell-02

        //   // fetchCalls: [],
        //   layout: {},
        //   onEnter: [],
        //   onLeave: [],
        //   fragments: [
        //     {
        //       component: 'ReminderView',
        //       layout: {
        //         marginTop: '10px',
        //         marginLeft: '10px',
        //         marginRight: '10px',
        //       },
        //       props: {
        //         reminders: [
        //           {
        //             background: 'primary',
        //             timeOfReminder: '01:00',
        //             reminderDate: '2024-07-01',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '09:30',
        //             reminderDate: '2024-07-03',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '00:01',
        //             reminderDate: '2024-07-09',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '01:00',
        //             reminderDate: '2024-07-01',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '09:30',
        //             reminderDate: '2024-07-03',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '00:01',
        //             reminderDate: '2024-07-09',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '01:00',
        //             reminderDate: '2024-07-01',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '09:30',
        //             reminderDate: '2024-07-03',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '00:01',
        //             reminderDate: '2024-07-09',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //         ],
        //         toolbar: {
        //           buttonText: 'CREATE REMINDER',
        //           onClick: [
        //             {
        //               type: 'clientAction',
        //               action: 'triggerModal',
        //               payload: [
        //                 {
        //                   display: true,
        //                   type: 'warning',
        //                   heading: 'Add New Reminder',
        //                   headingType: 'page-heading',
        //                   layout: {},
        //                   onEnter: [],
        //                   onLeave: [],
        //                   fragments: [
        //                     {
        //                       component: 'FormBuilder',
        //                       layout: {
        //                         display: 'grid',
        //                         justifyItems: 'center',
        //                         margin: '0 auto',
        //                       },
        //                       props: {
        //                         config: {
        //                           style: {
        //                             display: 'grid',
        //                             gridTemplateColumns: 'repeat(2, 1fr)',
        //                             rowGap: '1rem',
        //                             columnGap: '1rem',
        //                             width: 'calc(100vw * 0.5)',
        //                             maxWidth: '819px',
        //                           },
        //                           controls: [
        //                             {
        //                               type: 'plain-text',
        //                               name: 'reminder_label',
        //                               label: 'Reminder Label',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1',
        //                                   gridRow: '1',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'single-select',
        //                               name: 'channel',
        //                               label: 'Channel',
        //                               options: {
        //                                 source: 'literal',
        //                                 data: [
        //                                   {
        //                                     value: 1,
        //                                     label: 'General',
        //                                   },
        //                                   {
        //                                     value: 2,
        //                                     label: 'Phone',
        //                                   },
        //                                   {
        //                                     value: 3,
        //                                     label: 'Email',
        //                                   },
        //                                 ],
        //                               },
        //                               css: {
        //                                 wrapper: {
        //                                   wrapper: {
        //                                     gridColumn: '2',
        //                                     gridRow: '1',
        //                                     width: '100%',
        //                                   },
        //                                 },
        //                               },
        //                               instructions:
        //                                 'Password minimum characters 12, one uppercase, one lowercase, one special character, one number',
        //                             },
        //                             {
        //                               type: 'datepicker',
        //                               name: 'reminder_date',
        //                               label: 'Date',
        //                               placeholder: 'Select date for reminder',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1',
        //                                   gridRow: '2',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'plain-text',
        //                               name: 'time',
        //                               label: 'Time',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '2',
        //                                   gridRow: '2',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'textarea',
        //                               name: 'what_matters',
        //                               label: 'Reminder Message',
        //                               placeholder: 'Reminder Message',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1 / span 2',
        //                                   gridRow: '3',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'plain-text',
        //                               name: 'link_to_claim',
        //                               label: 'Link to claim',
        //                               icon: 'search-sm',
        //                               position: 'right',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1 / span 2',
        //                                   gridRow: '4',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                           ],
        //                         },
        //                       },
        //                     },
        //                     {
        //                       component: 'ButtonRow',
        //                       layout: {
        //                         width: 'fit-content',
        //                         margin: 'auto',
        //                       },
        //                       props: {
        //                         buttons: [
        //                           {
        //                             btnValue: 'Cancel',
        //                             onClick: [
        //                               {
        //                                 type: 'clientAction',
        //                                 action: 'log',
        //                                 payload: ['Clicked cancel'],
        //                               },
        //                             ],
        //                           },
        //                           {
        //                             btnValue: 'Add Reminder',
        //                             onClick: [
        //                               {
        //                                 type: 'clientAction',
        //                                 action: 'log',
        //                                 payload: ['Clicked Add Reminder'],
        //                               },
        //                             ],
        //                           },
        //                         ],
        //                       },
        //                     },
        //                   ],
        //                   navs: [],
        //                 },
        //               ],
        //             },
        //           ],
        //         },
        //       },
        //     },
        //   ],
        //   actionLevel: 'bottomControls',
        // },
        // Scratch Pad
        {
          icon: 'clipboard',
          title: 'Scratch Pad', //?actionPanel=Messages--bell-02
          // fetchCalls: [],
          layout: {},
          onEnter: [],
          onLeave: [],
          fragments: [
            {
              component: 'ScratchPadView',
              layout: { marginLeft: '10px', marginRight: '10px' },
              props: {
                titlePlaceholder: 'Heading',
                icon: 'trash-01',
                iconHandler: (data: { heading: string; body: string }) =>
                  console.log(
                    'got data: Heading - ' +
                      data.heading +
                      ' Body - ' +
                      data.body
                  ),
                placeHolder: 'Text here...',
              },
            },
          ],
          actionLevel: 'bottomControls',
        },
      ],
    },

    'manage-team/add': {
      title: { template: '' },
      fetchCalls: [],
      defaultScreen: 'add-member',
      screens: {
        'add-member': {
          layout: {},
          fetchCalls: [],
          onEnter: [],
          fragments: [
            {
              component: 'Text',
              props: {
                textItems: [
                  {
                    text: 'Add team member',
                    options: {
                      format: 'heading',
                      type: 'page-heading',
                    },
                  },
                  {
                    text: 'Please be aware that this image will be visible to customers',
                    options: {
                      format: 'heading',
                      type: 'sub-heading',
                    },
                  },
                ],
              },
              layout: { justifyItems: 'center', display: 'grid' },
            },
            // {
            //   component: 'UploadPhoto',
            //   props: {},
            //   layout: { paddingTop: '1rem' },
            // },
            {
              component: 'FormBuilder',
              props: {
                config: {
                  style: {
                    rowGap: '1rem',
                    columnGap: '1rem',
                    paddingTop: '2rem',
                    paddingBottom: '1rem',
                    width: '336px',
                    justifyItems: 'center',
                    alignContent: 'space-around',
                  },
                  controls: [
                    {
                      type: 'plain-text',
                      name: 'username',
                      label: 'User Name',
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                        pattern: {
                          value: validationRegex.username.pattern,
                          message: validationRegex.username.message,
                        },
                      },
                      instructions: 'Used to login to the platform', //TODO: the instructions does not work(comment under form)
                    },
                  ],
                },
              },
              layout: { display: 'grid', justifyItems: 'center' },
            },
            {
              component: 'FormBuilder',
              layout: {
                display: 'grid',
                justifyItems: 'center',
              },
              props: {
                config: {
                  style: {
                    display: 'grid',
                    gridTemplateColumns: 'repeat(2, 1fr)',
                    rowGap: '1rem',
                    columnGap: '1rem',
                    width: '712px',
                  },
                  controls: [
                    {
                      type: 'plain-text',
                      name: 'full_name',
                      label: 'Full Name',
                      validation: {
                        pattern: {
                          value: validationRegex.name.pattern,
                          message: validationRegex.name.message,
                        },
                        required: {
                          value: true,
                          message: 'Name is required',
                        },
                        minLength: {
                          value: 2,
                          message: 'Name must be at least 2 characters',
                        },
                        maxLength: {
                          value: 30,
                          message: 'You cant go past 30 characters',
                        },
                      },
                      css: {
                        wrapper: { gridColumn: '1', gridRow: '1' },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'id_passport',
                      label: 'Identification',
                      validation: {
                        required: {
                          value: true,
                          message: 'Field is required',
                        },
                        pattern: {
                          value: validationRegex.id_passport.pattern,
                          message: validationRegex.id_passport.message,
                        },
                      },
                      css: {
                        wrapper: { gridColumn: '2', gridRow: '1' },
                      },
                    },
                    // {
                    //   type: 'drag-and-drop-in-memory',
                    //   name: 'id_document',
                    //   label: 'ID Document',
                    //   purpose: 'Identity',
                    //   emptyText: 'Drag & drop here',
                    //   css: { wrapper: { gridColumn: '1', gridRow: '2' } },
                    // },
                    {
                      type: 'plain-text',
                      name: 'contact_number',
                      label: 'Contact number',
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                        pattern: {
                          value: validationRegex.phone_number.pattern,
                          message: validationRegex.phone_number.message,
                        },
                      },
                      // css: { wrapper: { gridColumn: '2', gridRow: '2' } },
                      css: { wrapper: { gridColumn: '1', gridRow: '2' } },
                    },
                    {
                      type: 'plain-text',
                      name: 'email_address',
                      label: 'Email Address',
                      validation: {
                        pattern: {
                          value: validationRegex.email.pattern,
                          message: validationRegex.email.message,
                        },
                      },
                      // css: { wrapper: { gridColumn: '1', gridRow: '3' } },
                      css: { wrapper: { gridColumn: '2', gridRow: '2' } },
                    },
                    {
                      type: 'multi-select',
                      name: 'roles',
                      label: 'Roles',
                      labelProp: 'description',
                      valueProp: 'id',
                      options: {
                        source: 'store',
                        storeDataPath: 'staff_enums.roles_2',
                      },
                      validation: {
                        required: true,
                        message: 'This field is required',
                      },
                      placeholder: 'Select roles...',
                      // css: { wrapper: { gridColumn: '2', gridRow: '3' } },
                      css: { wrapper: { gridColumn: '1', gridRow: '3' } },
                    },
                    {
                      type: 'single-select',
                      name: 'id_type',
                      label: 'ID Types',
                      placeholder: 'Select type',
                      labelProp: 'name',
                      valueProp: 'id',
                      validation: {
                        required: true,
                        message: 'This field is required',
                      },
                      options: {
                        source: 'store',
                        storeDataPath: 'staff_enums.id_types',
                      },
                      dropdownScroll: true,
                      // css: {
                      //   wrapper: {
                      //     gridColumn: 1,
                      //     gridRow: 4,
                      //     height: '4rem',
                      //     width: '100%',
                      //   },
                      // },
                      css: {
                        wrapper: {
                          gridColumn: 2,
                          gridRow: 3,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                  ],
                },
              },
            },
          ],
          navs: [
            {
              label: 'Back To Teams',
              position: 'left',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'navigate',
                  payload: ['/teams/manage-team/list-view'],
                },
              ],
            },
            {
              label: 'Add Team Member',
              position: 'right',
              disabledWhen: `!$formState.isDirty ||
               !$formState.isValid ||
               !(Object.keys($formState.touchedFields).length > 0) ||
               !$form.username ||
               !$form.full_name ||
               !$form.contact_number ||
               !$form.id_type ||
               !$form.roles ||
               !$form.id_passport`,
              onClick: [
                // update config to use onSuccess and onFailure handlers automatically
                // TODO: make sequential calls for the register_new_profile and upload_id_document
                // {
                //   type: 'clientAction',
                //   action: 'submitAsync',
                //   payload: {
                //     calls: [
                //       {
                //         key: 'register_new_profile',
                //         url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/register_new_profile',
                //         data: {
                //           username: '$username',
                //           full_name: '$full_name',
                //           contact_number: '$contact_number',
                //           email_address: '$email_address',
                //           id_type: '$id_type', // SA id or drivers license = 1, passport = 2
                //           id_passport: '$id_passport',
                //           roles: '$roles',
                //           staff_type: 2,
                //           qualification_number: null,
                //           callback_uri: `${
                //             import.meta.env.VITE_REGISTRATION_APP_URL
                //           }set-preregistration-password`,
                //         },
                //       },
                //       {
                //         key: 'upload_id_document',
                //         url: '{VITE_STAFF_SERVER}/api/v1/file_actions/upload_file',
                //         data: {
                //           file: '$id_document.file',
                //           purpose: 'Identity',
                //           list: 'False',
                //           director_id: '{register_new_profile.payload.sso_id}',
                //           sp_id: '$sp_profile.id',
                //         },
                //       },
                //     ],
                //     onFinish: {
                //       type: 'clientAction',
                //       action: 'resetFields',
                //       payload: {
                //         fields: [
                //           'username',
                //           'full_name',
                //           'contact_number',
                //           'email_address',
                //           'id_type',
                //           'id_passport',
                //           'roles',
                //           'id_document',
                //         ],
                //       },
                //     },
                //     redirect: 'teams/manage-team/add/successful-request',
                //   },
                // },
                {
                  type: 'clientAction',
                  action: 'submitAndNavigate',
                  payload: [
                    {
                      username: '$formDataRaw.username',
                      full_name: '$formDataRaw.full_name',
                      contact_number: '$formDataRaw.contact_number',
                      email_address: '$formDataRaw.email_address',
                      id_type: '$formDataRaw.id_type', // SA id or drivers license = 1, passport = 2
                      id_passport: '$formDataRaw.id_passport',
                      qualification_number: null,
                      available: true,
                      staff_type: 2,
                      roles: '$formDataRaw.roles',
                      callback_uri: `${
                        import.meta.env.VITE_REGISTRATION_APP_URL
                      }set-preregistration-password`,
                    },
                    {
                      url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/register_new_profile',
                      headers: {},
                      redirect: '/teams/manage-team/add/successful-request',
                    },
                    {
                      method: 'post',
                      action: '/teams/manage-team/add/add-member',
                    },
                  ],
                },
              ],
            },
          ],
        },

        'successful-request': {
          layout: {},
          fetchCalls: [
            {
              key: 'new_staff_profile',
              method: 'POST',
              body: {},
              url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/get_profile',
            },
          ],
          fragments: [
            {
              component: 'Text',
              props: {
                textItems: [
                  {
                    text: 'Join request successfully sent',
                    options: {
                      format: 'heading',
                      type: 'page-heading',
                    },
                  },
                ],
                layout: {
                  display: 'grid',
                  justifyItems: 'center',
                  // width: 'calc(100% - 226px - 56px)',
                  paddingTop: '2rem',
                  paddingBottom: '2rem',
                },
              },
              layout: { justifyItems: 'center', display: 'grid' },
            },
            {
              component: 'UploadPhoto',
              props: {
                image: '',
                action: 'upload',
                url: '',
                _fetcher: '$fetcher',
                buttonActive: false,
              },
              layout: {},
            },
            {
              component: 'Text',
              props: {
                textItems: [
                  {
                    // TODO: to amend this so as to make it a pure json object
                    text: `#{formDataRaw.full_name}`,
                    options: {
                      format: 'heading',
                      type: 'control-view-subheading',
                    },
                  },
                  {
                    // TODO: to amend this so as to make it a pure json object
                    text: `#{formDataRaw.email_address}`,
                    options: {
                      format: 'heading',
                      type: 'control-view-subheading',
                    },
                  },
                ],
              },
              layout: {
                display: 'grid',
                justifyItems: 'center',
                // width: 'calc(100% - 226px - 56px)',
                paddingTop: '2rem',
                paddingBottom: '1rem',
              },
            },
            {
              component: 'Text',
              props: {
                textItems: [
                  {
                    // TODO: to amend this so as to make it a pure json object
                    text: `#An email has been sent to {formDataRaw.full_name} to join your organization`,
                    options: {
                      format: 'heading',
                      type: 'sub-heading',
                    },
                  },
                ],
              },
              layout: {
                display: 'grid',
                justifyItems: 'center',
                // width: 'calc(100% - 226px - 56px)',
                paddingTop: '2rem',
                paddingBottom: '1rem',
              },
            },
          ],
          navs: [
            {
              label: 'Back To Teams',
              position: 'left',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'navigate',
                  payload: ['/teams/manage-team/list-view'],
                },
              ],
            },
          ],
        },
      },
      actionPanels: [
        // Reminders
        // {
        //   icon: 'bell-02',
        //   title: 'Reminders', //?actionPanel=Messages--bell-02

        //   // fetchCalls: [],
        //   layout: {},
        //   onEnter: [],
        //   onLeave: [],
        //   fragments: [
        //     {
        //       component: 'ReminderView',
        //       layout: {
        //         marginTop: '10px',
        //         marginLeft: '10px',
        //         marginRight: '10px',
        //       },
        //       props: {
        //         reminders: [
        //           {
        //             background: 'primary',
        //             timeOfReminder: '01:00',
        //             reminderDate: '2024-07-01',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '09:30',
        //             reminderDate: '2024-07-03',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '00:01',
        //             reminderDate: '2024-07-09',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '01:00',
        //             reminderDate: '2024-07-01',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '09:30',
        //             reminderDate: '2024-07-03',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '00:01',
        //             reminderDate: '2024-07-09',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '01:00',
        //             reminderDate: '2024-07-01',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '09:30',
        //             reminderDate: '2024-07-03',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '00:01',
        //             reminderDate: '2024-07-09',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //         ],
        //         toolbar: {
        //           buttonText: 'CREATE REMINDER',
        //           onClick: [
        //             {
        //               type: 'clientAction',
        //               action: 'triggerModal',
        //               payload: [
        //                 {
        //                   display: true,
        //                   type: 'warning',
        //                   heading: 'Add New Reminder',
        //                   headingType: 'page-heading',
        //                   layout: {},
        //                   onEnter: [],
        //                   onLeave: [],
        //                   fragments: [
        //                     {
        //                       component: 'FormBuilder',
        //                       layout: {
        //                         display: 'grid',
        //                         justifyItems: 'center',
        //                         margin: '0 auto',
        //                       },
        //                       props: {
        //                         config: {
        //                           style: {
        //                             display: 'grid',
        //                             gridTemplateColumns: 'repeat(2, 1fr)',
        //                             rowGap: '1rem',
        //                             columnGap: '1rem',
        //                             width: 'calc(100vw * 0.5)',
        //                             maxWidth: '819px',
        //                           },
        //                           controls: [
        //                             {
        //                               type: 'plain-text',
        //                               name: 'reminder_label',
        //                               label: 'Reminder Label',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1',
        //                                   gridRow: '1',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'single-select',
        //                               name: 'channel',
        //                               label: 'Channel',
        //                               options: {
        //                                 source: 'literal',
        //                                 data: [
        //                                   {
        //                                     value: 1,
        //                                     label: 'General',
        //                                   },
        //                                   {
        //                                     value: 2,
        //                                     label: 'Phone',
        //                                   },
        //                                   {
        //                                     value: 3,
        //                                     label: 'Email',
        //                                   },
        //                                 ],
        //                               },
        //                               css: {
        //                                 wrapper: {
        //                                   wrapper: {
        //                                     gridColumn: '2',
        //                                     gridRow: '1',
        //                                     width: '100%',
        //                                   },
        //                                 },
        //                               },
        //                               instructions:
        //                                 'Password minimum characters 12, one uppercase, one lowercase, one special character, one number',
        //                             },
        //                             {
        //                               type: 'datepicker',
        //                               name: 'reminder_date',
        //                               label: 'Date',
        //                               placeholder: 'Select date for reminder',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1',
        //                                   gridRow: '2',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'plain-text',
        //                               name: 'time',
        //                               label: 'Time',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '2',
        //                                   gridRow: '2',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'textarea',
        //                               name: 'what_matters',
        //                               label: 'Reminder Message',
        //                               placeholder: 'Reminder Message',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1 / span 2',
        //                                   gridRow: '3',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'plain-text',
        //                               name: 'link_to_claim',
        //                               label: 'Link to claim',
        //                               icon: 'search-sm',
        //                               position: 'right',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1 / span 2',
        //                                   gridRow: '4',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                           ],
        //                         },
        //                       },
        //                     },
        //                     {
        //                       component: 'ButtonRow',
        //                       layout: {
        //                         width: 'fit-content',
        //                         margin: 'auto',
        //                       },
        //                       props: {
        //                         buttons: [
        //                           {
        //                             btnValue: 'Cancel',
        //                             onClick: [
        //                               {
        //                                 type: 'clientAction',
        //                                 action: 'log',
        //                                 payload: ['Clicked cancel'],
        //                               },
        //                             ],
        //                           },
        //                           {
        //                             btnValue: 'Add Reminder',
        //                             onClick: [
        //                               {
        //                                 type: 'clientAction',
        //                                 action: 'log',
        //                                 payload: ['Clicked Add Reminder'],
        //                               },
        //                             ],
        //                           },
        //                         ],
        //                       },
        //                     },
        //                   ],
        //                   navs: [],
        //                 },
        //               ],
        //             },
        //           ],
        //         },
        //       },
        //     },
        //   ],
        //   actionLevel: 'bottomControls',
        // },
        // Scratch Pad
        {
          icon: 'clipboard',
          title: 'Scratch Pad', //?actionPanel=Messages--bell-02
          // fetchCalls: [],
          layout: {},
          onEnter: [],
          onLeave: [],
          fragments: [
            {
              component: 'ScratchPadView',
              layout: { marginLeft: '10px', marginRight: '10px' },
              props: {
                titlePlaceholder: 'Heading',
                icon: 'trash-01',
                iconHandler: (data: { heading: string; body: string }) =>
                  console.log(
                    'got data: Heading - ' +
                      data.heading +
                      ' Body - ' +
                      data.body
                  ),
                placeHolder: 'Text here...',
              },
            },
          ],
          actionLevel: 'bottomControls',
        },
      ],
    },

    'manage-team/profile': {
      title: { template: '' },
      fetchCalls: [],
      defaultScreen: 'member-profile',
      screens: {
        'member-profile': {
          fetchCalls: [
            {
              key: 'staff_profile',
              method: 'POST',
              url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/get_profile',
              slicePath: 'payload',
              successFetchCalls: [
                {
                  key: 'profile_picture',
                  method: 'POST',
                  url: '{VITE_STAFF_SERVER}/api/v1/file_actions/get_file',
                  body: { file_id: '$profile_pic' },
                  slicePath: 'payload',
                },
                {
                  key: 'staff_member_documentation',
                  method: 'POST',
                  url: '{VITE_STAFF_SERVER}/api/v1/file_actions/get_files',
                  body: {
                    with_thumbnails: false,
                    staff_id: '$sso_id',
                    purpose: 'Identity',
                  },
                  slicePath: 'payload',
                },
                {
                  key: 'my_files',
                  method: 'POST',
                  url: '{VITE_STAFF_SERVER}/api/v1/file_actions/get_files',
                  body: {},
                  slicePath: 'payload',
                },
                {
                  key: 'staffFieldAccess',
                  method: 'POST',
                  url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/staff_field_access',
                  body: {},
                  slicePath: 'payload',
                },
              ],
            },
          ],
          fragments: [
            {
              component: 'ProfileHero',
              layout: {
                display: 'grid',
                justifyContent: 'center',
                width: '50%',
                minWidth: '712px',
                margin: '0 auto 2rem',
              },
              props: {
                fullname: '$staff_profile.full_name',
                username: '$staff_profile.username',
                active: '$staff_profile.active.active_state',
                activeState: '$staff_profile.active.active_state_name',
                activeStateReason: '$staff_profile.active.reason',
                image: '$profile_picture.file',
                url: '{VITE_STAFF_SERVER}/api/v1/file_actions/upload_file',
                action: '/teams/manage-team/profile/member-profile',
                // state: '$staff_profile.onboarding_state',
              },
            },
            {
              component: 'ButtonRow',
              layout: {
                display: 'grid',
                gridAutoFlow: 'column',
                gap: '2rem',
                gridTemplateColumns: '1/3fr',
                justifyItems: 'center',
                alignContent: 'space-around',
                paddingTop: '2rem',
              },
              props: {
                buttons: [
                  {
                    btnValue: 'Remove Staff Member',
                    displayWhen: `$store?.auth?.sub !== $store?.staff_profile?.sso_id`,
                    onClick: [
                      {
                        type: 'clientAction',
                        action: 'triggerModal',
                        payload: [
                          {
                            display: true,
                            type: 'warning',
                            layout: {},
                            onEnter: [],
                            onLeave: [],
                            fragments: [
                              {
                                component: 'Text',
                                props: {
                                  textItems: [
                                    {
                                      text: 'Warning',
                                      options: {
                                        format: 'heading',
                                        type: 'page-heading',
                                      },
                                      icon: {
                                        type: 'alarm-clock',
                                        size: 60,
                                        strokeWidth: '1',
                                        color: '#e5e5e5',
                                        style: { paddingLeft: '1rem' },
                                      },
                                      iconPosition: 'right',
                                    },
                                    {
                                      text: 'This user will no longer be on the system.',
                                      options: {
                                        format: 'heading',
                                        type: 'sub-heading',
                                        style: {
                                          paddingTop: '2rem',
                                        },
                                      },
                                    },
                                    {
                                      text: 'You will need to contact 4-Sure to get this user back into the system',
                                      options: {
                                        format: 'heading',
                                        type: 'sub-heading',
                                      },
                                    },
                                    {
                                      text: 'Use the workflow to reallocate the work associated with this user to other staff members',
                                      options: {
                                        format: 'heading',
                                        type: 'sub-heading',
                                        style: {
                                          paddingTop: '2rem',
                                        },
                                      },
                                    },
                                  ],
                                },
                                layout: {
                                  display: 'grid',
                                  gridAutoFlow: 'row',
                                  justifyItems: 'center',
                                },
                              },
                              {
                                component: 'ButtonRow',
                                layout: {
                                  width: 'fit-content',
                                  margin: '0 auto',
                                },
                                props: {
                                  buttons: [
                                    {
                                      btnValue: 'Cancel Removal',
                                      onClick: [
                                        {
                                          type: 'clientAction',
                                          action: 'closeModal',
                                        },
                                      ],
                                    },
                                    {
                                      btnValue: 'Continue With Removal',
                                      onClick: [
                                        {
                                          type: 'clientAction',
                                          action: 'submitAndNavigate',
                                          payload: [
                                            {
                                              staff_id: '$staff_profile.sso_id',
                                            },
                                            {
                                              url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/remove_staff_from_sp',
                                              headers: {},
                                              redirect:
                                                '/teams/manage-team/list-view',
                                            },
                                            {
                                              method: 'post',
                                              action:
                                                '/teams/manage-team/profile/member-profile',
                                            },
                                          ],
                                        },
                                        {
                                          type: 'clientAction',
                                          action: 'closeModal',
                                        },
                                      ],
                                    },
                                  ],
                                },
                              },
                            ],
                            navs: [],
                          },
                        ],
                      },
                    ],
                    actiontype: 'alternative',
                  },
                  {
                    btnValue: 'Approve Staff Member',
                    displayWhen: `$store?.auth?.sub !== $store?.staff_profile?.sso_id &&
                    $store?.staff_profile.active.active_state !== 3 &&
                    $store?.sp_profile_onboarding_state === 3 &&
                    $store?.staff_members?.filter((member) => member.staff_id === $store?.auth?.sub)[0]?.active?.active_state === 3`,
                    onClick: [
                      {
                        type: 'clientAction',
                        action: 'triggerModal',
                        payload: [
                          {
                            display: true,
                            type: 'warning',
                            layout: {},
                            onEnter: [],
                            onLeave: [],
                            fragments: [
                              {
                                component: 'Text',
                                props: {
                                  textItems: [
                                    {
                                      text: 'Approve Staff Member',
                                      options: {
                                        format: 'heading',
                                        type: 'page-heading',
                                      },
                                      icon: {
                                        type: 'success-tick',
                                        size: 60,
                                        strokeWidth: '3',
                                        color: '#e5e5e5',
                                        style: { paddingLeft: '1rem' },
                                      },
                                      iconPosition: 'right',
                                    },
                                    {
                                      text: 'By approving this staff member, you are confirming that they belong to your company.',
                                      options: {
                                        format: 'heading',
                                        type: 'sub-heading',
                                        style: {
                                          paddingTop: '2rem',
                                        },
                                      },
                                    },
                                    // {
                                    //   text: 'The staff member will be attached to the company on the system and ',
                                    //   options: {
                                    //     format: 'heading',
                                    //     type: 'sub-heading',
                                    //   },
                                    // },
                                    {
                                      text: 'Please ensure that the staff member details are complete and correct.',
                                      options: {
                                        format: 'heading',
                                        type: 'sub-heading',
                                        style: {
                                          paddingTop: '2rem',
                                        },
                                      },
                                    },
                                  ],
                                },
                                layout: {
                                  display: 'grid',
                                  gridAutoFlow: 'row',
                                  justifyItems: 'center',
                                },
                              },
                              {
                                component: 'ButtonRow',
                                layout: {
                                  width: 'fit-content',
                                  margin: '0 auto',
                                },
                                props: {
                                  buttons: [
                                    {
                                      btnValue: 'Cancel Approval',
                                      onClick: [
                                        {
                                          type: 'clientAction',
                                          action: 'closeModal',
                                        },
                                      ],
                                    },
                                    {
                                      btnValue: 'Continue With Approval',
                                      onClick: [
                                        {
                                          type: 'clientAction',
                                          action: 'submitAndNavigate',
                                          payload: [
                                            {
                                              staff_id: '$staff_profile.sso_id',
                                            },
                                            {
                                              url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/approve_staffmember',
                                              headers: {},
                                              redirect:
                                                '/teams/manage-team/profile/member-profile',
                                            },
                                            {
                                              method: 'post',
                                              action:
                                                '/teams/manage-team/profile/member-profile',
                                            },
                                          ],
                                        },
                                        {
                                          type: 'clientAction',
                                          action: 'closeModal',
                                        },
                                      ],
                                    },
                                  ],
                                },
                              },
                            ],
                            navs: [],
                          },
                        ],
                      },
                    ],
                    actiontype: 'preferred',
                  },
                ],
              },
            },
            {
              component: 'FormBuilder',
              props: {
                proof_of_id:
                  "$staff_member_documentation?find:item.purpose === 'Identity'",
                defaultValues: {
                  full_name: '$staff_profile.full_name',
                  email_address: '$staff_profile.email_address',
                  contact_number: '$staff_profile.contact_number',
                  roles: '$staff_profile.roles',
                  id_type: '$staff_profile.id_type',
                  id_passport: '$staff_profile.id_passport',
                },
                fieldAccessPermissions: '#{staffFieldAccess}',
                config: {
                  style: {
                    display: 'grid',
                    gridTemplateColumns: 'repeat(2, 1fr)',
                    gridTemplateRows: 'repeat(3, 1fr)',
                    rowGap: '2rem',
                    columnGap: '1rem',
                    justifyItems: 'center',
                    alignContent: 'space-around',
                    height: 'auto',
                    paddingTop: '2rem',
                    paddingBottom: '2rem',
                    width: '50%',
                    minWidth: '712px',
                  },
                  controls: [
                    {
                      type: 'plain-text',
                      name: 'full_name',
                      label: 'Name',
                      // state: 'display-only',
                      validation: {
                        pattern: {
                          value: validationRegex.name.pattern,
                          message: validationRegex.name.message,
                        },
                        minLength: {
                          value: 2,
                          message: 'Name must be at least 2 characters',
                        },
                        maxLength: {
                          value: 30,
                          message: 'You cant go past 30 characters',
                        },
                      },
                      disabledWhen:
                        '$store?.auth?.sub === $store?.staff_profile?.sso_id',
                      fieldAccessPath: {
                        view: 'staffmember',
                        edit: 'staffmember',
                        special: 'staffmember',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 1,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    // {
                    //   type: 'drag-and-drop',
                    //   name: 'proof_of_id',
                    //   label: 'Proof of ID',
                    //   url: '{VITE_STAFF_SERVER}/api/v1/file_actions/upload_file',
                    //   action: '/teams/manage-team/profile/member-profile',
                    //   purpose: 'Identity', // expected by file sent to server
                    //   list: 'False', //expected by file sent to server
                    //   emptyText: 'No files uploaded',
                    //   documentSrc: 'staff',
                    //   css: {
                    //     wrapper: {
                    //       gridColumn: '1 / span 2',
                    //       gridRow: 3,
                    //       height: '4rem',
                    //       width: '100%',
                    //     },
                    //   },
                    // },
                    {
                      type: 'drag-and-drop-in-memory',
                      name: 'proof_of_id',
                      label: 'Proof of ID',
                      url: '{VITE_STAFF_SERVER}/api/v1/file_actions/upload_file',
                      action: '/teams/manage-team/profile/member-profile',
                      purpose: 'Identity', // expected by file sent to server
                      list: 'False', //expected by file sent to server
                      emptyText: 'No files uploaded',
                      fileTypesAllowed: ['pdf', 'image'],
                      fileSizeLimit: 5242880,
                      pathToRefId: 'staff_profile.sso_id',
                      disabledWhen:
                        '$store?.staff_profile.sso_id === $store?.auth?.sub',
                      fieldAccessPath: {
                        view: 'staffmember',
                        edit: 'staffmember',
                        special: 'staffmember',
                      },
                      css: {
                        wrapper: {
                          gridColumn: '1 / span 2',
                          gridRow: 3,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'contact_number',
                      label: 'Cell Number',
                      // state: 'display-only',
                      validation: {
                        pattern: {
                          value: validationRegex.phone_number.pattern,
                          message: validationRegex.phone_number.message,
                        },
                      },
                      disabledWhen:
                        '$store?.staff_profile.sso_id === $store?.auth?.sub',
                      fieldAccessPath: {
                        view: 'staffmember',
                        edit: 'staffmember',
                        special: 'staffmember',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 4,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'email_address',
                      label: 'Email',
                      // state: 'display-only',
                      validation: {
                        pattern: {
                          value: validationRegex.email.pattern,
                          message: validationRegex.email.message,
                        },
                      },
                      disabledWhen:
                        '$store?.staff_profile.sso_id === $store?.auth?.sub',
                      fieldAccessPath: {
                        view: 'staffmember',
                        edit: 'staffmember',
                        special: 'staffmember',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 2,
                          gridRow: 4,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'plain-text',
                      name: 'id_passport',
                      label: 'Identity Number',
                      // state: 'display-only',
                      validation: {
                        required: {
                          value: true,
                          message: 'This field is required',
                        },
                        pattern: {
                          value: validationRegex.id_passport.pattern,
                          message: validationRegex.id_passport.message,
                        },
                      },
                      disabledWhen:
                        '$store?.staff_profile.sso_id === $store?.auth?.sub',
                      fieldAccessPath: {
                        view: 'staffmember',
                        edit: 'staffmember',
                        special: 'staffmember',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 1,
                          gridRow: 2,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'single-select',
                      name: 'id_type',
                      label: 'ID Types',
                      // state: 'display-only',
                      placeholder: 'Select ID Type',
                      labelProp: 'name',
                      valueProp: 'id',
                      options: {
                        source: 'store',
                        storeDataPath: 'staff_enums.id_types',
                      },
                      disabledWhen:
                        '$store?.staff_profile.sso_id === $store?.auth?.sub',
                      validation: {
                        required: true,
                        message: 'This field is required',
                      },
                      fieldAccessPath: {
                        view: 'staffmember',
                        edit: 'staffmember',
                        special: 'staffmember',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 2,
                          gridRow: 2,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                    {
                      type: 'multi-select',
                      name: 'roles',
                      label: 'Roles',
                      // state: 'display-only',
                      placeholder: 'Select roles',
                      labelProp: 'description',
                      valueProp: 'id',
                      // Manually disabling staffmember's ability to change their own roles
                      disabledWhen:
                        '$store?.staff_profile?.sso_id === $store?.auth?.sub',
                      options: {
                        source: 'store',
                        storeDataPath: 'staff_enums.roles_2',
                      },
                      validation: {
                        required: true,
                        message: 'This field is required',
                      },
                      fieldAccessPath: {
                        view: 'staffmember',
                        edit: 'staffmember',
                        special: 'staffmember',
                      },
                      css: {
                        wrapper: {
                          gridColumn: 2,
                          gridRow: 1,
                          height: '4rem',
                          width: '100%',
                        },
                      },
                    },
                  ],
                },
              },
              layout: { display: 'grid', justifyItems: 'center' },
            },
          ],
          navs: [
            {
              label: 'Back To Teams',
              position: 'left',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'conditional',
                  payload: {
                    condition:
                      '$store?.postData && (Object.keys($store.postData).length > 0) && (Object.keys($formState.errors).length === 0)',
                    actions: {
                      whenTrue: [
                        {
                          type: 'clientAction',
                          action: 'triggerModal',
                          payload: [
                            {
                              display: true,
                              type: 'warning',
                              layout: {},
                              onEnter: [],
                              onLeave: [],
                              fragments: [
                                {
                                  component: 'Text',
                                  props: {
                                    textItems: [
                                      {
                                        text: 'Warning',
                                        options: {
                                          format: 'heading',
                                          type: 'page-heading',
                                        },
                                      },
                                      {
                                        text: `
                                  You are about to exit this page without saving.
                                `,
                                        options: {
                                          format: 'heading',
                                          type: 'sub-heading',
                                          style: {
                                            paddingTop: '2rem',
                                          },
                                        },
                                      },
                                      {
                                        text: `
                                  Would you like to save or Clear your changes?
                                `,
                                        options: {
                                          format: 'heading',
                                          type: 'sub-heading',
                                        },
                                      },
                                    ],
                                  },
                                  layout: {
                                    display: 'grid',
                                    gridAutoFlow: 'row',
                                    justifyItems: 'center',
                                  },
                                },
                                {
                                  component: 'ButtonRow',
                                  layout: {
                                    width: 'fit-content',
                                    margin: '0 auto',
                                  },
                                  props: {
                                    buttons: [
                                      {
                                        btnValue: 'Clear Changes',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'resetFields',
                                            payload: {
                                              fields: [
                                                {
                                                  fieldName: 'full_name',
                                                  defaultValue:
                                                    '$staff_profile.full_name',
                                                },
                                                {
                                                  fieldName: 'contact_number',
                                                  defaultValue:
                                                    '$staff_profile.contact_number',
                                                },
                                                {
                                                  fieldName: 'email_address',
                                                  defaultValue:
                                                    '$staff_profile.email_address',
                                                },
                                                {
                                                  fieldName: 'id_type',
                                                  defaultValue:
                                                    '$staff_profile.id_type',
                                                },
                                                {
                                                  fieldName: 'id_passport',
                                                  defaultValue:
                                                    '$staff_profile.id_passport',
                                                },
                                                {
                                                  fieldName: 'roles',
                                                  defaultValue:
                                                    '$staff_profile.roles',
                                                },
                                                {
                                                  fieldName: 'proof_of_id',
                                                  defaultValue: undefined,
                                                },
                                              ],
                                            },
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'navigate',
                                            payload: [
                                              '/teams/manage-team/list-view',
                                              { clearParams: true },
                                            ],
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'clearStore',
                                            payload: [
                                              'postData',
                                              'staff_profile',
                                              'staff_member_documentation',
                                              'profile_picture',
                                              'originalValues',
                                              'derivedproof_of_id', // Added to clear derived proof of ID
                                            ],
                                          },
                                        ],
                                      },
                                      {
                                        btnValue: 'Save Changes',
                                        onClick: [
                                          {
                                            type: 'clientAction',
                                            action: 'conditional',
                                            payload: {
                                              condition:
                                                '$store.filePostData && (Object.keys($store.filePostData) < 1)',
                                              actions: {
                                                whenTrue: [
                                                  {
                                                    type: 'clientAction',
                                                    action: 'submitAndNavigate',
                                                    payload: [
                                                      {
                                                        full_name:
                                                          '$formDataRaw.full_name',
                                                        contact_number:
                                                          '$formDataRaw.contact_number',
                                                        email_address:
                                                          '$formDataRaw.email_address',
                                                        id_type:
                                                          '$formDataRaw.id_type', // SA id or drivers license = 1, passport = 2
                                                        id_passport:
                                                          '$formDataRaw.id_passport',
                                                        // qualification_number: null,
                                                        // available: true,
                                                        // staff_type: 2,
                                                        roles:
                                                          '$formDataRaw.roles',
                                                        staff_id:
                                                          '$staff_profile.sso_id',
                                                      },
                                                      {
                                                        url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_staff_profile',
                                                        headers: {},
                                                        redirect:
                                                          '/teams/manage-team/list-view',
                                                      },
                                                      {
                                                        method: 'post',
                                                        action:
                                                          '/teams/manage-team/profile/member-profile',
                                                      },
                                                    ],
                                                  },
                                                ],
                                                whenFalse: [
                                                  {
                                                    type: 'clientAction',
                                                    action: 'conditional',
                                                    payload: {
                                                      condition:
                                                        '$store.formPostData && (Object.keys($store.formPostData) < 1)',
                                                      actions: {
                                                        whenTrue: [
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'submitAsync',
                                                            payload: {
                                                              calls: [
                                                                {
                                                                  key: 'update_proof_of_id',
                                                                  url: `${
                                                                    import.meta
                                                                      .env[
                                                                      'VITE_STAFF_SERVER'
                                                                    ]
                                                                  }/api/v1/file_actions/upload_file`,
                                                                  data: {
                                                                    file: '$proof_of_id.file',
                                                                    purpose:
                                                                      '$proof_of_id.purpose',
                                                                    list: '$proof_of_id.list',
                                                                    staff_id:
                                                                      '$proof_of_id.ref_id',
                                                                  },
                                                                },
                                                              ],
                                                              onFinish: {
                                                                type: 'clientAction',
                                                                action:
                                                                  'navigate',
                                                                payload: [
                                                                  '/teams/manage-team/list-view',
                                                                ],
                                                              },
                                                              redirect:
                                                                '/teams/manage-team/profile/member-profile',
                                                            },
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'resetFields',
                                                            payload: {
                                                              fields: [
                                                                {
                                                                  fieldName:
                                                                    'proof_of_id',
                                                                  defaultValue:
                                                                    undefined,
                                                                },
                                                              ],
                                                            },
                                                          },
                                                        ],
                                                        whenFalse: [
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'submitAsync',
                                                            payload: {
                                                              calls: [
                                                                {
                                                                  key: 'update_proof_of_id',
                                                                  // url: '{VITE_STAFF_SERVER}/api/v1/file_actions/upload_file',
                                                                  url: `${
                                                                    import.meta
                                                                      .env[
                                                                      'VITE_STAFF_SERVER'
                                                                    ]
                                                                  }/api/v1/file_actions/upload_file`,
                                                                  data: {
                                                                    file: '$proof_of_id.file',
                                                                    purpose:
                                                                      '$proof_of_id.purpose',
                                                                    list: '$proof_of_id.list',
                                                                    staff_id:
                                                                      '$proof_of_id.ref_id',
                                                                  },
                                                                },
                                                              ],
                                                              onFinish: {
                                                                type: 'clientAction',
                                                                action:
                                                                  'clearStore',
                                                                payload: [
                                                                  'postData',
                                                                  'filePostData',
                                                                ],
                                                              },
                                                              redirect:
                                                                '/teams/manage-team/list-view',
                                                            },
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'submitAndNavigate',
                                                            payload: [
                                                              {
                                                                full_name:
                                                                  '$formDataRaw.full_name',
                                                                contact_number:
                                                                  '$formDataRaw.contact_number',
                                                                email_address:
                                                                  '$formDataRaw.email_address',
                                                                id_type:
                                                                  '$formDataRaw.id_type', // SA id or drivers license = 1, passport = 2
                                                                id_passport:
                                                                  '$formDataRaw.id_passport',
                                                                // qualification_number: null,
                                                                // available: true,
                                                                // staff_type: 2,
                                                                roles:
                                                                  '$formDataRaw.roles',
                                                                staff_id:
                                                                  '$staff_profile.sso_id',
                                                              },
                                                              {
                                                                url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_staff_profile',
                                                                headers: {},
                                                                redirect:
                                                                  '/teams/manage-team/list-view',
                                                              },
                                                              {
                                                                method: 'post',
                                                                action:
                                                                  '/teams/manage-team/profile/member-profile',
                                                              },
                                                            ],
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'resetFields',
                                                            payload: {
                                                              fields: [
                                                                {
                                                                  fieldName:
                                                                    'proof_of_id',
                                                                  defaultValue:
                                                                    undefined,
                                                                },
                                                              ],
                                                            },
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'clearStore',
                                                            payload: [
                                                              'postData',
                                                            ],
                                                          },
                                                        ],
                                                      },
                                                    },
                                                  },
                                                ],
                                              },
                                            },
                                          },
                                          {
                                            type: 'clientAction',
                                            action: 'closeModal',
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                },
                              ],
                              navs: [],
                            },
                          ],
                        },
                      ],
                      whenFalse: [
                        {
                          type: 'clientAction',
                          action: 'navigate',
                          payload: [
                            '/teams/manage-team/list-view',
                            { clearParams: true },
                          ],
                        },
                      ],
                    },
                  },
                },
              ],
            },
            {
              label: 'Cancel Changes',
              position: 'center',
              disabledWhen:
                '$store?.postData && !(Object.keys($store.postData).length > 0)',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'resetFields',
                  payload: {
                    fields: [
                      {
                        fieldName: 'full_name',
                        defaultValue: '$staff_profile.full_name',
                      },
                      {
                        fieldName: 'contact_number',
                        defaultValue: '$staff_profile.contact_number',
                      },
                      {
                        fieldName: 'email_address',
                        defaultValue: '$staff_profile.email_address',
                      },
                      {
                        fieldName: 'id_type',
                        defaultValue: '$staff_profile.id_type',
                      },
                      {
                        fieldName: 'id_passport',
                        defaultValue: '$staff_profile.id_passport',
                      },
                      {
                        fieldName: 'roles',
                        defaultValue: '$staff_profile.roles',
                      },
                      {
                        fieldName: 'proof_of_id',
                        defaultValue: undefined,
                      },
                    ],
                  },
                },
              ],
            },
            {
              label: 'Save Changes',
              position: 'center',
              disabledWhen:
                '!$store?.postData || !(Object.keys($store.postData).length > 0) ||  (Object.keys($formState.errors).length > 0) || !$formState.isDirty',
              onClick: [
                {
                  type: 'clientAction',
                  action: 'conditional',
                  payload: {
                    condition:
                      '$store.filePostData && (Object.keys($store.filePostData) < 1)',
                    actions: {
                      whenTrue: [
                        {
                          type: 'clientAction',
                          action: 'submitAndNavigate',
                          payload: [
                            {
                              full_name: '$formDataRaw.full_name',
                              contact_number: '$formDataRaw.contact_number',
                              email_address: '$formDataRaw.email_address',
                              id_type: '$formDataRaw.id_type', // SA id or drivers license = 1, passport = 2
                              id_passport: '$formDataRaw.id_passport',
                              // qualification_number: null,
                              // available: true,
                              // staff_type: 2,
                              roles: '$formDataRaw.roles',
                              staff_id: '$staff_profile.sso_id',
                            },
                            {
                              url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_staff_profile',
                              headers: {},
                              redirect: '/teams/manage-team/list-view',
                            },
                            {
                              method: 'post',
                              action:
                                '/teams/manage-team/profile/member-profile',
                            },
                          ],
                        },
                      ],
                      whenFalse: [
                        {
                          type: 'clientAction',
                          action: 'conditional',
                          payload: {
                            condition:
                              '$store.formPostData && (Object.keys($store.formPostData) < 1)',
                            actions: {
                              whenTrue: [
                                {
                                  type: 'clientAction',
                                  action: 'submitAsync',
                                  payload: {
                                    calls: [
                                      {
                                        key: 'update_proof_of_id',
                                        url: '{VITE_STAFF_SERVER}/api/v1/file_actions/upload_file',
                                        data: {
                                          file: '$proof_of_id.file',
                                          purpose: '$proof_of_id.purpose',
                                          list: '$proof_of_id.list',
                                          staff_id: '$proof_of_id.ref_id',
                                        },
                                      },
                                    ],
                                    onFinish: {
                                      type: 'clientAction',
                                      action: 'navigate',
                                      payload: ['/teams/manage-team/list-view'],
                                    },
                                    redirect: '/teams/manage-team/list-view',
                                  },
                                },
                                {
                                  type: 'clientAction',
                                  action: 'resetFields',
                                  payload: {
                                    fields: [
                                      {
                                        fieldName: 'proof_of_id',
                                        defaultValue: undefined,
                                      },
                                    ],
                                  },
                                },
                              ],
                              whenFalse: [
                                {
                                  type: 'clientAction',
                                  action: 'submitAsync',
                                  payload: {
                                    calls: [
                                      {
                                        key: 'update_proof_of_id',
                                        url: '{VITE_STAFF_SERVER}/api/v1/file_actions/upload_file',
                                        data: {
                                          file: '$proof_of_id.file',
                                          purpose: '$proof_of_id.purpose',
                                          list: '$proof_of_id.list',
                                          staff_id: '$proof_of_id.ref_id',
                                        },
                                      },
                                    ],
                                    onFinish: {
                                      type: 'clientAction',
                                      action: 'clearStore',
                                      payload: ['postData', 'filePostData'],
                                    },
                                    redirect: '/teams/manage-team/list-view',
                                  },
                                },
                                {
                                  type: 'clientAction',
                                  action: 'submitAndNavigate',
                                  payload: [
                                    {
                                      full_name: '$formDataRaw.full_name',
                                      contact_number:
                                        '$formDataRaw.contact_number',
                                      email_address:
                                        '$formDataRaw.email_address',
                                      id_type: '$formDataRaw.id_type', // SA id or drivers license = 1, passport = 2
                                      id_passport: '$formDataRaw.id_passport',
                                      // qualification_number: null,
                                      // available: true,
                                      // staff_type: 2,
                                      roles: '$formDataRaw.roles',
                                      staff_id: '$staff_profile.sso_id',
                                    },
                                    {
                                      url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_staff_profile',
                                      headers: {},
                                      redirect: '/teams/manage-team/list-view',
                                    },
                                    {
                                      method: 'post',
                                      action:
                                        '/teams/manage-team/profile/member-profile',
                                    },
                                  ],
                                },
                                {
                                  type: 'clientAction',
                                  action: 'resetFields',
                                  payload: {
                                    fields: [
                                      {
                                        fieldName: 'proof_of_id',
                                        defaultValue: undefined,
                                      },
                                    ],
                                  },
                                },
                                {
                                  type: 'clientAction',
                                  action: 'clearStore',
                                  payload: ['postData'],
                                },
                              ],
                            },
                          },
                        },
                      ],
                    },
                  },
                },
                // {
                //   type: 'clientAction',
                //   action: 'submitAndNavigate',
                //   payload: [
                // {
                //   full_name: '$formDataRaw.full_name',
                //   contact_number: '$formDataRaw.contact_number',
                //   email_address: '$formDataRaw.email_address',
                //   id_type: '$formDataRaw.id_type', // SA id or drivers license = 1, passport = 2
                //   id_passport: '$formDataRaw.id_passport',
                //   // qualification_number: null,
                //   // available: true,
                //   // staff_type: 2,
                //   roles: '$formDataRaw.roles',
                //   staff_id: '$staff_profile.sso_id',
                // },
                //     {
                //       url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_staff_profile',
                //       headers: {},
                //       redirect: '/teams/manage-team/list-view',
                //     },
                //     {
                //       method: 'post',
                //       action: '/teams/manage-team/profile/member-profile',
                //     },
                //   ],
                // },
                {
                  type: 'clientAction',
                  action: 'clearStore',
                  payload: [
                    'postData',
                    'staff_profile',
                    'staff_member_documentation',
                    'profile_picture',
                    'originalValues',
                    'derivedproof_of_id', // Added to clear derived proof of ID
                  ],
                },
              ],
            },
          ],
          onLeave: [
            {
              type: 'clientAction',
              action: 'log',
              payload: ['Leaving the member profile'],
            },
            {
              type: 'clientAction',
              action: 'clearStore',
              payload: [
                'staff_profile',
                'staff_member_documentation',
                'profile_picture',
                'originalValues',
                'postData',
                'formDataRaw',
                'derivedproof_of_id', // Added to clear derived proof of ID
              ],
            },
          ],
        },
      },
      formOriginalValues: {
        // Orignals for diffing to find changed fields (from clientDataObject to store)
        'staff_profile.email_address': 'email_address',
        'staff_profile.contact_number': 'contact_number',
        'staff_profile.full_name': 'full_name',
        'staff_profile.id_type': 'id_type',
        'staff_profile.id_passport': 'id_passport',
        'staff_profile.roles': 'roles',
        derivedproof_of_id: 'proof_of_id',
      },
      formTransformMapper: {
        // Actual mapper to get final object shaped for server
        email_address: 'email_address',
        contact_number: 'contact_number',
        full_name: 'full_name',
        id_type: 'id_type',
        id_passport: 'id_passport',
        roles: 'roles',
        proof_of_id: 'proof_of_id',
      },
      actionPanels: [
        // Reminders
        // {
        //   icon: 'bell-02',
        //   title: 'Reminders', //?actionPanel=Messages--bell-02

        //   // fetchCalls: [],
        //   layout: {},
        //   onEnter: [],
        //   onLeave: [],
        //   fragments: [
        //     {
        //       component: 'ReminderView',
        //       layout: {
        //         marginTop: '10px',
        //         marginLeft: '10px',
        //         marginRight: '10px',
        //       },
        //       props: {
        //         reminders: [
        //           {
        //             background: 'primary',
        //             timeOfReminder: '01:00',
        //             reminderDate: '2024-07-01',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '09:30',
        //             reminderDate: '2024-07-03',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '00:01',
        //             reminderDate: '2024-07-09',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '01:00',
        //             reminderDate: '2024-07-01',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '09:30',
        //             reminderDate: '2024-07-03',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '00:01',
        //             reminderDate: '2024-07-09',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '01:00',
        //             reminderDate: '2024-07-01',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '09:30',
        //             reminderDate: '2024-07-03',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //           {
        //             background: 'primary',
        //             timeOfReminder: '00:01',
        //             reminderDate: '2024-07-09',
        //             description: 'This is a Description',
        //             label: 'This is a label',
        //           },
        //         ],
        //         toolbar: {
        //           buttonText: 'CREATE REMINDER',
        //           onClick: [
        //             {
        //               type: 'clientAction',
        //               action: 'triggerModal',
        //               payload: [
        //                 {
        //                   display: true,
        //                   type: 'warning',
        //                   heading: 'Add New Reminder',
        //                   headingType: 'page-heading',
        //                   layout: {},
        //                   onEnter: [],
        //                   onLeave: [],
        //                   fragments: [
        //                     {
        //                       component: 'FormBuilder',
        //                       layout: {
        //                         display: 'grid',
        //                         justifyItems: 'center',
        //                         margin: '0 auto',
        //                       },
        //                       props: {
        //                         config: {
        //                           style: {
        //                             display: 'grid',
        //                             gridTemplateColumns: 'repeat(2, 1fr)',
        //                             rowGap: '1rem',
        //                             columnGap: '1rem',
        //                             width: 'calc(100vw * 0.5)',
        //                             maxWidth: '819px',
        //                           },
        //                           controls: [
        //                             {
        //                               type: 'plain-text',
        //                               name: 'reminder_label',
        //                               label: 'Reminder Label',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1',
        //                                   gridRow: '1',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'single-select',
        //                               name: 'channel',
        //                               label: 'Channel',
        //                               options: {
        //                                 source: 'literal',
        //                                 data: [
        //                                   {
        //                                     value: 1,
        //                                     label: 'General',
        //                                   },
        //                                   {
        //                                     value: 2,
        //                                     label: 'Phone',
        //                                   },
        //                                   {
        //                                     value: 3,
        //                                     label: 'Email',
        //                                   },
        //                                 ],
        //                               },
        //                               css: {
        //                                 wrapper: {
        //                                   wrapper: {
        //                                     gridColumn: '2',
        //                                     gridRow: '1',
        //                                     width: '100%',
        //                                   },
        //                                 },
        //                               },
        //                               instructions:
        //                                 'Password minimum characters 12, one uppercase, one lowercase, one special character, one number',
        //                             },
        //                             {
        //                               type: 'datepicker',
        //                               name: 'reminder_date',
        //                               label: 'Date',
        //                               placeholder: 'Select date for reminder',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1',
        //                                   gridRow: '2',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'plain-text',
        //                               name: 'time',
        //                               label: 'Time',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '2',
        //                                   gridRow: '2',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'textarea',
        //                               name: 'what_matters',
        //                               label: 'Reminder Message',
        //                               placeholder: 'Reminder Message',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1 / span 2',
        //                                   gridRow: '3',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                             {
        //                               type: 'plain-text',
        //                               name: 'link_to_claim',
        //                               label: 'Link to claim',
        //                               icon: 'search-sm',
        //                               position: 'right',
        //                               css: {
        //                                 wrapper: {
        //                                   gridColumn: '1 / span 2',
        //                                   gridRow: '4',
        //                                   width: '100%',
        //                                 },
        //                               },
        //                             },
        //                           ],
        //                         },
        //                       },
        //                     },
        //                     {
        //                       component: 'ButtonRow',
        //                       layout: {
        //                         width: 'fit-content',
        //                         margin: 'auto',
        //                       },
        //                       props: {
        //                         buttons: [
        //                           {
        //                             btnValue: 'Cancel',
        //                             onClick: [
        //                               {
        //                                 type: 'clientAction',
        //                                 action: 'log',
        //                                 payload: ['Clicked cancel'],
        //                               },
        //                             ],
        //                           },
        //                           {
        //                             btnValue: 'Add Reminder',
        //                             onClick: [
        //                               {
        //                                 type: 'clientAction',
        //                                 action: 'log',
        //                                 payload: ['Clicked Add Reminder'],
        //                               },
        //                             ],
        //                           },
        //                         ],
        //                       },
        //                     },
        //                   ],
        //                   navs: [],
        //                 },
        //               ],
        //             },
        //           ],
        //         },
        //       },
        //     },
        //   ],
        //   actionLevel: 'bottomControls',
        // },
        // Scratch Pad
        {
          icon: 'clipboard',
          title: 'Scratch Pad', //?actionPanel=Messages--bell-02
          // fetchCalls: [],
          layout: {},
          onEnter: [],
          onLeave: [],
          fragments: [
            {
              component: 'ScratchPadView',
              layout: { marginLeft: '10px', marginRight: '10px' },
              props: {
                titlePlaceholder: 'Heading',
                icon: 'trash-01',
                iconHandler: (data: { heading: string; body: string }) =>
                  console.log(
                    'got data: Heading - ' +
                      data.heading +
                      ' Body - ' +
                      data.body
                  ),
                placeHolder: 'Text here...',
              },
            },
          ],
          actionLevel: 'bottomControls',
        },
      ],
    },
  },
};
