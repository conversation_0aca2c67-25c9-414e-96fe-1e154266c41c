{"name": "spa-test", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "spa-test/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/vite:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/spa-test"}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production"}}}, "serve": {"executor": "@nx/vite:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "spa-test:build"}, "configurations": {"development": {"buildTarget": "spa-test:build:development", "hmr": true}, "production": {"buildTarget": "spa-test:build:production", "hmr": false}}}, "preview": {"executor": "@nx/vite:preview-server", "defaultConfiguration": "development", "options": {"buildTarget": "spa-test:build"}, "configurations": {"development": {"buildTarget": "spa-test:build:development"}, "production": {"buildTarget": "spa-test:build:production"}}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "spa-test/jest.config.ts"}}}}