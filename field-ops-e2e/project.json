{"name": "field-ops-e2e", "$schema": "../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "field-ops-e2e/src", "implicitDependencies": ["field-ops"], "targets": {"e2e": {"executor": "@nx/playwright:playwright", "outputs": ["{workspaceRoot}/dist/.playwright/field-ops-e2e"], "options": {"config": "field-ops-e2e/playwright.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}