{"name": "@4sure-platform/source", "version": "0.0.0", "license": "MIT", "type": "module", "scripts": {"gen-desktop-dark-theme": "node ./desktopDarkThemeBuilder.js", "gen-desktop-light-theme": "node ./desktopLightThemeBuilder.js", "gen-mobile-light-theme": "node ./mobileLightThemeBuilder.js", "gen-mobile-dark-theme": "node ./mobileDarkThemeBuilder.js", "build:tokens": "npm run gen-desktop-light-theme && npm run gen-desktop-dark-theme && npm run gen-mobile-light-theme && npm run gen-mobile-dark-theme", "storybook": "npx nx storybook ui-platform"}, "private": true, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.20", "@mui/material": "^5.15.21", "@react-google-maps/api": "^2.19.3", "@storybook/manager-api": "^8.1.5", "@storybook/theming": "^8.1.5", "@tokens-studio/sd-transforms": "^0.15.2", "@types/react-router-dom": "^5.3.3", "@visx/visx": "^3.11.0", "axios": "^1.7.3", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "dayjs": "^1.11.11", "dexie": "^4.0.11", "dexie-react-hooks": "^1.1.7", "js-in-strings": "^0.1.4", "jwt-decode": "^4.0.0", "keycloak-js": "^25.0.2", "moment": "^2.30.1", "next": "14.2.3", "ramda": "^0.30.0", "react": "18.3.1", "react-day-picker": "^8.10.1", "react-dom": "18.3.1", "react-dropzone": "^14.2.3", "react-easy-crop": "^5.0.8", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.51.5", "react-is": "18.3.1", "react-router-dom": "^6.24.0", "react-spring": "^9.7.4", "react-toastify": "^11.0.5", "simplebar": "^6.2.7", "simplebar-react": "^3.2.6", "storybook-addon-styled-component-theme": "^2.0.0", "style-dictionary": "4.0.1", "styled-components": "5.3.6", "tslib": "^2.3.0", "uuid": "^9.0.1", "zod": "^3.23.8", "zustand": "^4.5.2"}, "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@chromatic-com/storybook": "^1.5.0", "@nx/cypress": "19.1.2", "@nx/devkit": "19.1.2", "@nx/eslint": "19.1.2", "@nx/eslint-plugin": "19.1.2", "@nx/jest": "19.1.2", "@nx/js": "19.1.2", "@nx/next": "^19.1.2", "@nx/playwright": "19.1.2", "@nx/react": "19.1.2", "@nx/storybook": "19.1.2", "@nx/vite": "19.1.2", "@nx/web": "19.1.2", "@nx/workspace": "19.1.2", "@playwright/test": "^1.36.0", "@rollup/plugin-commonjs": "^28.0.3", "@storybook/addon-essentials": "^8.1.5", "@storybook/addon-interactions": "^8.1.5", "@storybook/addon-themes": "^8.1.5", "@storybook/core-server": "^8.1.5", "@storybook/react-vite": "^8.1.5", "@storybook/test": "^8.1.5", "@storybook/test-runner": "^0.18.0", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.3.12", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@testing-library/react": "15.0.6", "@types/dexie": "^1.3.32", "@types/jest": "^29.4.0", "@types/node": "18.19.31", "@types/ramda": "^0.29.10", "@types/react": "^18.3.3", "@types/react-dom": "18.3.0", "@types/react-google-recaptcha": "^2.1.9", "@types/react-is": "18.3.0", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "7.7.1", "@typescript-eslint/parser": "7.7.1", "@vitejs/plugin-react": "4.2.1", "@vitest/ui": "^1.3.1", "babel-jest": "^29.4.1", "babel-plugin-styled-components": "1.10.7", "eslint": "8.57.0", "eslint-config-next": "14.2.3", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-playwright": "^0.15.3", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "jest": "^29.4.1", "jest-environment-jsdom": "^29.4.1", "jsdom": "~22.1.0", "nx": "19.1.2", "nx-cloud": "19.0.0", "prettier": "^2.6.2", "sass": "^1.55.0", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "typescript": "5.4.5", "vite": "5.2.10", "vite-plugin-dts": "~3.8.1", "vitest": "^1.3.1", "@types/crypto-js": "^4.2.2"}}