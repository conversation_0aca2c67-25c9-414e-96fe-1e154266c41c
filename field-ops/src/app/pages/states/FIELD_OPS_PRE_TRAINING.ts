import { bbbeee, StateConfig, validationRegex } from '@4-sure/ui-platform';

/*
 * SECTION: PRE-TRAINING STATE
 * The pre-traing state is a read only view for a FO agent to plan training with a sp after evaluation
 */
// #region PRE-TRAINING STATE
export const FIELD_OPS_PRE_TRAINING_STATE = {
  title: { template: '' },
  fetchCalls: [
    // #region TASKS EDIT STATE FETCHCALLS
    // fetchcalls that will be made the state level
    {
      key: 'sp_enums',
      method: 'POST',
      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_enum',
      body: { enum: 'all' },
      slicePath: 'payload',
    },
    // TODO: additional logic to be added to determine nature of task and respective api call
    {
      key: 'task_details',
      method: 'POST',
      url: '{VITE_SP_SERVER}/api/v1/task_actions/get_task_details',
      slicePath: 'payload',
      successFetchCalls: [
        {
          key: 'sp_profile',
          method: 'POST',
          url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_sp',
          body: { sp_id: '$object_id' },
          slicePath: 'payload',
        },
        {
          key: 'company_documentation',
          method: 'POST',
          url: '{VITE_SP_SERVER}/api/v1/file_actions/get_files',
          body: {
            with_thumbnails: true,
            sp_id: '$object_id',
            order: '-',
          },
          slicePath: 'payload',
        },
      ],
    },
  ],
  // #endregion
  defaultScreen: 'details',
  onLeave: [
    {
      type: 'clientAction',
      action: 'clearStore',
      payload: [
        'task_details',
        'sp_profile',
        'company_documentation',
        'documentsNotFound',
        'derivedCompanies',
        'derivedOperationalArea',
        'directors',
        'originalValues',
      ],
    },
  ],
  screens: {
    // #region TASKS/EDIT/DETAILS SCREEN
    details: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'ProfileHero',
          layout: {
            display: 'grid',
            justifyContent: 'center',
          },
          props: {
            fullname: '$sp_profile.details.name',
            subText: 'Registration number: ',
            username: '$sp_profile.details.co_reg',
            active: false,
            image: '$sp_profile.company_profile_picture',
            profileType: 'company',
            state: '$sp_profile.details.onboarding_state',
            showImgUpload: false,
          },
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Company details',
                options: {
                  format: 'heading',
                  type: 'section-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            bbbeee_certificate:
              "$company_documentation?find:item.purpose === 'Reg BBEEE certificate'",
            defaultValues: {
              trading_as: '$sp_profile.details.trading_as',
              bbeee: '$sp_profile.details.bbeee',
              co_reg: '$sp_profile.details.co_reg',
              name: '$sp_profile.details.name',
              company_type: '$sp_profile.details.company_type',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'co_reg',
                  label: 'Company registration',
                  disabledWhen: true,
                  state: 'display-only',
                  fieldAccessPath: { view: 'details', edit: 'details' },
                  validation: {
                    required: {
                      value: true,
                      message: 'Registration number is required',
                    },
                    conditional: {
                      value: `$store.formDataRaw.company_type === 2 ? 'registration_number' : 'company_registration_number'`,
                      message: 'Registration number is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'name',
                  label: 'Company name',
                  disabledWhen: true,
                  state: 'display-only',
                  fieldAccessPath: { view: 'details', edit: 'details' },
                  validation: {
                    required: {
                      value: true,
                      message: 'Name is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'trading_as',
                  label: 'Trading as',
                  disabledWhen: true,
                  state: 'display-only',
                  fieldAccessPath: { view: 'details', edit: 'details' },
                  validation: {},
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'single-select',
                  name: 'company_type',
                  label: 'Type of company',
                  state: 'display-only',
                  disabledWhen: true,
                  valueProp: 'id',
                  labelProp: 'name',
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.company_types',
                  },
                  fieldAccessPath: { view: 'details', edit: 'details' },
                  validation: {},
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'single-select',
                  dropdownScroll: true,
                  name: 'bbeee',
                  label: 'BBBEEE Level',
                  state: 'display-only',
                  disabledWhen: true,
                  labelProp: 'label',
                  valueProp: 'value',
                  fieldAccessPath: { view: 'details', edit: 'details' },
                  validation: {},
                  options: {
                    data: bbbeee,
                    source: 'literal',
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'bbbeee_certificate',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg BBEEE certificate',
                  purposeAsName: true,
                  isLandscape: true,
                  disabledWhen: true,
                  enableUpload: false,
                  fieldAccessPath: { view: 'details', edit: 'details' },
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/field-ops/tasks/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            // width: 'calc(100% - 226px - 56px)',
          },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store?.postData && (Object.keys($store.postData).length > 0) && (Object.keys($formState.errors).length === 0)',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: `
                                 You are about to exit this page without saving.
                               `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                  {
                                    text: `
                                 Would you like to save or Clear your changes?
                               `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            {
                                              fieldName: 'trading_as',
                                              defaultValue:
                                                '$sp_profile.details.trading_as',
                                            },
                                            {
                                              fieldName: 'bbeee',
                                              defaultValue:
                                                '$sp_profile.details.bbeee',
                                            },
                                            {
                                              fieldName: 'co_reg',
                                              defaultValue:
                                                '$sp_profile.details.co_reg',
                                            },
                                            {
                                              fieldName: 'name',
                                              defaultValue:
                                                '$sp_profile.details.name',
                                            },
                                            {
                                              fieldName: 'company_type',
                                              defaultValue:
                                                '$sp_profile.details.company_type',
                                            },
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: ['/field-ops/tasks/list-view'],
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndFetch',
                                        payload: [
                                          {
                                            postData: '$postData',
                                            sp_id: '$sp_profile.id',
                                          },
                                          {
                                            url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                            headers: {},
                                            bodySlicePath: 'postData',
                                          },
                                          {
                                            method: 'post',
                                            action:
                                              '/field-ops/tasks/list-view',
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: ['/field-ops/tasks/list-view'],
                    },
                  ],
                },
              },
            },
          ],
        },
        // #region DEACTIVATE SP BUTTON
        {
          label: `Deactivate SP`,
          colorVariant: 'alternative',
          position: 'right',
          onClick: [
            {
              type: 'clientAction',
              action: 'triggerModal',
              payload: [
                {
                  display: true,
                  type: 'warning',
                  layout: {
                    height: '75%',
                  },
                  onEnter: [],
                  onLeave: [],
                  fragments: [
                    {
                      component: 'Text',
                      props: {
                        textItems: [
                          {
                            text: 'Deactivate SP',
                            options: {
                              format: 'heading',
                              type: 'page-heading',
                            },
                          },
                          {
                            text: `
                      Deactivating this SP will remove them from the Connexa Platform.
                    `,
                            options: {
                              format: 'heading',
                              type: 'sub-heading',
                              style: {
                                paddingTop: '1rem',
                                textAlign: 'center',
                              },
                            },
                          },
                          {
                            text: `
                      Are you sure you want to deactivate this SP?
                    `,
                            options: {
                              format: 'heading',
                              type: 'sub-heading',
                              style: {
                                textAlign: 'center',
                              },
                            },
                          },
                        ],
                      },
                      layout: {
                        display: 'grid',
                        gridAutoFlow: 'row',
                        justifyItems: 'center',
                      },
                    },
                    {
                      component: 'ButtonRow',
                      layout: {
                        width: 'fit-content',
                        margin: '0 auto',
                      },
                      props: {
                        buttons: [
                          {
                            btnValue: 'No, cancel',
                            onClick: [
                              {
                                type: 'clientAction',
                                action: 'closeModal',
                              },
                            ],
                          },
                          {
                            btnValue: 'Yes, continue',
                            onClick: [
                              {
                                type: 'clientAction',
                                action: 'closeModal',
                              },
                              {
                                type: 'clientAction',
                                action: 'triggerModal',
                                payload: [
                                  {
                                    display: true,
                                    type: 'warning',
                                    layout: {
                                      height: '75%',
                                    },
                                    onEnter: [],
                                    onLeave: [],
                                    fragments: [
                                      {
                                        component: 'Text',
                                        props: {
                                          textItems: [
                                            {
                                              text: 'Deactivate SP',
                                              options: {
                                                format: 'heading',
                                                type: 'page-heading',
                                              },
                                            },
                                          ],
                                        },
                                        layout: {},
                                      },

                                      {
                                        component: 'FormBuilder',
                                        layout: {
                                          display: 'grid',
                                          justifyItems: 'center',
                                          marginTop: '2rem',
                                        },
                                        props: {
                                          config: {
                                            style: { width: '100%' },
                                            controls: [
                                              {
                                                type: 'single-select',
                                                dropdownScroll: true,
                                                name: 'reason',
                                                label:
                                                  'Reason for deactivate sp',
                                                placeholder: 'Select a Reason',
                                                labelProp: 'label',
                                                valueProp: 'value',
                                                validation: {},
                                                options: {
                                                  data: [
                                                    {
                                                      value:
                                                        'Fraudulant Activity',
                                                      label:
                                                        'Fraudulant Activity',
                                                    },
                                                    {
                                                      value: 'Requested by SP',
                                                      label: 'Requested by SP',
                                                    },
                                                  ],

                                                  source: 'literal',
                                                },
                                                css: {
                                                  wrapper: {
                                                    height: '4rem',
                                                    width: '100%',
                                                  },
                                                },
                                              },
                                              {
                                                type: 'textarea',
                                                name: 'deactivation_notes',
                                                label: 'Notes',
                                                rows: 10,
                                                // validation: {
                                                //   required: {
                                                //     value: true,
                                                //     message: 'This field is required',
                                                //   },
                                                // },
                                              },
                                            ],
                                          },
                                        },
                                      },
                                      {
                                        component: 'ButtonRow',
                                        layout: {
                                          width: 'fit-content',
                                          margin: '0 auto',
                                        },
                                        props: {
                                          buttons: [
                                            {
                                              btnValue: 'Cancel',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'resetFields',
                                                  payload: {
                                                    fields: [
                                                      'deactivation_notes',
                                                      'reason',
                                                    ],
                                                  },
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                              ],
                                            },
                                            {
                                              btnValue: 'Suspend SP',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'log',
                                                  payload: [
                                                    {
                                                      reason: `#{formDataRaw.reason} - {formDataRaw.deactivation_notes}`,
                                                      deactivation_notes: `#{formDataRaw.deactivation_notes}`,
                                                    },
                                                  ],
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'navigate',
                                                  payload: [
                                                    '/field-ops/tasks/list-view',
                                                  ],
                                                },
                                              ],
                                            },
                                          ],
                                        },
                                      },
                                    ],
                                    navs: [],
                                  },
                                ],
                              },
                            ],
                          },
                        ],
                      },
                    },
                  ],
                  navs: [],
                },
              ],
            },
          ],
        },
        // #endregion
        // #region CONTACTING COMPLETE BUTTON
        {
          label: `Contacting Complete`,
          colorVariant: 'preferred',
          position: 'right',
          onClick: [
            {
              type: 'clientAction',
              action: 'submitAndNavigate',
              payload: [
                {
                  sp_id: '$task_details.object_id',
                  task_id: '$task_details.id',
                },
                {
                  url: '{VITE_SP_SERVER}/api/v1/spaas_actions/sp_contacted_for_training',
                  headers: {},
                  redirect: '/field-ops/tasks/list-view',
                },
                {
                  method: 'post',
                  action: '/field-ops/tasks/pre-training/details',
                },
              ],
            },
            {
              type: 'clientAction',
              action: 'closeModal',
            },
            {
              type: 'clientAction',
              action: 'clearStore',
              payload: ['task_details', 'sp_profile', 'company_documentation'],
            },
          ],
        },
        // #endregion
      ],
    },
    // #endregion
    // #region TASKS/EDIT/CONTACT SCREEN
    contact: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Company contact information',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            defaultValues: {
              contact_primary: '$sp_profile.address.contact_primary',
              contact_secondary: '$sp_profile.address.contact_secondary',
              contact_person: '$sp_profile.address.contact_person',
              email_receiving: '$sp_profile.address.email_receiving',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'contact_person',
                  label: 'Primary Contact Person Name',
                  state: 'display-only',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'contact_primary',
                  label: 'Primary Contact Number',
                  state: 'display-only',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                    pattern: {
                      value: validationRegex.phone_number.pattern,
                      message: validationRegex.phone_number.message,
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'contact_secondary',
                  label: 'Secondary Contact Number',
                  state: 'display-only',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  validation: {
                    pattern: {
                      value: validationRegex.phone_number.pattern,
                      message: validationRegex.phone_number.message,
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'email_receiving',
                  label: 'Email Address',
                  state: 'display-only',
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                    pattern: {
                      value: validationRegex.email.pattern,
                      message: validationRegex.email.message,
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
        {
          component: 'Separator',
          layout: { width: '50%', minWidth: '712px', margin: 'auto' },
          props: { height: 'thin' },
        },
        {
          component: 'FormBuilder',
          props: {
            defaultValues: {
              physical_addr: '$sp_profile.address.physical_addr',
              physical_city: '$sp_profile.address.physical_city',
              physical_code: '$sp_profile.address.physical_code',
              physical_suburb: '$sp_profile.address.physical_suburb',
              province: '$sp_profile.address.province',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'physical_addr',
                  label: 'Company street address',
                  state: 'display-only',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'physical_suburb',
                  label: 'Company suburb',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  state: 'display-only',
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  disabledWhen: true,
                  type: 'plain-text',
                  name: 'physical_city',
                  label: 'Company city',
                  state: 'display-only',
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'physical_code',
                  label: 'Company postal code',
                  state: 'display-only',
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'single-select',
                  name: 'province',
                  label: 'Company province',
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  dropdownScroll: true,
                  state: 'display-only',
                  validation: {
                    required: {
                      value: true,
                      message: 'Province is required',
                    },
                  },
                  labelProp: 'name',
                  valueProp: 'name',
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.provinces',
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
        {
          component: 'Separator',
          layout: { width: '50%', minWidth: '712px', margin: 'auto' },
          props: { height: 'thin' },
        },
        {
          component: 'FormBuilder',
          props: {
            defaultValues: {
              postal_box: '$sp_profile.address.postal_box',
              postal_city: '$sp_profile.address.postal_city',
              postal_code: '$sp_profile.address.postal_code',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  disabledWhen: true,
                  type: 'plain-text',
                  name: 'postal_box',
                  label: 'Postal address',
                  state: 'display-only',
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  disabledWhen: true,
                  type: 'plain-text',
                  name: 'postal_city',
                  label: 'Postal address suburb/town',
                  state: 'display-only',
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'postal_code',
                  disabledWhen: true,
                  label: 'Postal address postal code',
                  state: 'display-only',
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store?.postData && (Object.keys($store.postData).length > 0) && (Object.keys($formState.errors).length === 0)',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: `
                                 You are about to exit this page without saving.
                               `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                  {
                                    text: `
                                 Would you like to save or Clear your changes?
                               `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            {
                                              fieldName: 'trading_as',
                                              defaultValue:
                                                '$sp_profile.details.trading_as',
                                            },
                                            {
                                              fieldName: 'bbeee',
                                              defaultValue:
                                                '$sp_profile.details.bbeee',
                                            },
                                            {
                                              fieldName: 'co_reg',
                                              defaultValue:
                                                '$sp_profile.details.co_reg',
                                            },
                                            {
                                              fieldName: 'name',
                                              defaultValue:
                                                '$sp_profile.details.name',
                                            },
                                            {
                                              fieldName: 'company_type',
                                              defaultValue:
                                                '$sp_profile.details.company_type',
                                            },
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: ['/field-ops/tasks/list-view'],
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndFetch',
                                        payload: [
                                          {
                                            postData: '$postData',
                                            sp_id: '$sp_profile.id',
                                          },
                                          {
                                            url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                            headers: {},
                                            bodySlicePath: 'postData',
                                          },
                                          {
                                            method: 'post',
                                            action:
                                              '/field-ops/tasks/list-view',
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: ['/field-ops/tasks/list-view'],
                    },
                  ],
                },
              },
            },
          ],
        },
        // #region DEACTIVATE SP BUTTON
        {
          label: `Deactivate SP`,
          colorVariant: 'alternative',
          position: 'right',
          onClick: [
            {
              type: 'clientAction',
              action: 'triggerModal',
              payload: [
                {
                  display: true,
                  type: 'warning',
                  layout: {
                    height: '75%',
                  },
                  onEnter: [],
                  onLeave: [],
                  fragments: [
                    {
                      component: 'Text',
                      props: {
                        textItems: [
                          {
                            text: 'Deactivate SP',
                            options: {
                              format: 'heading',
                              type: 'page-heading',
                            },
                          },
                          {
                            text: `
                      Deactivating this SP will remove them from the Connexa Platform.
                    `,
                            options: {
                              format: 'heading',
                              type: 'sub-heading',
                              style: {
                                paddingTop: '1rem',
                                textAlign: 'center',
                              },
                            },
                          },
                          {
                            text: `
                      Are you sure you want to deactivate this SP?
                    `,
                            options: {
                              format: 'heading',
                              type: 'sub-heading',
                              style: {
                                textAlign: 'center',
                              },
                            },
                          },
                        ],
                      },
                      layout: {
                        display: 'grid',
                        gridAutoFlow: 'row',
                        justifyItems: 'center',
                      },
                    },
                    {
                      component: 'ButtonRow',
                      layout: {
                        width: 'fit-content',
                        margin: '0 auto',
                      },
                      props: {
                        buttons: [
                          {
                            btnValue: 'No, cancel',
                            onClick: [
                              {
                                type: 'clientAction',
                                action: 'closeModal',
                              },
                            ],
                          },
                          {
                            btnValue: 'Yes, continue',
                            onClick: [
                              {
                                type: 'clientAction',
                                action: 'closeModal',
                              },
                              {
                                type: 'clientAction',
                                action: 'triggerModal',
                                payload: [
                                  {
                                    display: true,
                                    type: 'warning',
                                    layout: {
                                      height: '75%',
                                    },
                                    onEnter: [],
                                    onLeave: [],
                                    fragments: [
                                      {
                                        component: 'Text',
                                        props: {
                                          textItems: [
                                            {
                                              text: 'Deactivate SP',
                                              options: {
                                                format: 'heading',
                                                type: 'page-heading',
                                              },
                                            },
                                          ],
                                        },
                                        layout: {},
                                      },

                                      {
                                        component: 'FormBuilder',
                                        layout: {
                                          display: 'grid',
                                          justifyItems: 'center',
                                          marginTop: '2rem',
                                        },
                                        props: {
                                          config: {
                                            style: { width: '100%' },
                                            controls: [
                                              {
                                                type: 'single-select',
                                                dropdownScroll: true,
                                                name: 'reason',
                                                label:
                                                  'Reason for deactivate sp',
                                                placeholder: 'Select a Reason',
                                                labelProp: 'label',
                                                valueProp: 'value',
                                                validation: {},
                                                options: {
                                                  data: [
                                                    {
                                                      value:
                                                        'Fraudulant Activity',
                                                      label:
                                                        'Fraudulant Activity',
                                                    },
                                                    {
                                                      value: 'Requested by SP',
                                                      label: 'Requested by SP',
                                                    },
                                                  ],

                                                  source: 'literal',
                                                },
                                                css: {
                                                  wrapper: {
                                                    height: '4rem',
                                                    width: '100%',
                                                  },
                                                },
                                              },
                                              {
                                                type: 'textarea',
                                                name: 'deactivation_notes',
                                                label: 'Notes',
                                                rows: 10,
                                              },
                                            ],
                                          },
                                        },
                                      },
                                      {
                                        component: 'ButtonRow',
                                        layout: {
                                          width: 'fit-content',
                                          margin: '0 auto',
                                        },
                                        props: {
                                          buttons: [
                                            {
                                              btnValue: 'Cancel',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'resetFields',
                                                  payload: {
                                                    fields: [
                                                      'deactivation_notes',
                                                      'reason',
                                                    ],
                                                  },
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                              ],
                                            },
                                            {
                                              btnValue: 'Suspend SP',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'log',
                                                  payload: [
                                                    {
                                                      reason: `#{formDataRaw.reason} - {formDataRaw.deactivation_notes}`,
                                                      deactivation_notes: `#{formDataRaw.deactivation_notes}`,
                                                    },
                                                  ],
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'navigate',
                                                  payload: [
                                                    '/field-ops/tasks/list-view',
                                                  ],
                                                },
                                              ],
                                            },
                                          ],
                                        },
                                      },
                                    ],
                                    navs: [],
                                  },
                                ],
                              },
                            ],
                          },
                        ],
                      },
                    },
                  ],
                  navs: [],
                },
              ],
            },
          ],
        },
        // #endregion
        // #region CONTACTING COMPLETE BUTTON
        {
          label: `Contacting Complete`,
          colorVariant: 'preferred',
          position: 'right',
          onClick: [
            {
              type: 'clientAction',
              action: 'submitAndNavigate',
              payload: [
                {
                  sp_id: '$task_details.object_id',
                  task_id: '$task_details.id',
                },
                {
                  url: '{VITE_SP_SERVER}/api/v1/spaas_actions/sp_contacted_for_training',
                  headers: {},
                  redirect: '/field-ops/tasks/list-view',
                },
                {
                  method: 'post',
                  action: '/field-ops/tasks/pre-training/details',
                },
              ],
            },
            {
              type: 'clientAction',
              action: 'closeModal',
            },
            {
              type: 'clientAction',
              action: 'clearStore',
              payload: ['task_details', 'sp_profile', 'company_documentation'],
            },
          ],
        },
        // #endregion
      ],
    },
    // #endregion
    // #region TASKS/EDIT/WORK SCREEN
    work: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Scope of work you accept',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            sp_associated_companies: '$sp_profile.companies?map:item.client_id',
            defaultValues: {
              skills: '$sp_profile.skills',
              after_hours: '$sp_profile.after_hours',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'multiselect-checklist',
                  name: 'skills',
                  label: 'Company skills',
                  state: 'display-only',
                  fieldAccessPath: {
                    view: 'skills',
                    edit: 'skills',
                    special: 'skills',
                  },
                  labelProp: 'name',
                  valueProp: 'id',
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.skills',
                  },
                  checkedItems: 'sp_profile.skills',
                  maxColumns: 2,
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'radio-group',
                  name: 'after_hours',
                  fieldAccessPath: {
                    view: 'after_hours',
                    edit: 'after_hours',
                  },
                  label: 'Do you work after hours?',
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  options: {
                    source: 'literal',
                    data: [
                      { label: 'Yes', value: true },
                      { label: 'No', value: false },
                    ],
                  },
                  size: 'small',
                  returnBoolean: true,
                  css: {
                    wrapper: {
                      justifySelf: 'start',
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'multiselect-checklist',
                  name: 'sp_associated_companies',
                  companies: '$sp_profile.companies',
                  fieldAccessPath: {
                    view: 'companies',
                    edit: 'companies',
                  },
                  label: 'Companies you would like to recieve work from',
                  state: 'display-only',
                  labelProp: 'name',
                  valueProp: 'id',
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.companies',
                  },
                  checkedItems: 'sp_profile.companies',
                  checkedItemsTransformPath: 'client_id',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store?.postData && (Object.keys($store.postData).length > 0) && (Object.keys($formState.errors).length === 0)',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: `
                                 You are about to exit this page without saving.
                               `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                  {
                                    text: `
                                 Would you like to save or Clear your changes?
                               `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            {
                                              fieldName: 'trading_as',
                                              defaultValue:
                                                '$sp_profile.details.trading_as',
                                            },
                                            {
                                              fieldName: 'bbeee',
                                              defaultValue:
                                                '$sp_profile.details.bbeee',
                                            },
                                            {
                                              fieldName: 'co_reg',
                                              defaultValue:
                                                '$sp_profile.details.co_reg',
                                            },
                                            {
                                              fieldName: 'name',
                                              defaultValue:
                                                '$sp_profile.details.name',
                                            },
                                            {
                                              fieldName: 'company_type',
                                              defaultValue:
                                                '$sp_profile.details.company_type',
                                            },
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: ['/field-ops/tasks/list-view'],
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndFetch',
                                        payload: [
                                          {
                                            postData: '$postData',
                                            sp_id: '$sp_profile.id',
                                          },
                                          {
                                            url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                            headers: {},
                                            bodySlicePath: 'postData',
                                          },
                                          {
                                            method: 'post',
                                            action:
                                              '/field-ops/tasks/list-view',
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: ['/field-ops/tasks/list-view'],
                    },
                  ],
                },
              },
            },
          ],
        },
        // #region DEACTIVATE SP BUTTON
        {
          label: `Deactivate SP`,
          colorVariant: 'alternative',
          position: 'right',
          onClick: [
            {
              type: 'clientAction',
              action: 'triggerModal',
              payload: [
                {
                  display: true,
                  type: 'warning',
                  layout: {
                    height: '75%',
                  },
                  onEnter: [],
                  onLeave: [],
                  fragments: [
                    {
                      component: 'Text',
                      props: {
                        textItems: [
                          {
                            text: 'Deactivate SP',
                            options: {
                              format: 'heading',
                              type: 'page-heading',
                            },
                          },
                          {
                            text: `
                      Deactivating this SP will remove them from the Connexa Platform.
                    `,
                            options: {
                              format: 'heading',
                              type: 'sub-heading',
                              style: {
                                paddingTop: '1rem',
                                textAlign: 'center',
                              },
                            },
                          },
                          {
                            text: `
                      Are you sure you want to deactivate this SP?
                    `,
                            options: {
                              format: 'heading',
                              type: 'sub-heading',
                              style: {
                                textAlign: 'center',
                              },
                            },
                          },
                        ],
                      },
                      layout: {
                        display: 'grid',
                        gridAutoFlow: 'row',
                        justifyItems: 'center',
                      },
                    },
                    {
                      component: 'ButtonRow',
                      layout: {
                        width: 'fit-content',
                        margin: '0 auto',
                      },
                      props: {
                        buttons: [
                          {
                            btnValue: 'No, cancel',
                            onClick: [
                              {
                                type: 'clientAction',
                                action: 'closeModal',
                              },
                            ],
                          },
                          {
                            btnValue: 'Yes, continue',
                            onClick: [
                              {
                                type: 'clientAction',
                                action: 'closeModal',
                              },
                              {
                                type: 'clientAction',
                                action: 'triggerModal',
                                payload: [
                                  {
                                    display: true,
                                    type: 'warning',
                                    layout: {
                                      height: '75%',
                                    },
                                    onEnter: [],
                                    onLeave: [],
                                    fragments: [
                                      {
                                        component: 'Text',
                                        props: {
                                          textItems: [
                                            {
                                              text: 'Deactivate SP',
                                              options: {
                                                format: 'heading',
                                                type: 'page-heading',
                                              },
                                            },
                                          ],
                                        },
                                        layout: {},
                                      },

                                      {
                                        component: 'FormBuilder',
                                        layout: {
                                          display: 'grid',
                                          justifyItems: 'center',
                                          marginTop: '2rem',
                                        },
                                        props: {
                                          config: {
                                            style: { width: '100%' },
                                            controls: [
                                              {
                                                type: 'single-select',
                                                dropdownScroll: true,
                                                name: 'reason',
                                                label:
                                                  'Reason for deactivate sp',
                                                placeholder: 'Select a Reason',
                                                labelProp: 'label',
                                                valueProp: 'value',
                                                validation: {},
                                                options: {
                                                  data: [
                                                    {
                                                      value:
                                                        'Fraudulant Activity',
                                                      label:
                                                        'Fraudulant Activity',
                                                    },
                                                    {
                                                      value: 'Requested by SP',
                                                      label: 'Requested by SP',
                                                    },
                                                  ],

                                                  source: 'literal',
                                                },
                                                css: {
                                                  wrapper: {
                                                    height: '4rem',
                                                    width: '100%',
                                                  },
                                                },
                                              },
                                              {
                                                type: 'textarea',
                                                name: 'deactivation_notes',
                                                label: 'Notes',
                                                rows: 10,
                                              },
                                            ],
                                          },
                                        },
                                      },
                                      {
                                        component: 'ButtonRow',
                                        layout: {
                                          width: 'fit-content',
                                          margin: '0 auto',
                                        },
                                        props: {
                                          buttons: [
                                            {
                                              btnValue: 'Cancel',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'resetFields',
                                                  payload: {
                                                    fields: [
                                                      'deactivation_notes',
                                                      'reason',
                                                    ],
                                                  },
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                              ],
                                            },
                                            {
                                              btnValue: 'Suspend SP',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'log',
                                                  payload: [
                                                    {
                                                      reason: `#{formDataRaw.reason} - {formDataRaw.deactivation_notes}`,
                                                      deactivation_notes: `#{formDataRaw.deactivation_notes}`,
                                                    },
                                                  ],
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'navigate',
                                                  payload: [
                                                    '/field-ops/tasks/list-view',
                                                  ],
                                                },
                                              ],
                                            },
                                          ],
                                        },
                                      },
                                    ],
                                    navs: [],
                                  },
                                ],
                              },
                            ],
                          },
                        ],
                      },
                    },
                  ],
                  navs: [],
                },
              ],
            },
          ],
        },
        // #endregion
        // #region CONTACTING COMPLETE BUTTON
        {
          label: `Contacting Complete`,
          colorVariant: 'preferred',
          position: 'right',
          onClick: [
            {
              type: 'clientAction',
              action: 'submitAndNavigate',
              payload: [
                {
                  sp_id: '$task_details.object_id',
                  task_id: '$task_details.id',
                },
                {
                  url: '{VITE_SP_SERVER}/api/v1/spaas_actions/sp_contacted_for_training',
                  headers: {},
                  redirect: '/field-ops/tasks/list-view',
                },
                {
                  method: 'post',
                  action: '/field-ops/tasks/pre-training/details',
                },
              ],
            },
            {
              type: 'clientAction',
              action: 'closeModal',
            },
            {
              type: 'clientAction',
              action: 'clearStore',
              payload: ['task_details', 'sp_profile', 'company_documentation'],
            },
          ],
        },
        // #endregion
      ],
    },
    // #endregion
    // #region TASKS/EDIT/AREAS SCREEN
    areas: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'FormBuilder',
          layout: {
            paddingTop: '2rem',
          },
          props: {
            operational_area: '$sp_profile.operational_area',
            defaultValues: {
              radius: '$sp_profile.operational_area.0.operating_range',
              jobLocation: '$sp_profile.operational_area.0.location',
            },
            config: {
              style: {
                display: 'grid',
                gridTemplateColumns: '1fr',
                rowGap: '15px',
                justifyItems: 'center',
              },
              controls: [
                {
                  type: 'radius', // Custom control type for OperationAreas
                  name: 'operational_area',
                  label: 'Operational Area',
                  instructions: 'Drag the pin to adjust your work area radius',
                  marks: [
                    { value: 25, label: '25km' },
                    { value: 50, label: '50km' },
                    { value: 75, label: '75km' },
                    { value: 100, label: '100km' },
                    { value: 150, label: '150km' },
                    { value: 200, label: '200km' },
                    { value: 250, label: '250km' },
                  ],
                  disabledWhen: true,
                  css: { wrapper: { gridColumn: '1', gridRow: '1' } },
                },
              ],
            },
          },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store?.postData && (Object.keys($store.postData).length > 0) && (Object.keys($formState.errors).length === 0)',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: `
                                 You are about to exit this page without saving.
                               `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                  {
                                    text: `
                                 Would you like to save or Clear your changes?
                               `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            {
                                              fieldName: 'trading_as',
                                              defaultValue:
                                                '$sp_profile.details.trading_as',
                                            },
                                            {
                                              fieldName: 'bbeee',
                                              defaultValue:
                                                '$sp_profile.details.bbeee',
                                            },
                                            {
                                              fieldName: 'co_reg',
                                              defaultValue:
                                                '$sp_profile.details.co_reg',
                                            },
                                            {
                                              fieldName: 'name',
                                              defaultValue:
                                                '$sp_profile.details.name',
                                            },
                                            {
                                              fieldName: 'company_type',
                                              defaultValue:
                                                '$sp_profile.details.company_type',
                                            },
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: ['/field-ops/tasks/list-view'],
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndFetch',
                                        payload: [
                                          {
                                            postData: '$postData',
                                            sp_id: '$sp_profile.id',
                                          },
                                          {
                                            url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                            headers: {},
                                            bodySlicePath: 'postData',
                                          },
                                          {
                                            method: 'post',
                                            action:
                                              '/field-ops/tasks/list-view',
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: ['/field-ops/tasks/list-view'],
                    },
                  ],
                },
              },
            },
          ],
        },
        // #region DEACTIVATE SP BUTTON
        {
          label: `Deactivate SP`,
          colorVariant: 'alternative',
          position: 'right',
          onClick: [
            {
              type: 'clientAction',
              action: 'triggerModal',
              payload: [
                {
                  display: true,
                  type: 'warning',
                  layout: {
                    height: '75%',
                  },
                  onEnter: [],
                  onLeave: [],
                  fragments: [
                    {
                      component: 'Text',
                      props: {
                        textItems: [
                          {
                            text: 'Deactivate SP',
                            options: {
                              format: 'heading',
                              type: 'page-heading',
                            },
                          },
                          {
                            text: `
                      Deactivating this SP will remove them from the Connexa Platform.
                    `,
                            options: {
                              format: 'heading',
                              type: 'sub-heading',
                              style: {
                                paddingTop: '1rem',
                                textAlign: 'center',
                              },
                            },
                          },
                          {
                            text: `
                      Are you sure you want to deactivate this SP?
                    `,
                            options: {
                              format: 'heading',
                              type: 'sub-heading',
                              style: {
                                textAlign: 'center',
                              },
                            },
                          },
                        ],
                      },
                      layout: {
                        display: 'grid',
                        gridAutoFlow: 'row',
                        justifyItems: 'center',
                      },
                    },
                    {
                      component: 'ButtonRow',
                      layout: {
                        width: 'fit-content',
                        margin: '0 auto',
                      },
                      props: {
                        buttons: [
                          {
                            btnValue: 'No, cancel',
                            onClick: [
                              {
                                type: 'clientAction',
                                action: 'closeModal',
                              },
                            ],
                          },
                          {
                            btnValue: 'Yes, continue',
                            onClick: [
                              {
                                type: 'clientAction',
                                action: 'closeModal',
                              },
                              {
                                type: 'clientAction',
                                action: 'triggerModal',
                                payload: [
                                  {
                                    display: true,
                                    type: 'warning',
                                    layout: {
                                      height: '75%',
                                    },
                                    onEnter: [],
                                    onLeave: [],
                                    fragments: [
                                      {
                                        component: 'Text',
                                        props: {
                                          textItems: [
                                            {
                                              text: 'Deactivate SP',
                                              options: {
                                                format: 'heading',
                                                type: 'page-heading',
                                              },
                                            },
                                          ],
                                        },
                                        layout: {},
                                      },

                                      {
                                        component: 'FormBuilder',
                                        layout: {
                                          display: 'grid',
                                          justifyItems: 'center',
                                          marginTop: '2rem',
                                        },
                                        props: {
                                          config: {
                                            style: { width: '100%' },
                                            controls: [
                                              {
                                                type: 'single-select',
                                                dropdownScroll: true,
                                                name: 'reason',
                                                label:
                                                  'Reason for deactivate sp',
                                                placeholder: 'Select a Reason',
                                                labelProp: 'label',
                                                valueProp: 'value',
                                                validation: {},
                                                options: {
                                                  data: [
                                                    {
                                                      value:
                                                        'Fraudulant Activity',
                                                      label:
                                                        'Fraudulant Activity',
                                                    },
                                                    {
                                                      value: 'Requested by SP',
                                                      label: 'Requested by SP',
                                                    },
                                                  ],

                                                  source: 'literal',
                                                },
                                                css: {
                                                  wrapper: {
                                                    height: '4rem',
                                                    width: '100%',
                                                  },
                                                },
                                              },
                                              {
                                                type: 'textarea',
                                                name: 'deactivation_notes',
                                                label: 'Notes',
                                                rows: 10,
                                              },
                                            ],
                                          },
                                        },
                                      },
                                      {
                                        component: 'ButtonRow',
                                        layout: {
                                          width: 'fit-content',
                                          margin: '0 auto',
                                        },
                                        props: {
                                          buttons: [
                                            {
                                              btnValue: 'Cancel',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'resetFields',
                                                  payload: {
                                                    fields: [
                                                      'deactivation_notes',
                                                      'reason',
                                                    ],
                                                  },
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                              ],
                                            },
                                            {
                                              btnValue: 'Suspend SP',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'log',
                                                  payload: [
                                                    {
                                                      reason: `#{formDataRaw.reason} - {formDataRaw.deactivation_notes}`,
                                                      deactivation_notes: `#{formDataRaw.deactivation_notes}`,
                                                    },
                                                  ],
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'navigate',
                                                  payload: [
                                                    '/field-ops/tasks/list-view',
                                                  ],
                                                },
                                              ],
                                            },
                                          ],
                                        },
                                      },
                                    ],
                                    navs: [],
                                  },
                                ],
                              },
                            ],
                          },
                        ],
                      },
                    },
                  ],
                  navs: [],
                },
              ],
            },
          ],
        },
        // #endregion
        // #region CONTACTING COMPLETE BUTTON
        {
          label: `Contacting Complete`,
          colorVariant: 'preferred',
          position: 'right',
          onClick: [
            {
              type: 'clientAction',
              action: 'submitAndNavigate',
              payload: [
                {
                  sp_id: '$task_details.object_id',
                  task_id: '$task_details.id',
                },
                {
                  url: '{VITE_SP_SERVER}/api/v1/spaas_actions/sp_contacted_for_training',
                  headers: {},
                  redirect: '/field-ops/tasks/list-view',
                },
                {
                  method: 'post',
                  action: '/field-ops/tasks/pre-training/details',
                },
              ],
            },
            {
              type: 'clientAction',
              action: 'closeModal',
            },
            {
              type: 'clientAction',
              action: 'clearStore',
              payload: ['task_details', 'sp_profile', 'company_documentation'],
            },
          ],
        },
        // #endregion
      ],
    },
    // #endregion
  },
  /*
   * SECTION: TASKS/EDIT ACTION PANELS
   * Add all action panel item configurations here
   */
  // #region TASKS/EDIT ACTION PANELS
  actionPanels: [
    // #region SCRATCH PAD
    {
      icon: 'clipboard',
      title: 'Scratch Pad', //?actionPanel=Messages--bell-02
      // fetchCalls: [],
      layout: {},
      onEnter: [],
      onLeave: [],
      fragments: [
        {
          component: 'ScratchPadView',
          layout: { marginLeft: '10px', marginRight: '10px' },
          props: {
            titlePlaceholder: 'Heading',
            icon: 'trash-01',
            iconHandler: (data: { heading: string; body: string }) =>
              console.log(
                'got data: Heading - ' + data.heading + ' Body - ' + data.body
              ),
            placeHolder: 'Text here...',
          },
        },
      ],
      actionLevel: 'bottomControls',
    },
    // #endregion
  ],
  // #endregion
} satisfies StateConfig;
// #endregion
