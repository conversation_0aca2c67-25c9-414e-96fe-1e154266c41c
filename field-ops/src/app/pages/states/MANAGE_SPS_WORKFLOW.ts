import { companyProfileStates, StateConfig } from '@4-sure/ui-platform';

/*
 * SECTION: SPS STATE
 * SPS state which is the main section of the application
 */
// #region SPS STATE
export const MANAGE_SPS_WORKFLOW_STATE = {
  title: { template: '' },
  // #region SPS STATE LEVEL FETCH CALLS
  fetchCalls: [
    {
      key: 'sps',
      method: 'POST',
      body: {},
      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_sps',
      slicePath: 'payload',
    },
    {
      key: 'skills',
      method: 'POST',
      body: { enum: 'skills' },
      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_enum',
      slicePath: 'payload.skills',
    },
  ],
  // #endregion
  defaultScreen: 'list-view',
  screens: {
    /*
     * SECTION: SPS LIST VIEW
     * Sps list screen configuration
     */
    // #region SPS LIST VIEW SCREEN
    'list-view': {
      layout: {},
      fetchCalls: [],
      // #region SPS LIST FRAGMENTS
      fragments: [
        // TODO: add filters
        {
          component: 'TeamMemberCardList',
          props: {
            linkUrl: '/manage-sps/sps/edit/details',
            linkUrlParams: [{ key: 'sp_id', value: 'id' }],
            data: `js:{
                  const companyProfileStates = ${JSON.stringify(
                    companyProfileStates
                  )};
                  return sps.map(_sp => ({
                    ..._sp,
                    status: companyProfileStates[_sp.onboarding_state],
                    skills: _sp.skills.map(_skill => skills.find(_enum => _enum.id === _skill)?.name).join(", ")
                  }))
                }`,
            config: [
              {
                type: 'linkColumn',
                showAvatar: true,
                showTitleText: true,
                showRating: false,
                showStatusIndicator: false,
                titleKey: 'name',
              },
              { type: 'textColumn', titleKey: 'status' },
              { type: 'textColumn', titleKey: 'province' },
              { type: 'textColumn', titleKey: 'skills' },
              { type: 'textColumn', titleKey: 'contact_number' },
            ],
          },
          layout: {},
        },
      ],
      // #endregion
      navs: [],
    },
    // #endregion
  },
  /*
   * SECTION: SPS ACTION PANELS
   * Add all action panel item configurations here
   */
  // #region SPS ACTION PANELS
  actionPanels: [
    /*
     * SECTION: SPS SCRATCH PAD
     * Configuration for the scratch pad in the SPS state
     */
    {
      icon: 'clipboard',
      title: 'Scratch Pad', //?actionPanel=Messages--bell-02
      // fetchCalls: [],
      layout: {},
      onEnter: [],
      onLeave: [],
      fragments: [
        {
          component: 'ScratchPadView',
          layout: { marginLeft: '10px', marginRight: '10px' },
          props: {
            titlePlaceholder: 'Heading',
            icon: 'trash-01',
            iconHandler: (data: { heading: string; body: string }) =>
              console.log(
                'got data: Heading - ' + data.heading + ' Body - ' + data.body
              ),
            placeHolder: 'Text here...',
          },
        },
      ],
      actionLevel: 'bottomControls',
    },
  ],
  // #endregion
} satisfies StateConfig;
// #endregion
