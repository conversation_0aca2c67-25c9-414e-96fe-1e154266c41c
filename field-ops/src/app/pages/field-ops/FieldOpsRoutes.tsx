import {
  actionFunctionGenerator,
  makeFetchCalls,
  SpaPrivateRoute,
} from '@4-sure/ui-platform';
import Keycloak from 'keycloak-js';
import { Navigate, RouteObject } from 'react-router-dom';
import { envObject } from '../../../env';
import { getAppConfig } from '../../app-confg';
import { fieldOpsConfig } from './FieldOpsConfig';
import { FieldOpsModule } from './FieldOpsModule';
import { FieldOpsScreen } from './FieldOpsScreen';
import { FieldOpsState } from './FieldOpsState';

export const FieldOpsRoutes: (keycloak: Keycloak) => RouteObject = (
  keycloak: Keycloak
) => ({
  path: '/field-ops',
  element: <SpaPrivateRoute component={FieldOpsModule} />,
  loader: async ({ request }) => {
    const appConfig = await getAppConfig();
    const fetchCalls = appConfig.fetchCalls || [];
    const fetchResultsObject = await makeFetchCalls(
      fetchCalls,
      keycloak,
      request,
      envObject,
      'Bearer'
    );
    return { appConfig, fetchResultsObject };
  },
  action: actionFunctionGenerator(keycloak, envObject, 'Bearer'),
  children: Object.entries(fieldOpsConfig.states).reduce(
    (statesAcc, [statePath, stateConfig]) => {
      return [
        ...statesAcc,
        {
          path: statePath,
          element: <FieldOpsState />,
          loader: async ({ params, request }) => {
            const fetchCalls = stateConfig.fetchCalls || [];
            const fetchResultsObject = await makeFetchCalls(
              fetchCalls,
              keycloak,
              request,
              envObject,
              'Bearer'
            );
            return { stateConfig, fetchResultsObject };
          },
          action: actionFunctionGenerator(keycloak, envObject, 'Bearer'),
          children: Object.entries(stateConfig.screens).reduce(
            (screenAcc, [screenPath, screenConfig]) => {
              return [
                ...screenAcc,
                {
                  path: screenPath,
                  element: <FieldOpsScreen />,
                  loader: async ({ params, request }) => {
                    const fetchCalls = screenConfig.fetchCalls || [];
                    const fetchResultsObject = await makeFetchCalls(
                      fetchCalls,
                      keycloak,
                      request,
                      envObject,
                      'Bearer'
                    );
                    return {
                      screenConfig: { ...screenConfig, id: screenPath },
                      fetchResultsObject,
                    };
                  },
                  action: actionFunctionGenerator(
                    keycloak,
                    envObject,
                    'Bearer'
                  ),
                } as RouteObject,
              ];
            },
            [{ path: '', element: <Navigate to={stateConfig.defaultScreen} /> }]
          ),
        } as RouteObject,
      ];
    },
    [{ path: '', element: <Navigate to={fieldOpsConfig.defaultState} /> }]
  ),
});
