import {
  ScreenShell,
  useClientActionAsync,
  useErrorModal,
  useErrorStore,
  useSpaKeycloak,
} from '@4-sure/ui-platform';
import { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import {
  useFetcher,
  useLoaderData,
  useLocation,
  useNavigate,
  useNavigation,
  useSubmit,
} from 'react-router-dom';
import { envObject } from '../../../env';

export function FieldOpsScreen() {
  const data: any = useLoaderData();

  const fetcher = useFetcher({ key: `${data?.screenConfig.id}` });
  const submit = useSubmit();
  const navigation = useNavigation();
  const { keycloak } = useSpaKeycloak();
  const navigate = useNavigate();
  const location = useLocation();
  const formContext = useFormContext();
  const { callClientAction } = useClientActionAsync({
    keycloak,
    fetcher,
    navigate,
    submit,
    location,
    envObject,
    formContext,
  });

  const { errors, getErrorsBySource } = useErrorStore();
  const { handleServerError } = useErrorModal();
  const serverErrors = getErrorsBySource('server');
  useEffect(() => {
    console.log({ errors, serverErrors });
    if (errors.length > 0) {
      handleServerError(serverErrors[0].message);
    }
  }, [errors, serverErrors, callClientAction, handleServerError]);

  return (
    <ScreenShell
      callClientAction={callClientAction}
      fetcher={fetcher}
      submit={submit}
      navigation={navigation}
      screenConfig={data?.screenConfig || {}}
      clientDataObject={{ ...data?.fetchResultsObject }}
      envObject={envObject}
      keycloak={keycloak}
    />
  );
}
