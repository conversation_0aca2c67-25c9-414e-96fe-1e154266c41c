import { FieldOpsConfig } from '@4-sure/ui-platform';
import {
  FIELD_OPS_POST_TRAINING_STATE,
  FIELD_OPS_PRE_TRAINING_STATE,
  FIELD_OPS_TASK_EDIT_STATE,
  FIELD_OPS_TASKS_WORKFLOW_STATE,
} from '../states';

export const fieldOpsConfig: FieldOpsConfig = {
  defaultState: 'tasks',
  states: {
    tasks: FIELD_OPS_TASKS_WORKFLOW_STATE,
    'tasks/edit': FIELD_OPS_TASK_EDIT_STATE,
    'tasks/pre-training': FIELD_OPS_PRE_TRAINING_STATE,
    'tasks/post-training': FIELD_OPS_POST_TRAINING_STATE,
  },
};
