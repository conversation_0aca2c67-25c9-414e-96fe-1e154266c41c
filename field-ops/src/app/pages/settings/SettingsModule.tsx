import {
  AppShell,
  desktopDark,
  useAppStore,
  useClientActionAsync,
  useSpaKeycloak,
} from '@4-sure/ui-platform';
import {
  Outlet,
  useLoaderData,
  useLocation,
  useNavigate,
} from 'react-router-dom';

const rightsideTabs = [
  { name: 'Personal Settings', path: './personal' },
  //  { name: 'Company Settings', path: './company' },
];

export function SettingsModule() {
  const data: any = useLoaderData();
  const auth = useAppStore((state: any) => state.auth);
  const { keycloak } = useSpaKeycloak();
  const navigate = useNavigate();
  const location = useLocation();
  const { callClientAction } = useClientActionAsync({
    keycloak,
    navigate,
    location,
  });

  console.log('settings module!');

  return (
    <AppShell
      callClientAction={callClientAction}
      activeModuleName=""
      keycloak={keycloak}
      username={auth?.preferred_username}
      email={auth?.email}
      moduleTabs={[]}
      rightsideTabs={rightsideTabs}
      theme={desktopDark}
      appConfig={data?.appConfig}
      clientDataObject={{ ...data?.fetchResultsObject }}
    >
      <Outlet />
    </AppShell>
  );
}
