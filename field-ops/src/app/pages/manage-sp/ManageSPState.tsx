import {
  StateShell,
  useClientAction,
  useSpaKeycloak,
} from '@4-sure/ui-platform';
import { useFormContext } from 'react-hook-form';
import {
  Outlet,
  useFetcher,
  useLoaderData,
  useLocation,
  useNavigate,
  useSubmit,
} from 'react-router-dom';
import { envObject } from '../../../env';

// State
export function ManageSpState() {
  const data: any = useLoaderData();
  const fetcher = useFetcher({ key: `manage-sp-state-shell` });
  const submit = useSubmit();
  const { keycloak } = useSpaKeycloak();
  const navigate = useNavigate();
  const location = useLocation();
  const formContext = useFormContext();
  const { callClientAction } = useClientAction({
    keycloak,
    submit,
    fetcher,
    navigate,
    location,
    formContext,
    envObject,
  });

  return (
    <StateShell
      callClientAction={callClientAction}
      stateConfig={data?.stateConfig || {}}
      clientDataObject={{ ...data?.fetchResultsObject }}
      {...{ fetcher, submit }}
    >
      <Outlet />
    </StateShell>
  );
}
