import {
  actionFunctionGenerator,
  makeFetchCalls,
  SpaPrivateRoute,
} from '@4-sure/ui-platform';
import Keycloak from 'keycloak-js';
import { Navigate, RouteObject } from 'react-router-dom';
import { envObject } from '../../../env';
import { getAppConfig } from '../../app-confg';

import { manageSpsConfig } from './ManageSPConfig';
import { ManageSpModule } from './ManageSPModule';
import { ManageSpScreen } from './ManageSPScreen';
import { ManageSpState } from './ManageSPState';

export const ManageSpsRoutes: (keycloak: Keycloak) => RouteObject = (
  keycloak: Keycloak
) => ({
  path: '/manage-sps',
  element: <SpaPrivateRoute component={ManageSpModule} />,
  loader: async ({ request }) => {
    const appConfig = await getAppConfig();
    const fetchCalls = appConfig.fetchCalls || [];
    const fetchResultsObject = await makeFetchCalls(
      fetchCalls,
      keycloak,
      request,
      envObject
    );
    return { appConfig, fetchResultsObject };
  },
  action: actionFunctionGenerator(keycloak, envObject),
  children: Object.entries(manageSpsConfig.states).reduce(
    (statesAcc, [statePath, stateConfig]) => {
      return [
        ...statesAcc,
        {
          path: statePath,
          element: <ManageSpState />,
          loader: async ({ params, request }) => {
            const fetchCalls = stateConfig.fetchCalls || [];
            const fetchResultsObject = await makeFetchCalls(
              fetchCalls,
              keycloak,
              request,
              envObject
            );
            return { stateConfig, fetchResultsObject };
          },
          action: actionFunctionGenerator(keycloak, envObject),
          children: Object.entries(stateConfig.screens).reduce(
            (screenAcc, [screenPath, screenConfig]) => {
              return [
                ...screenAcc,
                {
                  path: screenPath,
                  element: <ManageSpScreen />,
                  loader: async ({ params, request }) => {
                    const fetchCalls = screenConfig.fetchCalls || [];
                    const fetchResultsObject = await makeFetchCalls(
                      fetchCalls,
                      keycloak,
                      request,
                      envObject
                    );
                    return {
                      screenConfig: { ...screenConfig, id: screenPath },
                      fetchResultsObject,
                    };
                  },
                  action: actionFunctionGenerator(keycloak, envObject),
                } as RouteObject,
              ];
            },
            [{ path: '', element: <Navigate to={stateConfig.defaultScreen} /> }]
          ),
        } as RouteObject,
      ];
    },
    [{ path: '', element: <Navigate to={manageSpsConfig.defaultState} /> }]
  ),
});
