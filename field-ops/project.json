{"name": "field-ops", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "field-ops/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/vite:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/field-ops"}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production"}}}, "serve": {"executor": "@nx/vite:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "field-ops:build"}, "configurations": {"development": {"buildTarget": "field-ops:build:development", "hmr": true}, "production": {"buildTarget": "field-ops:build:production", "hmr": false}}}, "preview": {"executor": "@nx/vite:preview-server", "defaultConfiguration": "development", "options": {"buildTarget": "field-ops:build"}, "configurations": {"development": {"buildTarget": "field-ops:build:development"}, "production": {"buildTarget": "field-ops:build:production"}}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "field-ops/jest.config.ts"}}}}