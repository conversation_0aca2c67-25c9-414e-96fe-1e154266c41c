{"name": "landing", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "landing/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/vite:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/landing"}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production"}}}, "serve": {"executor": "@nx/vite:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "landing:build"}, "configurations": {"development": {"buildTarget": "landing:build:development", "hmr": true}, "production": {"buildTarget": "landing:build:production", "hmr": false}}}, "preview": {"executor": "@nx/vite:preview-server", "defaultConfiguration": "development", "options": {"buildTarget": "landing:build"}, "configurations": {"development": {"buildTarget": "landing:build:development"}, "production": {"buildTarget": "landing:build:production"}}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "landing/jest.config.ts"}}}}