import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import 'styled-components';
import { desktopDark, useSpaKeycloak, } from '@4-sure/ui-platform';
import { Decision } from './Decision';
import { decisionLoaderFactory } from './decisionLoader';

type CustomTheme = typeof desktopDark;

declare module "styled-components" {
  export interface DefaultTheme extends CustomTheme {
    
  }
}


export function App() {
  const { keycloak } = useSpaKeycloak();
  
  const router = createBrowserRouter([
    {
      path: '/',
      element: <Decision />,
      loader: async (args) => {
        const loader = await decisionLoaderFactory(keycloak);
        return loader(args);
      },
    },
  ]);

  if (!keycloak) {
    return <h1>Loading...</h1>;
  }

  return (
    <RouterProvider router={router} />
  );
}

export default App;
