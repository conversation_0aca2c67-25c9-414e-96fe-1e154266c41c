import {
  desktopDark,
  Toolbar,
  ToolbarConfig,
  useClientAction,
  useSpaKeycloak,
} from '@4-sure/ui-platform';
import { useLoaderData, useLocation, useNavigate } from 'react-router-dom';
import styled, { ThemeProvider } from 'styled-components';
import { LandingPage } from './LandingPage';

const PageContainer = styled.div`
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const BackgroundImage = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('https://4-sure-media.s3.af-south-1.amazonaws.com/biab/login-screen-background-min.png');
  background-size: 110% 110%;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 0;
`;

const ToolbarContainer = styled.div`
  position: relative;
  z-index: 2;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
`;

const ContentContainer = styled.div`
  position: relative;
  flex: 1;
  z-index: 1;
  display: flex;
  flex-direction: column;
`;

interface LoaderData {
  component: string | null;
  props: { [key: string]: any };
}

export function Decision() {
  const { component, props } = useLoaderData() as LoaderData;
  const { keycloak } = useSpaKeycloak();
  const navigate = useNavigate();
  const location = useLocation();
  const { callClientAction } = useClientAction({
    keycloak,
    navigate,
    location,
  });

  const toolbarConfig: ToolbarConfig = {
    email: keycloak?.tokenParsed?.email || '',
    username: keycloak?.tokenParsed?.preferred_username || '',
    menuItems: [
      {
        label: '',
        menuItems: [
          {
            label: 'Logout',
            icon: 'bell-02',
            onClick: [{ type: 'clientAction', action: 'logout', payload: [] }],
          },
        ],
      },
    ],
    modulesList: [],
    buttonText: '',
  };

  const renderContent = () => {
    if (component === 'LandingPage') {
      return <LandingPage {...props} />;
    }
    return (
      <LandingPage
        headerText={'You are denied access!'}
        line1Text={'Denied'}
        line2Text={'Denied'}
      />
    );
  };

  return (
    <ThemeProvider theme={desktopDark}>
      <PageContainer>
        <BackgroundImage />
        <ToolbarContainer>
          <Toolbar
            callClientAction={callClientAction}
            activeModuleName=""
            moduleTabs={[]}
            rightsideTabs={[]}
            toolbarConfig={toolbarConfig}
            keycloak={keycloak}
          />
        </ToolbarContainer>
        <ContentContainer>{renderContent()}</ContentContainer>
      </PageContainer>
    </ThemeProvider>
  );
}
