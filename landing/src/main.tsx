import { StrictMode } from 'react';
import * as ReactDOM from 'react-dom/client';

import { SpaKeycloakAuthProvider } from '@4-sure/ui-platform';
import App from './app/app';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <StrictMode>
    <SpaKeycloakAuthProvider
      fieldAccessUrl={``}
      config={{
        clientId: import.meta.env.VITE_KEYCLOAK_CLIENT_ID,
        realm: import.meta.env.VITE_KEYCLOAK_REALM,
        url: import.meta.env.VITE_KEYCLOAK_URL,
      }}
    >
      <App />
    </SpaKeycloakAuthProvider>
  </StrictMode>
);
