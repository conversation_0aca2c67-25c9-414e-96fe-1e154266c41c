<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>BIAB - 4SURE</title>
    <base href="/" />

    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/src/styles.scss" />
    <style>
      html, body {
       overflow: hidden;
      }

      .app-loaded html, .app-loaded body {
          overflow: auto;
      }

      /* Styles for centering and animating the SVG */
    .splash-screen {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      min-height: 100vh;
      background-color: #121212; /* Background color for splash screen */
    }

    .splash-logo {
      animation: zoomInOut 2s ease-in-out infinite; /* Animation for zoom effect */
    }

    /* Keyframes for zoom in and out */
    @keyframes zoomInOut {
      0% {
        transform: scale(1); /* Initial scale */
      }
      50% {
        transform: scale(1.2); /* Zoom in */
      }
      100% {
        transform: scale(1); /* Zoom out */
      }
    }

    </style>
  </head>
  <body>
    <!-- <div class="splash-screen">
      <svg class="splash-logo" width="142" height="55" viewBox="0 0 142 55" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_10911_48505)">
        <path d="M54.9146 49.2744H50.233V41.7952H35L54.9146 13.0808V49.2744ZM50.233 37.5666V27.2579L43.0777 37.5666H50.233Z" fill="#6ECD23"/>
        <path d="M63.9864 37.531C65.2529 37.531 66.3315 37.3928 67.3826 36.4482C68.2923 35.6272 68.649 34.4878 68.649 33.1579C68.6577 32.6612 68.5864 32.1663 68.4379 31.6918C68.2866 31.2425 68.0502 30.8259 67.7414 30.4645C67.3935 30.0595 66.9883 29.7067 66.5383 29.4173C65.9797 29.0562 65.3908 28.7436 64.778 28.4832L62.1586 27.3815C58.4423 25.7939 56.5842 23.4705 56.5842 20.4113C56.5668 19.4318 56.7674 18.4605 57.1717 17.5667C57.5759 16.6728 58.1738 15.8785 58.9228 15.2402C60.4834 13.8439 62.4238 13.1458 64.7442 13.1458C67.8744 13.1458 68.5435 13.1165 73.3433 13.1165V17.0602L64.7083 17.0414C63.6744 17.0049 62.6643 17.3547 61.8778 18.0216C61.518 18.3141 61.2297 18.6838 61.0345 19.1027C60.8393 19.5217 60.7423 19.9791 60.7507 20.4406C60.7507 21.781 61.7357 22.8604 63.7057 23.6786L66.4138 24.8096C68.6188 25.713 70.2314 26.8132 71.2515 28.1104C72.2717 29.4075 72.7818 30.9986 72.7818 32.8835C72.7818 35.4094 71.9495 37.4975 70.2848 39.1479C68.6089 40.8088 66.5024 41.7869 64.016 41.7869H61.0821V37.531H63.9864Z" fill="white"/>
        <path d="M77.2291 13.1165V30.0644C77.2291 32.4828 77.6105 34.2505 78.3732 35.3675C79.5115 36.9858 81.1122 37.7949 83.175 37.7949C85.2379 37.7949 86.8442 36.9865 87.9938 35.3696C88.7565 34.2889 89.1371 32.5212 89.1357 30.0665V13.1165H93.2347V31.2436C93.2347 34.2107 92.3433 36.6542 90.5604 38.574C88.5552 40.7145 86.0927 41.7848 83.1729 41.7848C81.784 41.8084 80.4064 41.5337 79.1346 40.9795C77.8627 40.4253 76.7267 39.6047 75.8044 38.574C74.0202 36.6528 73.1287 34.2093 73.1301 31.2436V13.1165H77.2291Z" fill="white"/>
        <path d="M89.9378 29.1261L89.1526 13.1165H94.1845C97.9444 13.1165 100.66 13.8258 102.332 15.2444C104.177 16.8236 105.098 18.9089 105.097 21.5004C105.097 23.525 104.519 25.2654 103.364 26.7218C102.228 28.1665 100.606 29.1581 98.7943 29.5157L105.549 41.6947H100.293L94.2225 29.9974L92.8928 30.0184L89.9378 29.1261ZM93.4479 26.2484H94.8114C98.8781 26.2484 100.911 24.6874 100.911 21.5653C100.911 18.6415 98.9337 17.1789 94.9782 17.1775H93.4479V26.2484Z" fill="white"/>
        <path d="M121.864 17.1691H110.317V24.0367H121.539V28.0915H110.317V37.642H121.864V41.6947H105.971V13.1165H121.864V17.1691Z" fill="white"/>
        <path d="M60.1049 37.5121H55.8834V41.7868H60.1049V37.5121Z" fill="white"/>
        </g>
        <defs>
        <clipPath id="clip0_10911_48505">
        <rect width="88" height="36.1936" fill="white" transform="translate(35 13.0808)"/>
        </clipPath>
        </defs>
        </svg>

    </div> -->
    <div id="root"></div>
    <!-- <script>
      window.addEventListener('load', () => {
        document.body.classList.add('app-loaded');
    });
    </script> -->
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
