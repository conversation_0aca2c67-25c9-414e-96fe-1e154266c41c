{"name": "spa-test-e2e", "$schema": "../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "spa-test-e2e/src", "implicitDependencies": ["spa-test"], "targets": {"e2e": {"executor": "@nx/playwright:playwright", "outputs": ["{workspaceRoot}/dist/.playwright/spa-test-e2e"], "options": {"config": "spa-test-e2e/playwright.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}