/**
 * Do not edit directly, this file was auto-generated.
 */

export const ColorsBackgroundInverse = "#008000";
export const ColorsBackgroundShell = "#008000";
export const ColorsBackgroundModule = "#008000";
export const ColorsBackgroundToolbar = "#008000";
export const ColorsBackgroundWorkSpace = "#008000";
export const ColorsBackgroundActionBar = "#008000";
export const ColorsBackgroundShellStroke = "#008000";
export const ColorsBackgroundToolbarStroke = "#008000";
export const ColorsBackgroundWorkSpaceStroke = "#008000";
export const ColorsBackgroundModuleStroke = "#008000";
export const ColorsBackgroundActionBarStroke = "#008000";
export const ColorsBackgroundInverseStroke = "#008000";
export const ColorsUtilityColorWarning = "#ffffffbf";
export const ColorsUtilityColorError = "#ffffffbf";
export const ColorsUtilityColorSuccess = "#86efacbf";
export const ColorsUtilityColorFocus = "#ffffffbf";
export const ColorsUtilityColorProgress = "#ffffffbf";
export const ColorsControllersDefault = "#ffffff";
export const ColorsControllersInverse = "#ffffff";
export const ColorsControllersSecondary = "#ffffff";
export const ColorsControllersTertiary = "#ffffff";
export const ColorsStrokesDefault = "#ffffff";
export const ColorsStrokesFocus = "#ffffff";
export const ColorsStrokesInverse = "#ffffff";
export const ColorsStrokesGrey = "#ffffff";
export const ColorsCardColorJobCardPrimary = "#ffffff";
export const ColorsCardColorJobCardPrimaryStroke = "#ffffff";
export const ColorsCardColorDashboardPositive = "#ffffff";
export const ColorsCardColorDashboardError = "#ffffff";
export const ColorsCardColorDashboardWarning = "#ffffff";
export const ColorsCardColorDashboardPositiveStroke = "#ffffff";
export const ColorsCardColorDashboardErrorStroke = "#ffffff";
export const ColorsCardColorDashboardWarningStroke = "#ffffff";
export const ColorsCardColorCalendarPrimary = "#ffffff";
export const ColorsCardColorCalendarActive = "#ffffff";
export const ColorsCardColorCalendarWarning = "#ffffff";
export const ColorsCardColorCalendarError = "#ffffff";
export const ColorsCardColorCalendarSuccess = "#ffffff";
export const ColorsCardColorCalendarProgress = "#ffffff";
export const ColorsCardColorCalendarCreate = "#ffffff";
export const ColorsCardColorCalendarPrimaryStroke = "#ffffff";
export const ColorsCardColorCalendarActiveStroke = "#ffffff";
export const ColorsCardColorCalendarWarningStroke = "#ffffff";
export const ColorsCardColorCalendarErrorStroke = "#ffffff";
export const ColorsCardColorCalendarSuccessStroke = "#ffffff";
export const ColorsCardColorCalendarProgressStroke = "#ffffff";
export const ColorsCardColorCalendarCreateStroke = "#ffffff";
export const ColorsCardColorCoPilotWarning = "#ffffff";
export const ColorsCardColorCoPilotError = "#ffffff";
export const ColorsCardColorCoPilotWarningStroke = "#ffffff";
export const ColorsCardColorCoPilotErrorStroke = "#ffffff";
export const ColorsCardColorJobCardIndicatorPurple = "#ffffff";
export const ColorsCardColorJobCardIndicatorBlue = "#ffffff";
export const ColorsCardColorJobCardIndicatorBlack = "#ffffff";
export const ColorsCardColorJobCardIndicatorYellow = "#ffffff";
export const ColorsCardColorJobCardIndicatorOrange = "#ffffff";
export const ColorsCardColorJobCardIndicatorRed = "#ffffff";
export const ColorsCardColorJobCardIndicatorGreen = "#ffffff";
export const ColorsButtonColorToolbarButtonsPrimary = "#ffffff";
export const ColorsButtonColorToolbarButtonsHover = "#ffffff";
export const ColorsButtonColorToolbarButtonsActivated = "#ffffff";
export const ColorsButtonColorToolbarButtonsDisabled = "#ffffff";
export const ColorsButtonColorToolbarButtonsPrimaryStroke = "#ffffff";
export const ColorsButtonColorToolbarButtonsHoverStroke = "#ffffff";
export const ColorsButtonColorToolbarButtonsActivatedStroke = "#ffffff";
export const ColorsButtonColorToolbarButtonsDisabledStroke = "#ffffff";
export const ColorsButtonColorModuleNavigationPrimary = "#ffffff";
export const ColorsButtonColorModuleNavigationHover = "#ffffff";
export const ColorsButtonColorModuleNavigationActivated = "#ffffff";
export const ColorsButtonColorModuleNavigationDisabled = "#ffffff";
export const ColorsButtonColorModuleNavigationSecondary = "#ffffff";
export const ColorsButtonColorModuleNavigationPrimaryStroke = "#ffffff";
export const ColorsButtonColorModuleNavigationHoverStroke = "#ffffff";
export const ColorsButtonColorModuleNavigationActivatedStroke = "#ffffff";
export const ColorsButtonColorModuleNavigationDisabledStroke = "#ffffff";
export const ColorsButtonColorModuleNavigationSecondaryStroke = "#ffffff";
export const ColorsButtonColorActionPanelPrimary = "#ffffff";
export const ColorsButtonColorActionPanelHover = "#ffffff";
export const ColorsButtonColorActionPanelActivated = "#ffffff";
export const ColorsButtonColorActionPanelDisabled = "#ffffff";
export const ColorsButtonColorActionPanelPrimaryStroke = "#ffffff";
export const ColorsButtonColorActionPanelHoverStroke = "#ffffff";
export const ColorsButtonColorActionPanelActivatedStroke = "#ffffff";
export const ColorsButtonColorActionPanelDisabledStroke = "#ffffff";
export const ColorsButtonColorModuleFiltersPrimary = "#ffffff";
export const ColorsButtonColorModuleFiltersHover = "#ffffff";
export const ColorsButtonColorModuleFiltersActivated = "#ffffff";
export const ColorsButtonColorModuleFiltersDisabled = "#ffffff";
export const ColorsButtonColorModuleFiltersPrimaryStroke = "#ffffff";
export const ColorsButtonColorModuleFiltersHoverStroke = "#ffffff";
export const ColorsButtonColorModuleFiltersActivatedStroke = "#ffffff";
export const ColorsButtonColorModuleFiltersDisabledStroke = "#ffffff";
export const ColorsButtonColorModuleActionsPrimary = "#ffffff";
export const ColorsButtonColorModuleActionsHover = "#ffffff";
export const ColorsButtonColorModuleActionsActivated = "#ffffff";
export const ColorsButtonColorModuleActionsDisabled = "#ffffff";
export const ColorsButtonColorModuleActionsPrimaryStroke = "#ffffff";
export const ColorsButtonColorModuleActionsHoverStroke = "#ffffff";
export const ColorsButtonColorModuleActionsActivatedStroke = "#ffffff";
export const ColorsButtonColorModuleActionsDisabledStroke = "#ffffff";
export const ColorsButtonColorPagenationHover = "#ffffff";
export const ColorsButtonColorPagenationActivated = "#ffffff";
export const ColorsButtonColorPagenationDisabled = "#ffffff";
export const ColorsButtonColorPagenationHoverStroke = "#ffffff";
export const ColorsButtonColorPagenationActivatedStroke = "#ffffff";
export const ColorsButtonColorPagenationDisabledStroke = "#ffffff";
export const ColorsTypographyPrimary = "#ffffff";
export const ColorsTypographySecondary = "#ffffff";
export const ColorsTypographyDisabled = "#ffffff";
export const ColorsTypographyInverse = "#ffffff";
export const ColorsTypographyTertiary = "#ffffff";
export const ColorsTypographyLink = "#ffffff";
export const ColorsInputsPrimary = "#ffffff";
export const ColorsInputsInverse = "#ffffff";
export const ColorsInputsError = "#ffffff";
export const ColorsInputsPrimaryStroke = "#ffffff";
export const ColorsInputsInverseStroke = "#ffffff";
export const ColorsInputsErrorStroke = "#ffffff";
export const ColorsInputsNonEditable = "#ffffff";
export const ColorsInputsNonEditableStroke = "#ffffff";
export const ColorsTabsToolbarPrimary = "#ffffff";
export const ColorsTabsToolbarHover = "#ffffff";
export const ColorsTabsToolbarActivated = "#ffffff";
export const ColorsTabsToolbarDisabled = "#ffffff";
export const ColorsTabsToolbarPrimaryStroke = "#ffffff";
export const ColorsTabsToolbarHoverStroke = "#ffffff";
export const ColorsTabsToolbarActivatedStroke = "#ffffff";
export const ColorsTabsToolbarDisabledStroke = "#ffffff";
export const ColorsTabsModulePrimary = "#ffffff";
export const ColorsTabsModuleHover = "#ffffff";
export const ColorsTabsModuleActivated = "#ffffff";
export const ColorsTabsModuleDisabled = "#ffffff";
export const ColorsTabsModulePrimaryStroke = "#ffffff";
export const ColorsTabsModuleHoverStroke = "#ffffff";
export const ColorsTabsModuleActivatedStroke = "#ffffff";
export const ColorsTabsModuleDisabledStroke = "#ffffff";
export const ColorsIconColorPrimary = "#ffffff";
export const ColorsIconColorInverse = "#ffffff";
export const ColorsIconColorSecondary = "#ffffff";
export const ColorsIconColorTertiary = "#ffffff";
export const ColorsIconColorSelected = "#ffffff";
export const ColorsIconColorPrimaryStroke = "#ffffff";
export const ColorsIconColorInverseStroke = "#ffffff";
export const ColorsIconColorSecondaryStroke = "#ffffff";
export const ColorsIconColorTertiaryStroke = "#ffffff";
export const ColorsIconColorSelectedStroke = "#ffffff";
export const ColorsOverlaySurfaceOverlay = "#ffffff";
export const ColorsOverlayDynamicPanel = "#ffffff";
export const ColorsOverlayErrorOverlay = "#ffffff";
export const ColorsOverlayAlert = "#ffffff";
export const ColorsOverlaySurfaceOverlayStroke = "#ffffff";
export const ColorsOverlayDynamicPanelStroke = "#ffffff";
export const ColorsOverlayErrorOverlayStroke = "#ffffff";
export const ColorsOverlayAlertStroke = "#ffffff";
export const ColorsContextMenuSurfaceOverlay = "#ffffff";
export const ColorsContextMenuSurfaceOverlayStroke = "#ffffff";
export const ColorsTextListHover = "#ffffff";
export const ColorsTextListInverse = "#ffffff";
export const ColorsTextListHoverStroke = "#ffffff";
export const ColorsTextListInverseStroke = "#ffffff";
export const RadiusXxs = "0rem";
export const RadiusXs = "0rem";
export const RadiusSm = "0rem";
export const RadiusMd = "0rem";
export const RadiusLg = "0rem";
export const RadiusXl = "0rem";
export const RadiusXxl = "0rem";
export const RadiusRound = "0rem";
export const GapXxs = "0rem";
export const GapXs = "0rem";
export const GapSm = "0rem";
export const GapMd = "0rem";
export const GapLg = "0rem";
export const GapXl = "0rem";
export const GapXxl = "0rem";
export const SpacingXxs = "0rem";
export const SpacingXs = "0rem";
export const SpacingSm = "0rem";
export const SpacingMd = "0rem";
export const SpacingLg = "0rem";
export const SpacingXl = "0rem";
export const SpacingXxl = "0rem";
export const BottomActionDivider = "linear-gradient(90deg, #ffffff00 0%, #ffffff7d 50%, #ffffff00 100%)";
export const GradientColorsDividerGlow = "linear-gradient(90deg, #ffffff00 0%, #118ab27d 50%, #ffffff00 100%)";
export const GradientColorsPagenationGradient = "linear-gradient(0deg, #262728 0%, #26272800 94.79%)";
export const GradientColorsCalendarScrollbar = "linear-gradient(270deg, #262728 0%, #26272847 100%)";
export const GradientColorsActionPanelOverlay = "linear-gradient(180deg, #393d40 0%, #283033 100%)";
export const ToolbarButtonsSelected = {"color":"#fbfeff","type":"dropShadow","x":0,"y":0,"blur":5.199999809265137,"spread":0};
export const FontFamiliesInter = "Inter";
export const LineHeights0 = "AUTO";
export const FontWeightsInter0 = "Extra Light";
export const FontWeightsInter1 = "Semi Bold";
export const FontWeightsInter2 = "Thin";
export const FontWeightsInter3 = "Regular";
export const FontWeightsInter4 = "Bold Italic";
export const FontWeightsInter5 = "Light";
export const FontWeightsInter6 = "Bold";
export const FontWeightsInter7 = "Light Italic";
export const FontWeightsInter8 = "Semi Bold Italic";
export const FontSize0 = "10";
export const FontSize1 = "11.699999809265137";
export const FontSize2 = "12";
export const FontSize3 = "14";
export const FontSize4 = "16";
export const FontSize5 = "16.799999237060547";
export const FontSize6 = "21";
export const FontSize7 = "31.5";
export const FontSize8 = "47.29999923706055";
export const FontSize9 = "70";
export const LetterSpacing0 = "0%";
export const LetterSpacing1 = "-9%";
export const LetterSpacing2 = "0";
export const ParagraphSpacing0 = "0";
export const DesktopControlViewsPageHeading = {"fontFamily":"Inter","fontWeight":"Extra Light","lineHeight":"AUTO","fontSize":"31.5","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopControlViewsSubHeading = {"fontFamily":"Inter","fontWeight":"Semi Bold","lineHeight":"AUTO","fontSize":"21","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopFlowsHeadingsHero = {"fontFamily":"Inter","fontWeight":"Thin","lineHeight":"AUTO","fontSize":"70","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopFlowsHeadingsPageHeading = {"fontFamily":"Inter","fontWeight":"Thin","lineHeight":"AUTO","fontSize":"47.29999923706055","letterSpacing":"-9%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopFlowsHeadingsSubHeading = {"fontFamily":"Inter","fontWeight":"Extra Light","lineHeight":"AUTO","fontSize":"21","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopFlowsHeadingsSectionHeading = {"fontFamily":"Inter","fontWeight":"Extra Light","lineHeight":"AUTO","fontSize":"21","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopFlowsHeadingsMobileFlowsHero = {"fontFamily":"Inter","fontWeight":"Thin","lineHeight":"AUTO","fontSize":"31.5","letterSpacing":"-9%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopFlowsParagraphs = {"value":{"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"},"type":"typography","description":"p1r: Used for general body content (Paragraphs and Main Copy). This is just regular text","filePath":"ui-platform/src/lib/themes/tokens/Mobile/Light.json","isSource":true};
export const DesktopTabsToolbarTabDefault = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"11.699999809265137","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopTabsToolbarTabSelected = {"fontFamily":"Inter","fontWeight":"Semi Bold","lineHeight":"AUTO","fontSize":"11.699999809265137","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopTabsToolbarModuleSwitcher = {"fontFamily":"Inter","fontWeight":"Bold Italic","lineHeight":"AUTO","fontSize":"11.699999809265137","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopTabsModuleWarning = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"16.799999237060547","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopTabsModuleDefault = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"16.799999237060547","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopTabsModuleSelected = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"16.799999237060547","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopTabsModuleHover = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"16.799999237060547","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsJobClaimNumber = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsJobPrimaryText = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsJobSecondaryText = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsJobTertiaryText = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsJobTertiaryTextItalics = {"fontFamily":"Inter","fontWeight":"Light Italic","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsJobSubParagrapgh = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsJobClaimType = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsJobClaimHandler = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsJobClaimCustomer = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsJobClaimState = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsJobClaimTimer = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsJobJobAddress = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsJobJobAppointment = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsJobJobSkill = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsJobJobCompany = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsJobJobState = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsJobJobTimer = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsCalendarAddress = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsCalendarJobType = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsCalendarProgress = {"fontFamily":"Inter","fontWeight":"Light Italic","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsCalendarTime = {"fontFamily":"Inter","fontWeight":"Light Italic","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsTeamName = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsTeamActivity = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsTeamRating = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsTeamRole = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsTeamSkills = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsTeamNumber = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsTeamEmail = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsDashboardTilesMetric = {"fontFamily":"Inter","fontWeight":"Semi Bold","lineHeight":"AUTO","fontSize":"70","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsDashboardTilesHeading = {"fontFamily":"Inter","fontWeight":"Semi Bold","lineHeight":"AUTO","fontSize":"21","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopCardsDashboardTilesSubHeading = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"21","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopFormsHeading = {"fontFamily":"Inter","fontWeight":"Semi Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopFormsInputText = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopFormsSubText = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopFormsInputTextError = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopFiltersDefault = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopButtonsModuleNavigationMedium = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"uppercase","textDecoration":"none"};
export const DesktopButtonsModuleNavigationSmall = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"uppercase","textDecoration":"none"};
export const DesktopButtonsToolbar = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"capitalize","textDecoration":"none"};
export const DesktopButtonsOnboarding = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"21","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"uppercase","textDecoration":"none"};
export const DesktopButtonsModuleScreen = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopButtonsOnboardingSmall = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"16","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"uppercase","textDecoration":"none"};
export const DesktopSubtextS1sb = {"fontFamily":"Inter","fontWeight":"Semi Bold","lineHeight":"AUTO","fontSize":"10","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopSubtextS1r = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"10","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopParagraphP1el = {"fontFamily":"Inter","fontWeight":"Extra Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopParagraphP1l = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopParagraphP1b = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopParagraphP1bu = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"underline"};
export const DesktopParagraphP2r = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopParagraphP2b = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopRegistrationHeadingsSubHeading = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const DesktopRegistrationHeadingsHeader = {"fontFamily":"Inter","fontWeight":"Semi Bold","lineHeight":"AUTO","fontSize":"21","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileControlViewsPageHeading = {"fontFamily":"Inter","fontWeight":"Extra Light","lineHeight":"AUTO","fontSize":"31.5","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileControlViewsSubHeading = {"fontFamily":"Inter","fontWeight":"Semi Bold","lineHeight":"AUTO","fontSize":"21","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileControlViewsHeadingsPageHeading = {"fontFamily":"Inter","fontWeight":"Extra Light","lineHeight":"AUTO","fontSize":"31.5","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileControlViewsHeadingsSubHeadings = {"fontFamily":"Inter","fontWeight":"Semi Bold","lineHeight":"AUTO","fontSize":"21","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileFlowsHeadingsHero = {"fontFamily":"Inter","fontWeight":"Extra Light","lineHeight":"AUTO","fontSize":"31.5","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileFlowsHeadingsPageHeader = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"31.5","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileFlowsHeadingsPageHeading = {"fontFamily":"Inter","fontWeight":"Thin","lineHeight":"AUTO","fontSize":"47.29999923706055","letterSpacing":"-9%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileFlowsHeadingsSubHeading = {"fontFamily":"Inter","fontWeight":"Extra Light","lineHeight":"AUTO","fontSize":"21","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileFlowsHeadingsSectionHeading = {"fontFamily":"Inter","fontWeight":"Extra Light","lineHeight":"AUTO","fontSize":"21","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileFlowsHeadingsMobileFlowsHero = {"fontFamily":"Inter","fontWeight":"Thin","lineHeight":"AUTO","fontSize":"31.5","letterSpacing":"-9%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileFlowsParagraphs = {"value":{"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"},"type":"typography","description":"p1r: Used for general body content (Paragraphs and Main Copy). This is just regular text","filePath":"ui-platform/src/lib/themes/tokens/Mobile/Light.json","isSource":true};
export const MobileTabsToolbarModuleSwitcher = {"fontFamily":"Inter","fontWeight":"Semi Bold Italic","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileTabsToolbarTabDefault = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"11.699999809265137","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"underline"};
export const MobileTabsToolbarTabSelected = {"fontFamily":"Inter","fontWeight":"Semi Bold","lineHeight":"AUTO","fontSize":"11.699999809265137","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileTabsModuleWarning = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"16.799999237060547","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileTabsModuleDefault = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"16.799999237060547","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileTabsModuleSelected = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"16.799999237060547","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileTabsModuleHover = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"16.799999237060547","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsDashboardTilesHeadings = {"fontFamily":"Inter","fontWeight":"Semi Bold","lineHeight":"AUTO","fontSize":"21","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsDashboardTilesMetric = {"fontFamily":"Inter","fontWeight":"Semi Bold","lineHeight":"AUTO","fontSize":"70","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsDashboardTilesHeading = {"fontFamily":"Inter","fontWeight":"Semi Bold","lineHeight":"AUTO","fontSize":"21","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsDashboardTilesSubHeading = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"21","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsJobTertiaryText = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsJobPrimaryText = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsJobClaimNumber = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsJobSecondaryText = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsJobTertiaryTextItalics = {"fontFamily":"Inter","fontWeight":"Light Italic","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsJobSubParagrapgh = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsJobClaimType = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsJobClaimHandler = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsJobClaimCustomer = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsJobClaimState = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsJobClaimTimer = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsJobJobAddress = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsJobJobAppointment = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsJobJobSkill = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsJobJobCompany = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsJobJobState = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsJobJobTimer = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsCalendarAddress = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsCalendarJobType = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsCalendarProgress = {"fontFamily":"Inter","fontWeight":"Light Italic","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsCalendarTime = {"fontFamily":"Inter","fontWeight":"Light Italic","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsTeamName = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsTeamActivity = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsTeamRating = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsTeamRole = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsTeamSkills = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsTeamNumber = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileCardsTeamEmail = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileFormsHeading = {"fontFamily":"Inter","fontWeight":"Semi Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileFormsInputText = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileFormsSubText = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileFormsInputTextError = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileFiltersDefault = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"capitalize","textDecoration":"none"};
export const MobileButtonsModuleNavigation = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"uppercase","textDecoration":"none"};
export const MobileButtonsToolbar = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"capitalize","textDecoration":"none"};
export const MobileButtonsOnboarding = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"21","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileButtonsModuleScreen = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileSubtextS1sb = {"fontFamily":"Inter","fontWeight":"Semi Bold","lineHeight":"AUTO","fontSize":"10","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileSubtextS1r = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"10","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileParagraphP1el = {"fontFamily":"Inter","fontWeight":"Extra Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileParagraphP1l = {"fontFamily":"Inter","fontWeight":"Light","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"capitalize","textDecoration":"none"};
export const MobileParagraphP1b = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileParagraphP1bu = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"underline"};
export const MobileParagraphP2r = {"fontFamily":"Inter","fontWeight":"Regular","lineHeight":"AUTO","fontSize":"12","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileRegistrationHeadingsSubHeading = {"fontFamily":"Inter","fontWeight":"Bold","lineHeight":"AUTO","fontSize":"14","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const MobileRegistrationHeadingsHeader = {"fontFamily":"Inter","fontWeight":"Semi Bold","lineHeight":"AUTO","fontSize":"21","letterSpacing":"0%","paragraphSpacing":"0","paragraphIndent":"0px","textCase":"none","textDecoration":"none"};
export const TextCaseNone = "none";
export const TextCaseUppercase = "uppercase";
export const TextCaseCapitalize = "capitalize";
export const TextDecorationNone = "none";
export const TextDecorationUnderline = "underline";
export const ParagraphIndent0 = "0px";
