{"colors": {"background": {"inverse": {"value": "#008000", "type": "color"}, "shell": {"value": "#008000", "type": "color"}, "module": {"value": "#008000", "type": "color"}, "toolbar": {"value": "#008000", "type": "color"}, "work_space": {"value": "#008000", "type": "color"}, "action_bar": {"value": "#008000", "type": "color"}, "shell_stroke": {"value": "#008000", "type": "color"}, "toolbar_stroke": {"value": "#008000", "type": "color"}, "work_space_stroke": {"value": "#008000", "type": "color"}, "module_stroke": {"value": "#008000", "type": "color"}, "action_bar_stroke": {"value": "#008000", "type": "color"}, "inverse_stroke": {"value": "#008000", "type": "color"}}, "utility_color": {"warning": {"value": "#ffffffbf", "type": "color"}, "error": {"value": "#ffffffbf", "type": "color"}, "success": {"value": "#86efacbf", "type": "color"}, "focus": {"value": "#ffffffbf", "type": "color"}, "progress": {"value": "#ffffffbf", "type": "color"}}, "controllers": {"default": {"value": "#ffffff", "type": "color"}, "inverse": {"value": "#ffffff", "type": "color"}, "secondary": {"value": "#ffffff", "type": "color"}, "tertiary": {"value": "#ffffff", "type": "color"}}, "strokes": {"default": {"value": "#ffffff", "type": "color"}, "focus": {"value": "#ffffff", "type": "color"}, "inverse": {"value": "#ffffff", "type": "color"}, "grey": {"value": "#ffffff", "type": "color"}}, "card_color": {"job_card": {"primary": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}}, "dashboard": {"positive": {"value": "#ffffff", "type": "color"}, "error": {"value": "#ffffff", "type": "color"}, "warning": {"value": "#ffffff", "type": "color"}, "positive_stroke": {"value": "#ffffff", "type": "color"}, "error_stroke": {"value": "#ffffff", "type": "color"}, "warning_stroke": {"value": "#ffffff", "type": "color"}}, "calendar": {"primary": {"value": "#ffffff", "type": "color"}, "active": {"value": "#ffffff", "type": "color"}, "warning": {"value": "#ffffff", "type": "color"}, "error": {"value": "#ffffff", "type": "color"}, "success": {"value": "#ffffff", "type": "color"}, "progress": {"value": "#ffffff", "type": "color"}, "create": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "active_stroke": {"value": "#ffffff", "type": "color"}, "warning_stroke": {"value": "#ffffff", "type": "color"}, "error_stroke": {"value": "#ffffff", "type": "color"}, "success_stroke": {"value": "#ffffff", "type": "color"}, "progress_stroke": {"value": "#ffffff", "type": "color"}, "create_stroke": {"value": "#ffffff", "type": "color"}}, "co_pilot": {"warning": {"value": "#ffffff", "type": "color"}, "error": {"value": "#ffffff", "type": "color"}, "warning_stroke": {"value": "#ffffff", "type": "color"}, "error_stroke": {"value": "#ffffff", "type": "color"}}, "job_card_indicator": {"purple": {"value": "#ffffff", "type": "color"}, "blue": {"value": "#ffffff", "type": "color"}, "black": {"value": "#ffffff", "type": "color"}, "yellow": {"value": "#ffffff", "type": "color"}, "orange": {"value": "#ffffff", "type": "color"}, "red": {"value": "#ffffff", "type": "color"}, "green": {"value": "#ffffff", "type": "color"}}}, "button_color": {"toolbar_buttons": {"primary": {"value": "#ffffff", "type": "color"}, "hover": {"value": "#ffffff", "type": "color"}, "activated": {"value": "#ffffff", "type": "color"}, "disabled": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "hover_stroke": {"value": "#ffffff", "type": "color"}, "activated_stroke": {"value": "#ffffff", "type": "color"}, "disabled_stroke": {"value": "#ffffff", "type": "color"}}, "module_navigation": {"primary": {"value": "#ffffff", "type": "color"}, "hover": {"value": "#ffffff", "type": "color"}, "activated": {"value": "#ffffff", "type": "color"}, "disabled": {"value": "#ffffff", "type": "color"}, "secondary": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "hover_stroke": {"value": "#ffffff", "type": "color"}, "activated_stroke": {"value": "#ffffff", "type": "color"}, "disabled_stroke": {"value": "#ffffff", "type": "color"}, "secondary_stroke": {"value": "#ffffff", "type": "color"}}, "action_panel": {"primary": {"value": "#ffffff", "type": "color"}, "hover": {"value": "#ffffff", "type": "color"}, "activated": {"value": "#ffffff", "type": "color"}, "disabled": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "hover_stroke": {"value": "#ffffff", "type": "color"}, "activated_stroke": {"value": "#ffffff", "type": "color"}, "disabled_stroke": {"value": "#ffffff", "type": "color"}}, "module_filters": {"primary": {"value": "#ffffff", "type": "color"}, "hover": {"value": "#ffffff", "type": "color"}, "activated": {"value": "#ffffff", "type": "color"}, "disabled": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "hover_stroke": {"value": "#ffffff", "type": "color"}, "activated_stroke": {"value": "#ffffff", "type": "color"}, "disabled_stroke": {"value": "#ffffff", "type": "color"}}, "module_actions": {"primary": {"value": "#ffffff", "type": "color"}, "hover": {"value": "#ffffff", "type": "color"}, "activated": {"value": "#ffffff", "type": "color"}, "disabled": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "hover_stroke": {"value": "#ffffff", "type": "color"}, "activated_stroke": {"value": "#ffffff", "type": "color"}, "disabled_stroke": {"value": "#ffffff", "type": "color"}}, "pagenation": {"hover": {"value": "#ffffff", "type": "color"}, "activated": {"value": "#ffffff", "type": "color"}, "disabled": {"value": "#ffffff", "type": "color"}, "hover_stroke": {"value": "#ffffff", "type": "color"}, "activated_stroke": {"value": "#ffffff", "type": "color"}, "disabled_stroke": {"value": "#ffffff", "type": "color"}}}, "typography": {"primary": {"value": "#ffffff", "type": "color"}, "secondary": {"value": "#ffffff", "type": "color"}, "disabled": {"value": "#ffffff", "type": "color"}, "inverse": {"value": "#ffffff", "type": "color"}, "tertiary": {"value": "#ffffff", "type": "color"}, "link": {"value": "#ffffff", "type": "color"}}, "inputs": {"primary": {"value": "#ffffff", "type": "color"}, "inverse": {"value": "#ffffff", "type": "color"}, "error": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "inverse_stroke": {"value": "#ffffff", "type": "color"}, "error_stroke": {"value": "#ffffff", "type": "color"}, "non_editable": {"value": "#ffffff", "type": "color"}, "non_editable_stroke": {"value": "#ffffff", "type": "color"}}, "tabs": {"toolbar": {"primary": {"value": "#ffffff", "type": "color"}, "hover": {"value": "#ffffff", "type": "color"}, "activated": {"value": "#ffffff", "type": "color"}, "disabled": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "hover_stroke": {"value": "#ffffff", "type": "color"}, "activated_stroke": {"value": "#ffffff", "type": "color"}, "disabled_stroke": {"value": "#ffffff", "type": "color"}}, "module": {"primary": {"value": "#ffffff", "type": "color"}, "hover": {"value": "#ffffff", "type": "color"}, "activated": {"value": "#ffffff", "type": "color"}, "disabled": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "hover_stroke": {"value": "#ffffff", "type": "color"}, "activated_stroke": {"value": "#ffffff", "type": "color"}, "disabled_stroke": {"value": "#ffffff", "type": "color"}}}, "icon_color": {"primary": {"value": "#ffffff", "type": "color"}, "inverse": {"value": "#ffffff", "type": "color"}, "secondary": {"value": "#ffffff", "type": "color"}, "tertiary": {"value": "#ffffff", "type": "color"}, "selected": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "inverse_stroke": {"value": "#ffffff", "type": "color"}, "secondary_stroke": {"value": "#ffffff", "type": "color"}, "tertiary_stroke": {"value": "#ffffff", "type": "color"}, "selected_stroke": {"value": "#ffffff", "type": "color"}}, "overlay": {"surface_overlay": {"value": "#ffffff", "type": "color"}, "dynamic_panel": {"value": "#ffffff", "type": "color"}, "error_overlay": {"value": "#ffffff", "type": "color"}, "alert": {"value": "#ffffff", "type": "color"}, "surface_overlay_stroke": {"value": "#ffffff", "type": "color"}, "dynamic_panel_stroke": {"value": "#ffffff", "type": "color"}, "error_overlay_stroke": {"value": "#ffffff", "type": "color"}, "alert_stroke": {"value": "#ffffff", "type": "color"}}, "context_menu": {"surface_overlay": {"value": "#ffffff", "type": "color"}, "surface_overlay_stroke": {"value": "#ffffff", "type": "color"}}, "text_list": {"hover": {"value": "#ffffff", "type": "color"}, "inverse": {"value": "#ffffff", "type": "color"}, "hover_stroke": {"value": "#ffffff", "type": "color"}, "inverse_stroke": {"value": "#ffffff", "type": "color"}}}, "radius": {"xxs": {"value": 2, "type": "number"}, "xs": {"value": 4, "type": "number"}, "sm": {"value": 8, "type": "number"}, "md": {"value": 12, "type": "number"}, "lg": {"value": 16, "type": "number"}, "xl": {"value": 24, "type": "number"}, "xxl": {"value": 32, "type": "number"}, "round": {"value": 0, "type": "number"}}, "gap": {"xxs": {"value": 2, "type": "number"}, "xs": {"value": 4, "type": "number"}, "sm": {"value": 8, "type": "number"}, "md": {"value": 12, "type": "number"}, "lg": {"value": 16, "type": "number"}, "xl": {"value": 24, "type": "number"}, "xxl": {"value": 32, "type": "number"}}, "spacing": {"xxs": {"value": 2, "type": "number"}, "xs": {"value": 4, "type": "number"}, "sm": {"value": 8, "type": "number"}, "md": {"value": 12, "type": "number"}, "lg": {"value": 16, "type": "number"}, "xl": {"value": 24, "type": "number"}, "xxl": {"value": 32, "type": "number"}}}