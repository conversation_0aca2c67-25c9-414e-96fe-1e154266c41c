{"BottomActionDivider": {"value": "linear-gradient(90deg, #ffffff00 0%, #ffffff7d 50%, #ffffff00 100%)", "type": "color", "description": "This is used to show a clear divider between the content in a module or flow and the bottom action section (Pagenation of ModuleActionButtons)"}, "gradient_colors": {"DividerGlow": {"value": "linear-gradient(90deg, #ffffff00 0%, #118ab27d 50%, #ffffff00 100%)", "type": "color", "description": "Used to separate items"}, "PagenationGradient": {"value": "linear-gradient(0deg, #262728 0%, #26272800 94.79%)", "type": "color", "description": "Used for the Pagenation background linear gradient color"}, "calendar_scrollbar": {"value": "linear-gradient(270deg, #262728 0%, #26272847 100%)", "type": "color", "description": "Used as a background fade away for the scrollbar in the calendar component"}, "action_panel_overlay": {"value": "linear-gradient(180deg, #393d40 0%, #283033 100%)", "type": "color", "description": "Used for all action panel option overlays"}}, "ToolbarButtonsSelected": {"value": {"color": "#fbfeff", "type": "dropShadow", "x": 0, "y": 0, "blur": 5.199999809265137, "spread": 0}, "type": "boxShadow", "description": "The button glow that appears when the toolbar buttons have been clicked"}, "fontFamilies": {"inter": {"value": "Inter", "type": "fontFamilies"}}, "lineHeights": {"0": {"value": "AUTO", "type": "lineHeights"}}, "fontWeights": {"inter-0": {"value": "Extra Light", "type": "fontWeights"}, "inter-1": {"value": "Semi Bold", "type": "fontWeights"}, "inter-2": {"value": "Thin", "type": "fontWeights"}, "inter-3": {"value": "Regular", "type": "fontWeights"}, "inter-4": {"value": "Bold Italic", "type": "fontWeights"}, "inter-5": {"value": "Light", "type": "fontWeights"}, "inter-6": {"value": "Bold", "type": "fontWeights"}, "inter-7": {"value": "Light Italic", "type": "fontWeights"}, "inter-8": {"value": "Semi Bold Italic", "type": "fontWeights"}}, "fontSize": {"0": {"value": "10", "type": "fontSizes"}, "1": {"value": "11.699999809265137", "type": "fontSizes"}, "2": {"value": "12", "type": "fontSizes"}, "3": {"value": "14", "type": "fontSizes"}, "4": {"value": "16", "type": "fontSizes"}, "5": {"value": "16.799999237060547", "type": "fontSizes"}, "6": {"value": "21", "type": "fontSizes"}, "7": {"value": "31.5", "type": "fontSizes"}, "8": {"value": "47.29999923706055", "type": "fontSizes"}, "9": {"value": "70", "type": "fontSizes"}}, "letterSpacing": {"0": {"value": "0%", "type": "letterSpacing"}, "1": {"value": "-9%", "type": "letterSpacing"}, "2": {"value": "0", "type": "letterSpacing"}}, "paragraphSpacing": {"0": {"value": "0", "type": "paragraphSpacing"}}, "desktop": {"control_views": {"page_heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-0}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.7}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h1l: Used for the heading of the entire page"}, "sub_heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-1}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.6}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h2r: Used as the sub-heading text"}}, "flows": {"headings": {"hero": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-2}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.9}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h1t: Used for big descriptions"}, "page_heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-2}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.8}", "letterSpacing": "{letterSpacing.1}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h2t: This is used as the main page heading providing the simplest description of a page"}, "sub_heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-0}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.6}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h3el: Used to provide context to the Page Heading"}, "section_heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-0}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.6}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h3el: Used as the Heading for Important sections of information"}, "mobile": {"flows": {"hero": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-2}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.7}", "letterSpacing": "{letterSpacing.1}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}}}}, "paragraphs": {"value": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "p1r: Used for general body content (Paragraphs and Main Copy). This is just regular text"}, "property": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-1}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "p1sb : Used for general body content (Paragraphs and Main Copy)."}}}, "tabs": {"toolbar": {"tab_default": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.1}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "mt6ru: Used when the tabs are unselected"}, "tab_selected": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-1}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.1}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h6sb: Used when tabs are selected and are active"}, "module_switcher": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-4}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.1}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h6bi: Used for the menu tab headings."}}, "module": {"warning": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.5}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "default": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.5}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "selected": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.5}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "hover": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.5}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}}}, "cards": {"job": {"claim_number": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "primary_text": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "p1b: Used as the text for the most important piece of information on the card"}, "secondary_text": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "p1sb: Used as the text to highlight the second most important piece of information"}, "tertiary_text": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "p1el: Used as the text to highlight the third most important piece of information"}, "tertiary_text_italics": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-7}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "p1eli: Used as a stylize tertiary text"}, "sub_paragrapgh": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "p2r: Used for information that is not needed regularly"}, "claim_type": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "claim_handler": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "claim_customer": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "claim_state": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "claim_timer": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "job_address": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "job_appointment": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "job_skill": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "job_company": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "job_state": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "job_timer": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}}, "calendar": {"address": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for calendar card appointments"}, "job_type": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "progress": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-7}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "time": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-7}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}}, "team": {"name": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "activity": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "rating": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "role": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "skills": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "number": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "email": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}}, "dashboard_tiles": {"metric": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-1}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.9}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h1sb: Used for the card metric number"}, "heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-1}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.6}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h2sb: Used as the heading for the card"}, "sub_heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.6}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h2l: Used as the sub-heading for the card"}}}, "forms": {"heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-1}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for the Input headings when filling out forms"}, "input_text": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "i1r: This is used for all input text and form text"}, "sub_text": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for inputs as a sub text to provide context when filling out forms"}, "input_text_error": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for input error texts"}}, "filters": {"default": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for the filters"}}, "buttons": {"module_navigation_medium": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.2}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.uppercase}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for all CallToAction's"}, "module_navigation_small": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.2}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.uppercase}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for all CallToAction's"}, "toolbar": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.2}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.capitalize}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for navigation buttons"}, "onboarding": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.6}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.uppercase}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for onboarding buttons"}, "module_screen": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "onboarding_small": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.4}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.uppercase}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}}, "subtext": {"s1sb": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-1}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.0}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for text that provides context to other text but is not very necessary"}, "s1r": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.0}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for text that provides context to other text but is not very necessary"}}, "paragraph": {"p1el": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-0}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "This is used as a subtext to elaborate on the text that would be above it."}, "p1l": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "This is used for the tab headings of module pages"}, "p1b": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for general body content (Paragraphs and Main Copy). Bolded to attract the user to this copy"}, "p1bu": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.underline}"}, "type": "typography", "description": "Underlined version"}, "p2r": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for general body content (sub-Paragraphs)."}, "p2b": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}}, "registration": {"headings": {"sub_heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "header": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-1}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.6}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}}}}, "mobile": {"control_views": {"page_heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-0}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.7}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h1l: Used for the heading of the entire page"}, "sub_heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-1}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.6}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h2r: Used as the sub-heading text"}, "headings": {"page_heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-0}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.7}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "sub_headings": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-1}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.6}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}}}, "flows": {"headings": {"hero": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-0}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.7}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "page_header": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.7}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "page_heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-2}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.8}", "letterSpacing": "{letterSpacing.1}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h2t: This is used as the main page heading providing the simplest description of a page"}, "sub_heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-0}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.6}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h3el: Used to provide context to the Page Heading"}, "section_heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-0}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.6}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h3el: Used as the Heading for Important sections of information"}, "mobile": {"flows": {"hero": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-2}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.7}", "letterSpacing": "{letterSpacing.1}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}}}}, "paragraphs": {"value": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "property": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-1}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}}}, "tabs": {"toolbar": {"module_switcher": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-8}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "tab_default": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.1}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.underline}"}, "type": "typography", "description": "mt6ru: Used when the tabs are unselected"}, "tab_selected": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-1}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.1}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h6sb: Used when tabs are selected and are active"}}, "module": {"warning": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.5}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "default": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.5}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "selected": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.5}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "hover": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.5}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}}}, "cards": {"dashboard_tiles": {"headings": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-1}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.6}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "heading"}, "metric": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-1}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.9}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h1sb: Used for the card metric number"}, "heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-1}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.6}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h2sb: Used as the heading for the card"}, "sub_heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.6}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "h2l: Used as the sub-heading for the card"}}, "job": {"tertiary_text": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "primary_text": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "claim_number": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "secondary_text": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "p1sb: Used as the text to highlight the second most important piece of information"}, "tertiary_text_italics": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-7}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "p1eli: Used as a stylize tertiary text"}, "sub_paragrapgh": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "p2r: Used for information that is not needed regularly"}, "claim_type": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "claim_handler": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "claim_customer": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "claim_state": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "claim_timer": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "job_address": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "job_appointment": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "job_skill": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "job_company": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "job_state": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "job_timer": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}}, "calendar": {"address": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for calendar card appointments"}, "job_type": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "progress": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-7}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "time": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-7}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}}, "team": {"name": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "activity": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "rating": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "role": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "skills": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "number": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "email": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}}}, "forms": {"heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-1}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for the Input headings when filling out forms"}, "input_text": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "i1r: This is used for all input text and form text"}, "sub_text": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for inputs as a sub text to provide context when filling out forms"}, "input_text_error": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for the error text of input boxes"}}, "filters": {"default": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.capitalize}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for the filters"}}, "buttons": {"module_navigation": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.2}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.uppercase}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for all CallToAction's"}, "toolbar": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.2}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.capitalize}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for navigation buttons"}, "onboarding": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.6}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for onboarding buttons"}, "module_screen": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}}, "subtext": {"s1sb": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-1}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.0}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for text that provides context to other text but is not very necessary"}, "s1r": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.0}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for text that provides context to other text but is not very necessary"}}, "paragraph": {"p1el": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-0}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "This is used as a subtext to elaborate on the text that would be above it."}, "p1l": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-5}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.capitalize}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "This is used for the tab headings of module pages"}, "p1b": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for general body content (Paragraphs and Main Copy). Bolded to attract the user to this copy"}, "p1bu": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.underline}"}, "type": "typography", "description": "Underlined version"}, "p2r": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-3}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.2}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography", "description": "Used for general body content (sub-Paragraphs)."}}, "registration": {"headings": {"sub_heading": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-6}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.3}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}, "header": {"value": {"fontFamily": "{fontFamilies.inter}", "fontWeight": "{fontWeights.inter-1}", "lineHeight": "{lineHeights.0}", "fontSize": "{fontSize.6}", "letterSpacing": "{letterSpacing.0}", "paragraphSpacing": "{paragraphSpacing.0}", "paragraphIndent": "{paragraphIndent.0}", "textCase": "{textCase.none}", "textDecoration": "{textDecoration.none}"}, "type": "typography"}}}}, "textCase": {"none": {"value": "none", "type": "textCase"}, "uppercase": {"value": "uppercase", "type": "textCase"}, "capitalize": {"value": "capitalize", "type": "textCase"}}, "textDecoration": {"none": {"value": "none", "type": "textDecoration"}, "underline": {"value": "underline", "type": "textDecoration"}}, "paragraphIndent": {"0": {"value": "0px", "type": "dimension"}}}