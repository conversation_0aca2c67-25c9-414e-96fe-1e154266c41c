{"colors": {"background": {"inverse": {"value": "#121212", "type": "color"}, "shell": {"value": "#f4f4f4", "type": "color"}, "module": {"value": "#f4f4f4", "type": "color"}, "toolbar": {"value": "#f5f5f7", "type": "color"}, "work_space": {"value": "#f4f4f4", "type": "color"}, "action_bar": {"value": "#fbfbfb", "type": "color"}, "shell_stroke": {"value": "#f4f4f4", "type": "color"}, "action_bar_stroke": {"value": "#fbfbfb", "type": "color"}, "toolbar_stroke": {"value": "#f4f4f4", "type": "color"}, "work_space_stroke": {"value": "#f4f4f4", "type": "color"}, "module_stroke": {"value": "#f4f4f4", "type": "color"}, "inverse_stroke": {"value": "#121212", "type": "color"}}, "utility_color": {"warning": {"value": "#b28511", "type": "color"}, "error": {"value": "#b24111", "type": "color"}, "success": {"value": "#20b212", "type": "color"}, "focus": {"value": "#118ab2", "type": "color"}, "progress": {"value": "#11b29f", "type": "color"}}, "controllers": {"default": {"value": "#ffffff", "type": "color"}, "inverse": {"value": "#ffffff", "type": "color"}, "secondary": {"value": "#ffffff", "type": "color"}, "tertiary": {"value": "#ffffff", "type": "color"}, "default_stroke": {"value": "#ffffff", "type": "color"}, "secondary_stroke": {"value": "#ffffff", "type": "color"}, "tertiary_stroke": {"value": "#ffffff", "type": "color"}, "inverse_stroke": {"value": "#ffffff", "type": "color"}}, "strokes": {"default": {"value": "#121212", "type": "color"}, "focus": {"value": "#118ab2", "type": "color"}, "inverse": {"value": "#f0f0f0", "type": "color"}, "grey": {"value": "#8b8b8b", "type": "color"}, "error": {"value": "#b24111", "type": "color"}, "success": {"value": "#0f6f06", "type": "color"}}, "card_color": {"job_card": {"primary": {"value": "#ffffff", "type": "color"}, "stroke": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "stroke_stroke": {"value": "#ffffff", "type": "color"}}, "dashboard": {"positive": {"value": "#ffffff", "type": "color"}, "error": {"value": "#ffffff", "type": "color"}, "warning": {"value": "#ffffff", "type": "color"}, "positive_stroke": {"value": "#ffffff", "type": "color"}, "warning_stroke": {"value": "#ffffff", "type": "color"}, "error_stroke": {"value": "#ffffff", "type": "color"}}, "calendar": {"primary": {"value": "#ffffff", "type": "color"}, "active": {"value": "#ffffff", "type": "color"}, "warning": {"value": "#ffffff", "type": "color"}, "error": {"value": "#ffffff", "type": "color"}, "success": {"value": "#ffffff", "type": "color"}, "progress": {"value": "#ffffff", "type": "color"}, "create": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "active_stroke": {"value": "#ffffff", "type": "color"}, "progress_stroke": {"value": "#ffffff", "type": "color"}, "success_stroke": {"value": "#ffffff", "type": "color"}, "warning_stroke": {"value": "#ffffff", "type": "color"}, "error_stroke": {"value": "#ffffff", "type": "color"}, "create_stroke": {"value": "#ffffff", "type": "color"}}, "co_pilot": {"warning": {"value": "#ffffff", "type": "color"}, "error": {"value": "#ffffff", "type": "color"}, "warning_stroke": {"value": "#ffffff", "type": "color"}, "error_stroke": {"value": "#ffffff", "type": "color"}}, "job_card_indicator": {"purple": {"value": "#ffffff", "type": "color"}, "blue": {"value": "#ffffff", "type": "color"}, "green": {"value": "#ffffff", "type": "color"}, "red": {"value": "#ffffff", "type": "color"}, "orange": {"value": "#ffffff", "type": "color"}, "yellow": {"value": "#ffffff", "type": "color"}, "black": {"value": "#ffffff", "type": "color"}}}, "button_color": {"toolbar_buttons": {"primary": {"value": "#ffffff", "type": "color"}, "hover": {"value": "#ffffff", "type": "color"}, "activated": {"value": "#ffffff", "type": "color"}, "disabled": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "hover_stroke": {"value": "#ffffff", "type": "color"}, "activated_stroke": {"value": "#ffffff", "type": "color"}, "disabled_stroke": {"value": "#ffffff", "type": "color"}}, "module_navigation": {"default": {"value": "#ffffff", "type": "color"}, "hover": {"value": "#ffffff", "type": "color"}, "activated": {"value": "#ffffff", "type": "color"}, "disabled": {"value": "#ffffff", "type": "color"}, "highlight": {"value": "#ffffff", "type": "color"}, "inverse": {"value": "#ffffff", "type": "color"}, "default_stroke": {"value": "#ffffff", "type": "color"}, "highlight_stroke": {"value": "#ffffff", "type": "color"}, "hover_stroke": {"value": "#ffffff", "type": "color"}, "activated_stroke": {"value": "#ffffff", "type": "color"}, "disabled_stroke": {"value": "#ffffff", "type": "color"}, "inverse_stroke": {"value": "#ffffff", "type": "color"}}, "action_panel": {"primary": {"value": "#ffffff", "type": "color"}, "hover": {"value": "#ffffff", "type": "color"}, "activated": {"value": "#ffffff", "type": "color"}, "disabled": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "hover_stroke": {"value": "#ffffff", "type": "color"}, "activated_stroke": {"value": "#ffffff", "type": "color"}, "disabled_stroke": {"value": "#ffffff", "type": "color"}}, "module_filters": {"primary": {"value": "#ffffff", "type": "color"}, "hover": {"value": "#ffffff", "type": "color"}, "activated": {"value": "#ffffff", "type": "color"}, "disabled": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "hover_stroke": {"value": "#ffffff", "type": "color"}, "activated_stroke": {"value": "#ffffff", "type": "color"}, "disabled_stroke": {"value": "#ffffff", "type": "color"}}, "module_actions": {"primary": {"value": "#ffffff", "type": "color"}, "hover": {"value": "#ffffff", "type": "color"}, "activated": {"value": "#ffffff", "type": "color"}, "disabled": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "hover_stroke": {"value": "#ffffff", "type": "color"}, "activated_stroke": {"value": "#ffffff", "type": "color"}, "disabled_stroke": {"value": "#ffffff", "type": "color"}}, "pagenation": {"hover": {"value": "#ffffff", "type": "color"}, "activated": {"value": "#ffffff", "type": "color"}, "disabled": {"value": "#ffffff", "type": "color"}, "hover_stroke": {"value": "#ffffff", "type": "color"}, "activated_stroke": {"value": "#ffffff", "type": "color"}, "disabled_stroke": {"value": "#ffffff", "type": "color"}}}, "typography": {"primary": {"value": "#191919", "type": "color"}, "secondary": {"value": "#2e2d2d", "type": "color"}, "disabled": {"value": "#ccc9c9", "type": "color"}, "inverse": {"value": "#ffffff", "type": "color"}, "tertiary": {"value": "#9a9999", "type": "color"}, "link": {"value": "#a4deff", "type": "color"}, "primary_stroke": {"value": "#191919", "type": "color"}, "secondary_stroke": {"value": "#2e2d2d", "type": "color"}, "tertiary_stroke": {"value": "#9a9999", "type": "color"}, "disabled_stroke": {"value": "#ccc9c9", "type": "color"}, "inverse_stroke": {"value": "#ffffff", "type": "color"}, "link_stroke": {"value": "#a4deff", "type": "color"}}, "inputs": {"primary": {"value": "#ffffff", "type": "color"}, "inverse": {"value": "#ffffff", "type": "color"}, "error": {"value": "#ffffff", "type": "color"}, "non_editable": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "inverse_stroke": {"value": "#ffffff", "type": "color"}, "error_stroke": {"value": "#ffffff", "type": "color"}, "non_editable_stroke": {"value": "#ffffff", "type": "color"}}, "tabs": {"toolbar": {"primary": {"value": "#f4f4f4", "type": "color"}, "hover": {"value": "#ffffff", "type": "color"}, "activated": {"value": "#ffffff", "type": "color"}, "disabled": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "hover_stroke": {"value": "#ffffff", "type": "color"}, "activated_stroke": {"value": "#ffffff", "type": "color"}, "disabled_stroke": {"value": "#ffffff", "type": "color"}}, "module": {"primary": {"value": "#ffffff", "type": "color"}, "hover": {"value": "#ffffff", "type": "color"}, "activated": {"value": "#ffffff", "type": "color"}, "disabled": {"value": "#ffffff", "type": "color"}, "primary_stroke": {"value": "#ffffff", "type": "color"}, "hover_stroke": {"value": "#ffffff", "type": "color"}, "activated_stroke": {"value": "#ffffff", "type": "color"}, "disabled _stroke": {"value": "#ffffff", "type": "color"}}}, "icon_color": {"primary": {"value": "#191919", "type": "color"}, "inverse": {"value": "#ffffff", "type": "color"}, "secondary": {"value": "#2e2d2d", "type": "color"}, "tertiary": {"value": "#9a9999", "type": "color"}, "selected": {"value": "#118ab2", "type": "color"}, "primary_stroke": {"value": "#191919", "type": "color"}, "secondary_stroke": {"value": "#2e2d2d", "type": "color"}, "tertiary_stroke": {"value": "#9a9999", "type": "color"}, "inverse_stroke": {"value": "#ffffff", "type": "color"}, "selected_stroke": {"value": "#118ab2", "type": "color"}}, "overlay": {"surface_overlay": {"value": "#bdd4d9", "type": "color"}, "dynamic_panel": {"value": "#d6e4f1ed", "type": "color"}, "error_overlay": {"value": "#ffffff", "type": "color"}, "alert": {"value": "#ffffff", "type": "color"}, "surface_overlay_stroke": {"value": "#bdd4d9", "type": "color"}, "dynamic_panel_stroke": {"value": "#ffffff", "type": "color"}, "error_overlay_stroke": {"value": "#ffffff", "type": "color"}, "alert_stroke": {"value": "#ffffff", "type": "color"}}, "context_menu": {"surface_overlay": {"value": "#ffffff", "type": "color"}, "surface_overlay_stroke": {"value": "#ffffff", "type": "color"}}, "text_list": {"hover": {"value": "#ffffff", "type": "color"}, "inverse": {"value": "#ffffff", "type": "color"}, "hover_stroke": {"value": "#ffffff", "type": "color"}, "inverse_stroke": {"value": "#ffffff", "type": "color"}}, "interface_variant": {"15": {"value": "#262626", "type": "color"}, "35": {"value": "#595959", "type": "color"}, "45": {"value": "#737373", "type": "color"}, "65": {"value": "#a6a6a6", "type": "color"}, "100%": {"value": "#f8f8f8", "type": "color"}, "85%": {"value": "#d9d9d9", "type": "color"}}}, "radius": {"xxs": {"value": 2, "type": "number"}, "xs": {"value": 4, "type": "number"}, "sm": {"value": 8, "type": "number"}, "md": {"value": 12, "type": "number"}, "lg": {"value": 16, "type": "number"}, "xl": {"value": 24, "type": "number"}, "xxl": {"value": 32, "type": "number"}, "round": {"value": 104, "type": "number"}}, "gap": {"xxs": {"value": 2, "type": "number"}, "xs": {"value": 4, "type": "number"}, "sm": {"value": 8, "type": "number"}, "md": {"value": 12, "type": "number"}, "lg": {"value": 16, "type": "number"}, "xl": {"value": 24, "type": "number"}, "xxl": {"value": 32, "type": "number"}}, "spacing": {"xxs": {"value": 2, "type": "number"}, "xs": {"value": 4, "type": "number"}, "sm": {"value": 8, "type": "number"}, "md": {"value": 12, "type": "number"}, "lg": {"value": 16, "type": "number"}, "xl": {"value": 24, "type": "number"}, "xxl": {"value": 0, "type": "number"}}, "weight": {"xxs": {"value": 0, "type": "number"}, "xs": {"value": 0, "type": "number"}, "sm": {"value": 0, "type": "number"}, "md": {"value": 0, "type": "number"}, "lg": {"value": 0, "type": "number"}, "xl": {"value": 0, "type": "number"}, "xxl": {"value": 0, "type": "number"}, "xxxl": {"value": 0, "type": "number"}, "xxxxl": {"value": 0, "type": "number"}}, "font_weight": {"Thin": {"value": 100, "type": "number"}, "Extra Light": {"value": 200, "type": "number"}, "Light": {"value": 300, "type": "number"}, "Regular": {"value": 400, "type": "number"}, "Medium": {"value": 500, "type": "number"}, "Semi Bold": {"value": 600, "type": "number"}, "Bold": {"value": 700, "type": "number"}, "Extra Bold": {"value": 800, "type": "number"}, "Black": {"value": 900, "type": "number"}, "Thin Italic": {"value": 100, "type": "number"}, "Extra Light Italic": {"value": 200, "type": "number"}, "Light Italic": {"value": 300, "type": "number"}, "Regular Italic": {"value": 400, "type": "number"}, "Medium Italic": {"value": 500, "type": "number"}, "Semi Bold Italic": {"value": 600, "type": "number"}, "Bold Italic": {"value": 700, "type": "number"}, "Extra Bold Italic": {"value": 800, "type": "number"}, "Black Italic": {"value": 900, "type": "number"}}}