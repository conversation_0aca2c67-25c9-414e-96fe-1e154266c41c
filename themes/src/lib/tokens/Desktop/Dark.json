{"colors": {"background": {"inverse": {"value": "#f8f8f8", "type": "color"}, "shell": {"value": "#121212", "type": "color"}, "module": {"value": "#262728", "type": "color"}, "toolbar": {"value": "#121212", "type": "color"}, "work_space": {"value": "#121212", "type": "color"}, "action_bar": {"value": "#262728", "type": "color"}, "shell_stroke": {"value": "#121212", "type": "color"}, "action_bar_stroke": {"value": "#121212", "type": "color"}, "toolbar_stroke": {"value": "#121212", "type": "color"}, "work_space_stroke": {"value": "#121212", "type": "color"}, "module_stroke": {"value": "#121212", "type": "color"}, "inverse_stroke": {"value": "#f8f8f8", "type": "color"}}, "utility_color": {"warning": {"value": "#b28511", "type": "color"}, "error": {"value": "#b24111", "type": "color"}, "success": {"value": "#20b212", "type": "color"}, "focus": {"value": "#118ab2", "type": "color"}, "progress": {"value": "#11b29f", "type": "color"}}, "controllers": {"default": {"value": "#3f4142", "type": "color"}, "inverse": {"value": "#f8f8f8", "type": "color"}, "secondary": {"value": "#4e5052", "type": "color"}, "tertiary": {"value": "#66696b", "type": "color"}, "default_stroke": {"value": "#3f4142", "type": "color"}, "secondary_stroke": {"value": "#4e5052", "type": "color"}, "tertiary_stroke": {"value": "#66696b", "type": "color"}, "inverse_stroke": {"value": "#f8f8f8", "type": "color"}}, "strokes": {"default": {"value": "#f0f0f0", "type": "color"}, "focus": {"value": "#118ab2", "type": "color"}, "inverse": {"value": "#121212", "type": "color"}, "grey": {"value": "#8b8b8b", "type": "color"}, "error": {"value": "#b24111", "type": "color"}, "success": {"value": "#0f6f06", "type": "color"}}, "card_color": {"job_card": {"primary": {"value": "#3f4142", "type": "color"}, "stroke": {"value": "#3f4142", "type": "color"}, "primary_stroke": {"value": "#3f4142", "type": "color"}, "stroke_stroke": {"value": "#3f4142", "type": "color"}}, "dashboard": {"positive": {"value": "#118ab2bf", "type": "color"}, "error": {"value": "#b24111bf", "type": "color"}, "warning": {"value": "#b28511bf", "type": "color"}, "positive_stroke": {"value": "#118ab2bf", "type": "color"}, "warning_stroke": {"value": "#b28511bf", "type": "color"}, "error_stroke": {"value": "#b24111bf", "type": "color"}}, "calendar": {"primary": {"value": "#262728", "type": "color"}, "active": {"value": "#283133bf", "type": "color"}, "warning": {"value": "#333128bf", "type": "color"}, "error": {"value": "#332828bf", "type": "color"}, "success": {"value": "#293328bf", "type": "color"}, "progress": {"value": "#283133bf", "type": "color"}, "create": {"value": "#f8f8f840", "type": "color"}, "primary_stroke": {"value": "#262728", "type": "color"}, "active_stroke": {"value": "#283133bf", "type": "color"}, "progress_stroke": {"value": "#283133bf", "type": "color"}, "success_stroke": {"value": "#293328bf", "type": "color"}, "warning_stroke": {"value": "#333128bf", "type": "color"}, "error_stroke": {"value": "#332828bf", "type": "color"}, "create_stroke": {"value": "#f8f8f840", "type": "color"}}, "co_pilot": {"warning": {"value": "#b2851140", "type": "color"}, "error": {"value": "#b2411140", "type": "color"}, "warning_stroke": {"value": "#b2851140", "type": "color"}, "error_stroke": {"value": "#b2411140", "type": "color"}}, "job_card_indicator": {"purple": {"value": "#673ab7", "type": "color"}, "blue": {"value": "#0d47a1", "type": "color"}, "green": {"value": "#0f6f06", "type": "color"}, "red": {"value": "#b24111", "type": "color"}, "orange": {"value": "#f57c00", "type": "color"}, "yellow": {"value": "#ffa000", "type": "color"}, "black": {"value": "#3f4142", "type": "color"}}}, "button_color": {"toolbar_buttons": {"primary": {"value": "#262728", "type": "color"}, "hover": {"value": "#262728bf", "type": "color"}, "activated": {"value": "#118ab2bf", "type": "color"}, "disabled": {"value": "#26272880", "type": "color"}, "primary_stroke": {"value": "#646667", "type": "color"}, "hover_stroke": {"value": "#262728", "type": "color"}, "activated_stroke": {"value": "#262728", "type": "color"}, "disabled_stroke": {"value": "#262728", "type": "color"}}, "module_navigation": {"default": {"value": "#121212", "type": "color"}, "hover": {"value": "#283133bf", "type": "color"}, "activated": {"value": "#283133", "type": "color"}, "disabled": {"value": "#12121240", "type": "color"}, "highlight": {"value": "#114f64", "type": "color"}, "inverse": {"value": "#e5e5e5", "type": "color"}, "default_stroke": {"value": "#121212", "type": "color"}, "highlight_stroke": {"value": "#114f64", "type": "color"}, "hover_stroke": {"value": "#283133bf", "type": "color"}, "activated_stroke": {"value": "#283133", "type": "color"}, "disabled_stroke": {"value": "#12121240", "type": "color"}, "inverse_stroke": {"value": "#e5e5e5", "type": "color"}}, "action_panel": {"primary": {"value": "#262728", "type": "color"}, "hover": {"value": "#262728bf", "type": "color"}, "activated": {"value": "#118ab2bf", "type": "color"}, "disabled": {"value": "#26272880", "type": "color"}, "primary_stroke": {"value": "#262728", "type": "color"}, "hover_stroke": {"value": "#262728bf", "type": "color"}, "activated_stroke": {"value": "#118ab2bf", "type": "color"}, "disabled_stroke": {"value": "#26272880", "type": "color"}}, "module_filters": {"primary": {"value": "#3f4142", "type": "color"}, "hover": {"value": "#3f4142d6", "type": "color"}, "activated": {"value": "#283033", "type": "color"}, "disabled": {"value": "#12121240", "type": "color"}, "primary_stroke": {"value": "#3f4142", "type": "color"}, "hover_stroke": {"value": "#3f4142d6", "type": "color"}, "activated_stroke": {"value": "#283033", "type": "color"}, "disabled_stroke": {"value": "#12121240", "type": "color"}}, "module_actions": {"primary": {"value": "#283133d6", "type": "color"}, "hover": {"value": "#263133", "type": "color"}, "activated": {"value": "#283133", "type": "color"}, "disabled": {"value": "#12121280", "type": "color"}, "primary_stroke": {"value": "#283133d6", "type": "color"}, "hover_stroke": {"value": "#263133", "type": "color"}, "activated_stroke": {"value": "#283133", "type": "color"}, "disabled_stroke": {"value": "#12121280", "type": "color"}}, "pagenation": {"hover": {"value": "#262728bf", "type": "color"}, "activated": {"value": "#118ab259", "type": "color"}, "disabled": {"value": "#26272880", "type": "color"}, "hover_stroke": {"value": "#262728bf", "type": "color"}, "activated_stroke": {"value": "#118ab259", "type": "color"}, "disabled_stroke": {"value": "#26272880", "type": "color"}}}, "typography": {"primary": {"value": "#e5e5e5", "type": "color"}, "secondary": {"value": "#c4c4c4", "type": "color"}, "disabled": {"value": "#5c5b5b", "type": "color"}, "inverse": {"value": "#262728", "type": "color"}, "tertiary": {"value": "#919191", "type": "color"}, "link": {"value": "#a4deff", "type": "color"}, "primary_stroke": {"value": "#e5e5e5", "type": "color"}, "secondary_stroke": {"value": "#c4c4c4", "type": "color"}, "tertiary_stroke": {"value": "#919191", "type": "color"}, "disabled_stroke": {"value": "#5c5b5b", "type": "color"}, "inverse_stroke": {"value": "#262728", "type": "color"}, "link_stroke": {"value": "#a4deff", "type": "color"}}, "inputs": {"primary": {"value": "#4a5055", "type": "color"}, "inverse": {"value": "#f8f8f8", "type": "color"}, "error": {"value": "#ffebeb", "type": "color"}, "non_editable": {"value": "#262728", "type": "color"}, "primary_stroke": {"value": "#4a5055", "type": "color"}, "inverse_stroke": {"value": "#f8f8f8", "type": "color"}, "error_stroke": {"value": "#ffebeb", "type": "color"}, "non_editable_stroke": {"value": "#262728", "type": "color"}}, "tabs": {"toolbar": {"primary": {"value": "#262728", "type": "color"}, "hover": {"value": "#262728bf", "type": "color"}, "activated": {"value": "#118ab2bf", "type": "color"}, "disabled": {"value": "#26272880", "type": "color"}, "primary_stroke": {"value": "#444444", "type": "color"}, "hover_stroke": {"value": "#262728bf", "type": "color"}, "activated_stroke": {"value": "#118ab2bf", "type": "color"}, "disabled_stroke": {"value": "#26272880", "type": "color"}}, "module": {"primary": {"value": "#262728", "type": "color"}, "hover": {"value": "#262728bf", "type": "color"}, "activated": {"value": "#118ab2bf", "type": "color"}, "disabled": {"value": "#26272880", "type": "color"}, "primary_stroke": {"value": "#262728", "type": "color"}, "hover_stroke": {"value": "#262728bf", "type": "color"}, "activated_stroke": {"value": "#118ab2bf", "type": "color"}, "disabled _stroke": {"value": "#26272880", "type": "color"}}}, "icon_color": {"primary": {"value": "#e5e5e5", "type": "color"}, "inverse": {"value": "#262728", "type": "color"}, "secondary": {"value": "#c4c4c4", "type": "color"}, "tertiary": {"value": "#919191", "type": "color"}, "selected": {"value": "#118ab2", "type": "color"}, "primary_stroke": {"value": "#e5e5e5", "type": "color"}, "secondary_stroke": {"value": "#c4c4c4", "type": "color"}, "tertiary_stroke": {"value": "#919191", "type": "color"}, "inverse_stroke": {"value": "#262728", "type": "color"}, "selected_stroke": {"value": "#118ab2", "type": "color"}}, "overlay": {"surface_overlay": {"value": "#283133", "type": "color"}, "dynamic_panel": {"value": "#2e3236ed", "type": "color"}, "error_overlay": {"value": "#b24111b3", "type": "color"}, "alert": {"value": "#121212", "type": "color"}, "surface_overlay_stroke": {"value": "#283133", "type": "color"}, "dynamic_panel_stroke": {"value": "#2e3236ed", "type": "color"}, "error_overlay_stroke": {"value": "#560a0ab3", "type": "color"}, "alert_stroke": {"value": "#121212", "type": "color"}}, "context_menu": {"surface_overlay": {"value": "#283033d6", "type": "color"}, "surface_overlay_stroke": {"value": "#283033d6", "type": "color"}}, "text_list": {"hover": {"value": "#2e3236ed", "type": "color"}, "inverse": {"value": "#f8f8f8", "type": "color"}, "hover_stroke": {"value": "#2e3236ed", "type": "color"}, "inverse_stroke": {"value": "#f8f8f8", "type": "color"}}, "interface_variant": {"15": {"value": "#262626", "type": "color"}, "35": {"value": "#595959", "type": "color"}, "45": {"value": "#737373", "type": "color"}, "65": {"value": "#a6a6a6", "type": "color"}, "100%": {"value": "#f8f8f8", "type": "color"}, "85%": {"value": "#d9d9d9", "type": "color"}}}, "radius": {"xxs": {"value": 2, "type": "number"}, "xs": {"value": 4, "type": "number"}, "sm": {"value": 8, "type": "number"}, "md": {"value": 12, "type": "number"}, "lg": {"value": 16, "type": "number"}, "xl": {"value": 24, "type": "number"}, "xxl": {"value": 32, "type": "number"}, "round": {"value": 104, "type": "number"}}, "gap": {"xxs": {"value": 2, "type": "number"}, "xs": {"value": 4, "type": "number"}, "sm": {"value": 8, "type": "number"}, "md": {"value": 12, "type": "number"}, "lg": {"value": 16, "type": "number"}, "xl": {"value": 24, "type": "number"}, "xxl": {"value": 32, "type": "number"}}, "spacing": {"xxs": {"value": 2, "type": "number"}, "xs": {"value": 4, "type": "number"}, "sm": {"value": 8, "type": "number"}, "md": {"value": 12, "type": "number"}, "lg": {"value": 16, "type": "number"}, "xl": {"value": 24, "type": "number"}, "xxl": {"value": 32, "type": "number"}}, "weight": {"xxs": {"value": 100, "type": "number"}, "xs": {"value": 200, "type": "number"}, "sm": {"value": 300, "type": "number"}, "md": {"value": 400, "type": "number"}, "lg": {"value": 500, "type": "number"}, "xl": {"value": 600, "type": "number"}, "xxl": {"value": 700, "type": "number"}, "xxxl": {"value": 800, "type": "number"}, "xxxxl": {"value": 900, "type": "number"}}, "font_weight": {"Thin": {"value": 100, "type": "number"}, "Extra Light": {"value": 200, "type": "number"}, "Light": {"value": 300, "type": "number"}, "Regular": {"value": 400, "type": "number"}, "Medium": {"value": 500, "type": "number"}, "Semi Bold": {"value": 600, "type": "number"}, "Bold": {"value": 700, "type": "number"}, "Extra Bold": {"value": 800, "type": "number"}, "Black": {"value": 900, "type": "number"}, "Thin Italic": {"value": 100, "type": "number"}, "Extra Light Italic": {"value": 200, "type": "number"}, "Light Italic": {"value": 300, "type": "number"}, "Regular Italic": {"value": 400, "type": "number"}, "Medium Italic": {"value": 500, "type": "number"}, "Semi Bold Italic": {"value": 600, "type": "number"}, "Bold Italic": {"value": 700, "type": "number"}, "Extra Bold Italic": {"value": 800, "type": "number"}, "Black Italic": {"value": 900, "type": "number"}}}